import { useEffect, useRef } from "react";
import { scheduler, SchedulerStatic } from "dhtmlx-scheduler";
import moment from "moment";

interface UseSchedulerOptions {
  config?: (scheduler: SchedulerStatic) => void;
  events?: (scheduler: SchedulerStatic) => string[];
  data?: any;
  view?: string;
  timeoutCallback?: () => NodeJS.Timeout;
}

let timeout: NodeJS.Timeout | undefined;

export const useScheduler = (
  containerRef: React.RefObject<HTMLDivElement>,
  { config, events, data, view = "week", timeoutCallback }: UseSchedulerOptions
) => {
  const schedulerRef = useRef<SchedulerStatic | null>(null);

  useEffect(() => {
    let mounted = true;

    const schedulerEventsIds: string[] = [];

    const loadScheduler = async () => {
      if (!mounted || !containerRef.current) return;

      scheduler.config.start_on_monday = false;
      scheduler.config.hour_date = "%h:%i %A";
      scheduler.config.xml_date = "%Y-%m-%d %h:%i %a";
      scheduler.config.limit_time_select = true;
      scheduler.config.details_on_create = true;
      scheduler.config.details_on_dblclick = true;
      scheduler.config.prevent_cache = true;
      scheduler.config.repeat_precise = true;
      scheduler.config.multi_day = true;
      scheduler.config.occurrence_timestamp_in_utc = true;
      scheduler.config.include_end_by = true;
      scheduler.config.event_duration = 30; // set default event duration
      scheduler.config.auto_end_date = true;

      scheduler.config.time_step = 1; // time interval
      scheduler.config.first_hour = 0; //define start Time Hour
      scheduler.config.last_hour = 23.5; //define Last Time Hour
      scheduler.config.scroll_hour = 7; //sets the initial position of the vertical scroll in the scheduler (an hour in the 24-hour clock format)
      scheduler.config.full_day = true; //define whether include Full Day option

      // scheduler.config.dblclick_create = false;
      scheduler.config.drag_resize = false;
      scheduler.config.drag_in = false;
      scheduler.config.drag_move = false;
      scheduler.config.drag_create = false;
      scheduler.xy.margin_top = 40;

      scheduler.templates.hour_scale = function (date) {
        return moment(date).format("hh:mm A");
      };

      config?.(scheduler);

      // Event bindings
      const evntIds = events?.(scheduler);
      schedulerEventsIds.push(...(evntIds || []));

      if (!schedulerRef.current) {
        scheduler.init(containerRef.current, new Date(), view);
        schedulerRef.current = scheduler;
      }

      if (data) {
        scheduler.clearAll();
        scheduler.parse(data);
      }

      timeout = timeoutCallback?.();
    };

    loadScheduler();

    return () => {
      mounted = false;

      scheduler.clearAll();
      schedulerRef.current = null;

      if (schedulerEventsIds.length) {
        schedulerEventsIds.forEach((id) => {
          scheduler.detachEvent(id);
        });
      }

      if (timeout) {
        clearTimeout(timeout);
      }
    };
  }, [containerRef, config, data, view]);
};
