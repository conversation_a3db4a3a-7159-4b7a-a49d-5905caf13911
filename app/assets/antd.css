.ant-input-label {
  @apply break-words text-sm text-[#4B4B4B] !pr-0 font-semibold mb-0 w-full;
}

.cf-input-field.inline-edit input,
.cf-currency-input-field.inline-edit,
.cf-datepicker-field.inline-edit .ant-picker,
.cf-timepicker-field.inline-edit input,
.cf-textarea-field.inline-edit .ant-input,
.cf-select-field.inline-edit
  .ant-select-selector
  .ant-select-selection-item:not(
    .select-filed-header .ant-select-selector .ant-select-selection-item
  ),
.cf-select-field.inline-edit
  .ant-select-selector:not(
    .cf-select-field.inline-edit .select-filed-header .ant-select-selector,
    .common-filter
      .cf-select-field.inline-edit
      .filter-select-class
      .ant-select-selector,
    .cf-select-field.inline-edit
      .ant-select.select-apply-border
      .ant-select-selector
  ) {
  @apply text-primary-900 text-sm dark:text-white/90 hover:bg-[#f4f5f6] dark:hover:bg-dark-900 dark:focus-within:!bg-dark-900 focus-within:!bg-[#f4f5f6];
}

.common-filter .cf-new-input-field .ant-input[disabled],
.common-filter.cf-input-btn-filter .cf-input-field .ant-input[disabled],
.common-filter.cf-input-btn-filter
  > div:has(> div > div .cf-input-field .ant-input[disabled]),
.cf-input-field .ant-input[readonly],
.cf-currency-input-field input[readonly],
.cf-textarea-field .ant-input[readonly] {
  @apply cursor-no-drop;
}
.cf-input-field.cf-input-right .ant-input[readonly]:hover,
.cf-input-field.cf-input-right .ant-input[readonly]:focus {
  @apply !text-right;
}

.cf-input-field:has(
    > .ant-input-group-wrapper .ant-input-wrapper .ant-input[readonly]
  ),
.cf-input-field:has(> .ant-input[readonly]),
.cf-currency-input-field:has(> input[readonly]),
.cf-textarea-field:has(> .ant-input[readonly]) {
  @apply before:!scale-x-0;
}

.cf-select-field.inline-edit
  .ant-select.select-apply-border
  .ant-select-selector {
  @apply text-primary-900 text-sm;
}

.cf-select-field.inline-edit
  .ant-select.select-apply-border
  .ant-select-arrow
  span[aria-label="down"]
  svg {
  @apply dark:text-white/90;
}

.cf-select-field .ant-select-multiple .ant-select-selection-item {
  @apply hover:!bg-[#d9e1ed] dark:hover:!bg-dark-900;
}

.select-filed-header .ant-select-selector .ant-select-selection-item {
  @apply text-primary-900 text-13 dark:text-white/90;
}

.cf-datepicker-field.inline-edit .ant-picker.ant-picker-focused,
.cf-timepicker-field.inline-edit .ant-picker:hover,
.cf-datepicker-field.inline-edit .ant-picker.ant-picker-focused + .field-icon,
.cf-datepicker-field.inline-edit .ant-picker:hover + .field-icon {
  @apply dark:!bg-dark-900;
}

.cf-input-field.inline-edit > input,
.cf-currency-input-field.inline-edit > input {
  @apply !h-fit truncate;
}

.cf-input-field.apply-border
  > input:not(
    .inline-edit input,
    .file-photos-gallery,
    .url-inpul-filed,
    .input-border-apply .cf-input-field input
  ),
.cf-input-field
  .ant-input-group-wrapper:not(
    .cf-input-field.inlineleft-icon-suffix .ant-input-group-wrapper
  ),
.cf-currency-input-field.apply-border,
.cf-textarea-field .ant-input:not(.inline-edit .ant-input),
.cf-datepicker-field .ant-picker:not(.inline-edit .ant-picker),
.cf-timepicker-field .ant-picker:not(.inline-edit .ant-picker) {
  @apply !border-0 !border-b !border-solid !border-[#CED4DA];
}

.ant-select-selection-item,
.readOnly-select-field
  .ant-select-disabled.ant-select-multiple
  .ant-select-selection-overflow
  .ant-select-selection-item,
.readOnly-select-field
  .ant-select-borderless.ant-select-disabled:not(.ant-select-customize-input)
  .ant-select-selector,
.ant-select-selector,
.status-selection-dropdown .ant-select-arrow,
.status-selection-dropdown .ant-select-selection-item,
.status-selection-dropdown .ant-select-selector,
.retainage-payment-filed .ant-input-number-input {
  color: inherit;
}

.readOnly-select-field .cf-new-select-field.edit-inline .ant-select-arrow,
.field-select-inline-block
  .cf-select-field
  .ant-select:hover
  .ant-select-arrow:not(:last-child) {
  @apply !opacity-0;
}

.cf-datepicker-field.inline-edit .ant-picker-suffix,
.cf-timepicker-field.inline-edit .ant-picker-suffix,
.cf-select-field.inline-edit
  .ant-select-arrow:not(
    body .cf-select-field .select-filed-header .ant-select-arrow
  ),
.header-select-status-option .ant-select-arrow,
.multi-select-collapse .ant-collapse-expand-icon {
  @apply sm:opacity-0 text-primary-900 dark:text-white/90;
}

body
  .cf-select-field.inline-edit
  .ant-select.select-filed-header
  .ant-select-arrow,
.cf-datepicker-field.inline-edit:hover .ant-picker-suffix,
.cf-datepicker-field.inline-edit:focus-within .ant-picker-suffix,
.cf-timepicker-field.inline-edit:focus-within .ant-picker-suffix,
.multi-select-collapse:hover .ant-collapse-expand-icon,
.multi-select-collapse .ant-collapse-item-active .ant-collapse-expand-icon,
.cf-select-field.inline-edit
  .ant-select:hover
  .ant-select-arrow:not(
    .cf-select-field.inline-edit
      .ant-select.ant-select-disabled:hover
      .ant-select-arrow,
    .cf-select-field.inline-edit:hover
      .ant-select:hover
      .ant-select-arrow:not(:last-child)
  ),
.common-filter:hover
  .cf-select-field.inline-edit
  .ant-select
  .ant-select-arrow:not(
    .common-filter:hover
      .cf-select-field.inline-edit
      .ant-select.ant-select-disabled
      .ant-select-arrow
  ),
.cf-select-field.inline-edit.select-filed-header .ant-select .ant-select-arrow,
.cf-select-field.inline-edit .ant-select:focus-within .ant-select-arrow,
.header-select-status-option:hover .ant-select-arrow,
.header-select-status-option.ant-select-open .ant-select-arrow,
.preferences-client-filed:hover .ant-select .ant-select-arrow {
  @apply !opacity-100;
}

body
  .common-card
  .cf-select-field.inline-edit.select-filed-header
  .ant-select-selection-item {
  @apply !leading-[26px] text-13;
}

.cf-input-field,
.cf-currency-input-field,
.cf-select-field,
.cf-textarea-field,
.cf-datepicker-field,
.cf-inline-custom-btn,
.cf-timepicker-field {
  @apply relative;
}

.cf-input-field > input {
  @apply h-[34px] block;
}

.addon-before-icon .ant-input-group-wrapper,
.cf-datepicker-field,
.cf-currency-input-field,
.cf-timepicker-field {
  @apply h-[34px];
}

.cf-datepicker-field .ant-picker,
.cf-timepicker-field .ant-picker,
.cf-textarea-field .ant-input {
  @apply !px-0;
}

.cf-textarea-field.inline-edit .ant-input {
  @apply !pl-1.5 !pt-1.5 !pb-[3px] !pr-8 !leading-6 overflow-y-auto focus-within:overflow-y-auto focus-within:shadow-none;
}

.cf-datepicker-field.inline-edit .ant-picker,
.cf-timepicker-field.inline-edit .ant-picker {
  @apply !px-1.5 !py-1.5;
}

.cf-input-field,
.cf-currency-input-field,
.cf-select-field,
.cf-textarea-field,
.cf-datepicker-field,
.cf-inline-custom-btn,
.cf-timepicker-field {
  @apply before:absolute before:bottom-0 before:left-0 before:h-px before:w-full before:scale-x-0 before:origin-center before:bg-primary-900/60 dark:before:bg-[#696b6e]  before:z-[5];
}

.cf-textarea-field .ant-input {
  @apply mt-1.5 !min-h-[34px];
}

.cf-textarea-field.inline-edit .ant-input {
  @apply mt-0;
}

.cf-input-field:focus-within,
.cf-input-field:focus,
.cf-currency-input-field:focus-within,
.cf-currency-input-field:focus,
.cf-select-field:focus-within,
.cf-select-field:focus,
.cf-textarea-field:focus-within,
.cf-textarea-field:focus,
.cf-datepicker-field:focus-within,
.cf-datepicker-field:focus,
.cf-inline-custom-btn:focus-within,
.cf-inline-custom-btn:focus,
.cf-timepicker-field:focus-within,
.cf-timepicker-field:focus {
  @apply before:scale-x-100 before:transition-all before:ease-in-out before:duration-300;
}

.cf-currency-input-field {
  @apply before:-bottom-[1px];
}

.cf-select-field
  .ant-select-selector:not(
    .custom-select-dropdown .ant-select-selector,
    .cf-select-field.inline-edit .ant-select-selector,
    .select-add-icon-before .ant-select-selector
  ),
.cf-input-field .ant-input-wrapper > input,
.cf-input-field.inlineleft-icon-suffix .ant-input-affix-wrapper,
.ant-modal-content,
.file-photo-timeline-view .ant-collapse-content .ant-collapse-content-box,
.multi-select-collapse.no-data-list .ant-collapse-content-box,
.prifix-icon-number-field .ant-input-number-affix-wrapper{
    @apply !p-0
}

.prifix-icon-number-field .ant-input-number-prefix{
  @apply !pb-px
}

.cf-input-field.inputleft-icon-suffix .ant-input-group-addon {
  @apply px-0;
}

.addon-before-icon .ant-input-group-addon,
.inlineleft-icon-suffix .ant-input-group-addon {
  @apply pr-1 pl-0;
}

.cf-input-field.inlineleft-icon-suffix .ant-input-affix-wrapper {
  @apply focus-within:!shadow-none;
}

.cf-select-field
  .ant-select-selection-search:not(
    .custom-select-dropdown .ant-select-selection-search,
    .cf-singal-selection .ant-select-selection-search,
    .cf-select-field.inline-edit .ant-select-selection-search
  ),
.cf-select-field.inline-edit
  .ant-select-selection-overflow
  .ant-select-selection-search,
.cf-search-input .ant-input-group-addon {
  @apply !start-0;
}

.cf-select-field.inline-edit .ant-select-multiple .ant-select-selection-search {
  @apply !ml-0;
}

.cf-select-field.inline-edit
  .ant-select-selector:not(
    .common-filter
      .cf-select-field.inline-edit
      .filter-select-class
      .ant-select-selector,
    .addon-before-class .cf-select-field.inline-edit .ant-select-selector,
    .cf-select-field.inline-edit .custom-select-option .ant-select-selector
  ) {
  @apply !pl-1.5 !pr-6;
}

.cf-select-field.inline-edit .ant-select-selection-search {
  @apply !start-1.5;
}

.cf-select-field.inline-edit .ant-select-selection-placeholder {
  @apply left-1.5;
}

.cf-textarea-field .ant-input.ant-input-disabled {
  @apply hover:!resize-none focus-within:!resize-none;
}

.cf-textarea-field .ant-input.ant-input-disabled,
.cf-currency-input-field:has(> input[disabled]),
body
  .cf-select-field.inline-edit
  .ant-select.select-filed-header
  .ant-select-selector,
body
  .cf-select-field.inline-edit
  .ant-select.select-filed-header
  .ant-select-selection-item,
body .cf-select-field.inline-edit .ant-select.select-filed-header .ant-select,
body
  .common-filter
  .cf-select-field.inline-edit.custom-select-dropdown
  .ant-select-selector {
  @apply focus-within:!bg-transparent hover:!bg-transparent;
}

.ant-popover
  .common-filter
  .cf-select-field.inline-edit
  .ant-select-selection-placeholder {
  @apply left-0;
}

.more-option-filter {
  @apply min-h-[20px];
}

.cf-select-field.multiselect-tags .ant-select-selection-placeholder,
.checkBox-Font600 > span + span {
  @apply font-semibold;
}

.cf-select-field.multiselect-tags .ant-select-selection-search,
.cf-select-field.multiselect-tags .ant-select-selection-placeholder {
  @apply start-0 ms-0;
}

.ant-picker-dropdown
  .ant-picker-cell-in-view.ant-picker-cell-today
  .ant-picker-cell-inner::before,
.ant-checkbox .ant-checkbox-input:focus-visible + .ant-checkbox-inner {
  @apply border-primary-900;
}

.ant-popover-content .ant-btn-primary {
  @apply !bg-primary-900 shadow-none;
}

.ant-popover-content .ant-popconfirm-title,
.mleso-items-filter .ant-select-selector {
  @apply text-13;
}

.mleso-items-filter .ant-select-selector .ant-select-selection-item {
  @apply font-medium;
}

.ant-popover-content
  .ant-btn-icon[style="width: 0px; opacity: 0; transform: scale(0);"],
.ant-btn .ant-btn-icon:empty,
.cf-input-field .ant-input-suffix:empty,
.ant-checkbox-wrapper .ant-checkbox + span {
  @apply !m-0 !p-0;
}

.ant-select-dropdown.ant-slide-up-leave:not(
    .ant-select-dropdown.ant-slide-up-leave.inner-page-select-option
  ) {
  @apply !z-[9999] !block;
}

.radio-icon-btn,
.ant-progress-block .ant-progress-outer,
.ant-btn .ant-btn-icon {
  @apply flex;
}

.radio-icon-btn .ant-radio-button-wrapper {
  @apply p-1.5 first:rounded-r-none last:rounded-l-none;
}

.radio-icon-btn-border .ant-radio-button-wrapper {
  @apply !border-[#d9d9d9] before:hidden !bg-transparent;
}

.notification-btn .ant-tabs-nav-list .ant-tabs-tab {
  @apply w-1/2 justify-center text-13 hover:text-primary-900;
}

.radio-list-btn .ant-radio-button-wrapper {
  @apply border-0 !text-[15px] h-9 focus-within:outline-0 !px-2.5 !whitespace-nowrap !font-semibold !text-black !bg-transparent before:transition-all before:duration-300 before:ease-in-out before:absolute before:top-auto before:!p-0 before:bottom-0 before:rounded before:w-0 before:h-[3px] before:!bg-primary-900 dark:before:bg-white/60 before:left-1/2 before:-translate-x-1/2;
}

.radio-list-btn .ant-radio-button-wrapper.ant-radio-button-wrapper-checked {
  @apply before:w-full;
}

.checbox-success .ant-checkbox-checked .ant-checkbox-inner {
  @apply bg-[#66bb6a] border-[#66bb6a];
}

.checbox-success:hover .ant-checkbox-checked .ant-checkbox-inner {
  @apply !bg-[#66bb6a] !border-[#66bb6a];
}

.checbox-success:hover .ant-checkbox-checked::after {
  @apply !border-[#66bb6a];
}

.orange-checkBox .ant-checkbox-checked .ant-checkbox-inner,
.orange-checkBox:hover .ant-checkbox-checked .ant-checkbox-inner {
  @apply !bg-orange-500 !border-orange-500;
}

.orange-checkBox:hover .ant-checkbox-checked:not(.ant-checkbox-disabled):after {
  @apply !border-orange-500;
}

.dropdown-color-option-block.ant-dropdown
  .ant-dropdown-menu
  .ant-dropdown-menu-item-selected,
.ant-select-dropdown
  .ant-select-item-option-active:not(.ant-select-item-option-disabled),
.ant-select-dropdown
  .ant-select-item:not(.ant-select-dropdown .ant-select-item-option-disabled, .ant-select-dropdown .ant-select-item.ant-select-item-group) {
  @apply hover:!bg-blue-50 dark:hover:!bg-dark-900;
}

.dropdown-color-option-block ul {
  @apply max-h-[45vh];
}

.dropdown-color-option-block.ant-dropdown
  .ant-dropdown-menu
  .ant-dropdown-menu-item-selected,
.ant-select-dropdown
  .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  @apply text-primary-900 bg-blue-50 dark:bg-dark-800 dark:text-white;
}

.inner-page-select-option.ant-select-dropdown
  .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  @apply font-normal;
}

.ant-radio-group .ant-radio-button + span {
  @apply flex items-center justify-center h-full w-full sm:!text-13 !text-[11px] font-semibold;
}

.radio-list-btn.ant-radio-group .ant-radio-button + span {
  @apply text-[15px] dark:text-white;
}

.list-tab-button label {
  @apply !border-0 bg-[#EEEFF0] dark:bg-dark-800 h-auto px-3 py-[3px];
}

.radio-icon-btn label,
.list-tab-button label,
.list-text-button label {
  @apply dark:bg-dark-800 dark:!text-white;
}

.radio-icon-btn label:hover,
.list-tab-button label:hover,
.list-text-button label:hover {
  @apply dark:text-white;
}

.radio-icon-btn label.active,
.radio-icon-btn label.active:hover {
  @apply bg-primary-900 !border-[#d9d9d9] dark:!text-primary-900 dark:bg-white;
}

.list-text-button
  label.ant-radio-button-wrapper-checked
  .ant-radio-button-checked {
  @apply !outline-0;
}

.list-text-button label.ant-radio-button-wrapper-checked,
.list-text-button label.ant-radio-button-wrapper-checked:hover {
  @apply !bg-[#f1f4f9] dark:!bg-dark-950 !border-[#d9d9d9] dark:!border-dark-900 dark:!text-white/90 !outline-0;
}

.directory-select-filter .rc-virtual-list-scrollbar,
.ant-input-search
  .ant-input-suffix:has(> .ant-input-clear-icon.ant-input-clear-icon-hidden),
.radio-list-btn label.ant-radio-button-wrapper[style="display: none;"],
.inline-loading-success .anticon-calendar,
.inline-loading-success .ant-picker-clear,
.inline-loading-success .ant-select-clear,
.inline-loading-success .right-from-square-icon,
.inline-loading-success
  .ant-select-arrow:not(
    .inline-loading-success .select-filed-header .ant-select-arrow,
    .common-filter
      .inline-loading-success
      .select-filed-header
      .ant-select-arrow
  ) {
  @apply !hidden;
}

.list-tab-button label.active,
.list-tab-button label.active:hover {
  @apply !bg-white !border-[#d9d9d9] dark:!text-primary-900;
}

.list-tab-button label:before {
  content: none !important;
}

.list-tab-button label.active:before,
.list-text-button label.active::before,
.radio-icon-btn label.active:before {
  content: none;
}

.list-text-button label {
  @apply first:rounded-r-none last:rounded-l-none;
}

.list-text-button label:not(:first-child):not(:last-child) {
  @apply rounded-none;
}

.list-text-button label span + span {
  @apply sm:!text-[14px] !text-[12px];
}

.widget-tab label span + span {
  @apply !text-[13px] !font-normal;
}

.cf-search-input .ant-input-wrapper {
  direction: rtl;
}

.cf-search-input .ant-input-affix-wrapper {
  direction: ltr;
}

.cf-search-input .ant-input-affix-wrapper {
  @apply !rounded-r-md !rounded-l-none h-[34px] !border-0 dark:bg-[#ffffff0a];
}

.cf-datepicker-field.inline-edit .ant-picker-input:hover .ant-picker-clear {
  @apply bg-[#f4f5f6] w-3.5 h-3.5 dark:bg-dark-900;
}

.cf-datepicker-field .ant-picker-input .ant-picker-suffix,
.cf-timepicker-field .ant-picker-input .ant-picker-suffix {
  @apply dark:text-white/90;
}

.cf-search-input .ant-input-affix-wrapper input {
  @apply placeholder:!text-[#bdbdbd] dark:placeholder:!text-white/25;
}

.cf-input-field .ant-input-affix-wrapper .ant-input[readonly]:hover,
.cf-input-field .ant-input-affix-wrapper:has(> .ant-input[readonly]):hover,
.ant-picker .ant-picker-input > input[disabled]:hover,
.cf-search-input .ant-input-affix-wrapper input,
.cf-input-field .ant-input-group-wrapper .ant-input-group-addon,
.file-photo-timeline-view .ant-collapse-content,
.cf-select-field.inline-edit
  .ant-select-single
  .ant-select-selector
  .ant-select-selection-item:hover,
.cf-input-field.inline-edit input[disabled]:hover {
  @apply !bg-transparent;
}

.dashboard-header-search .search-custom-input .ant-input-affix-wrapper,
.cf-search-input.page-search-input .ant-input-affix-wrapper {
  @apply pl-0;
}

.cf-search-input.page-search-input .ant-input-affix-wrapper,
.cf-search-input.footer-search-input .ant-input-affix-wrapper,
.cf-search-input.chat-search-input .ant-input-affix-wrapper,
.time-material-date-picker .ant-picker-input input {
  @apply h-7;
}
.cf-search-input.page-search-input .ant-input-affix-wrapper,
.cf-search-input.page-search-input .ant-input-search-button {
  @apply dark:!bg-[#1E2732];
}
.cf-search-input.page-search-input .ant-input-group-addon button,
.cf-search-input.footer-search-input .ant-input-group-addon button,
.cf-search-input.chat-search-input .ant-input-group-addon button {
  @apply w-7 h-7;
}
.cf-search-input .ant-input-affix-wrapper:focus,
.cf-search-input .ant-input-affix-wrapper:focus-within {
  @apply shadow-none;
}

.cf-search-input .ant-input-group-addon button {
  @apply w-[34px] h-[34px] p-0 border-0 shadow-none hover:!bg-transparent;
}

.cf-search-input.search-btn-bg .ant-input-group-addon button {
  @apply !bg-black/5 hover:!bg-black/5;
}

.cf-add-btn .ant-btn-icon {
  @apply h-[26px] w-6 flex bg-primary-900 rounded-l-sm dark:!bg-dark-950;
}

.cf-add-select-btn .ant-select-arrows,
.cf-add-select-btn .ant-select-selection-item {
  @apply dark:text-black;
}

.cf-add-btn span {
  @apply mr-2;
}

.cf-header-add-select .ant-select-selector {
  @apply !pl-8;
}

.cf-header-add-select .ant-select-arrow {
  @apply !end-auto !start-0 !mt-0 -translate-y-1/2 h-[26px] w-6 bg-primary-900 dark:!bg-dark-950 rounded-t-sm;
}

.custom-select-dropdown
  .ant-select-selector:not(
    body .cf-select-field .select-filed-header .ant-select-selector,
    .common-filter .custom-select-dropdown .ant-select-selector:not
  ),
.custom-select-dropdown
  .ant-select-selection-item:not(
    .dashboard-select .ant-select-selection-item,
    .common-filter .custom-select-dropdown .ant-select-selection-item,
    body .cf-select-field .select-filed-header .ant-select-selection-item,
    .status-selection-dropdown .ant-select-selection-item
  ),
.custom-select-dropdown .ant-select-selection-search-input {
  @apply !h-full !leading-7 !border-0;
}

.cf-input-right input {
  @apply text-right;
}

.cf-input-right input::-webkit-outer-spin-button,
.cf-input-right input::-webkit-inner-spin-button {
  @apply appearance-none;
}

.cf-input-right:focus-within
  input::-webkit-outer-spin-button:not(
    .cf-input-right:focus-within:has(
        > input[readonly]::-webkit-outer-spin-button
      )
  ),
.cf-input-right:focus-within
  input::-webkit-inner-spin-button:not(
    .cf-input-right:focus-within:has(
        > input[readonly]::-webkit-inner-spin-button
      )
  ),
.cf-input-right:hover
  input::-webkit-outer-spin-button:not(
    .cf-input-right:hover:has(> input[readonly]::-webkit-outer-spin-button)
  ),
.cf-input-right:hover
  input::-webkit-inner-spin-button:not(
    .cf-input-right:hover:has(> input[readonly]::-webkit-inner-spin-button)
  ) {
  -webkit-appearance: textfield;
}

.inline-edit .custom-input-left .ant-input {
  @apply text-left cursor-pointer;
}

.ant-picker-ok .ant-btn:not(.ant-picker-ok .ant-btn[disabled]) {
  @apply text-white bg-primary-900 border-primary-900 dark:border-dark-400 dark:bg-dark-400 shadow-none;
}

.ant-picker-ok .ant-btn[disabled] {
  @apply text-white !bg-primary-700 border-primary-700 dark:border-dark-400 dark:bg-dark-400 shadow-none;
}

.notification-btn.ant-tabs .ant-tabs-ink-bar,
.ant-progress .ant-progress-bg {
  @apply bg-primary-900;
}

.file-photo-timeline-view .ant-collapse-item,
.file-photo-timeline-view .ant-collapse-content,
.multi-select-collapse .ant-collapse-item,
.cf-input-field .ant-input-group-wrapper .ant-input-group-addon,
.multi-select-collapse .ant-collapse-content.ant-collapse-content-active {
  @apply border-0;
}

.multi-select-collapse
  .ant-collapse-content-box:not(
    .multi-select-collapse.no-data-list .ant-collapse-content-box
  ) {
  @apply !p-0 !py-1.5;
}

.multi-select-collapse
  .ant-collapse-header:not(
    .multi-select-collapse.checkbox-not-show .ant-collapse-header
  ) {
  @apply !py-[9px] !pl-10 !pr-[15px];
}

.multi-select-collapse .checkbox-not-show .ant-collapse-header {
  @apply !py-[9px] !pl-[15px] !pr-[15px];
}

.multi-select-collapse .ant-checkbox-wrapper {
  @apply absolute left-[15px] top-[7px];
}

.multi-select-collapse .ant-collapse-expand-icon {
  @apply !h-4;
}

.multi-select-collapse
  .ant-collapse-item-active:not(
    .multi-select-collapse.hide-collapse-content .ant-collapse-item-active
  ) {
  @apply !rounded-none bg-gray-200/30;
}

.select-project-dropdown .ant-select-arrow {
  @apply right-0 -mt-[5px] text-primary-900 dark:text-white/90;
}

.select-project-dropdown .ant-select-selector {
  @apply !p-0 !text-xs;
}

.select-project-dropdown .ant-select-selection-item {
  @apply font-bold;
}

.header-select-status-option .ant-select-selection-item {
  @apply !text-xs;
}

.select-project-popup .ant-select-item-option-content {
  @apply text-xs;
}

.select-project-popup .ant-select-item {
  @apply !min-h-fit;
}

.ant-select-arrow span[aria-label="down"] svg {
  @apply !h-2.5 !w-2.5;
}
.ant-color-picker .ant-popover-inner {
  @apply !p-4;
}

.ant-popover-inner {
  @apply !p-0 dark:bg-white;
}

.project-percent .ant-progress .ant-progress-circle-trail {
  stroke-width: 2px;
}

.project-percent .ant-progress.ant-progress-circle .ant-progress-text {
  @apply text-[0.75em] text-[#008000] font-medium leading-[10px];
}

.estimates-percent .ant-progress.ant-progress-circle .ant-progress-text {
  @apply text-[7px] leading-[11px] text-primary-900 font-semibold pointer-events-none;
}

.text-green-sucess input {
  @apply !text-green-700;
}

.preview-image .ant-image-preview-close,
.file-photo-timeline-view .ant-collapse-header {
  @apply !p-0 w-fit;
}

.file-photo-timeline-view .ant-collapse-expand-icon {
  @apply w-[22px] h-[22px] !p-0 flex items-center justify-center rounded-[3px] bg-primary-900/5;
}

.file-photo-timeline-view .ant-collapse-item-active .ant-collapse-expand-icon {
  @apply bg-primary-900/20;
}

.ant-tooltip-inner {
  @apply !text-center;
}

.ant-tooltip-arrow {
  @apply !bottom-px;
}

/* Start: File and photo Module and project module */
.file-folderlist
  .ant-tree-node-content-wrapper.ant-tree-node-content-wrapper-open,
.file-folderlist
  .ant-tree-node-content-wrapper.ant-tree-node-content-wrapper-close,
.ant-checkbox-wrapper,
.radio-list-btn {
  @apply flex items-center;
}

.ant-btn-default.ant-btn-outlined:not(:disabled):not(.ant-btn-disabled):hover,
.ant-btn-default.ant-btn-dashed:not(:disabled):not(.ant-btn-disabled):hover {
  @apply text-primary-900 border-primary-900;
}

.file-folderlist .ant-tree-title {
  @apply inline-block truncate max-w-[calc(100%-50px)];
}

.file-folder-collapse.drawer-file-move .ant-tree-switcher {
  @apply absolute right-2.5;
}

.file-folder-collapse .ant-tree-switcher {
  @apply absolute right-2 top-1/2 -translate-y-1/2;
}

.file-folder-collapse.drawer-file-move
  .file-folderlist.ant-tree-treenode
  .ant-tree-node-content-wrapper,
.file-folder-collapse
  .file-folderlist.ant-tree-treenode
  .ant-tree-node-content-wrapper {
  @apply p-2;
}

.file-folder-collapse.drawer-file-move .file-folderlist.ant-tree-treenode,
.file-folder-collapse .file-folderlist.ant-tree-treenode {
  @apply p-0 before:!block before:h-full before:border-t before:border-solid before:border-primary-100 first:before:border-none dark:before:border-white/5;
}

.file-folderlist .ant-tree-switcher svg {
  @apply dark:text-white/80;
}

.file-folder-collapse .file-folderlist.ant-tree-treenode {
  @apply before:!bg-[#F1F4FA] dark:before:!bg-dark-800;
}

.file-folder-collapse.drawer-file-move .ant-tree-treenode {
  @apply !p-2;
}

.file-folder-collapse.drawer-file-move .file-folderlist.ant-tree-treenode {
  @apply before:!bg-transparent !py-2.5 !px-5;
}

.file-folder-collapse .ant-tree-treenode {
  @apply p-1 before:hidden text-primary-900 dark:text-white/90 font-semibold;
}

.notification-btn.ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn,
.ant-picker
  .ant-picker-input
  > input:not(.ant-picker.text-green-sucess .ant-picker-input > input),
.cf-input-field .ant-input-group-addon,
.cf-input-field .ant-input:not(.cf-input-field .text-success),
.cf-select-field
  .ant-select-selection-item:not(
    .cf-select-field .header-select-status-option .ant-select-selection-item
  ),
.loading-spinner .text-blue-500,
.ant-picker .ant-picker-input > input[disabled],
.cf-select-field .ant-select-disabled .ant-select-selection-item,
.cf-input-field .ant-input[disabled],
.cf-currency-input-field input[disabled],
.cf-new-input-mask-field input[disabled],
.ant-select-dropdown
  .ant-select-item-option-selected:not(.ant-select-item-option-disabled)
  .ant-select-item-option-state,
.cf-select-field
  .ant-select-multiple
  .ant-select-selection-item
  .ant-select-selection-item-content,
.file-folder-collapse
  .file-folderlist.ant-tree-treenode
  .ant-tree-node-content-wrapper,
.cf-search-input .ant-input-affix-wrapper input,
.cf-select-field
  .ant-select-multiple
  .ant-select-selection-item
  .ant-select-selection-item-remove:hover,
.ant-select-dropdown .ant-select-item-option,
.edit-block-select-view.ant-select-single.ant-select-open
  .ant-select-selection-item {
  @apply !text-primary-900 dark:!text-white/90;
}

.file-folder-collapse .file-folderlist span.ant-tree-title {
  @apply !text-black/80 dark:!text-white/90;
}

.file-folder-collapse
  .file-sub-folderlist
  .ant-tree-node-content-wrapper.ant-tree-node-selected
  .sub-folderlist-title {
  @apply !text-deep-orange-500;
}

.ant-tree.ant-tree-directory
  .ant-tree-treenode
  .ant-tree-node-content-wrapper.ant-tree-node-selected {
  @apply text-deep-orange-500;
}

.file-folder-collapse
  .file-folderlist
  .ant-tree-switcher.ant-tree-switcher_open
  .ant-tree-switcher-icon {
  @apply rotate-90;
}

.file-folder-collapse
  .file-folderlist
  .ant-tree-switcher.ant-tree-switcher_open
  .ant-tree-switcher-icon,
.file-folder-collapse
  .file-folderlist:hover
  .ant-tree-switcher
  .ant-tree-switcher-icon {
  @apply opacity-100;
}

.file-folder-collapse .file-add-folder {
  @apply pt-2 pb-0.5 px-2.5;
}
/* Hide last menu option in image markup popup  */
.SfxPopper-wrapper
  .SfxMenuItem-wrapper:last-child
  > .FIE_topbar-save-menu-item {
  display: none;
}

.header-select-status-option .header-status-color,
.file-sub-folderlist .ant-tree-switcher,
.file-folder-collapse .file-add-folder .ant-tree-indent,
.file-folder-collapse
  .file-add-folder
  .ant-tree-iconEle.ant-tree-icon__customize,
.hide-collapse-content .ant-collapse-content,
.remove-down-arrow .ant-select-arrow {
  @apply hidden;
}

.file-sub-folderlist
  .ant-tree-node-content-wrapper.ant-tree-node-content-wrapper-normal {
  @apply w-[calc(100%-30px)];
}

.file-folder-collapse
  .file-sub-folderlist.ant-tree-treenode-switcher-open
  .ant-tree-node-content-wrapper.ant-tree-node-selected
  .add-new-folder-brn {
  @apply !flex;
}

.file-folder-collapse
  .file-sub-folderlist.ant-tree-treenode-switcher-open
  .sub-folderlist-title-block {
  @apply max-w-[calc(100%-28px)];
}
/* End: File and photo Module and project module */

.ant-checkbox.ant-checkbox-checked .ant-checkbox-inner,
.ant-radio.ant-radio-checked .ant-radio-inner {
  @apply dark:bg-primary-900;
}

.ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover
  .ant-checkbox-inner,
.ant-checkbox:not(.ant-checkbox-disabled):hover .ant-checkbox-inner,
.ant-checkbox .ant-checkbox-inner {
  @apply dark:!bg-transparent dark:!border-white/90;
}

.ant-checkbox-inner,
.ant-radio-inner {
  @apply dark:bg-white/90;
}

.ant-popover.ant-popconfirm .ant-popover-inner {
  @apply !p-3;
}

.ant-modal-wrap .ant-modal-content {
  @apply dark:bg-dark-900;
}

.ant-popover .ant-popover-arrow {
  @apply dark:before:bg-dark-900;
}

.ant-notification-notice-message {
  @apply !mb-0;
}

.ant-radio-wrapper .ant-radio-checked .ant-radio-inner,
.ant-spin .ant-spin-dot-item {
  @apply dark:!bg-[#666e79];
}

.ag-checkbox-input-wrapper {
  @apply border border-solid border-primary-900/80 !rounded cursor-pointer;
}

.ag-checkbox-input-wrapper.ag-checked {
  @apply bg-primary-900 border-0;
}

.ag-checkbox-input-wrapper:after {
  top: 7px !important;
  left: 4px !important;
  inset-inline-start: 21.5%;
  display: table;
  width: 5.7142857142857135px;
  height: 9.142857142857142px;
  border: 2px solid #fff;
  border-top: 0;
  border-inline-start: 0;
  transform: rotate(45deg) scale(0) translate(-50%, -50%);
  opacity: 0;
  content: "" !important;
  transition: all 0.1s cubic-bezier(0.71, -0.46, 0.88, 0.6), opacity 0.1s;
}

.ag-checkbox-input-wrapper.ag-checked:after {
  opacity: 1;
  transform: rotate(45deg) scale(1) translate(-50%, -50%);
  transition: all 0.2s cubic-bezier(0.12, 0.4, 0.29, 1.46) 0.1s;
}

.ag-row-selected:before,
.no-data-list .ant-collapse-item.ant-collapse-item-active {
  @apply !bg-transparent;
}

.ag-row-hover.ag-row-selected:before {
  @apply !bg-none;
}

.ag-checkbox-input-wrapper:focus-within,
.ag-checkbox-input-wrapper:active,
.ant-radio-group .ant-wave,
.ant-btn .ant-wave {
  @apply !shadow-none;
}

.notification-btn .ant-tabs-nav-list,
.ant-select-dropdown .ant-space-item {
  @apply !w-full;
}

.ant-select-selection-placeholder {
  @apply !text-[#bdbdbd] dark:!text-white/25 !font-light dark:!font-normal;
}

.ant-select-multiple
  .ant-select-selection-item:not(
    .common-filter .ant-select-multiple .ant-select-selection-item
  ) {
  @apply bg-[#EBF1F9] border-[#B4C6DD] dark:bg-dark-600;
}

.common-filter .ant-select-multiple .ant-select-selection-item {
  @apply bg-[#d1e2f9] border-[#7a899d] dark:bg-dark-600;
}

.ant-popover
  .common-filter
  .ant-select-show-search
  .ant-select-selection-overflow:has(
    > .ant-select-selection-overflow-item .ant-select-selection-search
  ) {
  @apply pr-6;
}

.status-selection-dropdown {
  @apply rounded !h-5;
}

.status-selection-dropdown .ant-select-selection-item,
.status-selection-dropdown .ant-select-selector {
  @apply !text-13 !font-normal;
}

.status-selection-dropdown .ant-select-selection-item {
  @apply !pr-0;
}

.status-selection-dropdown .ant-select-arrow {
  @apply opacity-0 right-1;
}

.status-selection-dropdown:hover .ant-select-arrow,
.status-selection-dropdown.ant-select-open .ant-select-arrow {
  @apply opacity-100;
}

.status-selection-dropdown:hover .ant-select-selector,
.status-selection-dropdown.ant-select-open .ant-select-selector {
  @apply pl-1.5 pr-4;
}

.singal-icon-with-select .ant-select-arrow {
  @apply right-[30px];
}

@media (max-height: 600px), (max-width: 767px) {
  .ant-picker-dropdown-range.ant-picker-dropdown .ant-picker-header button {
    @apply leading-7;
  }
  .ant-picker-dropdown-range.ant-picker-dropdown .ant-picker-content th,
  .ant-picker-dropdown-range.ant-picker-dropdown .ant-picker-header-view {
    @apply h-7;
  }
  .ant-picker-dropdown-range.ant-picker-dropdown .ant-picker-body {
    @apply !py-0.5 !px-2.5;
  }
  .ant-picker-dropdown-range.ant-picker-dropdown .ant-picker-date-panel {
    @apply !w-[269px];
  }
  .ant-picker-dropdown-range.ant-picker-dropdown
    .ant-picker-cell
    .ant-picker-cell-inner {
    @apply min-w-[22px] h-[22px] leading-[22px];
  }
}

.ant-picker-dropdown
  .ant-picker-cell-in-view.ant-picker-cell-range-start:not(
    .ant-picker-cell-range-start-single
  )::before,
.ant-picker-dropdown
  .ant-picker-cell-in-view.ant-picker-cell-range-end:not(
    .ant-picker-cell-range-end-single
  )::before,
.ant-picker-dropdown .ant-picker-cell-in-view.ant-picker-cell-in-range::before {
  @apply bg-[#e4ecf6];
}

.ant-picker-dropdown
  .ant-picker-date-panel
  .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-start
  .ant-picker-cell-inner::after,
.ant-picker-dropdown
  .ant-picker-date-panel
  .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-end
  .ant-picker-cell-inner::after,
.ant-picker-dropdown
  .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover::before,
.ant-picker-dropdown
  .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-start::before,
.ant-picker-dropdown
  .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-end::before,
.ant-picker-dropdown
  .ant-picker-cell-in-view.ant-picker-cell-range-start.ant-picker-cell-range-hover::before,
.ant-picker-dropdown
  .ant-picker-cell-in-view.ant-picker-cell-range-end.ant-picker-cell-range-hover::before,
.ant-picker-dropdown
  .ant-picker-cell-in-view.ant-picker-cell-range-start:not(
    .ant-picker-cell-range-start-single
  ).ant-picker-cell-range-hover-start::before,
.ant-picker-dropdown
  .ant-picker-cell-in-view.ant-picker-cell-range-end:not(
    .ant-picker-cell-range-end-single
  ).ant-picker-cell-range-hover-end::before,
.ant-picker-panel
  > :not(.ant-picker-date-panel)
  .ant-picker-dropdown
  .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-start::before,
.ant-picker-panel
  > :not(.ant-picker-date-panel)
  .ant-picker-dropdown
  .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-end::before {
  background: #bbc9db !important;
}

.common-filter
  .ant-select-multiple.ant-select-disabled
  .ant-select-selection-item,
.cf-select-field
  .ant-select-disabled
  .ant-select-selection-overflow-item.ant-select-selection-overflow-item-rest
  .ant-select-selection-item {
  @apply !bg-black/20 dark:bg-dark-900;
}

.ant-picker-dropdown .ant-picker-time-panel-column::after {
  @apply h-2.5;
}

.ant-picker-time-panel-cell.ant-picker-time-panel-cell-selected
  .ant-picker-time-panel-cell-inner {
  @apply !bg-primary-900 !text-white;
}
.ant-notification-notice-content {
  @apply min-h-[24px];
}

.cf-notification-notice {
  @apply dark:bg-[#0a0f14];
}

.cf-notification-notice .ant-notification-notice-close-icon,
.cf-notification-notice .ant-notification-notice-description,
.cf-notification-notice .ant-notification-notice-message {
  @apply dark:text-white;
}

.addon-before-class .cf-select-field.inline-edit .ant-select-selector {
  @apply !pl-1.5 !pr-1.5;
}

.header-select-status-option .ant-select-selection-item,
.multi-selected-tags-padding .ant-select-selector {
  @apply !pe-0;
}

.multi-selected-tags-padding .ant-select-selection-search-input {
  @apply !mt-0.5;
}

.common-filter .ant-select-selector,
.edit-inline-select .addon-before-class .ant-select-selection-item {
  @apply !pe-[18px];
}

.header-select-status-option .ant-select-selection-search-input {
  @apply !h-[18px];
}

.header-select-status-option.ant-select-open .ant-select-arrow,
.header-select-status-option:hover .ant-select-arrow {
  @apply !end-1.5;
}

.header-select-status-option.ant-select-open .ant-select-selector,
.header-select-status-option:hover .ant-select-selector {
  @apply !pl-1.5 !pr-4;
}

.header-select-status-option .ant-select-selector,
.header-select-status-option .ant-select-arrow,
.header-select-status-option .ant-select-selector .ant-select-selection-item {
  color: inherit !important;
}

.addon-before-class:has(> .cf-select-field.inline-edit) {
  @apply pr-6;
}

.addon-before-class
  .cf-select-field.inline-edit.inline-loading-success
  .ant-select-selector
  .ant-select-selection-item {
  @apply !pe-6;
}

.field-select-inline-block:has(
    .addon-before-class .cf-select-field .ant-select-open
  ),
.addon-before-class:has(> .cf-select-field .ant-select.ant-select-focused) {
  @apply w-full;
}

.full-width-modal .ant-modal {
  @apply max-w-full;
}

.full-width-modal .ant-modal .ant-modal-content {
  @apply h-screen rounded-none;
}

.ant-radio-wrapper .ant-radio.ant-radio-disabled:hover:after {
  @apply invisible;
}

.ant-picker-dropdown.ant-picker-dropdown-range .ant-picker-panel-layout {
  @apply flex-row-reverse;
}

.ant-picker-dropdown .ant-picker-panel-container .ant-picker-footer a.ant-picker-now-btn,
.ant-picker-dropdown .ant-picker-panel-container .ant-picker-ranges a.ant-picker-now-btn{
  @apply hover:text-primary-900
}

.group-select-popup .ant-select-item-group {
  @apply text-sm font-semibold text-black;
}

.ant-select-dropdown .ant-select-item.ant-select-item-group{
  @apply text-primary-900 font-semibold !bg-[#f8f8f8] hover:!bg-[#f8f8f8] text-sm
}

@media (max-height: 560px) {
  .ant-picker-dropdown .ant-picker-time-panel .ant-picker-content {
    @apply h-[165px];
  }
}
.wizard-step .ant-steps-item-title {
  @apply !text-primary-900 font-semibold md:text-13 lg:text-sm pr-0 !leading-[26px];
}

.wizard-step .ant-steps-item-icon {
  @apply !h-[26px] !w-[26px] rounded-full !bg-white border border-primary-900 shadow-[0_0_0_2px_rgba(255,255,255,1),0_0_0_3px_rgba(34,53,88,1)] me-3.5 leading-[26px];
}

.wizard-step .ant-steps-item-icon .ant-steps-icon {
  @apply !text-sm !text-primary-900;
}

.wizard-step .ant-steps-item.ant-steps-item-active .ant-steps-item-icon {
  @apply !bg-primary-900;
}

.wizard-step
  .ant-steps-item.ant-steps-item-active
  .ant-steps-item-icon
  .ant-steps-icon {
  @apply !text-white;
}

.wizard-step .ant-steps-item-content {
  @apply !min-h-[64px];
}

.wizard-step .ant-steps-item-tail {
  @apply !w-0.5 after:!w-0.5 after:!bg-[#ccc] !start-3 !pt-[29px] !pb-0;
}

.cf-select-field .ant-select-selection-search-input {
  @apply !pe-[18px];
}

.cf-select-field
  .ant-select-selection-overflow
  .ant-select-selection-overflow-item
  .ant-select-selection-search-input {
  @apply !pe-0;
}

.checkbox-group .ant-checkbox-wrapper {
  @apply gap-2 text-primary-900;
}

.ant-dropdown-menu-item-disabled
  .ant-dropdown-menu-title-content
  .ant-typography,
.ant-dropdown-menu-item-disabled
  .ant-dropdown-menu-title-content
  .dropdown-list-icon {
  @apply !text-black/50;
}

.field-select-inline-block:not(
    .common-card-head
      .field-select-inline-block:has(
        > .field-select-inline-inner .cf-select-field .select-filed-header
      ),
    .field-select-inline-block:has(
        > .field-select-inline-inner .cf-select-field .custom-select-option
      )
  ) {
  @apply overflow-hidden;
}

.select-add-icon-before .ant-select-selector {
  @apply !pl-0 !pr-10;
}

.select-add-icon-before .ant-select-arrow {
  @apply right-6;
}

/* Start: New Component css */

/* Start: Common Css */
.success-btn {
  @apply shadow-none;
}

.primary-btn:not(
    .primary-btn[disabled],
    .primary-btn.success-btn,
    .primary-btn.orange-button
  ) {
  @apply !bg-primary-900 text-white shadow-none;
}

.primary-btn[disabled]:not(
    .primary-btn.success-btn[disabled],
    .primary-btn.orange-button[disabled]
  ) {
  @apply !bg-primary-700 text-white shadow-none;
}

.success-btn {
  @apply !bg-[#5BAA46] hover:!bg-[#5BAA46];
}

.table-tooltip-text {
  @apply truncate leading-5 max-w-full inline-block text-13 w-auto;
}
/* End: Common Css */
.drawer-open .ant-drawer-header {
  @apply px-4 py-2.5;
}

.drawer-open .ant-drawer-header-title {
  @apply flex-row-reverse;
}

.drawer-open .ant-drawer-close {
  @apply me-0 text-base !text-primary-800 dark:text-white/90;
}

.drawer-open .ant-drawer-close .anticon.anticon-close svg {
  @apply ease-in-out duration-300;
}

.drawer-open .ant-drawer-close:hover .anticon.anticon-close svg {
  @apply !text-primary-900 rotate-90 dark:text-white/90;
}

.cf-field {
  @apply before:absolute before:bottom-0 before:left-0 before:h-px before:w-full before:scale-x-0 before:origin-center before:bg-primary-900/60 dark:before:bg-[#696b6e] before:z-[5];
}

.header-date-picker .cf-field {
  @apply before:hidden;
}

.cf-field:focus-within,
.cf-field:focus {
  @apply before:scale-x-100 before:transition-all before:ease-in-out before:duration-300;
}

.cf-new-currency-input-field input:focus-visible {
  outline: none !important;
}

.cf-new-input-field.addon-after-icon input {
  @apply px-0 h-[34px];
}

.border-addonAfter-icon .ant-input-wrapper .ant-input-group-addon,
.input-addon-before-icon .ant-input-group-addon,
.addon-before-inputfield-border .ant-input-wrapper .ant-input-group-addon,
.cf-new-input-field.edit-inline .ant-input-wrapper > .ant-input-group-addon {
  @apply p-0;
}

.input-addon-before-icon .ant-input,
.cf-new-input-field.edit-inline
  .ant-input-wrapper
  > .ant-input-group-addon
  + .ant-input-affix-wrapper {
  @apply pl-1.5 pr-0;
}

.input-addon-before-icon .ant-input-number-wrapper,
.cf-new-input-number-field.currency-symbol-hidden
  .input-addon-before-icon
  .ant-input-number-input-wrap
  input {
  @apply h-[34px];
}

.input-addon-before-icon
  .ant-input-number-wrapper
  .ant-input-number-group-addon {
  @apply pl-0 pr-1.5;
}

.add-btn .ant-btn-icon + span {
  @apply mr-2;
}

.add-btn .ant-btn-icon {
  @apply h-[26px] w-6 min-w-6 flex bg-primary-900 rounded-l-sm dark:!bg-dark-950;
}

.search-custom-input .ant-input-group-addon {
  @apply !start-0;
}

.search-custom-input .ant-input-wrapper {
  direction: rtl;
}

.search-custom-input .ant-input-affix-wrapper {
  direction: ltr;
}

.search-custom-input .ant-input-affix-wrapper {
  @apply !rounded-r-md !rounded-l-none !border-0 dark:bg-[#ffffff0a] h-7 focus:shadow-none focus-within:shadow-none;
}

.search-custom-input .ant-input-affix-wrapper input {
  @apply placeholder:!text-[#bdbdbd] dark:placeholder:!text-white/25;
}

.search-custom-input .ant-input-group-addon button {
  @apply w-7 h-7 p-0 !bg-transparent border-0 shadow-none hover:!bg-transparent active:!bg-transparent hover:!text-primary-900;
}

.header-date-picker .ant-picker,
.search-custom-input .ant-input-affix-wrapper input {
  @apply !bg-transparent;
}

.common-tag {
  @apply px-1 py-1 text-xs rounded min-w-[80px] text-center block truncate;
}

.cf-new-select-field .ant-select:focus-within + .icon-view-block,
.cf-new-select-field .ant-select.ant-select-open + .icon-view-block,
.cf-new-select-field:hover .icon-view-block {
  @apply bg-[#f4f5f6];
}

.cf-new-select-field
  .ant-select-show-search:has(
    > .ant-select-selector .ant-select-selection-item:empty
  )
  .ant-select-clear {
  @apply hidden;
}

.cf-new-select-field
  .ant-select-show-search:has(
    > .ant-select-selector .ant-select-selection-item:empty
  )
  .ant-select-clear,
.cf-new-select-field.edit-inline .ant-select-open .ant-select-clear:not(.cf-new-select-field.edit-inline .ant-select-open:hover .ant-select-clear),
.cf-new-timepicker-field.edit-inline
  .ant-picker:hover
  .ant-picker-suffix:not(:last-child),
.cf-new-timepicker-field.edit-inline .ant-picker-suffix,
.cf-new-datepicker-field.edit-inline
  .ant-picker:hover
  .ant-picker-suffix:not(:last-child),
.cf-new-datepicker-field.edit-inline .ant-picker-suffix,
.cf-new-select-field.edit-inline .ant-select-arrow,
.new-file-upload-Dragger .ant-upload-list-item-actions,
.ant-upload-list-item-container:hover + .ant-upload-list-item-container:before {
  @apply sm:opacity-0;
}

.cf-new-select-field
  .ant-select-show-search:has(
    > .ant-select-selector .ant-select-selection-item:empty
  )
  .ant-select-arrow,
.cf-new-timepicker-field.edit-inline
  .ant-picker.ant-picker-focused
  .ant-picker-suffix,
.cf-new-timepicker-field.edit-inline:hover .ant-picker-suffix,
.cf-new-datepicker-field.edit-inline
  .ant-picker.ant-picker-focused
  .ant-picker-suffix,
.cf-new-datepicker-field.edit-inline:hover
  .ant-picker-suffix:not(
    .cf-new-datepicker-field.edit-inline:hover
      .ant-picker-disabled
      .ant-picker-suffix,
    .cf-new-datepicker-field.edit-inline:hover
      .ant-picker-suffix:not(:last-child)
  ),
.cf-new-select-field
  .ant-select:focus-within
  .ant-select-arrow:not(
    .cf-new-select-field .ant-select:hover .ant-select-arrow:not(:last-child)
  ),
.cf-new-select-field .ant-select.ant-select-open .ant-select-arrow,
.cf-new-select-field.edit-inline:hover
  .ant-select-arrow:not(
    .common-filter:has(
        > div > div .cf-new-select-field .ant-select.ant-select-disabled
      )
      .ant-select-arrow,
    .cf-new-select-field.edit-inline:hover
      .ant-select:hover
      .ant-select-arrow:not(:last-child),
    .readOnly-select-field .cf-new-select-field:hover .ant-select-arrow
  ),
.new-file-upload-Dragger
  .ant-upload-list-item-container:hover
  .ant-upload-list-item-actions {
  @apply sm:opacity-100;
}

.addon-before-icon:has(.cf-new-select-field .ant-select:focus-within),
.addon-before-icon:has(.cf-new-select-field.edit-inline:hover),
.addon-before-icon:has(.cf-new-select-field .ant-select.ant-select-open) {
  @apply w-full;
}

.cf-field.cf-new-select-field.edit-inline .ant-select-selection-placeholder {
  @apply start-1.5;
}

.cf-field.cf-new-select-field
  .ant-select-multiple
  .ant-select-selection-search:not(
    .custom-select-dropdown .ant-select-selection-search,
    .cf-singal-selection .ant-select-selection-search,
    .cf-field.cf-new-select-field.inline-edit .ant-select-selection-search
  ),
.cf-field.cf-new-select-field.inline-edit
  .ant-select-selection-overflow
  .ant-select-selection-search {
  @apply ms-0;
}

.cf-field.cf-new-select-field
  .ant-select-selection-search:not(
    .cf-field.cf-new-select-field
      .header-select-status-dropdown
      .ant-select-selection-search
  ),
.cf-field.cf-new-select-field
  .ant-select-multiple
  .ant-select-selection-search:not(
    .custom-select-dropdown .ant-select-selection-search,
    .cf-singal-selection .ant-select-selection-search,
    .cf-field.cf-new-select-field.inline-edit .ant-select-selection-search
  ),
.cf-field.cf-new-select-field.inline-edit
  .ant-select-selection-overflow
  .ant-select-selection-search,
.cf-field.cf-new-select-field .ant-select-selection-placeholder {
  @apply start-0;
}

.cf-field.cf-new-select-field.edit-inline
  .ant-select-multiple
  .ant-select-selection-search {
  @apply !start-0 !end-6;
}

.cf-field.cf-new-select-field
  .ant-select-selection-search:not(
    .cf-field.cf-new-select-field.edit-inline
      .ant-select-multiple
      .ant-select-selection-search
  ) {
  @apply !end-6;
}

.cf-field.cf-new-select-field.edit-inline
  .ant-select-selection-search:not(
    .cf-field.cf-new-select-field.edit-inline
      .ant-select-multiple
      .ant-select-selection-search,
    .tax-select-filed .cf-field.cf-new-select-field .ant-select-selection-search
  ),
.cf-field.cf-new-select-field
  .header-select-status-dropdown
  .ant-select-selection-search {
  @apply !start-1.5;
}

.cf-field.cf-new-select-field
  .ant-select-selector:not(
    .cf-field.cf-new-select-field .border-select-filed .ant-select-selector,
    .cf-field.cf-new-select-field
      .border-select-filed-option
      .ant-select-selector,
    .cf-field.cf-new-select-field
      .header-select-status-dropdown
      .ant-select-selector
  ) {
  @apply ps-0;
}

.cf-field.cf-new-select-field.edit-inline
  .ant-select-selector:not(
    .tax-select-filed
      .cf-field.cf-new-select-field.edit-inline
      .ant-select-selector,
    .project-select-field.cf-field.cf-new-select-field.edit-inline
      .ant-select-selector
  ) {
  @apply !ps-1.5;
}

.cf-field.cf-new-select-field
  .border-select-filed-option
  .ant-select-selector:not(
    .tax-select-filed
      .cf-field.cf-new-select-field
      .border-select-filed-option
      .ant-select-selector
  ) {
  @apply p-1.5;
}

.tax-select-filed
  .cf-field.cf-new-select-field
  .border-select-filed-option
  .ant-select-selector {
  @apply py-1.5 px-[15px] text-13;
}

.tax-select-filed .cf-field.cf-new-select-field .ant-select-selection-search {
  @apply !start-[15px];
}

.tax-select-filed
  .cf-field.cf-new-select-field
  .border-select-filed-option
  .ant-select-selector {
  @apply py-1.5 px-[15px] text-13;
}

.cf-new-textarea-field.edit-inline
  textarea:not(
    .cf-new-textarea-field.edit-inline:hover textarea,
    .cf-new-textarea-field.edit-inline:focus-within textarea
  ) {
  @apply resize-none;
}

.right-button-icon:has(> div .cf-new-input-field:hover),
.right-button-icon:has(> div .cf-new-input-field:focus-within),
.right-button-icon:has(> div .cf-new-button-field:hover),
.right-button-icon:has(> div .cf-new-button-field:focus-within) {
  @apply w-full;
}

.directory-upload-image .ant-upload-drag .ant-upload,
.ant-timeline .ant-timeline-item-head.ant-timeline-item-head-custom {
  @apply p-0 bg-transparent;
}

.add-button .ant-btn-icon {
  @apply rounded-l;
}

.contractor-type span {
  @apply ml-1;
}

.card-right-button label {
  @apply focus-within:!bg-[#eceff1] !bg-[#fff] border focus-within:!border-gray-300 !border-gray-300 !text-primary-900;
}
.search-input-border .ant-input-wrapper {
  direction: rtl;
}

.search-input-border .ant-input-group-addon .ant-btn {
  @apply border-0;
}

.search-input-border .ant-input {
  direction: ltr;
}

.dropdown-color-option-block.ant-dropdown
  .ant-dropdown-menu
  .ant-dropdown-menu-title-content,
.cf-new-select-field
  .ant-select-borderless.ant-select-disabled
  .ant-select-selector,
.cf-new-input-field .ant-input-affix-wrapper,
.cf-new-input-field .ant-input,
.cf-new-input-mask-field .ant-input,
.cf-new-textarea-field .ant-input,
.cf-new-select-field .ant-select-selector,
.search-input-border .ant-input,
.search-input-borderless .ant-input,
.ant-checkbox-wrapper .ant-checkbox-disabled + span,
.ant-radio-wrapper .ant-radio-disabled + span,
.ant-checkbox-wrapper,
.ant-radio-wrapper,
.report-menu-list .ant-menu-submenu .ant-menu-title-content,
.header-dropdown-select
  .ant-select-arrow:not(
    .header-dropdown-select.ant-select-single.ant-select-open .ant-select-arrow
  ) {
  @apply text-primary-900;
}

.cf-new-textarea-field textarea {
  @apply rounded-none;
}

.cf-new-input-field .ant-input-disabled,
.cf-new-textarea-field .ant-input[disabled] {
  @apply hover:!bg-transparent;
}

.cf-new-textarea-field.edit-inline .ant-input[disabled] {
  @apply hover:!bg-transparent hover:!resize-none;
}

.search-input-border .ant-input {
  @apply h-[34px] rounded-none !rounded-e-md shadow-none border-0 hover:border-transparent focus-within:border-transparent pl-0;
}

.ant-upload-wrapper .ant-upload-drag {
  @apply border-2 h-fit mx-4 w-[calc(100%-32px)] mb-4;
}

.new-file-upload-Dragger .ant-upload-list {
  @apply md:h-[calc(100dvh-245px)] h-[calc(100dvh-308px)] overflow-y-auto flex flex-col gap-2;
}

.new-file-upload-Dragger .ant-upload-list-item-container {
  @apply relative first:before:hidden px-4 before:absolute before:-top-[5px] before:left-0 before:!h-px before:bg-gray-200/50 before:!w-full hover:before:opacity-0;
}

.new-file-upload-Dragger
  .ant-upload-list-item-container:hover
  .ant-upload-list-item {
  @apply shadow-[0px_4px_15px] shadow-black/10;
}

.new-file-upload-Dragger .ant-upload-list-item {
  @apply !border-0 !py-3 !px-4 rounded-md !mt-0 !h-auto hover:bg-transparent;
}

.file-photos-tags-list .ant-select-selection-overflow {
  @apply flex-nowrap overflow-x-auto overflow-y-hidden pr-6;
}

.show-status-icon .ant-picker-input .ant-picker-suffix,
.show-status-icon .ant-picker-input .ant-picker-clear,
.show-status-icon .ant-select .ant-select-arrow,
.show-status-icon .ant-select .ant-select-clear,
.header-select-status-dropdown .ant-select-arrow,
.file-photos-tags-list .ant-select-clear,
.file-photos-tags-list .ant-select-arrow,
.edit-block-select-view .ant-select-arrow,
.time-material-date-picker .ant-picker-range-separator,
.time-material-date-picker .ant-picker-suffix {
  @apply hidden;
}

.cf-new-input-number-field .ant-input-number-input-wrap {
  @apply h-full;
}

.cf-new-input-number-field.number-handler-hidden
  .ant-input-number-handler-wrap {
  @apply w-0 opacity-0;
}

.cf-new-input-number-field.currency-symbol-hidden
  .ant-input-number-input-wrap
  input {
  @apply h-full p-0;
}

.cf-work-order-currency-input .ant-input-number-input-wrap input {
  @apply !text-[#008000] !text-right;
}

.cf-invoice-currency-input .ant-input-number-input-wrap input,
.text-success .ant-select-selector {
  @apply !text-[#008000];
}

.select-unit-filed .ant-select-arrow,
.select-unit-filed .ant-select-clear {
  @apply !end-1;
}

.select-unit-filed.ant-select-single.ant-select-show-arrow
  .ant-select-selection-item:not(.select-unit-filed.right-select-field .ant-select-selection-item) {
  @apply pe-2;
}

.select-unit-filed .ant-select-selection-item,
.select-unit-filed .ant-select-selection-placeholder {
  @apply !font-semibold;
}

.file-photos-tags-list .ant-select-selection-item {
  @apply h-5 leading-5 !bg-primary-900 text-white;
}

.file-photos-tags-list .ant-select-selection-item-remove {
  @apply mr-0 !text-white;
}

.addon-before-inputfield-border .ant-input-wrapper .ant-input {
  @apply py-[5px] px-1.5;
}

.header-select-status-dropdown .ant-select-selection-item,
.file-photos-tags-list .ant-select-selection-item-content {
  @apply text-xs leading-[18px];
}

.header-select-status-dropdown .ant-select-selection-item {
  @apply !leading-[18px];
}

.header-select-status-dropdown span.ant-select-selection-placeholder {
  @apply text-xs;
}

.border-select-filed .ant-select-selection-item,
.popup-select-option-header .ant-select-item-option-content {
  @apply text-13;
}

.header-select-status-dropdown .ant-select-arrow,
.header-select-status-dropdown .ant-select-selector {
  color: inherit;
}

.file-photos-tags-list .ant-select-selector,
.header-select-status-dropdown .ant-select-selection-item {
  @apply !pe-0;
}

.header-select-status-dropdown.ant-select-open .ant-select-arrow,
.header-select-status-dropdown:hover
  .ant-select-arrow:not(
    .ant-select-disabled.header-select-status-dropdown:hover .ant-select-arrow
  ) {
  @apply flex;
}

.header-select-status-dropdown .ant-select-arrow {
  @apply end-1;
}

.header-select-status-dropdown.ant-select-open .ant-select-selector,
.header-select-status-dropdown:hover .ant-select-selector {
  @apply pl-1.5 pr-4;
}

.cf-field.cf-new-select-field
  .header-select-status-dropdown.directory-stage
  .ant-select-selector:not(
    .cf-field.cf-new-select-field
      .header-select-status-dropdown.directory-stage.header-select-placeholder
      .ant-select-selector
  ) {
  @apply px-3.5;
}

.cf-field.cf-new-select-field
  .header-select-status-dropdown.directory-stage.header-select-placeholder
  .ant-select-selector {
  @apply pl-3.5 pr-0;
}

.header-select-status-dropdown.directory-stage.ant-select-open
  .ant-select-selector:not(
    .header-select-status-dropdown.directory-stage.header-select-placeholder.ant-select-open
      .ant-select-selector
  ),
.header-select-status-dropdown.directory-stage:hover
  .ant-select-selector:not(
    .ant-select-disabled.header-select-status-dropdown.directory-stage:hover
      .ant-select-selector,
    .header-select-status-dropdown.directory-stage.header-select-placeholder:hover
      .ant-select-selector
  ) {
  @apply !pl-1.5 !pr-[22px];
}

.header-select-status-dropdown.directory-stage.header-select-placeholder.ant-select-open
  .ant-select-selector,
.header-select-status-dropdown.directory-stage.header-select-placeholder:hover
  .ant-select-selector:not(
    .readOnly-select-field
      .header-select-status-dropdown.directory-stage.header-select-placeholder:hover
      .ant-select-selector
  ) {
  @apply !pl-1.5 !pr-2;
}

.header-select-status-dropdown.directory-stage.ant-select-open
  .ant-select-selector
  .ant-select-selection-search {
  @apply end-5;
}

.directory-stage .ant-select-selection-search input {
  @apply text-xs;
}

.header-select-status-dropdown .ant-select-clear {
  @apply right-1 !bg-[#ECF1F9];
}

.cf-field-right input {
  @apply text-right;
}

.checkbox-group-col .ant-checkbox-wrapper,
.checkbox-group-width-fit .ant-checkbox-wrapper {
  @apply w-fit;
}

.readOnly-select-field
  .select-field-block:not(
    .readOnly-select-field .select-field-block.directory-stage
  ),
.search-input-bg-transparent .ant-input,
.search-input-bg-transparent .ant-input-group-addon,
.search-input-bg-transparent .ant-input-group-addon .ant-btn {
  @apply !bg-transparent;
}

.ant-btn-default.ant-btn-outlined,
.ant-btn-default.ant-btn-dashed,
.readOnly-select-field
  .ant-select-disabled.ant-select-multiple
  .ant-select-selector,
.transparent-checkbox .ant-checkbox-inner,
.custom-checkbox.ant-checkbox-wrapper-disabled
  .ant-checkbox-disabled
  .ant-checkbox-inner:not(
    .custom-checkbox
      .ant-checkbox-checked.ant-checkbox-disabled
      .ant-checkbox-inner
  ),
.report-menu-list.ant-menu-light.ant-menu-inline .ant-menu-sub.ant-menu-inline {
  @apply bg-transparent;
}

.border-white-checkbox .ant-checkbox-inner {
  @apply !border-white;
}

.custom-checkbox.ant-checkbox-wrapper-disabled span {
  color: inherit;
}

.search-input-borderless .ant-input-wrapper {
  direction: rtl;
}

.search-input-borderless .ant-input-affix-wrapper .ant-input-suffix {
  @apply block;
}

.search-input-borderless .ant-input-group-addon .ant-btn {
  @apply border-0;
}

.search-input-borderless .ant-input-affix-wrapper {
  direction: ltr;
}

.search-input-borderless .ant-input-affix-wrapper {
  @apply pl-0.5;
}

.search-input-borderless .ant-input {
  @apply rounded-none !rounded-e-md shadow-none border-0 hover:border-transparent focus-within:border-transparent pl-0;
}

.readOnly-select-field
  .cf-new-select-field
  .select-cursor-no-drop
  .ant-select-selector,
.cf-new-textarea-field .ant-input[disabled],
.cf-new-input-mask-field .ant-input[disabled],
.cf-new-input-mask-field input[disabled],
.cf-new-input-field .ant-input[disabled],
.cf-new-currency-input-field input[disabled],
.cf-new-currency-input-field div:has(>input[disabled]) .ant-typography {
  @apply cursor-no-drop;
}

.common-filter
  .ant-select-show-arrow.ant-select-show-search
  .ant-select-selector {
  @apply !pe-6;
}

.ant-popover .common-filter .cf-new-input-field input[type="button"],
.ant-popover .common-filter .cf-new-input-field input[type="input"],
.ant-popover .common-filter .cf-new-input-field input[type="text"],
.ant-popover .date-text,
body
  .ant-popover
  .common-filter
  .cf-new-select-field.edit-inline
  .filter-select-class
  .ant-select-selector
  .ant-select-selection-item:not(
    body
      .ant-popover
      .common-filter
      .cf-new-select-field.edit-inline
      .filter-select-class.ant-select-multiple
      .ant-select-selector
      .ant-select-selection-item
  ) {
  @apply !text-xs !leading-[15px] !font-semibold;
}

body
  .ant-popover
  .common-filter
  .cf-new-select-field.edit-inline
  .filter-select-class.ant-select-multiple
  .ant-select-selector
  .ant-select-selection-item {
  @apply !text-xs !leading-[18px] !font-semibold !pl-0.5;
}

.common-filter
  .cf-new-select-field.edit-inline
  .filter-select-class.ant-select-multiple
  .ant-select-selector
  .ant-select-selection-item {
  @apply !h-5;
}

body
  .common-filter
  .cf-new-select-field.edit-inline
  .filter-select-class
  .ant-select-selector
  .ant-select-selection-item:not(
    body
      .common-filter
      .cf-new-select-field.edit-inline
      .filter-select-class.ant-select-multiple
      .ant-select-selection-item
  ) {
  @apply !leading-5;
}

.cf-new-input-field .field-text-13,
.cf-new-input-field .field-text-13 .ant-input-number,
.cf-new-textarea-field .field-text-13,
.cf-new-select-field .select-field-text-13 .ant-select-selector,
.cf-new-timepicker-field .field-text-13 .ant-picker-input input,
.common-filter
  .cf-new-select-field.edit-inline
  .filter-select-class
  .ant-select-selector
  .ant-select-selection-item {
  @apply !text-13 !leading-4;
}

.common-filter
  .cf-new-select-field.edit-inline
  .filter-select-class
  .ant-select-selector,
.ant-popover
  .more-option-filter-list
  .common-filter
  .cf-new-select-field.edit-inline
  .filter-select-class
  .ant-select-selector {
  @apply !pl-0;
}

.ant-popover
  .common-filter
  .cf-new-select-field.edit-inline
  .filter-select-class
  .ant-select-selector {
  @apply !pl-1.5;
}

.picker-dialog-bg {
  @apply !bg-black/45 !z-[1201];
}

.picker.picker-dialog {
  @apply z-[1202];
}

.directory-upload-image .ant-upload-drag {
  @apply w-full h-full border-0 m-0;
}

.ant-picker-dropdown
  .ant-picker-cell-in-view.ant-picker-cell-selected:not(
    .ant-picker-cell-disabled
  )
  .ant-picker-cell-inner,
.ant-picker-dropdown
  .ant-picker-cell-in-view.ant-picker-cell-range-start:not(
    .ant-picker-cell-disabled
  )
  .ant-picker-cell-inner,
.ant-picker-dropdown
  .ant-picker-cell-in-view.ant-picker-cell-range-end:not(
    .ant-picker-cell-disabled
  )
  .ant-picker-cell-inner,
.ant-checkbox-checked.ant-checkbox-disabled .ant-checkbox-inner {
  @apply bg-primary-900;
}

.ant-checkbox-checked.ant-checkbox-disabled .ant-checkbox-inner:after {
  @apply border-white;
}

.ellipsis-input-field .ant-input {
  @apply truncate;
}

.placeholder-white .ant-input {
  @apply placeholder:!text-white/60;
}

.menu-tooltip.ant-tooltip .ant-tooltip-arrow:before,
.menu-tooltip .ant-tooltip-inner {
  @apply bg-black;
}

.file-photo-folder-view [data-testid="virtuoso-item-list"] {
  @apply border border-solid border-[#E3E3E3];
}

.file-photo-folder-view
  [data-testid="virtuoso-item-list"]
  > div:last-child
  .file-tree-list {
  @apply border-b-0;
}

.readOnly-select-field
  .ant-select-disabled.ant-select-multiple
  .ant-select-selection-overflow
  .ant-select-selection-item {
  @apply px-1.5;
}

.readOnly-select-field
  .ant-select-disabled.ant-select-multiple
  .ant-select-selection-overflow
  .ant-select-selection-item,
.readOnly-select-field
  .ant-select-disabled.ant-select:not(
    .ant-select-customize-input,
    .select-cursor-no-drop
  )
  .ant-select-selector
  input,
.readOnly-select-field
  .ant-select-disabled.ant-select:not(
    .ant-select-customize-input,
    .select-cursor-no-drop
  )
  .ant-select-selector {
  @apply cursor-auto;
}

.report-menu-list .ant-menu-submenu-title,
.report-menu-list .ant-menu-sub .ant-menu-item {
  @apply m-0 pe-0 w-full rounded-r-none;
}

.report-menu-list .ant-menu-submenu-expand-icon {
  @apply !left-1 !top-3 !rotate-0;
}

.report-menu-list
  .ant-menu-submenu.ant-menu-submenu-open
  .ant-menu-submenu-title
  .ant-menu-title-content,
.report-menu-list
  .ant-menu-submenu.ant-menu-submenu-open
  .ant-menu-submenu-title
  .ant-menu-submenu-expand-icon {
  @apply !text-[#FF5400];
}

.report-menu-list
  .ant-menu-submenu.ant-menu-submenu-open
  .ant-menu-submenu-title
  .ant-menu-submenu-expand-icon {
  @apply !rotate-90;
}

.report-menu-list .ant-menu-sub li.ant-menu-item {
  @apply !pl-6 !mt-1 first:!mt-0;
}

.report-menu-list.ant-menu-light .ant-menu-item-selected,
.report-menu-list.ant-menu-light > .ant-menu .ant-menu-item-selected,
.report-menu-list.ant-menu-light:not(.ant-menu-horizontal)
  .ant-menu-submenu-title:hover,
.report-menu-list.ant-menu-light
  > .ant-menu:not(.ant-menu-horizontal)
  .ant-menu-submenu-title:hover,
.report-menu-list.ant-menu-light:not(.ant-menu-horizontal)
  .ant-menu-item:not(.ant-menu-item-selected):hover,
.report-menu-list.ant-menu-light
  > .ant-menu:not(.ant-menu-horizontal)
  .ant-menu-item:not(.ant-menu-item-selected):hover {
  @apply !bg-[linear-gradient(90deg,#3f4c653d_0%,#3F4C6500_100%)] !bg-transparent;
}

.report-menu-list .ant-menu-submenu .ant-menu-title-content {
  @apply text-base font-semibold;
}

.report-menu-list .ant-menu-sub .ant-menu-title-content {
  @apply text-[15px] font-normal;
}

.directory-select-filter .rc-virtual-list-holder {
  @apply !max-h-[288px];
}

.cf-new-datepicker-field:has(> .ant-picker-disabled) {
  @apply cursor-not-allowed;
}

.cf-new-datepicker-field .ant-picker-disabled,
.ant-tooltip,
.select-tooltip-none .ant-select-selection-item {
  @apply pointer-events-none;
}

.ant-tooltip{
  @apply min-w-10
}

.sent-email-preview #container {
  @apply h-full;
}

.ant-btn:not(:disabled):focus-visible {
  @apply outline-primary-900/70;
}

.header-date-picker .ant-picker-input input {
  @apply text-xs;
}

.procurement-table-radio .ant-radio-group .ant-radio-wrapper,
.cf-new-input-field .ant-input-number-affix-wrapper .ant-input-number-prefix {
  @apply m-0;
}

.procurement-table-radio .ant-radio-group {
  @apply flex-nowrap;
}

.multi-avatar-scroll-group .ant-avatar {
  @apply flex-shrink-0;
}

.multi-avatar-scroll-group > *:not(:first-child) {
  @apply -ml-1.5 !important;
}

.checkbox-custom .ant-checkbox-inner {
  @apply !border-[#d9d9d9] !cursor-default;
}

.checkbox-custom .ant-checkbox {
  @apply !cursor-default;
}

.input-table .ant-typography {
  @apply !text-black;
}

.footer-search .ant-input-wrapper .ant-input-affix-wrapper {
  @apply !py-0 !pl-2 h-7;
}

.footer-search .ant-input-group-addon .ant-input-search-button {
  @apply !h-7 !bg-white hover:!text-primary-900;
}

.add-items-drop-down
  .ant-dropdown-menu-item-disabled
  .ant-dropdown-menu-title-content
  a {
  @apply !text-black/30;
}

.estimate-finalize-dropdown .ant-dropdown-menu{
  @apply max-h-[calc(100dvh-400px)]
}

.add-report-dropdown .ant-dropdown-menu{
  @apply max-h-[calc(100dvh-300px)]
}

.cf-new-input-number-field
  .ant-input-number-affix-wrapper
  .ant-input-number-suffix {
  @apply !me-0;
}

.ant-radio-wrapper .ant-radio-disabled .ant-radio-inner::after {
  @apply bg-white;
}

.common-filter .cf-select-field .ant-select .ant-select-arrow {
  @apply !right-2;
}

.email-documents
  #container
  > div[style="margin: 5px; text-align: center;"]
  img {
  @apply mx-auto;
}

.email-documents #container > div[style="margin: 5px; text-align: right;"] img,
.email-documents #container > div[style="margin: 5px; text-align: end;"] img {
  @apply ml-auto;
}

.search-without-bg .ant-input-affix-wrapper,
.search-without-bg .ant-input-group-addon button,
.search-without-bg .ant-input-group-addon {
  @apply bg-transparent;
}

.search-without-bg .ant-input-affix-wrapper {
  @apply pl-0;
}

.jfiZwf {
  @apply !z-[1300];
}

.filter-select .ant-select-selection-search-input {
  @apply !h-5;
}

.filter-select .ant-select-selection-placeholder {
  @apply !leading-5;
}

.filter-select .ant-select-arrow {
  @apply right-0 opacity-0;
}

.filter-select .ant-select-selector,
.select-unit-filed .ant-select-selector {
  @apply p-0;
}

.filter-select .ant-select-arrow svg {
  @apply fill-primary-900;
}

.filter-select:hover .ant-select-arrow {
  @apply opacity-100;
}

.co-checkbox-group .ant-checkbox-wrapper {
  @apply w-[30%];
}
.ant-popover
  .common-filter
  .cf-select-field
  .ant-select-selection-overflow-item {
  @apply max-w-[calc(100%-15px)];
}

.input-search-number input[type="number"]::-webkit-inner-spin-button,
.input-search-number input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.ant-checkbox-wrapper:not(.checkbox-padding-remove) .ant-checkbox,
.custom-filed-checkbox.ant-checkbox-wrapper .ant-checkbox {
  @apply self-start mt-[3px];
}

.ant-radio-group.ant-radio-group-outline .custom-radio-button-wrapper:first-child{
  @apply rounded-s
}

.ant-radio-group.ant-radio-group-outline .custom-radio-button-wrapper:last-child{
  @apply rounded-e
}

.ant-picker-dropdown .ant-picker-header-view > button:hover {
  @apply text-primary-900;
}

.right-select-field .ant-select-selection-search-input{
  @apply text-right
}

.right-select-field .ant-select-selection-placeholder,
.right-select-field .ant-select-selection-item{
  @apply !pe-[13px]
}

/* End: New Component css */

@media (min-width: 1830px) {
  .upcoming-days-block {
    @apply grid-cols-2;
  }
}

@media (max-width: 1279px) {
  .upcoming-days-block {
    @apply grid-cols-2;
  }
}

@media (max-width: 680px) {
  .upcoming-days-block {
    @apply grid-cols-1;
  }
  .edit-block-select-view .ant-select-selection-item {
    @apply text-xs;
  }
}

@media (min-width: 1023px) {
  .file-photos-folder-view {
    @apply grid-cols-3;
  }
  .file-folder-list-view {
    @apply w-[calc(33%-10px)];
  }
}

@media (min-width: 1200px) {
  .file-photos-folder-view {
    @apply grid-cols-4;
  }
  .file-folder-list-view {
    @apply w-[calc(25%-11.25px)];
  }
}

@media (min-width: 1751px) {
  .file-photos-folder-view {
    @apply grid-cols-5;
  }
  .file-folder-list-view {
    @apply w-[calc(20%-12px)];
  }
}

@media (max-width: 1023px) {
  .file-photos-folder-view {
    @apply grid-cols-2;
  }
  .file-folder-list-view {
    @apply w-[calc(50%-7.5px)];
  }
}

@media (max-width: 400px) {
  .procurement-tbl-header {
    @apply flex-wrap;
  }
}
