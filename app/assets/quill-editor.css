/* Set font select font-families */
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="arial"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="arial"]::before {
  content: "Arial";
  font-family: "Arial";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="arial-black"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="arial-black"]::before {
  content: "Arial Black";
  font-family: "Arial Black";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="comic-sans-ms"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="comic-sans-ms"]::before {
  content: "Comic Sans Ms";
  font-family: "Comic Sans Ms";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="courier-new"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="courier-new"]::before {
  content: "Courier New";
  font-family: "Courier New";
}

.ql-snow .ql-picker.ql-font {
  @apply w-fit
}

.ql-snow .ql-picker.ql-font .ql-picker-label {
  @apply !pr-[18px]
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="helvetica-neue"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="helvetica-neue"]::before {
  content: "Helvetica Neue";
  font-family: "Helvetica Neue";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="helvetica"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="helvetica"]::before {
  content: "Helvetica";
  font-family: "Helvetica";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="impact"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="impact"]::before {
  content: "Impact";
  font-family: "Impact";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="lucida-grande"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="lucida-grande"]::before {
  content: "Lucida Grande";
  font-family: "Lucida Grande";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="tahoma"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="tahoma"]::before {
  content: "Tahoma";
  font-family: "Tahoma";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="times-new-roman"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="times-new-roman"]::before {
  content: "Times New Roman";
  font-family: "Times New Roman";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="verdana"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="verdana"]::before {
  content: "Verdana";
  font-family: "Verdana";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="open-sans"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="open-sans"]::before {
  content: "Open Sans";
  font-family: "Open Sans";
}

/* Set content font-families */
.ql-font-arial {
  font-family: "Arial";
}

.ql-font-arial-black {
  font-family: "Arial Black";
}

.ql-font-comic-sans-ms {
  font-family: "Comic Sans Ms";
}

.ql-font-courier-new {
  font-family: "Courier New";
}

.ql-font-helvetica-neue {
  font-family: "Helvetica Neue";
}

.ql-font-helvetica {
  font-family: "Helvetica";
}

.ql-font-impact {
  font-family: "Impact";
}

.ql-font-lucida-grande {
  font-family: "Lucida Grande";
}

.ql-font-tahoma {
  font-family: "Tahoma";
}

.ql-font-Times-new-roman {
  font-family: "Times New Roman";
}

.ql-font-verdana {
  font-family: "Verdana";
}

.ql-font-open-sans {
  font-family: "Open Sans";
}

/* Set font select size */
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="8px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="8px"]::before {
  content: "8";
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="9px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="9px"]::before {
  content: "9";
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="10px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="10px"]::before {
  content: "10";
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="11px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="11px"]::before {
  content: "11";
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="12px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="12px"]::before {
  content: "12";
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="14px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="14px"]::before {
  content: "14";
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="18px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="18px"]::before {
  content: "18";
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="24px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="24px"]::before {
  content: "24";
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="36px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="36px"]::before {
  content: "36";
}

.ql-tooltip.ql-editing[data-mode="link"],
.ql-tooltip {
  left: 0px !important;
}

.fr-popup .fr-layer {
  @apply !mt-1 !mx-[15px] !mb-2
}

.fr-popup .fr-checkbox-line {
  @apply !mt-0
}

.fr-popup .fr-checkbox {
  @apply !p-0 !pr-1
}

.fr-popup .fr-checkbox input:checked+span {
  @apply !bg-primary-900 !border-primary-900
}

.fr-popup .fr-input-line input[type=number]:focus,
.fr-popup .fr-input-line input[type=text]:focus,
.fr-popup .fr-input-line textarea:focus {
  @apply !border-primary-900 !border
}

.fr-popup .fr-input-line input[type=number],
.fr-popup .fr-input-line input[type=text],
.fr-popup .fr-input-line textarea {
  @apply !border-[#CED4DA]
}

.fr-toolbar.fr-top {
  @apply !rounded-t-md
}

.fr-second-toolbar {
  @apply !rounded-b-md
}

.fr-popup.fr-active {
  @apply !z-[9999] 
}

.submit-approval-froala-editor .fr-dropdown-wrapper:not(.submit-approval-froala-editor .fr-fullscreen .fr-dropdown-wrapper) {
  @apply max-h-[196px] overflow-y-auto;
}

.submit-approval-froala-editor .fr-wrapper {
  @apply !min-h-[200px];
}

.submit-approval-froala-editor .fr-wrapper .fr-element.fr-view{
  @apply !min-h-[199px];
}

.submit-approval-froala-editor .fr-action-buttons .fr-submit {
  @apply !p-0;
}

.submit-approval-froala-editor .fr-action-buttons button.fr-command {
  @apply !w-10;
}

.fr-fullscreen-wrapper .react-draggable.fr-fullscreen-wrapper{
  @apply !transform-none
}

.fr-box.fr-basic .fr-element ul{
  @apply pl-4 list-disc
}

.froala-editor ul{
  @apply pl-4 leading-normal
}

.fr-box.fr-basic .fr-element ol{
  @apply pl-4 list-decimal
}

.froala-editor-top .fr-popup.fr-desktop.fr-active{
  @apply !top-auto !bottom-full -translate-y-2.5
}

@media (max-height: 750px) {
  .fr-dropdown-menu .fr-dropdown-wrapper:not(.submit-approval-froala-editor .fr-dropdown-menu .fr-dropdown-wrapper) {
    @apply max-h-[calc(40dvh-96px)] overflow-y-auto;
  }
}

@media (max-height: 650px) {
  .fr-dropdown-menu .fr-dropdown-wrapper:not(.submit-approval-froala-editor .fr-dropdown-menu .fr-dropdown-wrapper) {
    @apply max-h-[calc(40dvh-120px)] overflow-y-auto;
  }
}

.froala-editor-container .fr-box.fr-basic.fr-top {
  @apply !overflow-auto !rounded-none;
}

/* End: Froala Editor css */