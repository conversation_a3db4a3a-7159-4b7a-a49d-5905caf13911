:root {
  --dhx-scheduler-base-colors-primary: #223558;
  --dhx-scheduler-base-colors-primary-hover: #223558;
  --dhx-scheduler-base-colors-primary-active: #223558;
  --dhx-scheduler-event-colors-primary: #223558;
  --dhx-scheduler-base-colors-primary-lighter: #22355833;
}

#tb_module_list + .btn-group .dropdown-toggle {
  width: 215px;
}

#tb_module_list.form-control[size="1"] {
  height: 34px;
  width: 215px;
  display: inline-block;
}

.dhx_cal_container .dhx_cal_header .dhx_scale_bar {
  @apply text-primary-900 bg-primary-900/10 font-medium;
}

.dhx_cal_container .dhx_cal_navline .dhx_cal_tab,
.dhx_cal_container .dhx_cal_today_button {
  @apply font-medium text-primary-gray-80 active:text-primary-900 active:bg-primary-900/10;
}

.dhx_cal_container .dhx_cal_navline .dhx_cal_tab.active {
  @apply bg-primary-900/10 text-primary-900;
}

.dhx_cal_light_rec {
  max-height: calc(100vh - 50px) !important;
  height: auto !important;
}

.dhx_cal_light_rec.dhx_cal_light_wide .dhx_cal_larea {
  max-height: calc(100vh - 50px - 80px) !important;
  height: auto !important;
  overflow: auto;
  width: 100%;
}

.dhx_cal_light_rec.dhx_cal_light_wide .dhx_cal_larea::-webkit-scrollbar {
  background-color: rgba(196, 196, 196, 0.2);
  width: 5px;
  height: 5px;
  -webkit-transition: all 0.5s ease;
  transition: all 0.5s ease;
  opacity: 1;
  visibility: hidden;
}

.dhx_cal_light_rec.dhx_cal_light_wide .dhx_cal_larea::-webkit-scrollbar-thumb {
  opacity: 1;
  visibility: hidden;
  background-color: #b8b8b8;
  border-radius: 50px;
  opacity: 1;
  visibility: visible;
}

.dhx_cal_light_rec.dhx_cal_light_wide .dhx_cal_larea::-webkit-scrollbar-track {
  opacity: 1;
  visibility: hidden;
}

.dhx_cal_light_rec.dhx_cal_light_wide
  .dhx_cal_larea
  .dhx_wrap_section
  .dhx_cal_ltext {
  padding-right: 5px;
}

.multiselect-selected-text [title="No filter selected"] {
  color: var(--theme-placeholder-color) !important;
}

.dhx_cal_light .dhx_cal_larea,
.dhx_cal_light .dhx_cal_ltitle {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 100%;
  text-align: left;
}

.dhx_cal_ltext textarea {
  padding: 8px !important;
}

.dhx_cal_light .dhx_cal_larea {
  border-bottom: 1px solid #cecece;
}

.dhx_cal_event_line_start {
  line-height: initial;
}

.dhx_cal_light select,
.dhx_cal_light_wide select {
  height: initial;
}

.dhx_cal_cover {
  z-index: 1050;
}

.dhx_cal_light {
  z-index: 1051;
  text-align: center;
}

.dhx_cal_light[style*="display: block;"] {
  display: -webkit-box !important;
  display: -ms-flexbox !important;
  display: flex !important;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.dhx_btn_set {
  margin-top: 10px;
  margin-bottom: 10px;
}

.dhx_cal_light_wide .dhx_cal_larea {
  margin-left: auto;
  margin-right: auto;
}

.dhtmlx_modal_box {
  z-index: 10002;
}

.dhx_minical_popup {
  z-index: 1053;
}

.employeeTab {
  width: auto;
  padding: 0px 8px;
}

.dhx_cal_event.assignee_none div {
  color: #ffffff !important;
}

.chosen-container .chosen-results {
  max-height: 130px;
}

.chosen-container {
  margin-left: 10px;
}

.dhx_cal_light .dhx_wrap_section:last-child {
  border-bottom: none;
}

.dhx_fullday {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.dhx_fullday input[type="checkbox"] {
  margin-right: 3px !important;
  margin-top: 3px !important;
}

.dhx_cal_event_line {
  height: 17px;
}

.project-weeks-tasks .dhx_multi_day .dhx_cal_event_line,
.project_scheduler {
  cursor: default !important;
}

.dhx_multi_day {
  min-height: 30px !important;
}

.dhx_cal_light .dhx_custom_button {
  height: 22px;
}

.dhx_modal_cover {
  z-index: 1051 !important;
}

input.get_unassign_event {
  vertical-align: top;
  margin: 5px 0 0;
}

.dhx_repeat_radio {
  vertical-align: sub !important;
}

.jump-directory-calendar,
.jump-project-calendar {
  cursor: pointer;
  font-size: 13px;
  vertical-align: middle;
}

.clear-assigned-to {
  font-weight: 600;
  color: #ed2940;
  vertical-align: middle;
}

.dhx_cal_larea {
  height: auto;
}

.btn-location {
  display: inline-block;
  float: right;
  margin-right: 10px;
}

.location-textarea {
  width: 68%;
  display: inline-block;
}

.btn-customer-address,
.btn-project-address {
  font-size: 11px;
  padding: 0px 8px;
  width: 110px;
}

.btn-project-address {
  margin-bottom: 5px;
}

.select-hr-line {
  padding-bottom: 5px !important;
  margin-bottom: 8px;
  border-bottom: 1px solid #e5e5e5;
}

#exportDiv i {
  color: #b4aeac;
}

.dhx_cal_navline div.dhx_minical_icon {
  top: 12px !important;
}

.gear_setting_icon {
  display: none !important;
}

.dhx_cal_light .directory-popup-button {
  width: auto !important;
  max-width: calc(100% - 162px) !important;
  white-space: nowrap;
}

.dhx_cal_container {
  overflow: visible;
  width: calc(100% - 1px) !important;
  min-height: calc(100% + 38px);
}

.project-schedule-tab-scheduler .dhx_cal_container {
  min-height: 100%;
  max-height: 100%;
}

.dhx_cal_navline {
  width: 100%;
}

.read_only_text .preview-restrict-msg {
  margin-bottom: 0 !important;
}

.project-dashboard-scheduler .dhx_cal_navline.dhx_cal_navline_flex {
  background-color: transparent;
}

.range-calender-picker .ant-picker-calendar-mini .ant-picker-body {
  @apply px-1;
}

.range-calender-picker .ant-picker-calendar-mini .ant-picker-content {
  @apply h-[200px];
}

.range-calender-picker .ant-picker-cell-today > div {
  @apply bg-[#d5d5d5] rounded;
}

.range-calender-picker .ant-picker-cell {
  @apply !p-0;
}

.range-calender-picker .ant-picker-content th {
  @apply font-semibold;
}

.range-calender-picker .ant-picker-cell.ant-picker-cell-in-view {
  @apply opacity-100 font-semibold rounded;
}

.daily-log-calendar .ant-picker-cell-inner {
  @apply cursor-default !m-0 !border-0 !pb-1.5;
}

.daily-log-calendar
  .ant-picker-calendar.ant-picker-calendar-full
  .ant-picker-calendar-date-content {
  @apply !h-[73px];
}

.ant-picker-calendar.ant-picker-calendar-full
  .ant-picker-cell-selected
  .ant-picker-calendar-date
  .ant-picker-calendar-date-value {
  @apply !text-primary-900 font-semibold;
}

.daily-log-calendar .ant-picker-calendar-date-today {
  @apply !bg-[#fff3a1] !border-[#fff3a1];
}

.daily-log-calendar .ant-picker-body {
  @apply !p-0;
}

.daily-log-calendar
  .ant-picker-calendar
  .ant-picker-cell:hover:not(.ant-picker-cell-in-view)
  .ant-picker-cell-inner,
.daily-log-calendar
  .ant-picker-calendar
  .ant-picker-cell:hover:not(.ant-picker-cell-selected):not(
    .ant-picker-cell-range-start
  ):not(.ant-picker-cell-range-end)
  .ant-picker-cell-inner,
.daily-log-calendar
  .ant-picker-calendar.ant-picker-calendar-full
  .ant-picker-cell-in-view.ant-picker-cell-selected
  .ant-picker-calendar-date,
.daily-log-calendar
  .ant-picker-calendar.ant-picker-calendar-full
  .ant-picker-cell-in-view.ant-picker-cell-selected
  .ant-picker-calendar-date-today {
  background: transparent;
}

.daily-log-calendar table,
.daily-log-calendar th,
.daily-log-calendar td {
  @apply border border-solid border-[#cecece] border-collapse;
}

.daily-log-calendar table thead th {
  @apply text-center bg-black/10 text-primary-900;
}

/* Hide weekend if perent class is cal-hide-weekends */
.cal-hide-weekends .ant-picker-content th:nth-child(1),
.cal-hide-weekends .ant-picker-content th:nth-child(7),
.cal-hide-weekends .ant-picker-cell:nth-child(1),
.cal-hide-weekends .ant-picker-cell:nth-child(7),
.project_map_view .dhx_map_area .dhx_v_border {
  @apply hidden;
}

/* Service Ticket Map */
.scheduler_ui .dhx_cal_navline {
  @apply px-0;
}

.scheduler_ui .dhx_cal_navline .dhx_cal_tab {
  @apply px-0;
}

.scheduler_ui .dhx_cal_navline .dhx_cal_tab.dhx_cal_tab_first {
  @apply !left-0;
}

.scheduler_ui .dhx_cal_navline .dhx_cal_tab.week_tab {
  @apply !left-[61px];
}

.scheduler_ui .dhx_cal_navline .dhx_cal_tab.dhx_cal_tab_last {
  @apply !left-[122px];
}

.scheduler_ui .dhx_cal_navline .dhx_cal_tab.employee_timeline_tab {
  @apply w-auto px-2 !left-[194px] m-0;
}

.scheduler_ui .dhx_cal_navline .dhx_cal_next_button,
.scheduler_ui .dhx_cal_navline .dhx_cal_prev_button {
  @apply bg-none h-7 p-0;
}

.scheduler_ui .dhx_cal_navline .dhx_cal_next_button,
.scheduler_ui .dhx_cal_navline .dhx_cal_prev_button {
  @apply hover:bg-primary-900;
}

.scheduler_ui .dhx_cal_navline .dhx_cal_next_button:hover,
.scheduler_ui .dhx_cal_navline .dhx_cal_prev_button:hover {
  --dhx-scheduler-base-colors-icons: #fff;
}

.scheduler_ui .dhx_cal_navline .dhx_cal_next_button {
  @apply right-0;
}

.scheduler_ui .dhx_cal_navline .dhx_cal_prev_button {
  @apply right-[47px];
}

.scheduler_ui .dhx_cal_navline .dhx_cal_today_button {
  @apply p-0 h-7 text-primary-900 font-semibold right-[105px] active:!text-white;
}

.scheduler_ui .dhx_cal_navline .dhx_cal_tab {
  @apply font-medium;
}

.scheduler_ui .dhx_cal_navline .dhx_cal_tab.active {
  @apply text-primary-900 bg-[#eceff1];
}

.scheduler_ui .dhx_cal_navline .dhx_cal_date {
  @apply text-primary-900 text-base font-semibold leading-8;
}

.scheduler_ui .dhx_cal_navline .dhx_cal_date {
  font-family: "Open Sans";
}

.scheduler_ui .dhx_cal_header .dhx_scale_bar {
  @apply text-primary-900 bg-primary-900/10 font-semibold leading-5 p-0 !h-5 truncate block;
}

.scheduler_ui .dhx_cal_header .dhx_map_line {
  @apply text-primary-900 bg-primary-900/10;
}

.scheduler_ui .dhx_cal_header .dhx_map_line .headline_date,
.scheduler_ui .dhx_cal_header .dhx_map_line .headline_description {
  @apply text-start font-semibold flex-shrink-0;
}

.project_map_view.scheduler_ui .dhx_cal_header .dhx_map_line .headline_date,
.project_map_view.scheduler_ui
  .dhx_cal_header
  .dhx_map_line
  .headline_description {
  @apply !text-start;
}

.scheduler_ui .dhx_cal_data .dhx_map_area .dhx_map_event_time {
  @apply text-start;
}

.scheduler_ui .dhx_cal_data .dhx_map_area .dhx_v_border {
  @apply border-r-0;
}

.scheduler_ui .dhx_cal_data {
  @apply !h-[calc(100vh-305px)];
}

.scheduler_ui.project_map_view .dhx_cal_data {
  @apply !h-[calc(100dvh-257px)];
}

.scheduler_ui [role="application"] {
  @apply !h-[calc(100vh-222px)];
}

.scheduler_ui.project_map_view [role="application"] {
  @apply !h-[calc(100dvh-175px)];
}

.scheduler_ui .dhx_map_area .dhx_map_line.highlight {
  @apply bg-primary-900;
}

.scheduler_ui .dhx_map_area .dhx_map_line.highlight .dhx_map_event_time,
.scheduler_ui .dhx_map_area .dhx_map_line.highlight .line_description {
  @apply text-white;
}

.scheduler_ui .dhx_event_icon {
  @apply shrink-0;
}

@media (min-width: 1700px) {
  .daily-log-calendar
    .ant-picker-calendar.ant-picker-calendar-full
    .ant-picker-calendar-date-content {
    @apply !h-[82px];
  }
}

@media (max-width: 1100px) {
  .scheduler_ui .dhx_cal_date {
    @apply top-11;
  }

  .scheduler_ui .dhx_cal_date,
  .scheduler_ui .dhx_cal_next_button,
  .scheduler_ui .dhx_cal_prev_button,
  .scheduler_ui .dhx_cal_today_button {
    @apply top-11;
  }
  .scheduler_ui .dhx_scheduler_day .dhx_cal_header,
  .scheduler_ui .dhx_scheduler_week .dhx_cal_header,
  .scheduler_ui .dhx_scheduler_month .dhx_cal_header,
  .scheduler_ui .dhx_scheduler_employee_timeline .dhx_cal_header {
    @apply !top-20;
  }

  .scheduler_ui .dhx_scheduler_day .dhx_cal_data,
  .scheduler_ui .dhx_scheduler_week .dhx_cal_data,
  .scheduler_ui .dhx_scheduler_month .dhx_cal_data,
  .scheduler_ui .dhx_scheduler_employee_timeline .dhx_cal_data {
    @apply !top-[101px] !h-[calc(100vh-325px)];
  }
}

@media (max-width: 991px) {
  .scheduler_ui .dhx_scheduler_map .dhx_cal_data,
  .scheduler_ui .dhx_scheduler_map .dhx_map {
    @apply !relative !m-0 !top-[81px] !w-full;
  }
  .scheduler_ui .dhx_scheduler_map .dhx_map#dhx_gmap {
    @apply !h-[500px];
  }
  .scheduler_ui .dhx_scheduler_map .dhx_map_line {
    @apply !w-full;
  }
  .scheduler_ui .dhx_scheduler_map {
    @apply !h-auto;
  }
}

@media (max-width: 768px) {
  .scheduler_ui .dhx_cal_next_button,
  .scheduler_ui .dhx_cal_prev_button,
  .scheduler_ui .dhx_cal_today_button {
    @apply top-20;
  }
  .scheduler_ui .dhx_scheduler_day .dhx_cal_header,
  .scheduler_ui .dhx_scheduler_week .dhx_cal_header,
  .scheduler_ui .dhx_scheduler_month .dhx_cal_header,
  .scheduler_ui .dhx_scheduler_employee_timeline .dhx_cal_header {
    @apply !top-[120px];
  }
  .scheduler_ui .dhx_scheduler_day .dhx_cal_data,
  .scheduler_ui .dhx_scheduler_week .dhx_cal_data,
  .scheduler_ui .dhx_scheduler_month .dhx_cal_data,
  .scheduler_ui .dhx_scheduler_employee_timeline .dhx_cal_data {
    @apply !top-[141px] !h-[calc(100vh-375px)];
  }
}

.dhx_scale_holder_now {
  @apply bg-[#fffacd] !important;
}

.dhx_cal_container .dhx_selected_cell {
  @apply bg-[#fffacd] !important; /* Lemon Chiffon color */
  /* Ensure the text color is readable */
}

.dhx_now {
  @apply bg-[#fffacd] !important;
}

.dhx_cal_data table * {
  box-sizing: border-box !important;
}

.headline_description {
  @apply !text-center !important;
}

.headline_date {
  @apply !text-center !important;
}

.scheduler_ui .dhx_cal_data .dhx_map_area .dhx_map_event_time {
  @apply !text-center !important;
}

.project_map_view .dhx_cal_data .dhx_map_area,
.project_map_view .dhx_cal_data .dhx_map_area .dhx_map_line .dhx_event_icon {
  @apply bg-none;
}

.project_map_view .dhx_cal_data .dhx_map_area .dhx_map_line .dhx_event_icon {
  @apply relative before:absolute before:top-[9px] before:left-3 before:-translate-x-1/2 before:-translate-y-1/2 before:w-3.5 before:h-3.5;
}

.project_map_view
  .dhx_cal_data
  .dhx_map_area
  .dhx_map_line
  .dhx_event_icon:before {
  background-image: url(https://cdn.contractorforeman.net/assets/images/calendar-days-regular.svg);
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.project_map_view
  .dhx_cal_data
  .dhx_map_area
  .dhx_map_line.highlight
  .dhx_event_icon:before {
  filter: invert(1) brightness(2);
}

.project_map_view .dhx_cal_data {
  @apply relative before:absolute before:content-[""] before:w-px before:h-full before:bg-[#cecece] before:top-0 before:right-[151px];
}

.project-schedule-tab-scheduler .dhx_cal_navline {
  @apply px-0;
}

.project-schedule-tab-scheduler .dhx_cal_tab_segmented {
  @apply px-2 min-w-[60px] !mx-0 active:bg-primary-900/20;
}

.project-schedule-tab-scheduler .dhx_cal_tab_segmented[name="month_tab"] {
  @apply !mr-2;
}

.project-schedule-tab-scheduler .dhx_cal_nav_button_custom {
  @apply px-2 border-0 active:bg-primary-900/20 ml-1;
}

.project-schedule-tab-scheduler
  .dhx_cal_nav_button_custom
  svg:not(
    .project-schedule-tab-scheduler
      .dhx_cal_nav_button_custom
      svg.gantt-redirection-icon
  ) {
  @apply text-base;
}

.project-schedule-tab-scheduler .dhx_cal_date {
  @apply 2xl:!ml-[calc(45%-290px)] xl:!ml-[calc(43%-295px)];
}

.project-schedule-tab-scheduler .dhx_cal_data .dhx_cal_month_table .dhx_cal_month_row .dhx_cal_month_cell:first-child .dhx_month_head,
.project-schedule-tab-scheduler .dhx_cal_data .dhx_cal_month_table .dhx_cal_month_row .dhx_cal_month_cell:first-child .dhx_month_body {
  box-shadow: 1px 0px 0px #d0dbe3 inset;
}

.dhx_cal_container .dhx_scale_hour {
  @apply px-0 text-[11px] leading-9 font-medium tracking-[-1px];
}

.dhx_cal_project_map .dhx_map_line {
  @apply !cursor-pointer
}

.dhx_cal_project_map .dhx_cal_data .dhx_map_area {
  @apply !h-[calc(100%-82px)]
}