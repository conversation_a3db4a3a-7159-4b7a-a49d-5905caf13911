import { useParams } from "@remix-run/react";
// Atoms
import { Spin } from "~/shared/components/atoms/spin";
// molecules
import { ProgressBarHeader } from "~/shared/components/molecules/ProgressBarHeader";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { ReadOnlyPermissionMsg } from "~/shared/components/molecules/readOnlyPermissionMsg";
// Organisms
import { TimeLineFooter } from "~/shared/components/organisms/timeLine/timeLineFooter";
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";
// Other
import OpportunitiesDetailsTab from "~/modules/people/opportunities/components/tab/OpportunitiesDetailsTab";
import OpportunitiNotesTab from "~/modules/people/opportunities/components/tab/OpportunitiNotesTab";
import OpportunitiAttachmentTab from "~/modules/people/opportunities/components/tab/OpportunitiAttachmentTab";
import DetailsTopBar from "~/modules/people/opportunities/components/tab/DetailsTopBar";
import { useEffect, useMemo, useRef, useState } from "react";
import {
  useAppOPPDispatch,
  useAppOPPSelector,
} from "~/modules/people/opportunities/redux/store";
import {
  getCommonSidebarCollapse,
  getGConfig,
  getGSettings,
  useGModules,
} from "~/zustand";
import { IOpportunity } from "~/modules/people/opportunities/utils/types";
import delay from "lodash/delay";
import {
  fetchEstimateData,
  fetchProjectTypeData,
  updateOppDetailApi,
} from "~/modules/people/opportunities/redux/action/opportunityDetailsActions";
import { updateOpportunityDetails } from "~/modules/people/opportunities/redux/slices/opportunityDetailsSlice";
import {
  oppDetailsField,
  oppDetailType,
  oppFieldStatus,
  STATUS_MAP,
} from "~/modules/people/opportunities/utils/constants";
import { defaultConfig } from "~/data";
import isEmpty from "lodash/isEmpty";
import { backendDateFormat } from "~/shared/utils/helper/defaultDateFormat";
import { getStatusActionForField } from "~/shared/utils/helper/common";
import dayjs from "dayjs";
import {
  incrementNotesListPage,
  isNewDataLoading,
} from "~/redux/slices/commonNoteSlice";
import useInfiniteScrollHandler from "~/hook/useInfiniteScrollHandler";
const OppotunitiesTab = () => {
  const { tab, id: opportunity_id }: RouteParams = useParams(); // This type already declare.
  const detailsRef = useRef<HTMLDivElement>(null);
  const sidebarCollapse: boolean | undefined = getCommonSidebarCollapse();
  const [activeStep, setActiveStep] = useState<string | number>("");
  const { date_format } = getGSettings();
  const handleStepClick = (value: string) => {};
  const dispatch = useAppOPPDispatch();
  const { module_id, module_access, module_key, module_singular_name } =
    getGConfig();
  const gConfig: GConfig = getGConfig();
  const { oppDetail, isOppDataLoading, stageDropDownData } = useAppOPPSelector(
    (state) => {
      return state.opportunityDetails;
    }
  );
  const { isNext, isDataLoading }: ICommonNoteInitialState = useAppOPPSelector(
    (state) => state.commonNoteData
  );
  const handleNotesScroll = useInfiniteScrollHandler({
    dispatch,
    hasNextPage: isNext,
    isLoading: isDataLoading,
    nextPageAction: incrementNotesListPage,
    loadingAction: isNewDataLoading,
  });
  const [inputValues, setInputValues] =
    useState<Partial<IOpportunity>>(oppDetailsField);
  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(oppFieldStatus);
  const loadingStatusRef = useRef(oppFieldStatus);
  const [customer, setCustomer] = useState<Partial<IDirectoryData>>({});
  const [assignedTo, setAssignedTo] = useState<Partial<IDirectoryData>>({});
  const [isOpenCustomer, setIsOpenCustomer] = useState<boolean>(false);
  const [isOpenAssignedTo, setIsOpenAssignedTo] = useState<boolean>(false);
  const [isOpenAssignedToContactDetails, setIsOpenAssignedToContactDetails] =
    useState<boolean>(false);
  const [isOpenCustomerContactDetails, setIsOpenCustomerContactDetails] =
    useState<boolean>(false);

  const [contactAddress, setContactAddress] = useState<boolean>(false);
  const { checkModuleAccessByKey } = useGModules();

  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(module_key) === "read_only",
    [module_key]
  );

  const handleChangeFieldStatus = ({
    field,
    status,
    action,
  }: IExpenseFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );

    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };

  const handleUpdateField = async (
    data: IOppDetailFields,
    customValue?: IOppDetailFields
  ) => {
    const field = Object.keys(data)[0] as keyof IOpportunity;
    const values = Object.values(data)[0];

    setInputValues({
      ...inputValues,
      ...data,
      ...(customValue ? customValue : {}),
    });
    if (field === "start_date" || field === "end_date") {
      handleChangeFieldStatus({
        field: "start_end_date",
        status: "loading",
        action: "API",
      });
    }
    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });
    let id = opportunity_id ? opportunity_id : "";

    if (data && data.est_sales_date) {
      data.est_sales_date = backendDateFormat(
        String(data.est_sales_date),
        date_format
      );
    }

    if (data && data.bid_due_date) {
      data.bid_due_date = backendDateFormat(
        String(data.bid_due_date),
        date_format
      );
    }

    if (data && data.start_date) {
      data.start_date = backendDateFormat(String(data.start_date), date_format);
    }

    if (data && data.end_date) {
      data.end_date = backendDateFormat(String(data.end_date), date_format);
    }
    const updateRes = (await updateOppDetailApi(
      {
        ...data,
        module_key,
      },
      id
    )) as IOppUpdateApiRes;

    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      if (field === "start_date" || field === "end_date") {
        handleChangeFieldStatus({
          field: "start_end_date",
          status: "success",
          action: "API",
        });
      }
      if (data && data.est_sales_date) {
        data.est_sales_date = dayjs(data.est_sales_date).format(date_format);
      }

      if (data && data.bid_due_date) {
        data.bid_due_date = dayjs(data.bid_due_date).format(date_format);
      }

      if (data && data.start_date) {
        data.start_date = dayjs(data.start_date).format(date_format);
      }

      if (data && data.end_date) {
        data.end_date = dayjs(data.end_date).format(date_format);
      }
      if (data && data.budget_amount) {
        data.budget_amount = !!data.budget_amount ? data.budget_amount : null;
      }
      let newData = data;
      dispatch(updateOpportunityDetails(newData));
    } else {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      if (field === "start_date" || field === "end_date") {
        handleChangeFieldStatus({
          field: "start_end_date",
          status: "error",
          action: "API",
        });
      }
      setInputValues({ ...inputValues, [field]: oppDetail?.[field] });
      notification.error({
        description: updateRes.message,
      });
    }

    // Remove success icon after 3 some second that's why use delay function.
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );
      if (field === "start_date" || field === "end_date") {
        handleChangeFieldStatus({
          field: "start_end_date",
          status: "button",
          action: "API",
        });
      }
      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 3000);
  };

  const handleCustomer = (data: Partial<IDirectoryData>, key: string) => {
    switch (key) {
      case "customer": {
        if (
          data?.user_id != oppDetail?.customer_id ||
          (data?.user_id === oppDetail?.customer_id &&
            data?.contact_id != oppDetail?.customer_contact_id)
        ) {
          const customerData = !isEmpty(data) ? data : {};
          setCustomer(customerData);
          handleUpdateField({
            project_type: !!data.lead_project_type
              ? data.lead_project_type
              : "",
            project_type_name: !!data.lead_project_type_name
              ? data.lead_project_type_name
              : "",
            customer_id: data.user_id || 0,
            customer_name: data.display_name || "",
            customer_company: data.company_name || "",
            customer_contact_id: data.contact_id || 0,
            customer_contact_phone: data.phone || "",
            cust_email: data.email || "",
            cust_zip: data.zip || "",
            cust_city: data.city || "",
            cust_state: data.state || "",
            cust_address1: data.address1 || "",
            cust_address2: data.address2 || "",
          });
        }
        break;
      }
      case "assignedTo": {
        if (
          data?.user_id != oppDetail?.assigned_to ||
          (data?.user_id == oppDetail?.assigned_to &&
            data?.contact_id != oppDetail?.assigned_contact_id)
        ) {
          const assignedToData = !isEmpty(data) ? data : {};
          setAssignedTo(assignedToData);
          handleUpdateField({
            assigned_to: data.user_id || 0,
            assignee_name: data.display_name || "",
            assigned_contact_id: data.contact_id || 0,
          });
        }
        break;
      }
      default:
        break;
    }
  };

  useEffect(() => {
    if (oppDetail && !inputValues?.id) {
      setInputValues({
        ...oppDetail,
        ...(oppDetail.budget_amount && {
          budget_amount: Number(oppDetail.budget_amount)
            ? (Number(oppDetail.budget_amount) / 100).toFixed(2)
            : "",
        }),
      });

      if (oppDetail?.customer_id) {
        setCustomer({
          user_id: oppDetail?.customer_id,
          display_name: oppDetail.customer_name,
          type_key: oppDetail?.customer_type_key || "",
          type_name: oppDetail?.customer_type_name || "",
          image: !!oppDetail?.customer_contact_id
            ? ""
            : oppDetail?.cust_image || undefined,
          contact_id: oppDetail?.customer_contact_id || 0,
        });
      }

      if (oppDetail?.assigned_to) {
        setAssignedTo({
          user_id: Number(oppDetail?.assigned_to),
          display_name: oppDetail?.assignee_name
            ? oppDetail?.assignee_name
            : "",
          image: oppDetail?.assignee_image || undefined,
          type_key: oppDetail?.assignee_type_key || "",
          type_name: oppDetail?.assignee_type_name || "",
          contact_id: oppDetail?.assigned_contact_id || 0,
        });
      }
    }
  }, [
    oppDetail?.assigned_to,
    oppDetail?.assignee_name,
    oppDetail?.assigned_contact_id,
    oppDetail,
  ]);

  const { stageId, projectTypeId, referralSourceId } = oppDetailType;

  useEffect(() => {
    if (tab !== "files" && tab !== "notes" && customer.user_id) {
      dispatch(
        fetchEstimateData({
          module_id: 88,
          filter: [
            {
              status: 0,
              customer: customer.user_id,
            },
          ],
          limited_fields: 1,
          estimate_for_opportunity: 1,
        })
      );
    }
  }, [customer]);

  useEffect(() => {
    if (tab !== "files" && tab !== "notes") {
      dispatch(
        fetchProjectTypeData({
          types: [stageId, projectTypeId, referralSourceId],
          moduleId: 88,
        })
      );
    }
  }, []);

  const statesToShow = useMemo(
    () =>
      (stageDropDownData || [])
        ?.filter((status) => {
          return (
            typeof status?.key === "string" &&
            status?.key?.startsWith("opportunity")
          );
        })
        .sort((a, b) => Number(a.sort_order) - Number(b.sort_order)),
    [stageDropDownData]
  );
  const statusNames = statesToShow?.map((item) => item.name);

  const statusesKeyLabelMap = useMemo(() => {
    return (stageDropDownData?.reduce((acc, item) => {
      if (item.key && item.name) {
        acc[item.key] = item.name;
      }
      return acc;
    }, {} as Record<string, string>) || {}) as Record<string, string>;
  }, [stageDropDownData]);

  const filteredStates = useMemo(() => {
    return stageDropDownData.map((item) => ({
      ...item,
      label: item.name || "",
      value: item.key?.toString() as string,
      default_color: item.status_color,
      icon: item.key
        ? STATUS_MAP[item.key as keyof typeof STATUS_MAP]?.icon ||
          "fa-regular fa-circle-pause"
        : "fa-regular fa-circle-pause",
      isActive: !(
        statusNames.indexOf(item.name || "") >
        statusNames.indexOf(
          statusesKeyLabelMap[inputValues?.stage?.toString() || ""] || ""
        )
      ),
    }));
    // .sort(
    //   (a, b) =>
    //     statusNames.indexOf(a.name || "") - statusNames.indexOf(b.name || "")
    // );
  }, [stageDropDownData, inputValues]);

  const progressBarStates = useMemo(() => {
    return filteredStates?.filter(
      (item) => !!Number(item.show_in_progress_bar)
    );
  }, [filteredStates]);

  const canShowProgressBar = useMemo(() => {
    return (
      progressBarStates.filter(
        (item) => item.key?.toString() === oppDetail?.stage?.toString()
      ).length > 0
    );
  }, [progressBarStates, oppDetail]);

  useEffect(() => {
    if (detailsRef.current) {
      detailsRef.current.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    }
  }, [tab, detailsRef.current]);

  return (
    <div
      className={`ease-in-out duration-300 w-full overflow-y-auto ${
        sidebarCollapse ? "lg:w-[calc(100%-75px)]" : "xl:w-[calc(100%-225px)]"
      }`}
      ref={detailsRef}
      onScroll={handleNotesScroll}
    >
      <DetailsTopBar
        sidebarCollapse={sidebarCollapse}
        handleStepClick={(value) => handleStepClick(value)}
        activeStep={activeStep}
        setActiveStep={setActiveStep}
        loadingStatus={loadingStatus}
        setIsOpenCustomer={setIsOpenCustomer}
        customer={customer}
        setIsOpenCustomerContactDetails={setIsOpenCustomerContactDetails}
        inputValues={inputValues}
        handleUpdateField={handleUpdateField}
        setInputValues={setInputValues}
        handleChangeFieldStatus={handleChangeFieldStatus}
        contactAddress={contactAddress}
        setContactAddress={setContactAddress}
      />
      {!isOppDataLoading && (
        <ReadOnlyPermissionMsg
          className="p-4 pt-0"
          view={module_access === "read_only"}
        />
      )}
      <div className={window.ENV.PAGE_IS_IFRAME ? "" : "sm:pb-0 pb-10"}>
        <div
          className={`px-[15px] pb-[15px] ${
            module_access === "read_only"
              ? window.ENV.PAGE_IS_IFRAME
                ? "md:min-h-[calc(100dvh-205px)] sm:min-h-[calc(100dvh-262px)] min-h-[calc(100dvh-284px)]"
                : "md:min-h-[calc(100dvh-348px)] min-h-[calc(100dvh-414px)]"
              : window.ENV.PAGE_IS_IFRAME
              ? "md:min-h-[calc(100dvh-167px)] min-h-[calc(100dvh-214px)]"
              : "md:min-h-[calc(100dvh-310px)] min-h-[calc(100dvh-365px)]"
          }`}
        >
          {isOppDataLoading ? (
            <Spin
              className={`flex items-center justify-center ${
                window.ENV.PAGE_IS_IFRAME
                  ? "md:h-[calc(100vh-161px)] h-[calc(100vh-205px)]"
                  : "md:h-[calc(100vh-304px)] h-[calc(100vh-357px)]"
              }`}
            />
          ) : (
            <>
              {canShowProgressBar ? (
                <ul className="items-center justify-center w-[calc(100%-0px)] xl:hidden md:flex hidden pb-[15px]">
                  {progressBarStates?.map((items) => {
                    return (
                      <li
                        className={`relative 2xl:min-w-[125px] xl:min-w-24 lg:w-24 w-20 grid justify-end first:before:hidden before:absolute before:h-[2px] before:!w-[calc(100%-54px)]  ${
                          items.isActive
                            ? "before:bg-primary-900"
                            : "before:bg-[#ACAEAF]"
                        } before:top-[30%] lg:before:left-[-22px] before:left-[-13px]`}
                      >
                        <ProgressBarHeader
                          option={items}
                          isActive={items.isActive}
                          onClick={(option) => {
                            if (option.value && !isReadOnly) {
                              const isValid = filteredStates.find(
                                (item) => item.value === option.value
                              );
                              if (isValid) {
                                handleUpdateField({
                                  stage: option.value,
                                  module_key,
                                });
                              } else {
                                notification.error({
                                  description: "You cannot set this status",
                                });
                              }
                            }
                          }}
                        />
                      </li>
                    );
                  })}
                </ul>
              ) : null}
              {tab === "details" || tab === undefined ? (
                <OpportunitiesDetailsTab
                  setIsOpenAssignedTo={setIsOpenAssignedTo}
                  assignedTo={assignedTo}
                  setIsOpenAssignedToContactDetails={
                    setIsOpenAssignedToContactDetails
                  }
                  loadingStatus={loadingStatus}
                  inputValues={inputValues}
                  setInputValues={setInputValues}
                  handleUpdateField={handleUpdateField}
                  handleChangeFieldStatus={handleChangeFieldStatus}
                />
              ) : tab === "notes" ? (
                <OpportunitiNotesTab />
              ) : tab === "files" ? (
                <OpportunitiAttachmentTab />
              ) : (
                <></>
              )}
            </>
          )}
        </div>
        <TimeLineFooter
          sidebarCollapse={sidebarCollapse}
          data={{
            addedDate: oppDetail?.date_added || "",
            addedTime: oppDetail?.time_added || "",
            addedBy: oppDetail?.added_by_name || "",
            moduleId: module_id,
            recordId: Number(oppDetail?.id),
            typeKey: "projects",
            moduleName: module_singular_name || "Opportunities",
            ccDateAdded: oppDetail?.companycam_date_added || "",
            ccTimeAdded: oppDetail?.companycam_time_added || "",
            companyCamProjectId: oppDetail?.companycam_project_id || 0,
          }}
          isLoading={isOppDataLoading}
          isSyncedWithCompanyCam={
            oppDetail?.companycam_project_id !== 0 &&
            !!oppDetail?.companycam_project_id
              ? `#${oppDetail?.companycam_project_id}`
              : ""
          }
          hrefLinkDetail={"customerdetail"}
          nameId={"nameId"}
        />
      </div>
      {(isOpenCustomer || isOpenAssignedTo) && (
        <SelectCustomerDrawer
          closeDrawer={() => {
            // dispatch(setActiveField(defaultConfig.employee_key));
            isOpenCustomer
              ? setIsOpenCustomer(false)
              : setIsOpenAssignedTo(false);
          }}
          singleSelecte={true}
          options={
            isOpenCustomer
              ? [defaultConfig.customer_key, "lead"]
              : [
                  defaultConfig.employee_key,
                  "my_crew",
                  defaultConfig.contractor_key,
                  "by_service",
                ]
          }
          openSelectCustomerSidebar={isOpenCustomer || isOpenAssignedTo}
          setCustomer={(data) => {
            let selectedCustomer: Partial<TselectedContactSendMail>;
            if (Array.isArray(data)) {
              selectedCustomer = data[0];
            } else {
              selectedCustomer = data;
            }
            if (Number(selectedCustomer?.contact_id)) {
              const {
                user_address1,
                user_address2,
                user_city,
                user_state,
                user_zip,
              } = selectedCustomer;

              selectedCustomer = {
                ...selectedCustomer,
                address1: user_address1,
                address2: user_address2,
                city: user_city,
                state: user_state,
                zip: user_zip,
              };
            }
            if (
              selectedCustomer &&
              Object.keys(selectedCustomer).length &&
              isOpenCustomer
            ) {
              setContactAddress(true);
            }
            if (isOpenCustomer || isOpenAssignedTo) {
              if (data.length) {
                handleCustomer(
                  selectedCustomer as Partial<IDirectoryData>,
                  isOpenAssignedTo ? "assignedTo" : "customer"
                );
              } else {
                notification.error({
                  description: `${
                    isOpenAssignedTo ? "Sales Rep." : "Contact"
                  } field is required.`,
                });
              }
            }
          }}
          selectedCustomer={
            isOpenCustomer
              ? customer?.user_id && customer?.display_name
                ? ([customer] as TselectedContactSendMail[])
                : []
              : assignedTo?.user_id && assignedTo?.display_name
              ? ([assignedTo] as TselectedContactSendMail[])
              : []
          }
          additionalContactDetails={1}
          projectId={oppDetail?.project_id ? oppDetail?.project_id : undefined}
          groupCheckBox={isOpenAssignedTo ? false : true}
        />
      )}
      {(isOpenCustomerContactDetails || isOpenAssignedToContactDetails) && (
        <ContactDetailsModal
          isOpenContact={
            isOpenCustomerContactDetails || isOpenAssignedToContactDetails
          }
          contactId={
            isOpenCustomerContactDetails
              ? Number(customer?.user_id)
              : isOpenAssignedToContactDetails
              ? Number(assignedTo?.user_id)
              : undefined
          }
          onCloseModal={() => {
            isOpenCustomerContactDetails
              ? setIsOpenCustomerContactDetails(false)
              : setIsOpenAssignedToContactDetails(false);
          }}
          sendEmailDrawer={{
            projectId: oppDetail?.project_id
              ? oppDetail?.project_id
              : undefined,
          }}
          readOnly={module_access === "read_only"}
          additional_contact_id={
            isOpenCustomerContactDetails
              ? Number(customer?.contact_id || 0)
              : isOpenAssignedToContactDetails
              ? Number(assignedTo?.contact_id)
              : 0
          } // as per PHP additional contact will not selected
        />
      )}
    </div>
  );
};

export default OppotunitiesTab;
