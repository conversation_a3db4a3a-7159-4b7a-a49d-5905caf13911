import { useNavigate } from "@remix-run/react";
import { useEffect, useMemo, useState } from "react";
// Atoms
import { Spin } from "~/shared/components/atoms/spin";
// molecules
import { ErrorBoundary } from "~/shared/components/molecules/errorBoundary/ErrorBoundary";
import { ReadOnlyPermissionMsg } from "~/shared/components/molecules/readOnlyPermissionMsg";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import GoToTop from "~/shared/components/molecules/goToTop/GoToTop";
// organisms
import { TimeLineFooter } from "~/shared/components/organisms/timeLine/timeLineFooter";
// Other
import delay from "lodash/delay";
import { Number, sanitizeString } from "~/helpers/helper";
import { useTranslation } from "~/hook";
import DetailsTopBar from "~/modules/financials/pages/estimates/components/tab/DetailsTopBar";
import EstimateAttachmentTab from "~/modules/financials/pages/estimates/components/tab/EstimateAttachmentTab";
import EstimateBidding from "~/modules/financials/pages/estimates/components/tab/EstimateBiddingTab";
import EstimateCoverSheetTab from "~/modules/financials/pages/estimates/components/tab/EstimateCoverSheetTab";
import EstimateFinalizeTab from "~/modules/financials/pages/estimates/components/tab/EstimateFinalizeTab";
import EstimateNotesTab from "~/modules/financials/pages/estimates/components/tab/EstimateNotesTab";
import EstimateScopeOfWork from "~/modules/financials/pages/estimates/components/tab/EstimateScopeOfWork";
import EstimatesDetailsTab from "~/modules/financials/pages/estimates/components/tab/EstimatesDetailsTab";
import EstimateStatusbar from "~/modules/financials/pages/estimates/components/tab/EstimateStatusbar";
import EstimateTermsCondition from "~/modules/financials/pages/estimates/components/tab/EstimateTermsConditionTab";
import ItemsTab from "~/modules/financials/pages/estimates/components/tab/ItemsTab";
import { getEstBidding } from "~/modules/financials/pages/estimates/redux/action/ESBiddingAction";
import {
  getEstimateCoverSheet,
  getEstimateCoverSheetList,
  getEstimateDetail,
  getEstimateScopeDetail,
  updateEstimateDetailApi,
  updateStatusProcessApi,
} from "~/modules/financials/pages/estimates/redux/action/ESDetailAction";
import { getEstimateItems } from "~/modules/financials/pages/estimates/redux/action/ESItemAction";
import { getEsNotes } from "~/modules/financials/pages/estimates/redux/action/EsNotes";
import { updateEstimateDetail } from "~/modules/financials/pages/estimates/redux/slices/ESDetailSlice";
import {
  useAppESDispatch,
  useAppESSelector,
} from "~/modules/financials/pages/estimates/redux/store";
import {
  approvalHideStatus,
  fieldStatus,
} from "~/modules/financials/pages/estimates/utils/common";
import { ESTIMATE_STATUS_ICON } from "~/modules/financials/pages/estimates/utils/constants";
import { getCommonAttachments } from "~/redux/action/commonAttachmentSection";
import { getStatusForField } from "~/shared/utils/helper/common";
import {
  getCommonSidebarCollapse,
  getGConfig,
  useGModules, // In future this code move in redux, developer change this code
} from "~/zustand";
import { routes } from "~/route-services/routes";
import {
  itemTotalCalculator,
  labourHoursUnits,
} from "~/modules/financials/pages/estimates/components/tab/details/EstimatesCalc";
import useFieldStatus from "~/modules/financials/pages/estimates/utils/useFieldStatus";
import { fetchBillingDetails } from "~/modules/financials/pages/estimates/utils/function";
import AntdAlert from "~/shared/components/atoms/alert/Alert";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { resetTDNotes } from "~/redux/slices/commonNoteSlice";
import { resetTDFiles } from "~/redux/slices/commonAttachmentSlice";
import { EVENT_LOGGER_NAME } from "~/shared/constants/event-logger";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { getModuleAccess } from "~/shared/utils/helper/module";
import { useGlobalMenuModule } from "~/zustand/global/menuModules/slice";

interface ManageEstimatesTabProps {
  estimate_id?: string;
  tab?: string;
}
const ManageEstimatesTab = (props: ManageEstimatesTabProps) => {
  const tab = props?.tab ?? "";
  const estimate_id = useAppESSelector(
    (state) => state.estimateDetail.estimateDetail?.estimate_id
  );
  const sidebarCollapse: boolean | undefined = getCommonSidebarCollapse();
  const { page_is_iframe }: GConfig = getGConfig();

  const { getGlobalModuleByKey } = useGlobalModule();

  const EstimateModule = useMemo(
    () => getGlobalModuleByKey(CFConfig.estimate_module),
    [getGlobalModuleByKey] // Ensures memoization based on function reference
  );

  const {
    module_id: estimate_module_id = 0,
    module_key: estimate_module_key = "",
    module_name: estimate_module_name = "",
  } = EstimateModule || {};

  const module_access = useMemo(
    () => getModuleAccess(EstimateModule),
    [EstimateModule]
  );
  const dispatch = useAppESDispatch();
  const navigate = useNavigate();
  const { checkModuleAccessByKey } = useGModules();
  const [activeStep, setActiveStep] = useState<string | number>("");
  const [selectedStatusKey, setSelectedStatusKey] = useState<string>("");
  const { isSLDataFetched, customStatusList }: ICustomStatusListInitialState =
    useAppESSelector((state) => state.customStatusListData);
  const user: IInitialGlobalData["user"] = getGlobalUser();

  const { user_id, is_primary_user } = user || {};

  const { _t } = useTranslation();
  const {
    estimateDetail = {},
    isEstimateDetailLoading,
    isDashLoading,
  } = useAppESSelector((state) => state.estimateDetail);
  const { isLoading, sections, flags } = useAppESSelector(
    (state) => state.estimateItems
  );
  const { taxIsReversible, taxRate } = useMemo(() => {
    return {
      taxIsReversible:
        typeof estimateDetail?.is_reversible_tax === "boolean"
          ? estimateDetail?.is_reversible_tax
          : estimateDetail?.is_reversible_tax?.toString() === "1",
      taxRate: parseFloat(`${estimateDetail?.tax_rate ?? ""}`),
    };
  }, [estimateDetail?.is_reversible_tax, estimateDetail?.tax_rate]);

  const { loadingStatus, handleChangeFieldStatus, setLoadingStatus } =
    useFieldStatus(fieldStatus);
  const isStatusLost: boolean = useMemo(() => {
    return estimateDetail?.approval_type === "estimate_lost";
  }, [estimateDetail?.approval_type]);

  const isLoadLock = useMemo(() => {
    if (!estimateDetail) return true; // Default to true if `estimateDetail` is not available.

    // Check if the estimate is locked by the current user
    const isLockedByUser =
      estimateDetail?.locked_by?.toString() === user_id?.toString();

    // Check if the user is the primary user
    const isPrimaryUser = is_primary_user?.toString() === "1";

    // Check if the estimate is locked
    const isLocked = estimateDetail?.is_lock?.toString() === "1";

    // Return true only if not locked by the user, not primary user, and is actively locked
    return !isLockedByUser && !isPrimaryUser && isLocked;
  }, [is_primary_user, user_id, estimateDetail]);

  const isReadOnly = useMemo(
    () =>
      Boolean(
        Boolean(checkModuleAccessByKey(estimate_module_key) === "read_only") ||
          isLoadLock
      ),
    [estimate_module_key, isLoadLock]
  );

  const [completedConfirmOpen, setCompletedConfirmOpen] =
    useState<IEStatusCompletedObj>({
      action: false,
      value: null,
    });

  const [approvedConfirmOpen, setApprovedConfirmOpen] =
    useState<IEStatusCompletedObj>({
      action: false,
      value: null,
    });

  const [completedIsProjectOpen, setCompletedIsProjectOpen] =
    useState<IEStatusCompletedObj>({
      action: false,
      value: null,
    });

  const [isCompletedLoading, setIsCompletedLoading] = useState<boolean>(false);
  const [isCompletedProjectLoading, setIsCompletedProjectLoading] =
    useState<boolean>(false);

  const estimateStatList: EStatusList[] = useMemo(
    () =>
      customStatusList?.map((item: ICustomStatusSL) => ({
        label: HTMLEntities.decode(sanitizeString(item?.name)),
        value: item?.key?.toString(),
        default_color: item?.status_color,
        sort_order: Number(item?.sort_order),
        icon: ESTIMATE_STATUS_ICON[
          item?.key as keyof typeof ESTIMATE_STATUS_ICON
        ],
        key: item?.key ?? "",
        does_sync_qb: item?.does_sync_qb,
        show_in_progress_bar: item?.show_in_progress_bar,
      })) as EStatusList[],
    [customStatusList, estimateDetail, estimateDetail?.approval_type]
  );

  const handleUpdateField = async (
    data: IEstimateDetailData,
    extraData?: IEstimateDetailData
  ) => {
    const field = Object?.keys(data)?.[0] as keyof IEstimateDetailData;
    const sortValue = estimateStatList?.find(
      (item) => item?.key === data?.approval_type
    ) ?? { key: "", sort_order: "" };
    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });
    // if (field === "project_id" && !isEmpty(extraData?.estimate_project_type)) {
    //   handleChangeFieldStatus({
    //     field: Projectfield,
    //     status: "loading",
    //     action: "API",
    //   });
    // }
    let newData = data;
    let newBillData: IIVUpdateFieldOtherData = {};
    if (field == "project_id") {
      if (!data?.billed_to || data?.billed_to === 0) {
        newData = await fetchBillingDetails(data, estimateDetail ?? {});
      }
    }

    const updateRes = (await updateEstimateDetailApi({
      estimate_id: estimate_id,
      ...newData,
      project_id: data.project_id as string | number | undefined,
    })) as IEDetailsApiRes;
    if (updateRes?.success) {
      if (sortValue?.key !== undefined) {
        setSelectedStatusKey(sortValue?.key);
      }
      if (
        "approval_type" in data &&
        data.approval_type === "estimate_approved"
      ) {
        EventLogger.log(EVENT_LOGGER_NAME.estimates + " Approved Manually", 1);
      }
      setActiveStep(Number(sortValue?.sort_order));
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      const isStatusEnd = ["estimate_approved", "estimate_completed"]?.includes(
        sortValue?.key ?? ""
      );
      const isProjectOpen =
        estimateDetail?.customer_id !== estimateDetail?.project_manager_id;
      if (isStatusEnd) {
        if (sortValue?.key === "estimate_completed") {
          if (
            isProjectOpen &&
            (!estimateDetail?.project_id || estimateDetail?.project_id == 0)
          ) {
            setCompletedIsProjectOpen({ action: true, value: null });
          } else {
            if (
              estimateDetail?.project_id ||
              estimateDetail?.project_id !== 0
            ) {
              setCompletedConfirmOpen({
                action: true,
                value: null,
              });
            } else if (
              !estimateDetail?.project_id ||
              estimateDetail?.project_id == 0
            ) {
              setApprovedConfirmOpen({
                action: true,
                value: null,
              });
            }
          }
        } else {
          setApprovedConfirmOpen({ action: true, value: null });
        }
      }

      if (extraData) {
        dispatch(
          updateEstimateDetail({
            ...data,
            ...extraData,
            ...updateRes?.data,
          })
        );
      } else {
        dispatch(updateEstimateDetail(data));
      }
      if (field == "is_lock") {
        handleReloadDetails(estimate_id?.toString() || "");
      }
    } else {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      notification.error({
        description: updateRes?.message || "Something went wrong",
      });
      dispatch(updateEstimateDetail(estimateDetail));
    }
    delay(() => {
      handleChangeFieldStatus({
        field: field,
        status: "button",
        action: "API",
      });
    }, 3000);
  };

  useEffect(() => {
    // setActiveStep(
    //   estimateStatList?.find((el) => el.key === estimateDetail?.approval_type)
    //     ?.sort_order || ""
    // );
    setActiveStep(estimateDetail?.approval_type?.toString() || "");
    setSelectedStatusKey(estimateDetail?.approval_type?.toString() || "");
  }, [estimateDetail?.approval_type, estimateStatList]);

  useEffect(() => {
    if (estimate_id) {
      dispatch(resetTDFiles());
      dispatch(resetTDNotes());
    }
  }, [estimate_id]);

  const getEstimateDataFetsh = async () => {
    const response = await dispatch(
      getEstimateDetail({ estimate_id, silent: true })
    );
    if ((response?.payload as { statusCode?: number })?.statusCode === 404) {
      navigate(`${routes.MANAGE_ESTIMATE.url}`);
      return;
    }
  };

  const fetchData = (isReload = false) => {
    if (!isReload) return;
    getEstimateDataFetsh();
    switch (tab) {
      case "scope_of_work":
        dispatch(getEstimateScopeDetail({ estimate_id }));
        break;
      case "cover_sheet":
        dispatch(getEstimateCoverSheetList({ module_id: estimate_module_id }));
        dispatch(
          getEstimateCoverSheet({ estimate_id, module_id: estimate_module_id })
        );
        break;
      case "items":
        dispatch(getEstimateItems({ estimate_id }));
        break;
      case "bidding":
        dispatch(getEstBidding({ estimate_id }));
        break;
      case "files":
        dispatch(
          getCommonAttachments({
            record_id: Number(estimate_id),
            module_key: estimate_module_key,
          })
        );
        break;
      case "notes":
        dispatch(
          getEsNotes({
            record_id: Number(estimate_id),
            module_key: estimate_module_key,
          })
        );
        break;
      case "estimate_finalize":
        // No specific action defined for this tab
        break;
      default:
        if (!tab?.trim()?.length || tab === undefined) {
          if (Object.keys(estimateDetail ?? {}).length === 0) {
            getEstimateDataFetsh();
          }
        }
        break;
    }
  };

  const handleReloadDetails = (id: string) => {
    fetchData(true);
  };

  const estimateStatusVal = useMemo(() => {
    return estimateStatList?.find((item) => item?.value === activeStep) || {};
  }, [activeStep, estimateStatList]);

  const isStatusNotShow = useMemo(() => {
    if (customStatusList) {
      return customStatusList?.find((el) => el.key === selectedStatusKey)
        ?.show_in_progress_bar;
    }
  }, [selectedStatusKey, customStatusList]);

  const statusCompletedModalHandler = async (value: number) => {
    try {
      if (!isCompletedLoading) {
        setIsCompletedLoading(true);

        const payload: IEStatusProcess = {
          estimate_id: Number(estimate_id),
          complete_project: Number(value),
        };
        const isUpdateCLSection = (await updateStatusProcessApi(
          payload
        )) as IEDetailsApiRes;

        if (isUpdateCLSection?.success) {
          // if we get data add get dispatch store the obj ====>
          setIsCompletedLoading(false);
          setCompletedConfirmOpen({ action: false, value: null });
        } else {
          notification.error({
            description: isUpdateCLSection?.message,
          });
          setIsCompletedLoading(false);
          setCompletedConfirmOpen({ action: false, value: null });
        }
      } else {
        notification.error({
          description: "Something went wrong!",
        });
        setIsCompletedLoading(false);
        setCompletedConfirmOpen({ action: false, value: null });
      }
    } catch (err) {
      notification.error({
        description: (err as Error).message || "Something went wrong!",
      });
      setIsCompletedLoading(false);
    } finally {
      setIsCompletedLoading(false);
      setCompletedConfirmOpen({ action: false, value: null });
    }
  };

  const onCloseCompletedModal = () => {
    setCompletedConfirmOpen({ action: false, value: null });
  };

  const allSectionsMetrics = useMemo(() => {
    return sections?.map((section) => {
      const items = section?.items;

      const filteredItems = items
        ?.filter((el: ESEstimateItem) => {
          const totalCost = Number(el?.quantity) * Number(el?.unit_cost);
          return (
            !el?.is_optional_item &&
            (flags?.showZeroQuantityItemsOnly ? totalCost == 0 : true)
          );
        })
        ?.map((el: ESEstimateItem) => ({
          ...el,
          unit_cost: `${Number(el.unit_cost)}`,
          markup: Number(el?.markup ?? 0),
        }));

      const calculateSum = (
        array: ESEstimateItem[],
        callback: (item: ESEstimateItem) => number
      ) => array?.map(callback)?.reduce((sum, value) => sum + value, 0) || 0;

      const estimatedCost = calculateSum(filteredItems, (ite: ESEstimateItem) =>
        Number(itemTotalCalculator(ite, false) / 100)
      );
      const subtotal = calculateSum(filteredItems, (ite: ESEstimateItem) =>
        Number(itemTotalCalculator(ite, !flags?.isMarkupHidden) / 100)
      );

      const subtotalTaxeble = calculateSum(
        filteredItems?.filter((ite: ESEstimateItem) =>
          typeof ite?.apply_global_tax === "boolean"
            ? ite?.apply_global_tax
            : ite?.apply_global_tax?.toString() === "1"
        ),
        (ite: ESEstimateItem) =>
          Number(
            (itemTotalCalculator(ite, !flags?.isMarkupHidden) / 100).toFixed(2)
          )
      );

      const markup = calculateSum(
        filteredItems,
        (item: ESEstimateItem) =>
          (item?.is_markup_percentage
            ? Number(item?.unit_cost) *
              (Number(item?.markup ?? 0) / 100) *
              Number(item?.quantity)
            : Number(item?.markup ?? 0) &&
              Number(item?.unit_cost) * Number(item?.quantity)
            ? Number(item?.markup ?? 0) -
              Number(item?.unit_cost) * Number(item?.quantity)
            : 0) / 100
      );

      const profitMargin = estimatedCost
        ? ((markup / estimatedCost) * 100).toFixed(2) + "%"
        : "0%";

      const tax = taxIsReversible ? 0 : subtotalTaxeble * (taxRate / 100);
      const grandTotal = subtotal + tax;

      const hours = calculateSum(
        items?.filter((item) => {
          const totalCost = Number(item?.quantity) * Number(item?.unit_cost);
          return (
            !item?.is_optional_item &&
            labourHoursUnits?.[item?.unit] &&
            item?.item_type_display_name === "LBR" &&
            (flags?.showZeroQuantityItemsOnly ? totalCost == 0 : true)
          );
        }),
        (item) => item?.quantity
      );

      return {
        sectionName: section?.section_name,
        sectionId: section?.section_id,
        estimatedCost,
        subtotalTaxeble,
        subtotal,
        markup,
        profitMargin,
        tax,
        grandTotal,
        hours,
      };
    });
  }, [sections, flags, taxRate, taxIsReversible, labourHoursUnits]);

  const calculateTotal = (key: string) =>
    allSectionsMetrics?.reduce(
      (sum: number, section) => sum + (section as SectionMetrics)[key],
      0
    ) || 0;

  const allTotals = useMemo(
    () => ({
      totalEstimatedCost: calculateTotal("estimatedCost"),
      totalSubtotal: calculateTotal("subtotal"),
      totalTaxableSubtotal: calculateTotal("subtotalTaxeble"),
      totalMarkup: calculateTotal("markup"),
      totalTax: calculateTotal("tax"),
      totalGrandTotal: calculateTotal("grandTotal"),
      totalHours: calculateTotal("hours"),
    }),
    [allSectionsMetrics]
  );

  const selectedStatusInd = useMemo(() => {
    return estimateStatList
      ?.filter((item) => item.show_in_progress_bar != 0)
      ?.findIndex((item) => item.value == activeStep);
  }, [JSON.stringify(estimateStatList), activeStep]);

  const { checkGlobalMenuModulePermissionByKey } = useGlobalMenuModule();

  const bidManagerModulePermissionDis = checkGlobalMenuModulePermissionByKey(
    CFConfig.estimate_bidding_module
  );

  const bidManagerDisabled = bidManagerModulePermissionDis === "disabled";

  return (
    <GoToTop
      tab={tab}
      className={`ease-in-out duration-300 w-full overflow-y-auto ${
        sidebarCollapse ? "lg:w-[calc(100%-75px)]" : "xl:w-[calc(100%-225px)]"
      }`}
    >
      <DetailsTopBar
        sidebarCollapse={sidebarCollapse}
        onReloadEstimateDetails={() =>
          handleReloadDetails(estimate_id?.toString() || "")
        }
        activeStep={activeStep}
        setActiveStep={setActiveStep}
        estimateStatList={estimateStatList}
        estimateStatusVal={estimateStatusVal}
        isLoading={isEstimateDetailLoading}
        handleChangeFieldStatus={handleChangeFieldStatus}
        handleUpdateField={handleUpdateField}
        isLoadingStatus={{ loadingStatus, setLoadingStatus }}
        selectStatusKey={{ setSelectedStatusKey, selectedStatusKey }}
        isConfirmCompletedStatus={{
          completedConfirmOpen,
          setCompletedConfirmOpen,
        }}
        isConfirmCompletedProjectStatus={{
          completedIsProjectOpen,
          setCompletedIsProjectOpen,
        }}
        isConfirmApprovedStatus={{
          approvedConfirmOpen,
          setApprovedConfirmOpen,
        }}
        statusCompletedModalHandler={statusCompletedModalHandler}
        onCloseCompletedModal={onCloseCompletedModal}
        isCompletedLoading={isCompletedLoading}
        isCompletedProjectLoading={isCompletedProjectLoading}
        isLoadLock={isLoadLock}
        isReadOnly={isReadOnly}
        isStatusLost={isStatusLost}
        selectedStatusInd={selectedStatusInd}
      />
      <div className={window.ENV.PAGE_IS_IFRAME ? "" : "sm:pb-0 pb-10"}>
        <div
          className={`px-[15px] pb-[15px] ${
            window.ENV.PAGE_IS_IFRAME
              ? "md:min-h-[calc(100dvh-165px)] min-h-[calc(100dvh-205px)]"
              : "md:min-h-[calc(100dvh-308px)] min-h-[calc(100dvh-366px)]"
          }`}
        >
          {isEstimateDetailLoading ? (
            <Spin
              className={`flex items-center justify-center ${
                page_is_iframe
                  ? "md:h-[calc(100dvh-161px)] h-[calc(100dvh-205px)]"
                  : "md:h-[calc(100dvh-304px)] h-[calc(100dvh-357px)]"
              }`}
            />
          ) : (
            <>
              <div className="text-center pb-2.5 hover-scroll overflow-y-auto overflow-hidden">
                <AntdAlert
                  message={_t(
                    "You have the option to continue using our classic User Interface (UI) or test out the new Beta UI. However, we recommend using the classic UI for critical tasks. While it is possible to view both UIs side by side, we advise against working in both simultaneously. To avoid potential data corruption, please close one UI before proceeding with the other."
                  )}
                  className="text-[#ed143d] dark:text-[#d52647] font-semibold text-13"
                />
              </div>
              <ReadOnlyPermissionMsg
                className="p-4 pt-0"
                view={module_access === "read_only"}
              />
              {isStatusNotShow ? (
                <div className="w-auto flex-[0_0_auto]">
                  <ul className="items-center justify-center 2xl:w-[calc(100%-30px)] w-[calc(100%-0px)] xl:hidden sm:flex hidden mb-[15px]">
                    {!isEstimateDetailLoading && !isLoadLock && (
                      <EstimateStatusbar
                        handleUpdateField={handleUpdateField}
                        handleChangeFieldStatus={handleChangeFieldStatus}
                        estimateStatusList={estimateStatList}
                        isReadOnly={isReadOnly}
                        isStatusLost={isStatusLost}
                        activeStep={activeStep}
                        setActiveStep={setActiveStep}
                        selectedStatusInd={selectedStatusInd}
                      />
                    )}
                  </ul>
                </div>
              ) : (
                <></>
              )}
              {renderTabContent(
                tab,
                isReadOnly,
                estimateDetail,
                allTotals,
                bidManagerDisabled
              )}
            </>
          )}
        </div>
        <TimeLineFooter
          data={{
            addedDate: estimateDetail?.date_added || "",
            addedTime: estimateDetail?.time_added || "",
            addedBy: estimateDetail?.employee || "",
            moduleId: estimate_module_id,
            moduleName: estimate_module_name || "Estimate",
            recordId: Number(estimateDetail?.estimate_id),
            typeKey: estimate_module_key || "",
            qbDateAdded: estimateDetail?.qb_date_added || "",
            qbTimeAdded: estimateDetail?.qb_time_added || "",
            quickbookUserId: Number(estimateDetail?.company_estimate_id) || 0,
          }}
          isSynced={
            estimateDetail?.quickbook_estimate_id !== 0 &&
            !!estimateDetail?.quickbook_estimate_id
              ? `#${estimateDetail?.quickbook_estimate_id}`
              : ""
          }
          hrefLinkDetail={"estimate"}
          nameId={"txnId"}
          sidebarCollapse={sidebarCollapse}
          isLoading={isEstimateDetailLoading}
          customAction={
            <div className="px-2 sm:flex hidden flex-col justify-center min-h-[40px]">
              <CustomCheckBox
                className="gap-1.5 whitespace-nowrap"
                name="save_estimate_template"
                // defaultChecked={
                //   !!estimateDetail?.save_estimate_template
                // }
                checked={
                  isDashLoading
                    ? false
                    : !!estimateDetail?.save_estimate_template
                }
                onChange={(e) => {
                  handleUpdateField(
                    {
                      save_estimate_template: e.target.checked ? 1 : 0,
                      customer_id: estimateDetail?.customer_id,
                    }
                    // commented code -> https://app.clickup.com/t/86cydq0de -> Create Lead > Create Estimate > Save & Open Estimate > Select "Save Estimate as Template" > Check status at Top(It shown Status as Estimating) > Go to Estimate Dashboard > check status in listed Estimates(It shown- Template) > Open estimate & Check status "It shown- Template"
                    // {
                    //   approval_type: "estimate_template",
                    //   approval_type_name: "Template",
                    // }
                  );
                }}
                disabled={
                  isReadOnly ||
                  getStatusForField(loadingStatus, "save_estimate_template") ===
                    "loading"
                }
                loadingProps={{
                  isLoading:
                    getStatusForField(
                      loadingStatus,
                      "save_estimate_template"
                    ) === "loading",
                  className: "bg-[#ffffff]",
                }}
              >
                {_t("Save Estimate as Template")}
              </CustomCheckBox>
            </div>
          }
        />
      </div>
    </GoToTop>
  );
};

export default ManageEstimatesTab;

export { ErrorBoundary };
const renderTabContent = (
  tab: string,
  isReadOnly: boolean,
  estimateDetail: IEstimateDetailData,
  allTotals: IEAllTotal,
  bidManagerDisabled: boolean // 👈 Add this prop
) => {
  switch (tab) {
    case "items":
      return <ItemsTab isReadOnly={isReadOnly} allTotals={allTotals} />;
    case "estimate_finalize":
      if (
        !isReadOnly &&
        !approvalHideStatus[estimateDetail?.approval_type?.toString() ?? ""]
      ) {
        return <EstimateFinalizeTab isReadOnly={isReadOnly} />;
      }
      break;
    case "terms":
      return <EstimateTermsCondition isReadOnly={isReadOnly} />;
    case "scope_of_work":
      return <EstimateScopeOfWork isReadOnly={isReadOnly} />;
    case "files":
      return <EstimateAttachmentTab isReadOnly={isReadOnly} />;
    case "cover_sheet":
      return <EstimateCoverSheetTab isReadOnly={isReadOnly} />;
    case "notes":
      return <EstimateNotesTab isReadOnly={isReadOnly} />;
    case "bidding":
      if (!bidManagerDisabled) {
        return <EstimateBidding isReadOnly={isReadOnly} />;
      }
    default:
      return (
        <EstimatesDetailsTab isReadOnly={isReadOnly} allTotals={allTotals} />
      );
  }
};
