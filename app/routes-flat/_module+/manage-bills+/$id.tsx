import {
  Outlet,
  use<PERSON><PERSON>der<PERSON><PERSON>,
  use<PERSON><PERSON><PERSON>,
  use<PERSON><PERSON><PERSON>,
  useRevalidator,
} from "@remix-run/react";
import { defer } from "@remix-run/node";
import { authenticator } from "~/services/auth.server";
import { useEffect } from "react";
import { getCommonSidebarCollapse, setCommonSidebarCollapse } from "~/zustand";
import BillStoreProvider from "~/modules/financials/pages/bills/redux/BillStoreProvider";

import AgGridStylesClass from "~/assets/minify/ag-grid.style.css";
import AgGridStyles from "ag-grid-community/styles/ag-grid.css";
import AgThemeAlpineStyles from "ag-grid-community/styles/ag-theme-alpine.css";
import { ModuleSidebar } from "~/shared/components/moduleSidebar";
import { JwtDecoded } from "~/helpers/helper.server";
// FontAwesome File
import { BillDetailRegularIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/bill/detail/regular";
import { BillDetailSolidIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/bill/detail/solid";
import { BillDetailLightIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/bill/detail/light";
// Other
import { useAppBillDispatch } from "~/modules/financials/pages/bills/redux/store";
import { getSinglebillDetail } from "~/modules/financials/pages/bills/redux/slices/billDetailSlice";
import { getBillDetail } from "~/routes-flat/api+/bills+/$id+/get-bill-details";
import ManageBillDetails from "~/routes-flat/_module+/manage-bills+/$id+/$tab";
import { routes } from "~/route-services/routes";
import { BILLS_SELECTOPTION } from "~/modules/financials/pages/bills/utils/constants";

// Fort Awesome Library Add icons
BillDetailRegularIconAdd();
BillDetailSolidIconAdd();
BillDetailLightIconAdd();

export const links: TLinksFunction = () => [
  { rel: "stylesheet", href: AgGridStyles },
  { rel: "stylesheet", href: AgThemeAlpineStyles },
  { rel: "stylesheet", href: AgGridStylesClass },
];

export const loader = async ({ request, params }: TLoaderFunctionArgs) => {
  const { id } = params;
  const authSession: string = (await authenticator.isAuthenticated(
    request
  )) as unknown as string;
  const tokenData = ((await JwtDecoded(authSession, process.env.JWT || "")) ||
    {}) as AuthDecodedPayload;
  const billDetailResponse = await getBillDetail(request, authSession, {
    bill_id: id,
    user_id: tokenData.user_id,
    company_id: tokenData.company_id,
  });

  return defer({
    billDetailResponse,
  });
};

const BillDetails = () => {
  const { tab, id: bill_id }: RouteParams = useParams();
  const navigate = useNavigate();
  const revalidator = useRevalidator();
  const dispatch = useAppBillDispatch();
  const sidebarCollapse = getCommonSidebarCollapse();
  const { billDetailResponse } = useLoaderData<typeof loader>();

  useEffect(() => {
    dispatch(getSinglebillDetail(billDetailResponse));
  }, [bill_id, billDetailResponse]);

  useEffect(() => {
    const fetchData = async () => {
      const response = (await billDetailResponse) || {};

      if (response.statusCode === 401) {
        revalidator.revalidate();
      } else if (response.statusCode === 400) {
        notification.error({
          description: response?.message || "No data found.",
        });
        navigate(`${routes.MANAGE_BILL.url}`);
      }
    };
    fetchData();
  }, []);

  return (
    <div
      className={`flex overflow-hidden ${
        !window.ENV.PAGE_IS_IFRAME
          ? "md:h-[calc(100dvh-143px)] h-[calc(100dvh-112px)]"
          : "h-screen"
      }`}
    >
      {!sidebarCollapse && (
        <div
          className={`xl:hidden block absolute bg-black/20 dark:bg-white/10 z-[100] ease-in-out duration-300 ${
            sidebarCollapse
              ? "w-0 h-0"
              : window.ENV.PAGE_IS_IFRAME
              ? "w-full h-full"
              : "w-full h-[calc(100dvh-112px)]"
          }`}
          onClick={() => setCommonSidebarCollapse(true)}
        ></div>
      )}
      <ModuleSidebar
        sidebarCollapse={sidebarCollapse}
        onSidebarCollapse={setCommonSidebarCollapse}
        selectOptions={BILLS_SELECTOPTION}
        onSelectedOption={(value: string) => {
          navigate(value);
        }}
        selectedOption={tab ?? "details"}
      />
      {tab ? <Outlet /> : <ManageBillDetails />}
    </div>
  );
};

const ManageBills = () => {
  return (
    <BillStoreProvider>
      <BillDetails />
    </BillStoreProvider>
  );
};

export default ManageBills;
