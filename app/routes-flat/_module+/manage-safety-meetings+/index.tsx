import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import debounce from "lodash/debounce";
import { type RadioChangeEvent } from "antd";
import { useSearchParams } from "@remix-run/react";
// Hook + Redux
import {
  getGConfig,
  getGModuleFilters,
  updateModuleFilter,
  useGModules,
} from "~/zustand";
import { useTranslation } from "~/hook";
import { defaultConfig } from "~/data";
import { sanitizeString } from "~/helpers/helper";
import {
  ADD_SAFETY_OPTION,
  ADD_SAFETY_OPTION_RESPONSIVE,
} from "~/modules/people/safetymeetings/utils/constants";
import SMStoreProvider from "~/modules/people/safetymeetings/redux/SMStoreProvider";
import {
  useAppSMDispatch,
  useAppSMSelector,
} from "~/modules/people/safetymeetings/redux/store";
import { fetchDashData } from "~/modules/people/safetymeetings/redux/action";
import { setSearchValueAct } from "~/modules/people/safetymeetings/redux/slices";
import {
  fetchSafetyTopicData,
  fetchSafetyTopicServices,
} from "~/modules/people/safetymeetings/redux/action/commonSMAction";

// FontAwesome File
import { SMDashboardRegularIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/safety-meeting/dashboard/regular";
import { SMDashboardLightIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/safety-meeting/dashboard/light";

// Components
import { Typography } from "~/shared/components/atoms/typography";
import { SkeletonInput } from "~/shared/components/atoms/skeleton";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { AddButton } from "~/shared/components/molecules/addButton";
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { AccordionButton } from "~/shared/components/molecules/accordionButton";
import { DashboardHeader } from "~/shared/components/molecules/dashboardHeader";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { ErrorBoundary } from "~/shared/components/molecules/errorBoundary/ErrorBoundary";
import { ReadOnlyPermissionMsg } from "~/shared/components/molecules/readOnlyPermissionMsg";
import SafetyMeetingList from "~/modules/people/safetymeetings/components/dashboard/SafetyMeetingList";
import SafetyMeetingStats from "~/modules/people/safetymeetings/components/dashboard/SafetyMeetingStats";
import DaysSince from "~/modules/people/safetymeetings/components/dashboard/DaysSince";
import IncidentsCostCode from "~/modules/people/safetymeetings/components/dashboard/IncidentsCostCode";
import { SafetyMeetingOptions } from "~/modules/people/safetymeetings/components/modal/safetyMeetingOptions";
import SafetyMeetingFilter from "~/modules/people/safetymeetings/components/dashboard/SafetyMeetingFilter";
import SafetyMeetingTopics from "~/modules/people/safetymeetings/components/dashboard/SafetyMeetingTopics";
import { AddSafetyMeeting } from "~/modules/people/safetymeetings/components/sidebar/addSafetyMeeting";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";

// Fort Awesome Library Add icons
SMDashboardRegularIconAdd();
SMDashboardLightIconAdd();

const SafetyMeeting = () => {
  const { _t } = useTranslation();
  const dispatch = useAppSMDispatch();
  const [searchParams, setSearchParams] = useSearchParams();
  const { checkModuleAccessByKey, getGModuleByKey } = useGModules();
  const {
    module_id,
    module_name,
    module_singular_name,
    module_access,
  }: GConfig = getGConfig();

  const module: GModule | undefined = getGModuleByKey(
    defaultConfig.safety_meeting_module
  );

  const {
    isDataFetched,
    dailySafetyTips,
    isDashLoading,
    safetyMeetingOption,
  }: ISafetyMeetingsIntlState = useAppSMSelector((state) => state.dashboard);

  const { searchValue, safetyTopicData, topicServices }: ISMCommonInitialState =
    useAppSMSelector((state) => state.sMCommonData);

  const [fullScreenTable, setFullScreenTable] = useState<boolean>(false);
  const [isDrawerOpen, setIsDrawerOpen] = useState<boolean>(false);
  const [meetingType, setMeetingType] = useState<string>("");
  const [safetyTopic, setSafetyTopic] = useState<boolean>(false);
  const [meetingOption, setMeetingOption] = useState<boolean>(false);
  const [addSafetyTopic, setAddSafetyTopic] = useState<boolean>(false);
  const [viewSearch, setViewSearch] = useState<boolean>(false);
  const [topicSearchVal, setTopicSearchVal] = useState<string>("");
  const [selectedTopicId, setSelectedTopicId] = useState<string>("");
  const [isReloadList, setIsReloadList] = useState<boolean>(false);

  const modulePLName =
    _t(HTMLEntities.decode(sanitizeString(module_name))) || _t("Leads");

  const { filter_my_list } =
    (getGModuleFilters() as Partial<SafetyMeetingsFilter> | undefined) || {};

  const handleAddClick = (data?: string) => {
    const meetingTypeData = data || meetingType;
    setMeetingType(meetingTypeData);
    setMeetingOption(false);
    setIsDrawerOpen(true);
  };

  useEffect(() => {
    if (!isDataFetched) {
      dispatch(fetchDashData());
    }
  }, [isDataFetched]);

  useEffect(() => {
    if (!safetyTopicData.isDataFetched) {
      dispatch(
        fetchSafetyTopicData({
          start: 0,
          limit: 0,
          only_topics: 1,
          status: "0",
          language: "0,1",
        })
      );
    }
  }, [safetyTopicData.isDataFetched]);

  useEffect(() => {
    if (!topicServices.isDataFetched) {
      dispatch(
        fetchSafetyTopicServices({
          start: 0,
          limit: 0,
          status: "0",
        })
      );
    }
  }, [topicServices.isDataFetched]);

  const debouncedOnSearchChange = useCallback(
    debounce(({ target: { value } }) => {
      if (safetyTopic) {
        setTopicSearchVal(value);
      } else {
        dispatch(setSearchValueAct(value));
      }
    }, 500),
    [safetyTopic]
  );

  useEffect(() => {
    setTopicSearchVal("");
  }, [safetyTopic]);

  useEffect(() => {
    return () => {
      const newUrl = window?.location?.pathname?.includes(
        "manage-safety-meetings"
      );
      if (!newUrl) {
        dispatch(setSearchValueAct(""));
      }
    };
  }, []);

  useEffect(() => {
    if (searchParams?.get("action")?.trim() === "new") {
      setMeetingOption(true);
    }
  }, [searchParams.get("action")]);

  const removeQueryParamsToDo = () => {
    searchParams.delete("action");
    setSearchParams(searchParams);
  };

  const isNoAccessGroupAndIndividual = useMemo(() => {
    const groupAccess = checkModuleAccessByKey(
      defaultConfig.safety_meeting_module_group
    );
    const individualAccess = checkModuleAccessByKey(
      defaultConfig.safety_meeting_module_individual
    );

    return {
      group: groupAccess === "no_access",
      individual: individualAccess === "no_access",
    };
  }, [
    defaultConfig.safety_meeting_module_group,
    defaultConfig.safety_meeting_module_individual,
  ]);

  const availableMeetingOptions = useMemo(() => {
    if (
      isNoAccessGroupAndIndividual.individual &&
      isNoAccessGroupAndIndividual.group
    )
      return [];

    return ADD_SAFETY_OPTION.filter((option) => {
      if (option.value === "group") {
        if (
          isNoAccessGroupAndIndividual.group ||
          safetyMeetingOption.group_safety_meeting !== 1
        ) {
          return false;
        }
      }

      if (option.value === "individual") {
        if (
          isNoAccessGroupAndIndividual.individual ||
          safetyMeetingOption.individual_safety_meeting !== 1
        ) {
          return false;
        }
      }

      if (option.value === "schedule") {
        if (safetyMeetingOption.scheduled_safety_meeting !== 1) {
          return false;
        }
      }

      return true;
    }).map((option) => ({
      ...option,
      label: option.label.replace("Safety Meeting", module_singular_name),
    }));
  }, [isNoAccessGroupAndIndividual, safetyMeetingOption, module_singular_name]);

  const updateFilter = useCallback(
    (filter: Partial<LeadsFilter>) => {
      if (module_id) {
        updateModuleFilter({
          filter,
          moduleId: module_id.toString(),
          onError: (response: IApiCallResponse) => {
            notification.error({
              description: response.message,
            });
          },
        });
      }
    },
    [module_id]
  );

  const onMyLeadTabListButtonChange = (e: RadioChangeEvent) => {
    try {
      const listValue = e.target.value as string;
      updateFilter({
        filter_my_list: listValue == "1" ? "1" : "",
      });
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    }
  };

  const SM_BUTTON_TAB = [
    {
      label: (
        <div className="flex gap-1.5 items-center font-medium">
          <FontAwesomeIcon icon="fa-duotone fa-solid fa-people-group" />
          {`All ${modulePLName}`}
        </div>
      ),
      value: "0",
    },
    {
      label: (
        <div className="flex gap-1.5 items-center font-medium">
          <FontAwesomeIcon icon="fa-duotone fa-solid fa-user" />
          {`My ${modulePLName}`}
        </div>
      ),
      value: "1",
    },
  ];

  return (
    <>
      <DashboardHeader
        key={`sm-header-${safetyTopic}`}
        searchPlaceHolder={
          safetyTopic
            ? _t("Search for a Safety Topics")
            : module_singular_name
            ? _t(`Search for a ${module_singular_name}`)
            : ""
        }
        onSearchChange={debouncedOnSearchChange}
        searchVal={safetyTopic ? topicSearchVal : searchValue}
        viewSearch={viewSearch}
        setViewSearch={setViewSearch}
        filterComponent={
          !safetyTopic && (
            <SafetyMeetingFilter
              onClearSearch={() => {
                dispatch(setSearchValueAct(""));
              }}
            />
          )
        }
        leftComponent={
          <>
            {safetyTopic ? (
              <li>
                <ButtonWithTooltip
                  tooltipTitle={_t("Return to Dashboard")}
                  tooltipPlacement="top"
                  icon="fa-regular fa-square-list"
                  iconClassName="h-5 w-5"
                  className="!w-7 !h-7"
                  onClick={() => setSafetyTopic(false)}
                />
              </li>
            ) : (
              ""
            )}
          </>
        }
        rightComponent={(() => {
          const hasPermission = module?.has_student_permission === "0";
          const hasFullOrOwnAccess =
            module_access === "full_access" ||
            module_access === "own_data_access";
          const hasReadOnlyAccess = module_access === "read_only";
          const canShowMeetingButton =
            !isNoAccessGroupAndIndividual.group ||
            !isNoAccessGroupAndIndividual.individual;

          const items: React.ReactNode[] = [];

          const viewSafetyTopicsButton = (
            <li className="sm:flex hidden" key="view-safety-topics">
              <div
                className="bg-blue-100 text-13 text-primary-900 rounded-sm mb-0 p-1 px-3 cursor-pointer font-medium"
                onClick={() => setSafetyTopic(true)}
              >
                {_t("View Safety Topics")}
              </div>
            </li>
          );

          if (hasPermission && hasFullOrOwnAccess) {
            if (canShowMeetingButton && availableMeetingOptions?.length > 0) {
              items.push(
                <li className="sm:flex hidden" key="meeting-button">
                  <DropdownMenu
                    options={availableMeetingOptions.map((option) => ({
                      ...option,
                      onClick: () => {
                        handleAddClick(option?.value);
                        setMeetingType(option?.value);
                      },
                    }))}
                    contentClassName="add-items-drop-down"
                    placement="bottomRight"
                    buttonClass="w-fit h-[26px] m-0 !bg-blue-100 hover:!bg-blue-100 p-0 add-select-dropdown rounded-r-sm"
                    children={
                      <div className="flex items-center gap-2">
                        <div className="h-[26px] w-6 flex items-center justify-center text-white bg-primary-900 rounded-l dark:!bg-dark-950">
                          <FontAwesomeIcon
                            icon="fa-regular fa-plus"
                            className="w-[13px] h-[13px] m-auto !text-white dark:!text-white"
                          />
                        </div>
                        <Typography className="text-13 text-primary-900 font-medium">
                          {_t(`${module_singular_name}`)}
                        </Typography>
                        <FontAwesomeIcon
                          className="pr-2 w-3 h-3 text-primary-900"
                          icon="fa-regular fa-chevron-down"
                        />
                      </div>
                    }
                  />
                </li>
              );
            }

            items.push(
              <li className="sm:flex hidden" key="safety-topic">
                {safetyTopic ? (
                  <AddButton onClick={() => setAddSafetyTopic(true)}>
                    {_t("Add Safety Topic")}
                  </AddButton>
                ) : (
                  viewSafetyTopicsButton.props.children
                )}
              </li>
            );
          } else if (hasPermission && hasReadOnlyAccess && !safetyTopic) {
            items.push(viewSafetyTopicsButton);
          }

          return items.length > 0 ? (
            <>
              {items}
              <li className="sm:hidden flex">
                <DropdownMenu
                  contentClassName="w-[180px] add-items-drop-down"
                  options={ADD_SAFETY_OPTION_RESPONSIVE.filter((i) => {
                    if (i.value === "view_safety_topic" && safetyTopic)
                      return false;
                    if (i.value === "add_safety_topic" && !safetyTopic)
                      return false;
                    if (
                      i.value === "add_safety_meeting" &&
                      !canShowMeetingButton
                    )
                      return false;

                    return true;
                  }).map((option) => ({
                    ...option,
                    label: option.label.replace(
                      "Safety Meeting",
                      module_singular_name
                    ),
                    onClick: () => {
                      if (option?.value == "add_safety_meeting") {
                        setMeetingOption(true);
                      } else if (option?.value == "add_safety_topic") {
                        setAddSafetyTopic(true);
                      } else {
                        setSafetyTopic(true);
                      }
                    },
                  }))}
                  buttonClass="w-fit h-[26px] m-0 !bg-blue-100 hover:!bg-blue-100 p-0 add-select-dropdown rounded-r-sm"
                  children={
                    <div className="flex items-center gap-2">
                      <div className="h-[26px] w-6 flex items-center justify-center text-white bg-primary-900 rounded-l dark:!bg-dark-950">
                        <FontAwesomeIcon
                          icon="fa-regular fa-plus"
                          className="w-[13px] h-[13px] m-auto !text-white dark:!text-white"
                        />
                      </div>
                      <Typography className="text-13 text-primary-900 font-medium">
                        {_t("Add Safety")}
                      </Typography>
                      <FontAwesomeIcon
                        className="pr-2 w-3 h-3 text-primary-900"
                        icon="fa-regular fa-chevron-down"
                      />
                    </div>
                  }
                />
              </li>
            </>
          ) : null;
        })()}
      />
      <div
        className={`pt-[41px] overflow-y-auto overflow-hidden ${
          !window.ENV.PAGE_IS_IFRAME
            ? "md:h-[calc(100dvh-143px)] h-[calc(100dvh-112px)]"
            : "h-screen"
        }`}
      >
        <ReadOnlyPermissionMsg view={module_access === "read_only"} />
        {safetyTopic ? (
          <SafetyMeetingTopics
            addSafetyTopic={addSafetyTopic}
            setAddSafetyTopic={setAddSafetyTopic}
            search={topicSearchVal}
            isReadOnly={module_access === "read_only"}
            onStartMeeting={(data) => {
              setMeetingOption(true);
              setSelectedTopicId(data.topic_id.toString());
            }}
            isAvailableMeetOpt={availableMeetingOptions?.length > 0}
          />
        ) : (
          <div className="p-4">
            <div className="grid gap-2.5">
              <div
                className={`grid gap-y-2.5 transition-all ease-in-out duration-300 ${
                  fullScreenTable
                    ? "max-h-0 overflow-hidden"
                    : "xl:max-h-[650px] md:max-h-[900px] max-h-[2000px]"
                }`}
              >
                <div className="common-card p-2">
                  {isDashLoading ? (
                    <SkeletonInput className="sm:!w-1/4 !w-10 !h-5" />
                  ) : (
                    <Typography className="block text-[#777] text-[13px]">
                      {_t(`Daily Safety Tip: ${dailySafetyTips}`)}
                    </Typography>
                  )}
                </div>
                <div className="grid xl:grid-cols-5 grid-cols-1 xl:gap-x-2.5 gap-y-2.5">
                  <div className="md:col-span-3 common-card">
                    <SafetyMeetingStats moduleName={module_singular_name} />
                  </div>
                  <div className="grid md:grid-cols-2 grid-cols-1 col-span-2 gap-2.5">
                    <div className="col-span-1  common-card min-h-[235px] max-md:col-span-1 max-md:order-2">
                      <IncidentsCostCode />
                    </div>
                    <div className="col-span-1  common-card min-h-[235px] max-md:order-3">
                      <DaysSince moduleName={module_singular_name} />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* <div className={`w-full ${fullScreenTable ? "" : "mt-4"}`}> */}
            <div
              className={`w-full ${
                fullScreenTable || viewSearch
                  ? "sm:mt-2.5 mt-8"
                  : "sm:mt-7 mt-14"
              }`}
            >
              <div className="relative h-7 z-[999] flex items-center justify-end">
                <AccordionButton
                  onClick={() => setFullScreenTable((prev: boolean) => !prev)}
                  fullScreenTable={fullScreenTable}
                />
                <div className="flex justify-between items-center w-full sm:mb-7 mb-20 flex-wrap md:flex-nowrap">
                  {module_access !== "own_data_access" && (
                    <div className="w-fit p-1 bg-[#EEEFF0] rounded dark:bg-dark-800 ml-auto md:ml-0">
                      <ListTabButton
                        value={filter_my_list == "1" ? "1" : "0"}
                        options={SM_BUTTON_TAB}
                        activeclassName="active:bg-[#ffffff] !text-primary-900 !rounded-md"
                        className="sm:min-w-[100px] min-w-fit sm:px-1.5 !text-[#868D8D] px-2 !border-transparent bg-[#EEEFF0]"
                        onChange={onMyLeadTabListButtonChange}
                      />
                    </div>
                  )}
                </div>
              </div>
              <div className="p-2 bg-white dark:bg-dark-600 common-list-table rounded">
                <SafetyMeetingList
                  setMeetingOption={setMeetingOption}
                  search={searchValue || ""}
                  isReloadList={isReloadList}
                  setIsReloadList={setIsReloadList}
                  isNoAccessGroupAndIndividual={isNoAccessGroupAndIndividual}
                />
              </div>
            </div>
          </div>
        )}
      </div>

      <SafetyMeetingOptions
        moduleName={module_singular_name}
        availableMeetingOptions={availableMeetingOptions}
        isOpen={meetingOption}
        onCloseModal={() => {
          setSelectedTopicId("");
          setMeetingOption(false);
          setMeetingType("");
          removeQueryParamsToDo();
        }}
        onClickAdd={() => handleAddClick()}
        value={meetingType}
        setValue={setMeetingType}
      />
      {isDrawerOpen && (
        <AddSafetyMeeting
          meetingType={meetingType}
          isDrawerOpen={isDrawerOpen}
          selectedTopicId={selectedTopicId}
          setMeetingType={setMeetingType}
          setIsDrawerOpen={setIsDrawerOpen}
          setSelectedTopicId={setSelectedTopicId}
          removeQueryParamsToDo={removeQueryParamsToDo}
          isNoAccessGroupAndIndividual={isNoAccessGroupAndIndividual}
          onSaved={setIsReloadList}
        />
      )}
    </>
  );
};

const ManageSafetyMeeting = () => {
  return (
    <SMStoreProvider>
      <SafetyMeeting />
    </SMStoreProvider>
  );
};

export default React.memo(ManageSafetyMeeting);

export { ErrorBoundary };
