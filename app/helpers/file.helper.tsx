import isEmpty from "lodash/isEmpty";

import { faFile } from "@fortawesome/pro-regular-svg-icons/faFile";
import { faFileCsv } from "@fortawesome/pro-regular-svg-icons/faFileCsv";
import { faFileExcel } from "@fortawesome/pro-regular-svg-icons/faFileExcel";
import { faFileImage } from "@fortawesome/pro-regular-svg-icons/faFileImage";
import { faFileLines } from "@fortawesome/pro-regular-svg-icons/faFileLines";
import { faFilePdf } from "@fortawesome/pro-regular-svg-icons/faFilePdf";
import { faFilePowerpoint } from "@fortawesome/pro-regular-svg-icons/faFilePowerpoint";
import { faFileWord } from "@fortawesome/pro-regular-svg-icons/faFileWord";
import { faFileZip } from "@fortawesome/pro-regular-svg-icons/faFileZip";
import { faMusic } from "@fortawesome/pro-regular-svg-icons/faMusic";

const FileDefaultPdfImage =
  "https://cdn.contractorforeman.net/assets/images/file_icons/pdf.png";
const FileDefaultCsvImage =
  "https://cdn.contractorforeman.net/assets/images/file_icons/csv.png";
const FileDefaultDocxImage =
  "https://cdn.contractorforeman.net/assets/images/file_icons/docx.png";
const FileDefaultDwgImage =
  "https://cdn.contractorforeman.net/assets/images/file_icons/dwg.png";
const FileDefaultMusicImage =
  "https://cdn.contractorforeman.net/assets/images/file_icons/music.png";
const FileDefaultPptImage =
  "https://cdn.contractorforeman.net/assets/images/file_icons/ppt.png";
const FileDefaultTxtImage =
  "https://cdn.contractorforeman.net/assets/images/file_icons/txt.png";
const FileDefaultXlsxImage =
  "https://cdn.contractorforeman.net/assets/images/file_icons/xlsx.png";
const FileDefaultZipImage =
  "https://cdn.contractorforeman.net/assets/images/file_icons/zip.png";
const FileDefaultUnknownImage =
  "https://cdn.contractorforeman.net/assets/images/file_icons/unknown.png";

export const getFileData = ({
  file,
  tags,
}: {
  file: Partial<SGalleryFile>;
  tags?: SGalleryTagsData;
}) => {
  let tempFile: Partial<GetFileDataResponse> = {};
  if (file && !isEmpty(file)) {
    let filePath: string | undefined = !file.file_path
      ? undefined
      : file?.file_path?.replace("+", "%2B") +
        "?" +
        Math.random() * Math.random();
    tempFile = {
      name: encodeURI(file.file_name || "").trim(),
      date: file.date_added,
      file_path: filePath,
      is_image: file.is_image?.toString() === "1",
      is_file_shared: file.is_file_shared?.toString() === "1",
      size: file.size,
      camera_res: file.camera_res,
      image_res: file.image_res,
      notes: file.notes,
      image_id: file.image_id,
      file_ext: file.file_ext,
      annotation_data: file.annotation_data,
      original_file_path: file.original_file_path,
    };

    if (!isEmpty(file.file_tags) && tags) {
      const fileTags: string[] | undefined = file.file_tags?.split(",");
      const fileTagsData = fileTags
        ?.map((fileTagId: string) => {
          let tempTag: Partial<SGalleryTag> | undefined = tags?.data?.find(
            (tag: Partial<SGalleryTag>) => tag?.tag_id === fileTagId
          );
          if (isEmpty(tempTag)) {
            tempTag = tags?.data_all?.find(
              (tag: Partial<SGalleryTag>) => tag?.tag_id === fileTagId
            );
          }
          return tempTag;
        })
        ?.filter(
          (tag: Partial<SGalleryTag> | undefined) => tag
        ) as Partial<SGalleryTag>[];
      tempFile = {
        ...tempFile,
        file_tags: fileTagsData?.filter(
          (tag: Partial<SGalleryTag> | undefined) => tag
        ),
      };
    }

    if (tempFile?.is_image) {
      tempFile = {
        ...tempFile,
        image: filePath ?? FileDefaultUnknownImage,
        icon: faFileImage,
        iconClassName: "text-[#8380F6]",
      };
    } else {
      switch (file.file_ext?.toLowerCase()) {
        case "pdf":
          tempFile = {
            ...tempFile,
            image: FileDefaultPdfImage,
            icon: faFilePdf,
            iconClassName: "text-[#FC3830]",
          };
          break;
        case "csv":
          tempFile = {
            ...tempFile,
            image: FileDefaultCsvImage,
            icon: faFileCsv,
            iconClassName: "text-[#31D071]",
          };
          break;
        case "docx":
        case "doc":
        case "rtf":
          tempFile = {
            ...tempFile,
            image: FileDefaultDocxImage,
            icon: faFileWord,
            iconClassName: "text-[#004EAE]",
          };
          break;
        case "dwg":
          tempFile = {
            ...tempFile,
            image: FileDefaultDwgImage,
            icon: faFile,
            iconClassName: "text-[#5B5B5B]",
          };
          break;
        case "mp4":
        case "mov":
        case "flv":
        case "webm":
        case ".m2ts":
        case ".mts":
        case "avi":
        case "wmv":
        case "mkv":
        case "m2ts":
        case "mts":
          tempFile = {
            ...tempFile,
            image: FileDefaultMusicImage,
            icon: faMusic,
            iconClassName: "text-[#FF3E4C]",
          };
          break;
        case "ppt":
        case "pptx":
          tempFile = {
            ...tempFile,
            image: FileDefaultPptImage,
            icon: faFilePowerpoint,
            iconClassName: "text-[#ff5400]",
          };
          break;
        case "txt":
          tempFile = {
            ...tempFile,
            image: FileDefaultTxtImage,
            icon: faFileLines,
            iconClassName: "text-[#5659E9]",
          };
          break;
        case "xlsx":
        case "xsl":
        case "xlsm":
          tempFile = {
            ...tempFile,
            image: FileDefaultXlsxImage,
            icon: faFileExcel,
            iconClassName: "text-[#127F45]",
          };
          break;
        case "zip":
        case "rar":
          tempFile = {
            ...tempFile,
            image: FileDefaultZipImage,
            icon: faFileZip,
            iconClassName: "text-[#307DBC]",
          };
          break;
        default:
          tempFile = {
            ...tempFile,
            image: FileDefaultUnknownImage,
            icon: faFile,
            iconClassName: "text-[#4A5A76]",
          };
          break;
      }
    }
  }

  return tempFile;
};

export const getFileDataFromFileObj = (file: File, date: string) => {
  return getFileData({
    file: {
      file_name: file?.name,
      file_ext: file?.name?.split(".")?.pop(),
      date_added: date,
      file_path: URL.createObjectURL(file),
      is_image: file?.type?.startsWith("image/") ? "1" : "0",
      is_file_shared: "0",
      size: file?.size?.toString(),
    },
  });
};

export const getFileType = ({ file }: { file: string }): GetFileTypeValue => {
  const fileDotIndex: number = file?.lastIndexOf(".") ?? -1;
  const fileType: string =
    fileDotIndex >= 0 ? file?.substring(fileDotIndex + 1, file?.length) : "";

  let tempFile;
  switch (fileType) {
    case "png":
    case "gif":
    case "jpeg":
    case "jpg":
      tempFile = {
        isImage: true,
        image: file,
        icon: faFileImage,
        fileUrl: file,
        iconClassName: "text-[#8380F6]",
      };
      break;
    case "docx":
    case "doc":
    case "rtf":
      tempFile = {
        isImage: false,
        image: FileDefaultDocxImage,
        icon: faFileWord,
        fileUrl: file,
        iconClassName: "text-[#004EAE]",
      };
      break;
    case "ppt":
    case "pptx":
      tempFile = {
        isImage: false,
        image: FileDefaultPptImage,
        icon: faFilePowerpoint,
        fileUrl: file,
        iconClassName: "text-[#ff5400]",
      };
      break;
    case "xlsx":
    case "xsl":
      tempFile = {
        isImage: false,
        image: FileDefaultXlsxImage,
        icon: faFileExcel,
        fileUrl: file,
        iconClassName: "text-[#127F45]",
      };
      break;
    case "zip":
    case "rar":
      tempFile = {
        isImage: false,
        image: FileDefaultZipImage,
        icon: faFileZip,
        fileUrl: file,
        iconClassName: "text-[#307DBC]",
      };
      break;
    case "pdf":
      tempFile = {
        isImage: false,
        image: FileDefaultPdfImage,
        icon: faFilePdf,
        fileUrl: file,
        iconClassName: "text-[#FC3830]",
      };
      break;
    case "csv":
      tempFile = {
        isImage: false,
        image: FileDefaultCsvImage,
        icon: faFileCsv,
        fileUrl: file,
        iconClassName: "text-[#31D071]",
      };
      break;
    case "dwg":
      tempFile = {
        isImage: false,
        image: FileDefaultDwgImage,
        icon: faFile,
        fileUrl: file,
        iconClassName: "text-[#5B5B5B]",
      };
      break;
    case "mp4":
      tempFile = {
        isImage: false,
        image: FileDefaultMusicImage,
        icon: faMusic,
        fileUrl: file,
        iconClassName: "text-[#FF3E4C]",
      };
      break;
    case "txt":
      tempFile = {
        isImage: false,
        image: FileDefaultTxtImage,
        icon: faFileLines,
        fileUrl: file,
        iconClassName: "text-[#5659E9]",
      };
      break;
    default:
      tempFile = {
        isImage: true,
        image: FileDefaultUnknownImage,
        icon: faFile,
        fileUrl: file,
        iconClassName: "text-[#4A5A76]",
      };
      break;
  }
  return tempFile;
};
