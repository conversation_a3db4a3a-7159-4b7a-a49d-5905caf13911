import { NavigateFunction } from "@remix-run/react";
import { CellClickedEvent, GetContextMenuItemsParams } from "ag-grid-community";

export const handleRowNavigation = <T>(
  event: MouseEvent | CellClickedEvent<T> | GetContextMenuItemsParams<T>,
  data: T,
  generateUrl: (data: T) => string,
  clearData?: () => void,
  navigate?: NavigateFunction
): boolean | void => {
  const url = generateUrl(data);

  if ("button" in event && event.button === 1 && Number(window.ENV.ENABLE_ALL_CLICK)) {
    window.open(url, "_blank");
    return false;
  }

  if ("event" in event && event.event instanceof MouseEvent) {
    const mouseEvent = event.event as MouseEvent;
    if (Number(window.ENV.ENABLE_ALL_CLICK) && mouseEvent.ctrlKey || mouseEvent.metaKey) {
      window.open(url, "_blank");
      mouseEvent.preventDefault();
      clearData?.();
    } else if (!("colDef" in event) || event.colDef.headerName) {
      if (navigate) {
        navigate(url);
      }
      clearData?.();
    }
  }
};
