import { memo, useEffect, useMemo, useRef, useState } from "react";
import { CFDynamicTable } from "~/components/third-party/ag-grid";
import {
  formatAmount,
  Number,
  replaceDOMParams,
  sanitizeString,
} from "~/helpers/helper";
import { useNavigate } from "@remix-run/react";
import { getGConfig, getGModuleFilters, getGSettings } from "~/zustand";
// Hook
import { useTranslation } from "~/hook";
// Molecules
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import {
  clearBilldetailObject,
  updateBillableApi,
} from "~/zustand/pages/manage-bills/actions";
import DateTimeIcon from "~/components/common/date-time-icon";
import { CFTableTooltip } from "~/components/third-party/ant-design/cf-table-tooltip";
import TableActions from "./table-actions";
import { useModuleDataSearch } from "~/zustand/global-module-filter/hook";
import { defaultConfig } from "~/data";
import { billRoutes } from "~/route-services/bill.routes";
import {
  CellMouseDownEvent,
  GetContextMenuItemsParams,
  ICellRendererParams,
  type CellClickedEvent,
} from "ag-grid-community";
import { Tooltip } from "~/shared/components/atoms/tooltip";

const BillsMainTable = ({
  accessContactList = true,
}: {
  accessContactList?: boolean;
}) => {
  const { _t } = useTranslation();
  const { module_access }: GConfig = getGConfig();
  const gConfig: GConfig = getGConfig();
  const navigate = useNavigate();
  const filter = getGModuleFilters() as Partial<BillModuleFilter> | undefined;
  const { formatter } = useCurrencyFormatter();
  const agGridRef = useRef<ExtendedAgGridReact | null>(null);
  const { search }: IModuleDataSearchParams = useModuleDataSearch(
    defaultConfig.bill_module
  );
  const [billLoadingStatus, setBillLoadingStatus] = useState<{
    [key: string]: boolean;
  }>({});
  const { quickbook_sync, quickbook_desktop_sync }: GSettings = getGSettings();
  const [activityData, setActivityData] = useState<
    ICellRendererParams | undefined
  >(undefined);

  const noData = useMemo(
    () =>
      agGridRef &&
      agGridRef.current &&
      agGridRef.current.api &&
      (agGridRef.current.api.getDisplayedRowCount() || 0) <= 1,
    [agGridRef?.current?.api?.getDisplayedRowCount()]
  );

  const handleChange = async (params: any, fieldName?: keyof TBill) => {
    const { data }: BillCellRenderer = params;
    if (!data || !data.bill_id || !fieldName) return;
    const isChecked =
      String(data[fieldName]) === "1" || data[fieldName] === true
        ? false
        : true;
    fieldName === "is_billable" &&
      setBillLoadingStatus({
        [data.bill_id]: true,
      });
    const updateRes = (await updateBillableApi({
      bill_id: data.bill_id,
      module_id: gConfig?.module_id,
      custom_bill_id: data.custom_bill_id || data.company_bill_id,
      supplier_id: data.supplier_id,
      ...(fieldName && { [fieldName]: isChecked }),
    })) as any;
    if (updateRes?.success) {
      agGridRef.current?.api?.getDisplayedRowAtIndex(params.rowIndex)?.setData({
        ...data,
        ...(fieldName ? { [fieldName]: isChecked } : { is_shared: isChecked }),
      });
    } else {
      notification.error({
        description: updateRes.message,
      });
    }
    fieldName === "is_billable" && setBillLoadingStatus({});
  };

  const quickbookSync = useMemo(
    () =>
      Boolean(Number(quickbook_sync)) ||
      Boolean(Number(quickbook_desktop_sync)),
    [quickbook_sync, quickbook_desktop_sync]
  );

  const tableHeader = useMemo(() => {
    return [
      {
        headerName: _t("Bill") + " #",
        minWidth: noData ? undefined : 130,
        flex: 1,
        field: "company_bill_id",
        sortable: true,
        resizable: true,
        suppressMovable: false,
        suppressMenu: true,
        cellRenderer: ({ data: { company_bill_id } }: BillCellRenderer) => {
          const companyBillId = replaceDOMParams(
            sanitizeString(company_bill_id)
          );
          return companyBillId ? (
            <CFTableTooltip
              tooltipContent={companyBillId}
              children={companyBillId}
            />
          ) : (
            "-"
          );
        },
      },
      {
        headerName: _t("Due Date"),
        minWidth: noData ? undefined : 135,
        maxWidth: noData ? undefined : 135,
        field: "due_date",
        sortable: true,
        cellRenderer: ({ data: { due_date } }: BillCellRenderer) => {
          return due_date ? (
            <div className="text-left">
              <DateTimeIcon format="date" date={due_date} />
            </div>
          ) : (
            "-"
          );
        },
      },
      {
        headerName: _t("Vendor"),
        minWidth: noData ? undefined : 150,
        flex: 1,
        sortable: true,
        field: "supplier_id",
        resizable: true,
        suppressMovable: false,
        suppressMenu: true,
        cellRenderer: ({ data: { supplier_name } }: BillCellRenderer) => {
          const companyName = replaceDOMParams(sanitizeString(supplier_name));
          return companyName ? (
            <CFTableTooltip
              tooltipContent={companyName}
              children={companyName}
            />
          ) : (
            "-"
          );
        },
      },
      {
        headerName: _t("Project"),
        minWidth: noData ? undefined : 150,
        sortable: true,
        field: "project_name",
        flex: 1,
        resizable: true,
        suppressMovable: false,
        suppressMenu: true,
        cellRenderer: ({ data: { project_name } }: BillCellRenderer) => {
          const projectName = replaceDOMParams(sanitizeString(project_name));
          return projectName ? (
            <CFTableTooltip
              tooltipContent={projectName}
              children={projectName}
            />
          ) : (
            "-"
          );
        },
      },
      {
        headerName: _t("Description"),
        minWidth: noData ? undefined : 150,
        flex: 1,
        field: "notes",
        sortable: true,
        resizable: true,
        suppressMovable: false,
        suppressMenu: true,
        cellRenderer: ({ data: { notes } }: BillCellRenderer) => {
          const notesStr = notes?.toString();
          return notesStr ? (
            <CFTableTooltip tooltipContent={notesStr} children={notesStr} />
          ) : (
            "-"
          );
        },
      },
      {
        headerName: _t("Billable"),
        field: "is_billable",
        maxWidth: 92,
        minWidth: 92,
        suppressMovable: true,
        suppressMenu: true,
        sortable: true,
        headerClass: "ag-header-center",
        cellRenderer: (params: BillCellRenderer) => {
          const { data } = params;
          return (
            <div className="flex items-center gap-2 justify-center">
              <Tooltip
                title={
                  (typeof data.is_billable === "boolean"
                    ? data.is_billable
                    : data.is_billable?.toString() === "1") &&
                  !(data && data.bill_id
                    ? billLoadingStatus[data.bill_id]
                    : false) &&
                  "This Item is Billable to the Customer"
                }
              >
                <CustomCheckBox
                  className="gap-0"
                  name="is_billable"
                  onChange={() => {
                    handleChange(params, "is_billable");
                  }}
                  checked={
                    typeof data.is_billable === "boolean"
                      ? data.is_billable
                      : data.is_billable?.toString() === "1"
                  }
                  disabled={
                    (data && data.bill_id && billLoadingStatus[data.bill_id]) ||
                    !(
                      module_access === "full_access" ||
                      module_access === "own_data_access"
                    )
                  }
                  loadingProps={{
                    isLoading:
                      data && data.bill_id
                        ? billLoadingStatus[data.bill_id]
                        : false,
                    className: "bg-white",
                  }}
                />
              </Tooltip>
            </div>
          );
        },
      },
      {
        headerName: _t("Total"),
        minWidth: noData ? undefined : 120,
        maxWidth: noData ? undefined : 150,
        flex: 1,
        field: "total",
        type: "total",
        headerClass: "ag-header-right",
        cellClass: "ag-right-aligned-cell",
        sortable: true,
        cellRenderer: ({ data: { total = 0 } }: BillCellRenderer) => {
          const amount = total / 100;
          const formattedAmount = formatter(
            formatAmount(amount, { isDashboard: true })
          ).value_with_symbol;

          return (
            <CFTableTooltip
              tooltipContent={formattedAmount}
              children={formattedAmount}
              mainClassName="justify-end"
            />
          );
        },
      },
      {
        headerName: _t("Balance Due"),
        maxWidth: noData ? undefined : 130,
        minWidth: noData ? undefined : 150,
        flex: 1,
        field: "due_balance",
        type: "due_balance",
        headerClass: "ag-header-right",
        cellClass: "ag-right-aligned-cell",
        sortable: true,
        cellRenderer: ({ data: { due_balance } }: BillCellRenderer) => {
          const amount = Number(due_balance) / 100;
          const formattedAmount = formatter(
            formatAmount(amount, { isDashboard: true })
          ).value_with_symbol;

          return formattedAmount ? (
            <CFTableTooltip
              tooltipContent={formattedAmount}
              children={formattedAmount}
              mainClassName="justify-end"
            />
          ) : (
            "-"
          );
        },
      },
      ...(module_access === "full_access" || module_access === "own_data_access"
        ? [
            {
              headerName: "",
              maxWidth: 45,
              minWidth: 45,
              field: "more",
              sortable: false,
              headerClass: "ag-header-right",
              cellClass: "!cursor-auto",
              cellRenderer: (params: BillCellRenderer) =>
                params.data ? (
                  <TableActions
                    data={params?.data}
                    tableRef={agGridRef}
                    accessContactList={accessContactList}
                    paramsData={params}
                  />
                ) : null,
            },
          ]
        : []),
    ];
  }, [agGridRef?.current, noData, module_access, billLoadingStatus]);

  return (
    <div className="h-[calc(100dvh-292px)] ag-grid-cell-pointer">
      {filter && (
        <CFDynamicTable
          headers={tableHeader}
          tableHeightClassName="h-[693px]"
          url={billRoutes.list}
          params={{
            start: 0,
            limit: 20,
            search: search,
            filter: filter,
            order_by_name: "",
            order_by_dir: "",
            billLoadingStatus: billLoadingStatus,
          }}
          excludeEmptyParamerts={["order_by_name", "order_by_dir", "search"]}
          cacheBlockSize={20}
          maxBlocksInCache={20}
          rowSelection={"single"}
          ref={agGridRef}
          enableOpenInNewTab={window.ENV.ENABLE_ALL_CLICK}
          generateOpenInNewTabUrl={(data: TBill) =>
            `/manage-bills/${data.bill_id}`
          }
          clearDataOnNavigation={clearBilldetailObject}
          restrictOpenInNewTabFields={["is_billable"]}
        />
      )}
    </div>
  );
};

export default memo(BillsMainTable);
