import DateTimeIcon from "~/components/common/date-time-icon";
import { CFButton } from "~/components/third-party/ant-design/cf-button";
import {
  CFConfirmHeader,
  CFConfirmModal,
} from "~/components/third-party/ant-design/cf-confirm-modal";
import { CFDropdown } from "~/components/third-party/ant-design/cf-dropdown";
import {
  Number,
  getApiDefaultParams,
  replaceDOMParams,
  sanitizeString,
  Int,
} from "~/helpers/helper";
import { apiRoutes } from "~/route-services/routes";
import { useTranslation } from "~/hook";
import { getGModuleFilters } from "~/zustand/global-module-filter/store";
import { RefObject, memo, useEffect, useMemo, useRef, useState } from "react";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { getApiData } from "~/helpers/axios-api-helper";
import { useNavigate } from "@remix-run/react";
import { getGConfig, getPermitTab } from "~/zustand";
import { refreshPermitWizardData } from "~/zustand/pages/manage-permits/actions";
import ConfirmDeleteModal from "~/components/modals/confirm-delete-modal";
import { CFDynamicTable } from "~/components/third-party/ag-grid";
import { ColDef } from "ag-grid-community";
import { CFTableTooltip } from "~/components/third-party/ant-design/cf-table-tooltip";
import { permitRoutes } from "~/route-services/permit.routes";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { getGlobalUser } from "~/zustand/global/user/slice";

interface PermitsMainTableProps {
  search: string;
  setLoading: (val: boolean) => void;
  innerRef?: React.MutableRefObject<ExtendedAgGridReact | null>;
}

interface PermitsCellRenderer {
  data: Partial<TPermits>;
}

interface TPermitFilter {
  filter: Partial<ProjectPermitFilter> | undefined;
  search: string;
  tab: number | undefined | string;
}

const PermitsMainTable = ({
  search,
  innerRef,
  setLoading,
}: PermitsMainTableProps) => {
  const { _t } = useTranslation();
  const navigate = useNavigate();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const filter = getGModuleFilters() as
    | Partial<ProjectPermitFilter>
    | undefined;

  let permitIdAdded = false;

  const [confirmDialogOpen, setConfirmDialogOpen] = useState<boolean>(false);
  const [confirmArchiveDialogOpen, setConfirmArchiveDialogOpen] =
    useState<boolean>(false);
  const { module_access }: GConfig = getGConfig();
  const [deleteId, setDeleteId] = useState<number>();
  const [archiveItem, setArchiveItem] = useState<object>({});
  const [statusChange, setStatusChange] = useState<string | number>();

  const agGridRef = innerRef ?? useRef<ExtendedAgGridReact | null>(null);

  const permitTab: number | undefined = getPermitTab();
  let isFetching = false;
  const handleStatus = async (permitData: Partial<TPermits>) => {
    try {
      const response = (await webWorkerApi<ArchivePermit>({
        url: permitRoutes.archive,
        method: "post",
        data: {
          permitId: permitData?.permit_id,
          isDeleted: permitData.is_deleted === 0 ? 1 : 0,
          iframe_call: 0,
        },
      })) as ArchivePermit;
      if (response) {
        if (response.success) {
          agGridRef?.current?.refresh?.(); // for refresh ag grid
          const permitWizardResponse = await refreshPermitWizardData({
            type: "PermitsMainTable",
          });
          if (!permitWizardResponse.success) {
            notification.error({
              description: permitWizardResponse.message,
            });
          }
        } else {
          notification.error({
            description: response.message,
          });
        }
      }
    } catch (error: unknown) {
      notification.error({
        description: (error as Error)?.message,
      });
    }
  };

  const inputTagRef: RefObject<HTMLInputElement> = useRef(null);

  const [state, setState] = useState<TPermitFilter>({
    filter: {},
    search: "",
    tab: "",
  });

  useEffect(() => {
    if (inputTagRef.current) {
      inputTagRef.current?.focus();
    }
  }, [inputTagRef.current, confirmDialogOpen]);

  useEffect(() => {
    if (filter || search || permitTab)
      setState({ ...state, filter: filter, search: search, tab: permitTab });
  }, [permitTab, search, filter]);

  const handleDelete = () => {
    setConfirmDialogOpen(false);
    try {
      getApiData({
        url: apiRoutes.SERVICE.url,
        method: "post",
        data: getApiDefaultParams({
          op: "delete_project_permit",
          user,
          otherParams: {
            permit_id: deleteId,
            iframe_call: 0,
          },
        }),
        success: async (response: { message: string; success: string }) => {
          if (response?.success == "1") {
            agGridRef?.current?.refresh?.(); // for refresh ag grid
            const permitWizardResponse = await refreshPermitWizardData({
              type: "PermitsMainTable",
            });
            if (!permitWizardResponse.success) {
              notification.error({
                description: permitWizardResponse.message,
              });
            }
          } else {
            notification.error({
              description: response?.message,
            });
          }
        },
        error: (description) => {
          notification.error({
            description,
          });
        },
      });
    } catch (error: unknown) {
      notification.error({
        description: (error as Error)?.message,
      });
    }
  };

  const tableHeader: ColDef[] = useMemo(() => {
    return [
      {
        headerName: _t("Project"),
        minWidth: 100,
        flex: 2,
        field: "project_name",
        sortable: true,
        resizable: true,
        suppressMovable: false,
        suppressMenu: true,
        cellRenderer: ({
          data: { project_name = "" },
        }: PermitsCellRenderer) => {
          const projectName = replaceDOMParams(
            sanitizeString(project_name) ?? ""
          );
          return (
            <CFTableTooltip
              tooltipContent={projectName}
              children={projectName}
            />
          );
        },
      },
      {
        headerName: _t("Type"),
        minWidth: 150,
        flex: 2,
        field: "permit_type_name",
        sortable: true,
        resizable: true,
        suppressMovable: false,
        suppressMenu: true,
        cellRenderer: ({ data: { permit_type_name } }: PermitsCellRenderer) => {
          return (
            <CFTableTooltip
              tooltipContent={permit_type_name}
              children={permit_type_name}
            />
          );
        },
      },
      {
        headerName: _t("Permit") + " #",
        minWidth: 150,
        flex: 2,
        field: "permission",
        headerClass: "ag-cell-left",
        cellClass: "ag-left-aligned-cell",
        sortable: true,
        resizable: true,
        suppressMovable: false,
        suppressMenu: true,
        cellRenderer: ({ data: { permission } }: PermitsCellRenderer) => {
          return (
            <CFTableTooltip tooltipContent={permission} children={permission} />
          );
        },
      },
      {
        headerName: _t("Approved"),
        maxWidth: 150,
        minWidth: 130,
        field: "approval_date",
        headerClass: "ag-cell-left",
        cellClass: "ag-left-aligned-cell",
        sortable: true,
        cellRenderer: ({ data: { approval_date } }: PermitsCellRenderer) => {
          return (
            approval_date && (
              <div className="text-left">
                <DateTimeIcon format="date" date={approval_date} />
              </div>
            )
          );
        },
      },
      {
        headerName: _t("Expires"),
        maxWidth: 150,
        minWidth: 150,
        field: "expire_date",
        headerClass: "ag-cell-left",
        sortable: true,
        cellRenderer: ({ data: { expire_date } }: PermitsCellRenderer) => {
          return (
            expire_date && (
              <div className="text-left">
                <DateTimeIcon format="date" date={expire_date} />
              </div>
            )
          );
        },
      },
      {
        headerName: _t("Agency"),
        minWidth: 150,
        flex: 2,
        field: "agency_name",
        headerClass: "ag-cell-left",
        cellClass: "ag-left-aligned-cell",
        sortable: true,
        resizable: true,
        suppressMovable: false,
        suppressMenu: true,
        cellRenderer: ({ data: { agency_name } }: PermitsCellRenderer) => {
          return (
            <CFTableTooltip
              tooltipContent={agency_name}
              children={agency_name}
            />
          );
        },
      },
      {
        headerName: "",
        maxWidth: 46,
        minWidth: 46,
        field: "more",
        headerClass: "ag-header-right ",
        cellClass: "!cursor-auto",
        cellRenderer: ({ data }: PermitsCellRenderer) => {
          if (
            data &&
            (module_access === "full_access" ||
              module_access === "own_data_access")
          ) {
            const { is_deleted, permit_id } = data;
            const PermitViewTableOptions = [
              {
                label:
                  Int(is_deleted) == 0
                    ? _t("Archive This Item")
                    : _t("Activate This Item"),
                icon:
                  Int(is_deleted) == 0
                    ? "fa-regular fa-box-archive"
                    : "fa-regular fa-regular-active",
                content:
                  Int(is_deleted) == 0
                    ? _t("Archive This Item")
                    : _t("Change Status to Active"),
                onClick: () => {
                  setStatusChange(is_deleted);
                  setConfirmArchiveDialogOpen(true);
                  setArchiveItem(data);
                },
                onlyIconView: true,
              },
              {
                label: _t("Delete"),
                icon: "fa-regular fa-trash-can",
                content: "Delete",
                onClick: () => {
                  setConfirmDialogOpen(true);
                  setDeleteId(Number(permit_id));
                },
                disabled: user?.allow_delete_module_items === "0",
                onlyIconView: true,
              },
            ];
            return (
              <CFDropdown
                icon="fa-regular fa-ellipsis-vertical"
                options={PermitViewTableOptions}
                isScrollable={true}
                buttonClass="active-button hover:!bg-primary-900/10 !rounded"
              />
            );
          }
          return <></>;
        },
      },
    ];
  }, [module_access, user?.allow_delete_module_items]);

  return (
    <div className="md:h-[calc(100dvh-265px)] h-[calc(100dvh-270px)] ag-grid-cell-pointer">
      {useMemo(
        () =>
          filter && (
            <CFDynamicTable
              setLoading={setLoading}
              headers={tableHeader}
              tableHeightClassName=""
              url={permitRoutes.list}
              params={{
                start: 0,
                limit: 20,
                search: HTMLEntities.encode(sanitizeString(search)),
                filter,
                order_by_name: "",
                order_by_dir: "",
                is_expired: permitTab as number,
                status: permitTab as number,
              }}
              cacheBlockSize={20} // this attribute important when you get data on scroll
              maxBlocksInCache={20} // this attribute important when you get data on scroll
              rowSelection={"single"}
              ref={agGridRef}
              enableOpenInNewTab={window.ENV.ENABLE_ALL_CLICK}
              onCellClicked={(params: PermitsCellRenderer) => {
                if (!permitIdAdded) {
                  navigate(`${location?.pathname}/${params?.data?.permit_id}`);
                  permitIdAdded = true;
                }
              }}
            />
          ),
        [state, agGridRef]
      )}
      <ConfirmDeleteModal
        setConfirmDialogOpen={setConfirmDialogOpen}
        confirmDialogOpen={confirmDialogOpen}
        onClick={handleDelete}
      />

      <CFConfirmModal
        open={confirmArchiveDialogOpen}
        bodyClassName="flex text-center justify-center"
        message={_t(
          `Are you sure you want to ${
            statusChange == 1 ? "Activate" : "Archive"
          } this ${statusChange == 1 ? "data?" : "item?"} ${
            statusChange == 0
              ? "To view it or Activate it later, set the filter to show Archived items."
              : ""
          }`
        )}
        handelModal={() => setConfirmArchiveDialogOpen(false)}
        header={
          <CFConfirmHeader
            title={`${statusChange == 1 ? "Active" : "Archive"}`}
            icon={
              statusChange == 1 ? (
                <FontAwesomeIcon
                  className="w-3.5 h-3.5"
                  icon={"fa-regular fa-regular-active"}
                />
              ) : (
                <FontAwesomeIcon
                  className="w-3.5 h-3.5"
                  icon="fa-regular fa-box-archive"
                />
              )
            }
            handelModal={() => setConfirmArchiveDialogOpen(false)}
          />
        }
        footer={
          <>
            <CFButton
              variant="primary"
              className="justify-center min-w-[75px] primary-btn"
              onClick={() => {
                setConfirmArchiveDialogOpen(false);
                handleStatus(archiveItem);
              }}
            >
              {_t("Yes")}
            </CFButton>
            <CFButton
              variant="default"
              className="min-w-[75px] justify-center hover:!border-primary-900 hover:!text-primary-900 active:!border-primary-900 active:!text-primary-900"
              onClick={() => {
                setConfirmArchiveDialogOpen(false);
              }}
            >
              {_t("No")}
            </CFButton>
          </>
        }
      />
    </div>
  );
};

export default memo(PermitsMainTable);
