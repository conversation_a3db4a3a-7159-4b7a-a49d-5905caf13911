import React, { Suspense, lazy, useState, useEffect } from "react";
import { CFSkeletonInput } from "../ant-design/cf-skeleton";
import { CFSkeletonAvatar } from "../ant-design/cf-skeleton";
import isEmpty from "lodash/isEmpty";
import {
  CellClickedEvent,
  ColDef,
  ColGroupDef,
  GetContextMenuItemsParams,
  GridOptions,
} from "ag-grid-community";
import { AgGridReact } from "ag-grid-react";
import { handleRowNavigation } from "~/helpers/navigationHelper";
import { useNavigate } from "@remix-run/react";
import ReactDOMServer from "react-dom/server";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

const CFAgGrid = lazy(() => import("./cf-ag-grid.client"));

interface AgDragData {
  id: number;
  name: string;
  value: number;
  bill_item_no: string;
}

interface CFStaticTableProps<TData> extends CFAgGridProps {
  headers: (ColDef<TData> | ColGroupDef<TData>)[];
  onCellClicked?: (value: CellClickedEvent) => void;
  tableHeightClassName?: string;
  staticTableMainClass?: string;
  data?: TData[];
  onRowDragDropEnd?: (data: AgDragData[]) => void;
  enableOpenInNewTab?: number;
  generateOpenInNewTabUrl?: (data: TData) => string;
  clearDataOnNavigation?: () => void;
  restrictOpenInNewTabFields?: string[];
}

export function CFStaticTable<TData>(
  {
    data,
    headers,
    tableHeightClassName,
    staticTableMainClass,
    rowHeight = 34,
    onCellClicked = () => {},
    onRowDragDropEnd = () => {},
    enableOpenInNewTab = 0,
    generateOpenInNewTabUrl,
    clearDataOnNavigation,
    getContextMenuItems: customGetContextMenuItems,
    restrictOpenInNewTabFields = [],
    ...props
  }: CFStaticTableProps<TData>,
  ref: React.Ref<AgGridReact>
) {
  const [loading, setLoading] = useState<boolean>(isEmpty(data));

  useEffect(() => {
    !isEmpty(data) ? setLoading(false) : null;
  }, [data]);

  const renderSkeleton = () => {
    return data?.map((record: TData, recordIndex: number) => (
      <div className="flex items-center w-full" key={recordIndex}>
        {headers?.map((header: ColDef, key: number) => (
          <div
            className="py-[7px] px-2.5"
            style={{ minWidth: header?.minWidth, maxWidth: header?.maxWidth }}
            key={key}
          >
            {header?.type === "avatar" ? (
              <CFSkeletonAvatar />
            ) : (
              <CFSkeletonInput className="!w-full" />
            )}
          </div>
        ))}
      </div>
    ));
  };

  const gridOptions: GridOptions = {
    onRowDragEnd: function (event) {
      // ag grid community provide "RowNode" But not working that's why use any
      const { node, overIndex } = event as { node: any; overIndex: number };
      if (!gridOptions.api || !node) return;

      const rowData: AgDragData[] = [];
      gridOptions.api.forEachNode((node) => rowData.push(node.data));
      rowData.splice(overIndex, 0, rowData.splice(node.rowIndex, 1)[0]);
      onRowDragDropEnd(rowData);
    },
  };

  const navigate = useNavigate();
  const defaultGetContextMenuItems = (params: GetContextMenuItemsParams) => {
    const field = params?.column?.getColDef().field || "";
    const headerName = params?.column?.getColDef().headerName || "";
    const isNonClickableField =
      headerName === "" || restrictOpenInNewTabFields.includes(field);
    const defaultItems = customGetContextMenuItems
      ? customGetContextMenuItems(params)
      : params?.defaultItems || [];

    const data = params?.node?.data;

    const iconString = ReactDOMServer.renderToStaticMarkup(
      <FontAwesomeIcon icon="fa-regular fa-arrow-up-right-from-square" />
    );

    if (!isNonClickableField) {
      const openInNewTabItem =
        Number(window.ENV.ENABLE_ALL_CLICK) && generateOpenInNewTabUrl && data
          ? [
              {
                name: ReactDOMServer.renderToStaticMarkup(
                  <span className="cursor-pointer">Open in new tab</span>
                ),
                action: () => {
                  window.open(generateOpenInNewTabUrl(data), "_blank");
                  clearDataOnNavigation?.();
                },
                icon: iconString,
              },
            ]
          : [];

      // return [...openInNewTabItem, ...defaultItems];
      return [...openInNewTabItem];
    }
    return [];
  };

  return (
    <div className={`relative h-full ${staticTableMainClass}`}>
      <Suspense>
        {loading && renderSkeleton()}
        <CFAgGrid
          tableHeightClassName={tableHeightClassName}
          columnDefs={headers?.map((header: ColDef) => ({
            ...header,
            minWidth: isEmpty(data) ? undefined : header?.minWidth,
            maxWidth: isEmpty(data) ? undefined : header?.maxWidth,
          }))}
          rowData={data}
          onCellClicked={(params: CellClickedEvent) => {
            const field = params?.column?.getColDef().field || "";
            const headerName = params?.column?.getColDef().headerName || "";
            const isNonClickableField =
              headerName === "" || restrictOpenInNewTabFields.includes(field);
            if (
              generateOpenInNewTabUrl &&
              params.data &&
              params.column.getColDef().headerName &&
              !isNonClickableField
            ) {
              handleRowNavigation(
                params,
                params.data,
                generateOpenInNewTabUrl,
                clearDataOnNavigation,
                navigate
              );
            } else {
              const column = params.column;
              if (column && column.getColDef().headerName) {
                onCellClicked?.(params);
              }
            }
          }}
          onCellMouseDown={
            Number(window.ENV.ENABLE_ALL_CLICK) && generateOpenInNewTabUrl
              ? (params: any) => {
                  const field = params?.column?.getColDef().field || "";
                  const headerName =
                    params?.column?.getColDef().headerName || "";
                  const isNonClickableField =
                    headerName === "" ||
                    restrictOpenInNewTabFields.includes(field);
                  if (
                    !isNonClickableField &&
                    params?.event?.button === 1 &&
                    params.data
                  ) {
                    handleRowNavigation(
                      params.event,
                      params.data,
                      generateOpenInNewTabUrl,
                      clearDataOnNavigation
                    );
                  }
                }
              : undefined
          }
          getContextMenuItems={defaultGetContextMenuItems}
          rowHeight={rowHeight}
          gridOptions={gridOptions}
          headerHeight={32}
          onGridReady={() => {
            setLoading(false);
          }}
          ref={ref}
          {...props}
        />
      </Suspense>
    </div>
  );
}
