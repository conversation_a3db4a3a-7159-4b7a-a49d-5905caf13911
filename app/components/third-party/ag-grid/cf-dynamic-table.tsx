import React, {
  Suspense,
  lazy,
  memo,
  useEffect,
  useRef,
  useState,
} from "react";
import { CFSpin } from "../ant-design/cf-spin";
import { CFSkeletonAvatar } from "../ant-design/cf-skeleton";
import { CFSkeletonInput } from "../ant-design/cf-skeleton";
import {
  CellClickedEvent,
  CellMouseDownEvent,
  ColDef,
  GetContextMenuItemsParams,
  GridReadyEvent,
  SortChangedEvent,
} from "ag-grid-community";
import { IServerSideGetRowsParams } from "ag-grid-community";
import { ColGroupDef } from "ag-grid-community";
import { getApiData } from "~/helpers/axios-api-helper";
import { handleRowNavigation } from "~/helpers/navigationHelper";
import { icon } from "@fortawesome/fontawesome-svg-core";
import ReactDOMServer from "react-dom/server";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { useNavigate } from "@remix-run/react";

const CFAgGrid = lazy(() => import("./cf-ag-grid.client"));

interface CFDynamicTableProps<TData = any> extends CFAgGridProps {
  headers: (ColDef<TData, TData> | ColGroupDef<TData>)[];
  onCellClicked?: (value: CellClickedEvent) => void;
  url: string;
  params: ApiDefaultParams | ObjType;
  setLoading?: (val: boolean) => void;
  excludeEmptyParamerts?: string[];
  responseCleaner?: (response: TData) =>
    | {
        data: TData;
      }
    | {
        data: {
          data: TData;
        };
      };
  initialData?: TData[];
  onDataChange?: (
    data: TData[],
    listParams: ApiDefaultParams | ObjType
  ) => void;
  enableOpenInNewTab?: number;
  generateOpenInNewTabUrl?: (data: TData) => string;
  clearDataOnNavigation?: () => void;
  restrictOpenInNewTabFields?: string[];
}

let timeout: NodeJS.Timeout;

let isFetching = false;

const CFDynamicTable = React.forwardRef<
  ExtendedAgGridReact,
  CFDynamicTableProps
>(
  (
    {
      headers,
      params,
      url,
      setLoading,
      onCellClicked,
      excludeEmptyParamerts = [],
      responseCleaner,
      onDataChange,
      initialData,
      enableOpenInNewTab = 0,
      generateOpenInNewTabUrl,
      clearDataOnNavigation,
      getContextMenuItems: customGetContextMenuItems,
      restrictOpenInNewTabFields = [],
      ...props
    },
    ref
  ) => {
    const navigate = useNavigate();
    const initialLoad = useRef<boolean>(!initialData || !initialData.length);
    const [gridRowParams, setGridRowParams] = useState<GridParamsCus | null>(
      null
    );

    const defaultGetContextMenuItems = (params: GetContextMenuItemsParams) => {
      const field = params?.column?.getColDef().field || "";
      const isNonClickableField =
        field === "" || restrictOpenInNewTabFields.includes(field);
      const defaultItems = customGetContextMenuItems
        ? customGetContextMenuItems(params)
        : params?.defaultItems || [];

      const data = params?.node?.data;

      const iconString = ReactDOMServer.renderToStaticMarkup(
        <FontAwesomeIcon icon="fa-regular fa-arrow-up-right-from-square" />
      );

      if (!isNonClickableField) {
        const openInNewTabItem =
          Number(window.ENV.ENABLE_ALL_CLICK) && generateOpenInNewTabUrl && data
            ? [
                {
                  name: ReactDOMServer.renderToStaticMarkup(
                    <span className="cursor-pointer">Open in new tab</span>
                  ),
                  action: () => {
                    window.open(generateOpenInNewTabUrl(data), "_blank");
                    clearDataOnNavigation?.();
                  },
                  icon: iconString,
                },
              ]
            : [];

        // return [...openInNewTabItem, ...defaultItems];
        return [...openInNewTabItem];
      }
      return [];
    };

    const gridTableRef =
      (ref as
        | React.MutableRefObject<ExtendedAgGridReact<any> | null>
        | undefined) ?? useRef<ExtendedAgGridReact | null>(null);

    const fetch = () => {
      let gridData: { rowCount?: number; rowData: ObjType[] } = { rowData: [] };
      const { changeGridParams, gridParams } = gridRowParams ?? {};
      const length = changeGridParams?.length ?? 0;
      let data: ApiDefaultParams | ObjType = {
        ...params,
        length,
        order_by_name: changeGridParams?.order_by_name ?? "",
        order_by_dir: changeGridParams?.order_by_dir ?? "",
      };
      if ("page" in params) {
        data = {
          ...data,
          page: changeGridParams?.start ? changeGridParams?.start / length : 0,
        };
      } else {
        data = {
          ...data,
          start: changeGridParams?.start ?? 0,
        };
      }
      let filteredData = data;
      if (excludeEmptyParamerts && excludeEmptyParamerts.length > 0) {
        filteredData = Object.keys(data).reduce((acc, key) => {
          if (
            !excludeEmptyParamerts.includes(key as keyof typeof data) ||
            (data[key as keyof typeof data] !== undefined &&
              data[key as keyof typeof data] !== "")
          ) {
            acc[key] = data[key as keyof typeof data] as string;
          }

          return acc;
        }, {} as { [key: string]: string });
      }
      getApiData({
        url,
        method: "post",
        data: filteredData,
        success: (_response: { data: ObjType[] | { data: ObjType[] } }) => {
          const response = responseCleaner
            ? responseCleaner(_response)
            : _response;
          isFetching = true;
          if (response?.data) {
            const rowCount = gridParams?.api?.getDisplayedRowCount() ?? 0;
            if (Array.isArray(response?.data)) {
              if (response?.data?.length < length) {
                gridData = {
                  ...gridData,
                  rowCount: rowCount + (response?.data?.length ?? 0) - 1,
                };
              }
              gridData = { ...gridData, rowData: response?.data ?? [] };
            } else if (Array.isArray(response?.data?.data)) {
              if (response?.data?.data?.length < length) {
                gridData = {
                  ...gridData,
                  rowCount: rowCount + (response?.data?.data?.length ?? 0) - 1,
                };
              }
              gridData = {
                ...gridData,
                rowData: response?.data?.data ?? [],
              };
            } else {
              gridData = {
                ...gridData,
                rowCount: rowCount,
              };
            }

            if (
              ((Array.isArray(response?.data) &&
                response?.data?.length === 0) ||
                (!Array.isArray(response?.data) &&
                  Array.isArray(response?.data?.data) &&
                  response?.data?.data?.length === 0)) &&
              (gridData.rowCount == 0 || gridData?.rowData.length !== 0)
            ) {
              gridParams?.api?.showNoRowsOverlay?.();
            }
            gridParams?.success(gridData);
            onDataChange?.(gridData.rowData, filteredData);
          }
        },
        error: (description) => {
          notification.error({ description });
          gridParams?.fail();
        },
        callComplete: () => {
          setLoading?.(false);
          isFetching = false;
        },
      });
    };

    const refreshAgGrid = () => {
      const gridParams = gridRowParams?.gridParams;
      if (gridParams) {
        gridParams.api.setServerSideDatasource({ getRows: () => {} });
        gridParams.api.setServerSideDatasource(datasource);
      }
    };

    useEffect(() => {
      if (gridRowParams) {
        if (timeout) {
          clearTimeout(timeout);
        }
        timeout = setTimeout(() => {
          fetch();
        }, 500);
      }
    }, [gridRowParams]);

    useEffect(() => {
      if (gridRowParams && gridTableRef?.current) {
        gridTableRef.current.refresh = refreshAgGrid;
      }
    }, [gridRowParams, gridTableRef?.current]);

    const datasource = {
      async getRows(gridParams: IServerSideGetRowsParams) {
        if (!initialLoad.current && initialData && initialData.length) {
          initialLoad.current = true;
          gridParams.success({ rowData: initialData });
        } else {
          const request = gridParams?.request;
          let changeGridParams: ChangeGridParams = {
            start: request?.startRow ?? 0,
            length: (request?.endRow ?? 0) - (request?.startRow ?? 0),
            order_by_name: "",
            order_by_dir: "",
          };
          const sortModel = request?.sortModel;
          if (sortModel.length) {
            const { colId, sort } = sortModel[0];
            changeGridParams = {
              ...changeGridParams,
              order_by_name: colId,
              order_by_dir: sort,
            };
          }
          setGridRowParams({
            changeGridParams,
            gridParams,
          });
        }
      },
    };

    const onGridReady = (gridParams: GridReadyEvent) => {
      gridParams?.api?.setServerSideDatasource(datasource);
    };

    const onSortChanged = async (params: SortChangedEvent) => {
      params.api.setServerSideDatasource({ getRows: () => {} });
      params.api.setServerSideDatasource(datasource);
    };

    const gridOptions = {
      onSortChanged,
      suppressScrollOnNewData: true,
    };
    useEffect(() => {
      if (!isFetching) {
        refreshAgGrid();
      }
    }, [
      params.start,
      params.limit,
      params.search,
      params.filter,
      params.order_by_name,
      params.order_by_dir,
      params.status,
    ]);

    return (
      <Suspense fallback={<CFSpin className="h-screen" />}>
        <CFAgGrid
          onGridReady={onGridReady}
          gridOptions={gridOptions}
          rowModelType="serverSide"
          onCellClicked={(params: CellClickedEvent) => {
            const field = params?.column?.getColDef().field || "";
            const isNonClickableField =
              field === "" || restrictOpenInNewTabFields.includes(field);
            if (
              generateOpenInNewTabUrl &&
              params.data &&
              params.column.getColDef().headerName &&
              !isNonClickableField
            ) {
              handleRowNavigation(
                params,
                params.data,
                generateOpenInNewTabUrl,
                clearDataOnNavigation,
                navigate
              );
            } else {
              const column = params.column;
              if (column && column.getColDef().headerName) {
                onCellClicked?.(params);
              }
            }
          }}
          onCellMouseDown={
            Number(window.ENV.ENABLE_ALL_CLICK) && generateOpenInNewTabUrl
              ? (params: any) => {
                  const field = params?.column?.getColDef().field || "";
                  const isNonClickableField =
                    field === "" || restrictOpenInNewTabFields.includes(field);
                  if (
                    !isNonClickableField &&
                    params?.event?.button === 1 &&
                    params.data
                  ) {
                    handleRowNavigation(
                      params.event,
                      params.data,
                      generateOpenInNewTabUrl,
                      clearDataOnNavigation
                    );
                  }
                }
              : undefined
          }
          getContextMenuItems={defaultGetContextMenuItems}
          columnDefs={headers}
          rowHeight={34}
          headerHeight={32}
          loadingCellRenderer={Spin}
          loadingCellRendererParams={{
            cacheBlockSize: props?.cacheBlockSize ?? 1,
          }}
          ref={gridTableRef}
          {...props}
        />
      </Suspense>
    );
  }
);

interface SpinProps {
  rowIndex: number;
  cacheBlockSize: number;
  eGridCell: HTMLElement;
}

const Spin = ({ rowIndex, cacheBlockSize, eGridCell }: SpinProps) => {
  const getSkeleton = () => {
    let widths: SpinWidth[] = [];
    const headerChildrens =
      eGridCell?.parentElement?.parentElement?.parentElement?.parentElement?.parentElement?.getElementsByClassName(
        "ag-header-row ag-header-row-column"
      )?.[0]?.children;
    if (headerChildrens?.length) {
      for (let index = 1; index <= headerChildrens?.length; index++) {
        for (let element of headerChildrens) {
          if (element?.getAttribute("aria-colindex") === index?.toString()) {
            widths = [
              ...widths,
              {
                width: element?.getBoundingClientRect()?.width,
                type: Boolean(
                  element
                    .getAttribute("class")
                    ?.split(" ")
                    ?.find((calssName: string) => calssName === "avatar")
                )
                  ? "avatar"
                  : "input",
              },
            ];
            break;
          }
        }
      }
    }

    return widths?.map((item: SpinWidth, key: number) => {
      return (
        <div
          className="py-[7px] px-2.5"
          style={{
            minWidth: item?.width,
          }}
          key={key}
        >
          {item?.type === "avatar" ? (
            <CFSkeletonAvatar />
          ) : (
            <CFSkeletonInput className="!w-full" />
          )}
        </div>
      );
    });
  };
  return rowIndex ? (
    <div className="flex items-center w-full">{getSkeleton()}</div>
  ) : (
    Array(cacheBlockSize)
      .fill("")
      .map((value: string, key: number) => (
        <div className="w-full" key={key}>
          <div className="flex items-center w-full">{getSkeleton()}</div>
        </div>
      ))
  );
};

export default memo(CFDynamicTable);
