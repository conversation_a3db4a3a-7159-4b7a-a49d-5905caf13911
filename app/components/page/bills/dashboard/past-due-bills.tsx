import { useMemo, useState } from "react";
// Hook
import { useTranslation } from "~/hook";
// molecules
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import DateTimeIcon from "~/components/common/date-time-icon";
import { CFStaticTable } from "~/components/third-party/ag-grid";
import { CFSpin } from "~/components/third-party/ant-design/cf-spin";
import {
  getPastDueBillsData,
  isBillsWizardDataLoading,
} from "~/zustand/pages/manage-bills/store";
import { getGConfig } from "~/zustand";
import {
  clearBilldetailObject,
  setManageBills,
} from "~/zustand/pages/manage-bills/actions";
import { useNavigate } from "@remix-run/react";
import { CFTableTooltip } from "~/components/third-party/ant-design/cf-table-tooltip";
import { formatAmount, replaceDOMParams } from "~/helpers/helper";

interface BillCellRenderer {
  data: Partial<TBill>;
}

const headerRenderer = (params: any) => {
  return <DateTimeIcon format="date" date={params?.data?.due_date} />;
};

const PastDueBills = () => {
  const { _t } = useTranslation();
  const isDashboardLoading: boolean = isBillsWizardDataLoading();

  const [isCashLoading, setIsCashLoading] = useState<boolean>(false);
  const { pastDueBills, billPastDueLastRefreshTime } = getPastDueBillsData();
  const { formatter } = useCurrencyFormatter();
  const gConfig: GConfig = getGConfig();
  const navigate = useNavigate();

  const handleRefreshClick = async () => {
    setIsCashLoading(true);
    await setManageBills({
      refresh_type: "bill_past_due",
    });
    setIsCashLoading(false);
  };
  const headers = useMemo(
    () => [
      {
        headerName: _t("Due"),
        maxWidth: 60,
        minWidth: 60,
        field: "due_date",
      },
      {
        headerName: _t("Vendor"),
        minWidth: 80,
        flex: 1,
        field: "supplier_company_name",
      },
      {
        headerName: _t("Bill #"),
        minWidth: 60,
        flex: 1,
        field: "bill",
      },
      {
        headerName: _t("Amount"),
        maxWidth: 80,
        minWidth: 80,
        field: "total",
        cellClass: "ag-right-aligned-cell",
        headerClass: "ag-header-right",
      },
    ],
    []
  );
  return (
    <>
      <DashboardCardHeader
        title={`Past Due ${gConfig?.module_name}`}
        isRefreshing={isCashLoading}
        showRefreshIcon={true}
        refreshIconTooltip={billPastDueLastRefreshTime}
        onClickRefresh={handleRefreshClick}
      />
      <div className="py-2 px-2.5">
        {!pastDueBills || isDashboardLoading || isCashLoading ? (
          <CFStaticTable
            className="ag-grid-cell-pointer"
            tableHeightClassName="!h-[209px]"
            headers={headers}
            loadingOverlayComponent={() => <CFSpin className="h-[190px]" />}
            noRowsOverlayComponent={() =>
              !pastDueBills || isDashboardLoading || isCashLoading ? (
                <CFSpin className="h-[190px]" />
              ) : (
                ""
              )
            }
          />
        ) : (
          <CFStaticTable
            className="ag-grid-cell-pointer"
            headers={[
              {
                headerName: _t("Due"),
                maxWidth: 135,
                minWidth: 135,
                field: "due_date",
                cellRenderer: headerRenderer,
              },
              {
                headerName: _t("Vendor"),
                minWidth: 130,
                flex: 1,
                field: "supplier_company_name",
                cellRenderer: (params: any) => {
                  const companyName = replaceDOMParams(
                    params?.data?.supplier_company_name?.replaceAll(
                      /\\|"/g,
                      ""
                    ) ?? ""
                  );
                  return (
                    <CFTableTooltip
                      tooltipContent={companyName}
                      children={companyName}
                    />
                  );
                },
              },
              {
                headerName: _t("Bill #"),
                minWidth: 130,
                flex: 1,
                field: "bill",
                cellRenderer: (params: any) => {
                  return (
                    <CFTableTooltip
                      tooltipContent={params?.data?.bill}
                      children={params?.data?.bill}
                    />
                  );
                },
              },
              {
                headerName: _t("Amount"),
                maxWidth: 120,
                minWidth: 120,
                field: "total",
                cellClass: "ag-right-aligned-cell",
                headerClass: "ag-header-right",
                cellRenderer: (params: any) => {
                  const amount = formatter(
                    formatAmount(Number(params?.data?.total), {
                      isDashboard: true,
                    })
                  ).value_with_symbol;
                  return (
                    <CFTableTooltip
                      tooltipContent={amount}
                      children={amount}
                      mainClassName="justify-end"
                    />
                  );
                },
              },
            ]}
            enableOpenInNewTab={window.ENV.ENABLE_ALL_CLICK}
            generateOpenInNewTabUrl={(data: TBill) =>
              `/manage-bills/${data.bill_id}`
            }
            clearDataOnNavigation={clearBilldetailObject}
            tableHeightClassName="!h-[209px]"
            data={(pastDueBills as unknown as any[]) ?? []} // change this in future ( temporary resolve type issue )
          />
        )}
      </div>
    </>
  );
};

export default PastDueBills;
