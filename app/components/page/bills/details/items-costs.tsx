import { faSackDollar } from "@fortawesome/pro-solid-svg-icons/faSackDollar";
import GradientIconCard from "~/components/common/gradient-icon-card";
import { useParams } from "@remix-run/react";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { useEffect, useRef, useState, useMemo } from "react";
import { CFStaticTable } from "~/components/third-party/ag-grid";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
import { useTranslation } from "~/hook";
import {
  getBillsDetails,
  getFilteritems,
} from "~/zustand/pages/manage-bills/store";
import AddBillItem from "~/components/sidebars/add-bill-item";
import ImportPurchaseOrderItems from "~/components/sidebars/import-purchase-order-items";
import ImportSubContractItems from "~/components/sidebars/import-sub-contract-items";
import ImportExpense from "~/components/sidebars/import-expense";
import LumpSumTotal from "~/components/sidebars/lump-sum-total";
import BillDiscountItemModal from "~/components/modals/bill-discount-item";
import BillTaxItemModal from "~/components/modals/bill-tax-item";
import BillCreditItem from "~/components/sidebars/bill-credit-item";
import BillRetainagePaymentModal from "~/components/modals/bill-retainage-payment";
import BillFreightChargeItemModal from "~/components/modals/bill-freight-charge-item";
import { CFButton } from "~/components/third-party/ant-design/cf-button";
import {
  CFConfirmModal,
  CFConfirmHeader,
} from "~/components/third-party/ant-design/cf-confirm-modal";
import { CFDropdown } from "~/components/third-party/ant-design/cf-dropdown";
import { CFIconButton } from "~/components/third-party/ant-design/cf-iconbutton";
import { CFSpin } from "~/components/third-party/ant-design/cf-spin";
import { CFTooltip } from "~/components/third-party/ant-design/cf-tooltip";
import { CFTypography } from "~/components/third-party/ant-design/cf-typography";
import {
  setAddBillItems,
  setBillItemsEdit,
  setMultipleBillItemsEdit,
  setDeleteItem,
  setManageBillsDetails,
  tableAllUpdatedData,
  updateItemWithoutData,
  setBillItemsEditDataset,
  filterBilldetailItems,
  setItemCalculations,
} from "~/zustand/pages/manage-bills/actions";
import {
  setBillRetainageHeldInfo,
  setExpenceItems,
  setPurchaseOrder,
  setSubContracts,
} from "~/zustand/pages/manage-bills/addBillDropDown/actions";
import { ColDef, GridApi, GridReadyEvent } from "ag-grid-community";
import debounce from "lodash/debounce";
import isEmpty from "lodash/isEmpty";
import isEqual from "lodash/isEqual";
import {
  getGConfig,
  getIsLoading,
  getGSettings,
  useGModules,
  getGTypes,
} from "~/zustand";
import {
  Float,
  formatAmount,
  Int,
  Number,
  replaceDOMParams,
  sanitizeString,
} from "~/helpers/helper";
import { costDataBase, defaultConfig } from "~/data";
import { CheckboxChangeEvent } from "antd/es/checkbox";
import { AnimateError } from "~/components/common/animate/animate-error";
import { CFTableIcon } from "~/components/third-party/ant-design/cf-table-icon";
import { CFTableTooltip } from "~/components/third-party/ant-design/cf-table-tooltip";
import { getLumpSumTotal } from "~/zustand/pages/manage-bills/addBillDropDown/store";
import { ICellEditor, ICellEditorParams } from "ag-grid-community";
import { resetItemFilter } from "~/components/sidebars/multi-select/items/zustand/action";
import { CidbItemDrawer } from "~/shared/components/molecules/cidbItemDrawer";
import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";
import { apiRoutes } from "~/route-services/routes";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import { qtyNumberCheck } from "~/shared/utils/helper/common";
import { floatWithNegativeRegex } from "~/modules/financials/pages/changeOrder/utils/helpers";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { ModuleItemsFilter } from "~/shared/components/molecules/moduleItemsFilter";
import { NoRecords } from "~/shared/components/molecules/noRecords";

type RouteParams = {
  id: string;
  type?: string;
};

interface BillsItemCostsCardCellRenderer {
  data?: any;
}

type IRefFields =
  | "material_id"
  | "labor_id"
  | "contractor_id"
  | "equipment_id"
  | "other_item_id";

const itemKeyValueMap: {
  [key: string]: {
    type: string;
    name: string;
    field: IRefFields;
  };
} = {
  item_material: {
    type: "161",
    name: "Material",
    field: "material_id",
  },
  item_labour: {
    type: "163",
    name: "Labor",
    field: "labor_id",
  },
  item_sub_contractor: {
    type: "164",
    name: "Subcontractor",
    field: "contractor_id",
  },
  item_equipment: {
    type: "162",
    name: "Equipment",
    field: "equipment_id",
  },
  item_other: {
    type: "165",
    name: "Other Items",
    field: "other_item_id",
  },
};

const BillsItemCostsCard = () => {
  const { _t } = useTranslation();
  const Billparams: Partial<RouteParams> = useParams();
  const costCodeData: CostCodeData[] = getLumpSumTotal();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const filteritems: IFilteredData[] = getFilteritems();

  const { allow_delete_module_items = "0" } = user || {};
  const gSettings: GSettings = getGSettings();
  const gConfig: GConfig = getGConfig();
  const [itemTypes, setItemTypes] = useState<IItemTypes[]>([]);
  const [open, setOpen] = useState<boolean>(false);
  const [selectItem, setSelectItem] = useState<Partial<ItemSideData>>();
  const [isView, setIsView] = useState<boolean>(false);
  const [editForm, setEditform] = useState<boolean>(false);
  const [filter, setFilter] = useState<ISelectItemFilter["key"][]>([]);
  const [openConfirmModal, setOpenConfirmModal] = useState<boolean>(false);
  const [itemBox, setItemBox] = useState<boolean>(false);
  const [selectedValue, setSelectedValue] = useState<string>();
  const billDetailsSc: BillDetails = getBillsDetails();

  const [billData, setBillData] = useState<Partial<ItemSideData>[]>([]);
  const { formatter } = useCurrencyFormatter();

  const gridApiRef = useRef<GridApi | null>(null);
  const loading = getIsLoading();
  const [filteredItems, setFilteredItems] = useState<Partial<ItemSideData>[]>(
    []
  );
  const [totalSum, setTotalSum] = useState(0);
  const itemType: GType[] = getGTypes();
  const { getGModule } = useGModules();
  const [debouncedFilter, setDebouncedFilter] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [lumpsumTableFieldEdited, setLumpsumTableFieldEdited] = useState(false);
  const [addItemBill, setAddItemBill] = useState<AddItem[]>([
    {
      label: `Add Item to ${gConfig?.module_singular_name}`,
      value: "add_item_to_bill",
      disabled: true,
    },
    {
      label: "Import from Cost Items Database",
      value: "Database",
    },
    {
      label: "Import from Purchase Order",
      value: "purchase_order",
      module_id: defaultConfig.purchase_order_module_id,
    },
    {
      label: "Import from Sub-Contract",
      value: "subContract",
      module_id: defaultConfig.sub_contract_module_id,
    },
    {
      label: "Import from Expense",
      value: "expense",
      module_id: defaultConfig.expense_module_id,
    },
    {
      label: "Lump Sum Total",
      value: "new_lum_sum_item",
    },
    {
      label: `Add Manual ${gConfig?.module_singular_name} Item`,
      value: "new_item",
    },
    {
      label: "Add Discount",
      value: "discount_item",
    },
    {
      label: "Freight Charge",
      value: "freight_charge",
    },
    {
      label: "Credit",
      value: "credit_item",
    },
    {
      label: "Add Retainage (Partial or Full) for Payment",
      value: "retainage_payment",
    },
  ]);

  const {
    total,
    items,
    project_id,
    bill_payment,
    quickbook_bill_id,
    allow_overbilling,
    due_balance,
  }: BillDetails = getBillsDetails();

  const billDetails: BillDetails = useMemo(
    () => ({ project_id, total, items, bill_payment, quickbook_bill_id }),
    [items, bill_payment]
  );

  const detaisRef = useRef<BillDetails>(billDetails);
  const tableAllData: Partial<ItemSideData>[] = billDetails?.items;

  useEffect(() => {
    const updatedAddItemBill = addItemBill.map((item) => {
      if (item?.module_id !== undefined) {
        const module = getGModule(item?.module_id);
        if (module) {
          return {
            ...item,
            label: `Import from ${module.module_name}`,
          };
        }
      }

      return item;
    });

    setAddItemBill(updatedAddItemBill);
  }, []);

  useEffect(() => {
    if (filteritems.length) {
      const totalSum = filteritems.reduce(
        (sum: number, item: IFilteredData) => sum + (Number(item.total) || 0),
        0
      );
      setTotalSum(totalSum);
    } else {
      setTotalSum(0);
    }
  }, [filteritems, totalSum]);

  useEffect(() => {
    if (
      billDetails?.quickbook_bill_id &&
      gSettings?.qb_country === "us" &&
      gSettings?.quickbook_sync === "1"
    ) {
      addItemBill.push({
        label: "Tax Total",
        value: "tax_total",
      });
    }
  }, [billDetails?.quickbook_bill_id, gSettings]);

  const costItemsFromDatabase = useMemo(() => {
    const data = items
      ?.filter((item: IBillDetailsItem) => Number(item?.reference_item_id) > 0)
      ?.map((item: IBillDetailsItem) => {
        let temData: Partial<CIDBItemSideData> = {
          name: item.subject || "",
          reference_item_id: item?.reference_item_id,
          item_id: String(item?.item_id),
          reference_module_item_id: item.reference_module_item_id,
        };

        const key = item?.item_type_key as keyof typeof itemKeyValueMap;
        const temDataKey = itemKeyValueMap[key]?.field;
        if (item && temDataKey) {
          temData[temDataKey] = Number(item.reference_item_id);
          temData.item_type = itemKeyValueMap[key]?.type;
          temData.type_name = itemKeyValueMap[key]?.name;
        }
        return temData;
      })
      ?.reduce(
        (
          acc: { reference_item_id: number }[],
          currentItem: { reference_item_id: number }
        ) => {
          const exists = acc.some(
            (item: { reference_item_id: number }) =>
              item.reference_item_id === currentItem.reference_item_id
          );
          if (!exists) {
            acc.push(currentItem);
          }
          return acc;
        },
        [] as Partial<CIDBItemSideData>[]
      );

    return data;
  }, [items]);

  useEffect(() => {
    const fetchCompanyItems = async () => {
      if (billDetails?.project_id) {
        try {
          const data = await getWebWorkerApiParams({
            otherParams: billDetails?.project_id,
          });

          const response = (await webWorkerApi({
            url: apiRoutes.COMMON.get_company_items,
            method: "post",
            data: data,
          })) as IStatusRes;
          setItemTypes(response?.data as unknown as IItemTypes[]);
        } catch (error) {
          console.error(error);
        }
      }
    };

    fetchCompanyItems();
  }, [billDetails?.project_id]);

  const isProjectExist =
    billDetails &&
    billDetails.project_id &&
    billDetails.project_id.toString() !== "0" &&
    billDetails.project_id !== "";

  useEffect(() => {
    if (loading) {
      setFilter([]);
    }
  }, [loading]);

  /// this useEffect called only once when page load (not need isLoading dependency in this useEffect)
  useEffect(() => {
    if (billDetails && billDetails.items && !isLoading) {
      filterData(filter);
    }
    if (billDetails && billDetails.items && isLoading) {
      setIsLoading(false);
    }
    if (Object.keys(billDetails)?.length > 0) {
      detaisRef.current = billDetails;
    }
  }, [billDetails]);

  const filteredItemsType = itemType?.filter(
    (item: Partial<GType>) => item?.type === "company_items"
  );

  const handleSelectChange = (selectedItem: string) => {
    const state = detaisRef?.current;
    if (selectedItem) {
      if (
        selectedItem === "purchase_order" ||
        selectedItem === "subContract" ||
        selectedItem === "expense"
      ) {
        if (isProjectExist) {
          setSelectedValue(selectedItem);
          switch (selectedItem) {
            case "purchase_order":
              setPurchaseOrder(state?.project_id);
              break;
            case "subContract":
              setSubContracts([Number(state?.project_id)], 1);
              break;
            default:
              setExpenceItems(state?.project_id);
              break;
          }
        } else {
          setSelectedValue(addItemBill[0]?.value);
          notification.error({
            description: "Please select project.",
          });
        }
      } else {
        if (selectedItem === "retainage_payment") {
          setBillRetainageHeldInfo(Billparams?.id ?? "", () => {});
        }
        setSelectedValue(selectedItem);
      }
      setItemBox(true);
    }
  };
  const filterData = (filterString: string[]) => {
    tableAllData?.sort((itemA, itemB) => {
      const billItemNoA = itemA.bill_item_no;
      const billItemNoB = itemB.bill_item_no;

      if (billItemNoA === undefined && billItemNoB === undefined) {
        return 0;
      } else if (billItemNoA === undefined) {
        return 1;
      } else if (billItemNoB === undefined) {
        return -1;
      } else {
        const billItemNoAInt = parseInt(billItemNoA);
        const billItemNoBInt = parseInt(billItemNoB);
        return billItemNoAInt - billItemNoBInt;
      }
    });

    // if (tableAllData && tableAllData.length > 0) {
    if (filterString && filterString.length > 0) {
      const filteredData = tableAllData.filter(
        (item: Partial<ItemSideData>) => {
          return (
            item?.item_type_key !== undefined &&
            filterString.includes(item?.item_type_key?.toString())
          );
        }
      );
      setFilteredItems(
        filteredData?.filter(
          (data: Partial<ItemSideData>) =>
            data.is_retainage_item?.toString() !== "1"
        )
      );
    } else {
      setFilteredItems(
        tableAllData?.filter(
          (data: Partial<ItemSideData>) =>
            data.is_retainage_item?.toString() !== "1"
        )
      );
    }
    // }
    setIsLoading(false);
  };

  const handleFilterChange = debounce((value: string[]) => {
    setDebouncedFilter(value);
  }, 300);

  useEffect(() => {
    setIsLoading(true);
    handleFilterChange(filter);
    setLumpsumTableFieldEdited(false);
    return handleFilterChange.cancel;
  }, [filter, lumpsumTableFieldEdited]);

  useEffect(() => {
    filterData(debouncedFilter);
  }, [debouncedFilter]);

  useEffect(() => {
    tableAllUpdatedData(filteredItems);
  }, [filteredItems]);

  const onCellValueChanged = (event: any) => {
    const { data, colDef, value, oldValue, node } = event;
    if (
      colDef.field === "sc_paid_bill_amt" ||
      colDef.field === "sc_paid_bill_percentage" ||
      data.sc_paid_bill_amt !== oldValue ||
      data.sc_paid_bill_percentage !== oldValue
    ) {
      node.setDataValue(
        "total_sc_paid_bill_percentage",
        data.total_sc_paid_bill_percentage
      );
      node.setDataValue("total_sc_paid_bill_amt", data.total_sc_paid_bill_amt);
    }
    // as per the this bug https://app.clickup.com/t/86cxfkb60 i have comment this if needed in future
    // setLumpsumTableFieldEdited(true);
  };

  const handleDelete = async (
    data: string | undefined = selectItem?.item_id
  ) => {
    if (data) {
      try {
        await setDeleteItem({
          billId: Billparams?.id || "",
          selectItem: data,
          setOpenConfirmModal,
          onError: () => {
            setManageBillsDetails({
              id: Billparams?.id || "",
            });
          },
        });
      } catch (error) {
        notification.error({
          description: "something went wrong!",
        });
      }
    }
  };

  const onGridReady = (params: GridReadyEvent) => {
    gridApiRef.current = params.api as GridApi;
  };

  useEffect(() => {
    if (addItemBill && addItemBill?.length > 0 && selectedValue === undefined) {
      setSelectedValue(addItemBill[0]?.value);
    }
  }, [addItemBill, selectedValue]);

  const data = useMemo(() => {
    return filter.length
      ? tableAllData?.filter((data: Partial<ItemSideData>) =>
          filter?.includes(data?.item_type ?? "")
        )
      : tableAllData?.filter(
          (data: Partial<ItemSideData>) =>
            data?.is_retainage_item?.toString() !== "1"
        );
  }, [filter.length, tableAllData]);

  const itemData: Partial<ItemSideData>[] = useMemo(() => {
    return data?.length
      ? data
          ?.filter(
            (item: Partial<ItemSideData>) => Number(item?.reference_item_id) > 0
          )
          ?.map((item: Partial<ItemSideData>) => {
            let temData = item as Partial<ItemSideData>;
            if (item?.item_type_key === "item_material") {
              temData.material_id = item?.reference_item_id;
              temData.item_type = "161";
            } else if (item?.item_type_key === "item_labour") {
              temData.labor_id = item?.reference_item_id;
              temData.item_type = "163";
            } else if (item?.item_type_key === "item_sub_contractor") {
              temData.contractor_id = item?.reference_item_id;
              temData.item_type = "164";
            } else if (item?.item_type_key === "item_equipment") {
              temData.equipment_id = item?.reference_item_id;
              temData.item_type = "162";
            } else if (item?.item_type_key === "item_other") {
              temData.other_item_id = item?.reference_item_id;
              temData.item_type = "165";
            }
            temData.name = item?.subject;
            return temData;
          })
      : data;
  }, [data]);

  const closeModalHandler = () => {
    setItemBox(false);
    setSelectedValue(addItemBill?.length > 0 ? addItemBill[0]?.value : "");
  };

  useEffect(() => {
    if (!isEqual(billData, data)) setBillData(data);
  }, [data]);

  const uniCostDropdownHight =
    billDetails?.items?.length === 1
      ? 60
      : billDetails?.items?.length === 2
      ? 90
      : billDetails?.items?.length === 3
      ? 120
      : billDetails?.items?.length === 4
      ? 150
      : 180;

  class DecimalLimitedEditor implements ICellEditor {
    private eInput: HTMLInputElement;

    constructor() {
      this.eInput = document.createElement("input");
      this.eInput.type = "text";
      this.eInput.classList.add("ag-input-field-style");
    }

    init(params: ICellEditorParams) {
      this.eInput.value = params.value ? params.value.toString() : "";
      this.eInput.addEventListener("input", () => this.validateInput());
    }

    private validateInput() {
      const value = this.eInput.value;

      const regex = /^(\d{0,10})(\.\d{0,2})?$/;
      if (regex.test(value)) {
        this.eInput.value = value;
      } else {
        this.eInput.value = this.eInput.value.slice(0, -1);
      }
    }

    getGui() {
      return this.eInput;
    }

    afterGuiAttached() {
      this.eInput.focus();
      this.eInput.select();
    }

    getValue() {
      return this.eInput.value;
    }

    setValue(value: string | number) {
      this.eInput.value = value ? value.toString() : "";
    }

    destroy() {
      this.eInput.removeEventListener("input", () => this.validateInput());
    }
  }

  const costCodeWithOption = (params: {
    data: {
      cost_code?: string;
      cost_code_id: string;
      cost_code_name?: string;
    };
  }) => {
    const costCodesData = costCodeData.find(
      (ele) => ele.code_id?.toString() === params.data.cost_code_id?.toString()
    );

    // Use cost_code if cost_code_name is null or empty
    const costCodeValue =
      params.data.cost_code_name || params.data.cost_code || "";

    // Add CSI code or Archived information if available
    const additionalInfo = costCodesData
      ? costCodesData.csi_code
        ? ` (${costCodesData.csi_code})`
        : costCodesData.archive
        ? " (Archived)"
        : ""
      : " (Archived)";

    return costCodeValue ? `${costCodeValue}${additionalInfo}` : "";
  };
  const itemCalculation = (response: {} | undefined) => {
    if (
      response &&
      typeof response === "object" &&
      "updateBillTotalValue" in response &&
      "updatedItemdata" in response
    ) {
      const typedResponse = response as {
        updateBillTotalValue: Partial<BillDetails>;
        updatedItemdata: Partial<ItemSideData>[];
      };
      const data = {
        ...billDetailsSc,
        ...typedResponse.updateBillTotalValue,
        items: typedResponse.updatedItemdata[0],
      };
      setItemCalculations(data);
    } else {
      console.warn("Unexpected response format:", response);
    }
  };
  const icons: Record<IconKey, any> = {
    item_material: "fa-regular fa-block-brick",
    item_labour: "fa-regular fa-user-helmet-safety",
    item_sub_contractor: "fa-regular fa-file-signature",
    item_equipment: "fa-regular fa-screwdriver-wrench",
    item_other: "fa-regular fa-boxes-stacked",
  };

  const suppressKeyboardEvent: ColDef<
    CostItemTableCellRenderer["data"]
  >["suppressKeyboardEvent"] = (params) => {
    const { event, api } = params;

    if (event.key === "ArrowUp" || event.key === "ArrowDown") {
      event.preventDefault();
      event.stopPropagation();
      return true;
    }

    if (event.key === "Enter") {
      event.preventDefault();
      event.stopPropagation();

      // Call valueSetter or custom logic for saving the value
      api.stopEditing(); // Stop editing to trigger valueSetter
      return true;
    }

    return true;
  };

  const headers: ColDef[] = useMemo(
    () => [
      {
        headerName: "",
        minWidth: 30,
        maxWidth: 30,
        rowDrag: true,
        field: "move",
        cellClass: "ag-cell-center ag-move-cell",
        cellRenderer: () => {
          return (
            <CFTooltip content={"Move"} placement="top">
              <CFIconButton
                htmlType="button"
                className={`w-6 group/move max-w-[24px] max-h-[24px]`}
                variant="text"
              >
                <FontAwesomeIcon
                  className="text-base w-4 h-4 text-primary-900/80 dark:text-white/90 group-hover/move:text-primary-900 group-hover/move:dark:text-white/90"
                  icon="fa-regular fa-bars"
                />
              </CFIconButton>
            </CFTooltip>
          );
        },
      },
      {
        headerName: _t("Type"),
        minWidth: 50,
        maxWidth: 50,
        field: "item_type_display_name",
        headerClass: "ag-header-center",
        cellClass: "ag-cell-center",
        suppressMovable: false,
        suppressMenu: true,
        cellRenderer: (params: CostItemTableCellRenderer) => {
          params.data.item_type =
            typeof params.data.item_type === "number"
              ? String(params.data.item_type)
              : params.data.item_type;
          const item = filteredItemsType?.find(
            (ele) => ele.type_id === params?.data.item_type
          );
          const itemTypeKey: IconKey = params.data.item_type_key as IconKey;
          const itemTypeIcon = icons[itemTypeKey];

          return (
            item && (
              <div className="text-right">
                <CFTooltip content={item?.name} placement="top">
                  <CFIconButton
                    htmlType="button"
                    className={`w-6 group/block max-w-[24px] max-h-[24px] mx-auto cursor-auto`}
                    variant="text"
                  >
                    <FontAwesomeIcon
                      className="text-base w-4 h-4 text-primary-900/80 dark:text-white/90 group-hover/block:text-primary-900 group-hover/block:dark:text-white/90"
                      icon={itemTypeIcon}
                    />
                  </CFIconButton>
                </CFTooltip>
              </div>
            )
          );
        },
      },
      {
        headerName: _t("Item Name"),
        minWidth: 150,
        field: "subject",
        flex: 2,
        resizable: true,
        suppressMovable: false,
        suppressMenu: true,
        editable: () => (gConfig?.module_read_only ? false : true),
        cellRenderer: (params: BillsItemCostsCardCellRenderer) => {
          return (
            <CFTableTooltip
              tooltipContent={replaceDOMParams(
                sanitizeString(params?.data?.subject)
              )}
              children={replaceDOMParams(sanitizeString(params?.data?.subject))}
            />
          );
        },
        valueGetter: (params) => {
          const itemName = replaceDOMParams(
            sanitizeString(params?.data?.subject)
          );
          return itemName;
        },
        valueSetter: (params: {
          data: IWorkorderDetailsItem;
          newValue: string;
        }) => {
          if (params.newValue && params.newValue !== params.data.subject) {
            params.data.subject = replaceDOMParams(
              sanitizeString(params.newValue)
            );
            setBillItemsEdit({
              id: Billparams?.id ?? "",
              moduleId: gConfig?.module_id,
              items: { ...params.data },
              callComplete: () => {
                setManageBillsDetails({
                  id: Billparams?.id || "",
                });
              },
            });
            return true;
          }
          return false;
        },
      },
      {
        headerName: _t("Cost Code "),
        minWidth: 180,
        field: "cost_code_name",
        cellEditor: "agRichSelectCellEditor",
        flex: 2,
        resizable: true,
        suppressMovable: false,

        suppressMenu: true,
        valueGetter: (params: {
          data: {
            cost_code: string;
            cost_code_id: string;
            cost_code_name: string;
          };
        }) => {
          if (
            (gConfig && !gConfig.module_read_only) ||
            Boolean(Number(due_balance ?? 0))
          ) {
            const costCodeValue = costCodeWithOption(params);
            return costCodeValue;
          }
        },
        editable: (params: CostItemTableCellRenderer) =>
          gConfig?.module_read_only
            ? // will remove after testing
              //  || (Number(due_balance ?? 0) <= 0 && Number(total ?? 0) > 0)
              false
            : params?.data?.item_category === "item",
        cellEditorParams: {
          values: costCodeData?.map(
            (item: CostCodeData) =>
              `${item?.cost_code_name}` +
              `${item?.csi_code ? ` (${item?.csi_code})` : ""}`
          ),
          filterList: true,
          searchType: "matchAny",
          allowTyping: true,
          valueListMaxHeight: uniCostDropdownHight,
        },
        valueSetter: (params: {
          data: Partial<ItemSideData>;
          newValue: string;
        }) => {
          if (params?.newValue !== undefined) {
            const newCostCodeName = params.newValue.split("(")[0].trim();
            const items: Partial<ItemSideData> = {
              ...params.data,
              cost_code_id:
                costCodeData.find(
                  (item) => item.cost_code_name === newCostCodeName
                )?.code_id || "",
              cost_code_name: newCostCodeName,
              unit_cost: (Number(params.data?.unit_cost) * 100).toFixed(2),
              sc_paid_bill_amt: Number(params.data?.sc_paid_bill_amt) / 100,
              total: Number(params.data?.total || 0 / 100),
            };

            const editItem = {
              ...items,
              unit_cost: (Number(items?.unit_cost) / 100).toFixed(2),
              sc_paid_bill_amt: Number(items?.sc_paid_bill_amt) * 100,
            };
            if (!items?.isCostCode) {
              setBillItemsEditDataset(editItem as any); // change this in future ( temporary resolve type issue )
              setBillItemsEdit({
                id: Billparams?.id ?? "",
                moduleId: gConfig?.module_id,
                items,
                callComplete: () => {
                  setManageBillsDetails({
                    id: Billparams?.id || "",
                  });
                },
              });
            }
            return true;
          }
          return false;
        },
        cellRenderer: (params: CostItemTableCellRenderer) => {
          const costCodeValue = costCodeWithOption(params);
          return (
            <div className="flex gap-1">
              {!isEmpty(costCodeValue) ||
              params?.data?.item_category === "category" ? (
                costCodeValue ? (
                  <CFTableTooltip
                    tooltipContent={costCodeValue}
                    children={costCodeValue}
                  />
                ) : (
                  "-"
                )
              ) : gSettings?.quickbook_sync === "1" ||
                gSettings?.quickbook_desktop_sync === "1" ? (
                <CFTooltip
                  content={"Cost code is required to save this item."}
                  placement="top"
                >
                  <div className="w-4 h-4">
                    <AnimateError color="#ef4444" />
                  </div>
                </CFTooltip>
              ) : (
                "-"
              )}
            </div>
          );
        },
      },
      {
        headerName: _t("QTY"),
        minWidth: 80,
        maxWidth: 80,
        suppressKeyboardEvent,
        editable: (params: CostItemTableCellRenderer) =>
          gConfig?.module_read_only
            ? // will remove after testing
              // || (Number(due_balance ?? 0) <= 0 && Number(total ?? 0) > 0)
              false
            : !(
                Number(params.data.reference_primary_id ?? "") > 0 &&
                billDetailsSc?.sc_multi_bill?.toString() === "1"
              ),
        valueGetter: (params: CostItemTableCellRenderer) => {
          return params?.data?.quantity;
        },
        valueSetter: (params: {
          data: Partial<ItemSideData>;
          newValue: any;
        }) => {
          const newQuantityValue = Number(params?.newValue);
          const data = params?.data;
          let newQuantity = params.newValue ? params.newValue.toString() : "";
          const checkNum = qtyNumberCheck(newQuantityValue);
          const [integerPart, decimalPart] = newQuantity.split(".");
          if (integerPart.length > 6) {
            notification.error({
              description: _t(
                "Quantity should be less than or equal to 6 digits."
              ),
            });
            return false;
          }
          if (
            newQuantityValue &&
            newQuantityValue.toString().length > 8 &&
            (!newQuantityValue.toString().includes(".") || !checkNum)
          ) {
            notification.error({
              description: "Quantity should be less than or equal to 6 digits.",
            });
            return false;
          }
          if (!floatWithNegativeRegex.test(newQuantityValue)) {
            notification.error({
              description: _t(
                "Decimal part should be less than or equal to 2 digits."
              ),
            });
            return false;
          }
          // Check if the quantity is being emptied
          if (isNaN(newQuantityValue)) {
            data.quantity = 0;
            data.total = 0;

            const items: Partial<ItemSideData> = {
              ...params?.data,
              quantity: data?.quantity,
              total: data?.total,
              unit_cost: (Number(data?.unit_cost) * 100).toFixed(2),
            };

            if (items?.isCostCode && items?.item_id) {
              updateItemWithoutData(items?.item_id, {
                ...items,
                quantity: 0,
                total: 0,
                unit_cost: (Number(items?.unit_cost) / 100).toFixed(2),
              });
            } else {
              const updatedData = {
                ...items,
                unit_cost: (Number(items?.unit_cost) / 100).toFixed(2),
              };
              setBillItemsEdit({
                id: Billparams?.id ?? "",
                moduleId: gConfig?.module_id,
                items: updatedData,
                callComplete: (response?: {} | undefined) => {
                  itemCalculation(response);
                },
              });
            }
            return true;
          }

          const regexPattern = new RegExp(`^-?\\d{0,6}(\\.\\d{0,2})?$`);
          if (regexPattern?.test(newQuantityValue.toString())) {
            if (Number(data.quantity) !== newQuantityValue) {
              data.quantity = newQuantityValue;
              data.total = newQuantityValue * Number(data?.unit_cost) * 100;

              const items: Partial<ItemSideData> = {
                ...params?.data,
                quantity: data?.quantity,
                total: Float(data?.total),
                unit_cost: (Number(data?.unit_cost) * 100).toFixed(2),
              };
              if (items?.isCostCode) {
                if (items?.item_id) {
                  updateItemWithoutData(items?.item_id, {
                    ...items,
                    quantity: items?.quantity,
                    total: Float(items?.total) / 100,
                    unit_cost: (Number(items?.unit_cost) / 100).toFixed(2),
                  });
                }
              } else {
                const data = {
                  ...items,
                  unit_cost: (Number(items?.unit_cost) / 100).toFixed(2),
                };
                setBillItemsEdit({
                  id: Billparams?.id ?? "",
                  moduleId: gConfig?.module_id,
                  items,
                  callComplete: (response?: {} | undefined) => {
                    itemCalculation(response);
                  },
                });
              }
              return true;
            }
          } else {
            notification.error({
              message: "Notification Title",
              description:
                "Quantity field value must have a length less than or equal to 6.",
            });
            return false;
          }
          return false;
        },
        field: "quantity",
        suppressMovable: false,
        suppressMenu: true,
        cellEditor: "agNumberCellEditor",
        cellClass: "ag-right-aligned-cell",
        headerClass: "ag-header-right",
        cellRenderer: (params: CostItemTableCellRenderer) => {
          const quantityValue = formatter(
            formatAmount(Number(params?.data?.quantity), { isQuantity: true })
          ).value;
          return (
            <CFTableTooltip
              tooltipContent={quantityValue}
              children={quantityValue}
              mainClassName="justify-end"
            />
          );
        },
      },
      {
        headerName: _t("Unit Cost"),
        minWidth: 150,
        maxWidth: 150,
        suppressMovable: false,
        suppressMenu: true,
        cellEditorParams: {
          maxLength: 20,
        },
        cellEditor: "agNumberCellEditor",

        suppressKeyboardEvent,
        editable: (params: CostItemTableCellRenderer) =>
          gConfig?.module_read_only
            ? // will remove after testing
              // || (Number(due_balance ?? 0) <= 0 && Number(total ?? 0) > 0)
              false
            : !(
                Number(params.data.reference_primary_id ?? "") > 0 &&
                billDetailsSc?.sc_multi_bill?.toString() === "1"
              ),
        valueGetter: (params: CostItemTableCellRenderer) => {
          return params?.data?.unit_cost;
        },
        valueSetter: (params: {
          data: Partial<ItemSideData>;
          newValue: any;
        }) => {
          if (params?.newValue === null) {
            params.newValue = "0"; // Set the value to 0 when null
          }
          if (params?.newValue !== undefined && params?.newValue !== null) {
            const cleanedValue = params.newValue
              .toString()
              .split(".")[0]
              .replace("-", "");

            if (Number(params.newValue) < 0) {
              notification.error({
                description: _t(
                  "Negative values are not allowed for Unit Cost."
                ),
              });
              return false;
            }
            const checkNum1 = qtyNumberCheck(params.newValue);
            if (
              params.newValue.toString().length > 10 &&
              (!params.newValue.toString().includes(".") || !checkNum1)
            ) {
              notification.error({
                description: _t(
                  "Unit cost should be less than or equal to 10 digits"
                ),
              });
              return false;
            }
            let newUnitCost = params.newValue.toString();

            if (!floatWithNegativeRegex.test(params.newValue)) {
              notification.error({
                description: _t(
                  "Decimal part should be less than or equal to 2 digits"
                ),
              });
              return false;
            }
            const data = params.data;
            const newUnitCostNumber = !isEmpty(newUnitCost)
              ? parseFloat(newUnitCost)
              : 0;

            if (
              !isNaN(newUnitCostNumber) &&
              Number(data.unit_cost) !== newUnitCostNumber
            ) {
              data.unit_cost = newUnitCost;
              data.total =
                parseFloat(newUnitCost) * (Number(data?.quantity) || 0) * 100;

              const items: Partial<ItemSideData> = {
                ...params?.data,
                quantity: data?.quantity,
                total: data?.total,
                unit_cost: (Number(data?.unit_cost) * 100).toFixed(2),
              };

              if (items?.isCostCode) {
                if (items?.item_id) {
                  updateItemWithoutData(items.item_id, {
                    ...items,
                    quantity: items?.quantity,
                    total: Float(items?.total) / 100,
                    unit_cost: (Number(items?.unit_cost) / 100).toFixed(2),
                  });
                }
              } else {
                setBillItemsEdit({
                  id: Billparams?.id ?? "",
                  moduleId: gConfig?.module_id,
                  items,
                  callComplete: (response?: {} | undefined) => {
                    itemCalculation(response);
                  },
                });
              }

              return true;
            }
          }
          return false;
        },
        field: "unit_cost",
        headerClass: "ag-header-right",
        cellClass: "ag-right-aligned-cell",
        cellRenderer: (params: CostItemTableCellRenderer) => {
          const found = items?.find(
            (
              item: any // change this in future ( temporary resolve type issue )
            ) => item.item_id === params?.data?.item_id
          );
          return (
            // this will update based on https://app.clickup.com/t/86cy4tk1a
            <div className="text-right">
              {
                formatter(formatAmount(Number(found?.unit_cost)))
                  .value_with_symbol
              }
            </div>
          );
        },
      },
      {
        headerName: _t("Unit"),
        minWidth: 60,
        maxWidth: 60,
        field: "unit",
        suppressMovable: false,

        suppressMenu: true,
        cellRenderer: (params: CostItemTableCellRenderer) => {
          const Unit = params.data.unit;
          return Unit ? (
            <CFTableTooltip
              tooltipContent={params.data.unit}
              children={params.data.unit}
            />
          ) : (
            "-"
          );
        },
      },
      {
        headerName: _t(""),
        minWidth: 130,
        maxWidth: 130,
        field: "sc_paid_bill_amt",
        suppressMovable: false,
        suppressMenu: true,

        cellEditor: "agNumberCellEditor",
        suppressKeyboardEvent,
        editable: (params: CostItemTableCellRenderer) =>
          gConfig?.module_read_only
            ? // will remove after testing
              // || (Number(due_balance ?? 0) <= 0 && Number(total ?? 0) > 0)
              false
            : params.data.reference_module_id?.toString() ===
              defaultConfig.sub_contract_module_id.toString(),
        headerClass: "ag-header-right",
        cellClass: "ag-right-aligned-cell",
        valueGetter: (params: CostItemTableCellRenderer) => {
          const amount = params?.data?.sc_paid_bill_amt;
          return !isNaN(Number(amount)) ? +(amount / 100).toFixed(2) : "";
        },
        hide: billDetailsSc?.sc_multi_bill?.toString() === "0",
        valueSetter: (params: {
          data: {
            unit_cost: number;
            sc_paid_bill_amt: string;
            tax_rate: string;
            sc_paid_bill_percentage: number;
            total: number;
            quantity: number;
            total_sc_paid_bill_amt: number;
          };
          newValue: string;
        }) => {
          if (
            params?.newValue !== undefined &&
            !isNaN(Number(params?.newValue))
          ) {
            let newPaidAmt = params?.newValue;
            const data = params?.data;

            const originalAmt = data?.quantity * data?.unit_cost;
            const billAmtRemain = (
              originalAmt -
              Number(data?.total_sc_paid_bill_amt) / 100
            ).toFixed(2);

            const amt =
              Number(data?.sc_paid_bill_amt) / 100 + Number(billAmtRemain);

            if (Number(newPaidAmt) > amt && allow_overbilling === "0") {
              if (amt > 0) {
                newPaidAmt = amt.toString();
                notification.error({
                  message: "Notification Title",
                  description: `Select a amount between 0 and ${amt}`,
                });
                return false;
              } else {
                notification.error({
                  message: "Notification Title",
                  description: _t(
                    "This amount is higher than 100% of the Sub-Contract."
                  ),
                });
              }
              return false;
            }

            if (data?.unit_cost * data.quantity < parseFloat(newPaidAmt)) {
              notification.error({
                message: "Notification Title",
                description: `Select a amount between 0 and ${
                  data?.unit_cost * data.quantity
                }`,
              });
              return false;
            }

            data.sc_paid_bill_percentage = Number(
              (
                (parseFloat(newPaidAmt) * 100) /
                (data?.unit_cost * data.quantity)
              )?.toFixed(2) ?? "0"
            );
            data.total =
              !newPaidAmt || isNaN(parseFloat(newPaidAmt))
                ? 0
                : parseFloat(newPaidAmt);

            data.sc_paid_bill_amt =
              !newPaidAmt || isNaN(parseFloat(newPaidAmt))
                ? ""
                : parseFloat(newPaidAmt).toString();

            const items: any = {
              // change this in future ( temporary resolve type issue )
              ...data,
              // quantity: data?.quantity,
              // total: (Float(data?.total) * 100).toString(),
              unit_cost: (data?.unit_cost * 100).toFixed(2),
              reference_module_id: defaultConfig.sub_contract_module_id,
            };

            const editItem = {
              ...items,
              unit_cost: (Number(items?.unit_cost) / 100).toFixed(2),
              sc_paid_bill_amt: isNaN(Number(items?.sc_paid_bill_amt * 100))
                ? ""
                : Number(items?.sc_paid_bill_amt * 100),
              total: isNaN(Number(items?.total * 100))
                ? 0
                : Number(items?.total * 100),
            };
            setBillItemsEditDataset(editItem);

            setBillItemsEdit({
              id: Billparams?.id ?? "",
              moduleId: gConfig?.module_id,
              items,
              callComplete: () => {
                setManageBillsDetails({
                  id: Billparams?.id || "",
                });
              },
            });

            return true;
          }
          return false;
        },
        cellRenderer: (params: CostItemTableCellRenderer) => {
          return (
            <>
              {params.data.reference_module_id?.toString() ===
                defaultConfig.sub_contract_module_id.toString() && (
                <div className="text-right">
                  {isNaN(Number(params?.data?.sc_paid_bill_amt))
                    ? ""
                    : formatter(
                        formatAmount(
                          Number(params?.data?.sc_paid_bill_amt / 100)
                        )
                      ).value_with_symbol}
                </div>
              )}
            </>
          );
        },
      },
      {
        headerName: _t("This Bill"),
        minWidth: 80,
        maxWidth: 80,
        field: "sc_paid_bill_percentage",
        suppressMovable: false,

        suppressMenu: true,
        suppressKeyboardEvent,
        editable: (params: CostItemTableCellRenderer) =>
          gConfig?.module_read_only
            ? // will remove after testing
              // || (Number(due_balance ?? 0) <= 0 && Number(total ?? 0) > 0)
              false
            : params.data.reference_module_id?.toString() ===
              defaultConfig.sub_contract_module_id?.toString(),
        hide: billDetailsSc?.sc_multi_bill?.toString() === "0",
        headerClass: "ag-header-right",
        cellClass: "ag-right-aligned-cell",
        valueSetter: (params: {
          data: {
            unit_cost: number;
            sc_paid_bill_amt: number;
            sc_paid_bill_percentage: number;
            total: number;
            quantity: number;
            total_sc_paid_bill_amt: string;
            total_sc_paid_bill_percentage: string;
            tax_rate: string;
          };
          newValue: string;
        }) => {
          if (params?.newValue !== undefined) {
            const newPaidPercentage = params?.newValue;
            const data = params?.data;

            const totalScBilled = Number(
              data?.total_sc_paid_bill_percentage?.toString()
            )?.toFixed(2);

            const billPercentageRemain = 100 - Number(totalScBilled);

            const billedAmtPercentage = Number(
              data?.sc_paid_bill_percentage?.toString()
            )?.toFixed(2);

            const amtPercentage =
              Number(billedAmtPercentage) + billPercentageRemain;

            if (
              Number(newPaidPercentage) > amtPercentage &&
              allow_overbilling === "0"
            ) {
              if (amtPercentage > 0) {
                notification.error({
                  message: "Notification Title",
                  description: `Select a percentage between 0 and ${amtPercentage}%.`,
                });
                return false;
              } else {
                notification.error({
                  message: "Notification Title",
                  description: `This amount is higher than 100% of the Sub-Contract.`,
                });
                return false;
              }
            }

            if (parseFloat(newPaidPercentage) > 100) {
              notification.error({
                message: "Notification Title",
                description: `Select a percentage between 0 and 100%.`,
              });
              return false;
            }

            const total = (
              (data.unit_cost / 100) *
              data.quantity *
              parseFloat(newPaidPercentage)
            )?.toFixed(2);

            data.sc_paid_bill_amt = Number(total);

            data.total = Number(total);

            data.sc_paid_bill_percentage = parseFloat(newPaidPercentage);

            const items: any = {
              // change this in future ( temporary resolve type issue )
              ...data,
              // quantity: data?.quantity,
              // total: (Number(data?.total?.toString()) * 100).toString(),
              unit_cost: (data?.unit_cost * 100).toFixed(2),
              reference_module_id: defaultConfig.sub_contract_module_id,
            };

            const editItem = {
              ...items,
              unit_cost: (Number(items?.unit_cost) / 100).toFixed(2),
              sc_paid_bill_amt: Number(Number(total) * 100),
              total: Number(Number(total) * 100),
            };
            setBillItemsEditDataset(editItem);

            setBillItemsEdit({
              id: Billparams?.id ?? "",
              moduleId: gConfig?.module_id,
              items,
              callComplete: () => {
                setManageBillsDetails({
                  id: Billparams?.id || "",
                });
              },
            });

            return true;
          }
          return false;
        },
        cellRenderer: (params: CostItemTableCellRenderer) => {
          return (
            <>
              {params.data.reference_module_id?.toString() ===
                defaultConfig.sub_contract_module_id.toString() && (
                <div className="text-right">
                  {params?.data?.sc_paid_bill_percentage !== undefined &&
                  !isNaN(params?.data?.sc_paid_bill_percentage)
                    ? `${formatAmount(
                        Number(
                          params?.data?.sc_paid_bill_percentage?.toString()
                        )
                      )}%`
                    : "0%"}
                </div>
              )}
            </>
          );
        },
      },
      {
        headerName: _t("Billed"),
        minWidth: 130,
        maxWidth: 130,
        field: "total_sc_paid_bill_amt",
        headerClass: "ag-header-right",
        cellClass: "ag-right-aligned-cell",
        suppressMovable: false,

        suppressMenu: true,
        hide: billDetailsSc?.sc_multi_bill?.toString() === "0",
        cellRenderer: (params: CostItemTableCellRenderer) => {
          return (
            <>
              {params.data.reference_module_id?.toString() ===
                defaultConfig.sub_contract_module_id?.toString() && (
                <div className="py-0.5">
                  <div className="text-right">
                    {
                      formatter(
                        formatAmount(
                          (params?.data?.total_sc_paid_bill_amt / 100).toFixed(
                            2
                          )
                        )
                      ).value_with_symbol
                    }
                  </div>
                  <div className="text-right">
                    (
                    {params?.data?.total_sc_paid_bill_percentage !==
                      undefined &&
                    !isNaN(params?.data?.total_sc_paid_bill_percentage)
                      ? `${formatAmount(
                          Number(
                            params?.data?.total_sc_paid_bill_percentage?.toString()
                          )
                        )}%`
                      : "0%"}
                    )
                  </div>
                </div>
              )}
            </>
          );
        },
      },
      {
        headerName: _t("Total"),
        minWidth: 130,
        maxWidth: 130,
        field: "total",
        headerClass: "ag-header-right",
        cellClass: "ag-right-aligned-cell",
        suppressMovable: false,
        suppressMenu: true,
        valueGetter: (params: {
          data: {
            total: number;
          };
        }) => {
          return params.data.total;
        },

        cellRenderer: (params: CostItemTableCellRenderer) => {
          const found = items?.find(
            (
              item: any // change this in future ( temporary resolve type issue )
            ) => item.item_id === params?.data?.item_id
          );
          // this will update based on https://app.clickup.com/t/86cy4tk1a
          const total = formatter(
            formatAmount(Number(found?.total / 100))
          ).value_with_symbol;
          return (
            <CFTableTooltip
              mainClassName="justify-end"
              tooltipContent={total}
              children={total}
            />
          );
        },
      },
      {
        headerName: _t("Tax"),
        maxWidth: 50,
        minWidth: 50,
        field: "apply_global_tax",
        headerClass: "ag-header-center",
        cellClass: "ag-cell-center",
        suppressMovable: false,

        suppressMenu: true,
        cellRenderer: (params) => CheckCellRenderer(params, due_balance, total),
        hide: !(
          gSettings?.is_non_united_state_qb_country ||
          gSettings?.quickbook_sync === "0"
        ),
      },
      {
        headerName: "",
        minWidth: 80,
        maxWidth: 80,
        field: "action",
        suppressMenu: true,

        headerClass: "ag-header-center",
        cellRenderer: (params: any) => {
          return (
            <div className="justify-center flex gap-1">
              {gConfig?.module_read_only && (
                <>
                  <CFTableIcon
                    icon="fa-solid fa-eye"
                    icontooltip="View"
                    onClick={() => {
                      setSelectedValue("new_item");
                      setSelectItem(params?.data);
                      setItemBox(true);
                      setIsView(true);
                      setEditform(false);
                    }}
                  />
                </>
              )}
              {!gConfig?.module_read_only && (
                <>
                  <CFTableIcon
                    icon="fa-solid fa-eye"
                    icontooltip="View"
                    onClick={() => {
                      setSelectedValue("new_item");
                      setSelectItem(params?.data);
                      setItemBox(true);
                      setEditform(true);
                      setIsView(false);
                    }}
                  />
                  <CFTableIcon
                    icon="fa-regular fa-trash-can"
                    icontooltip="Delete"
                    onClick={() => {
                      setSelectItem(params?.data);
                      setOpenConfirmModal(true);
                    }}
                  />
                  {/* will remove after testing */}
                  {/* {!(
                    Number(due_balance ?? 0) <= 0 && Number(total ?? 0) > 0
                  ) ? (
                    <CFTableIcon
                      icon={faTrashCan}
                      icontooltip="Delete"
                      onClick={() => {
                        setSelectItem(params?.data);
                        setOpenConfirmModal(true);
                      }}
                    />
                  ) : (
                    ""
                  )} */}
                </>
              )}
            </div>
          );
        },
      },
    ],
    [billDetails?.items?.[0]?.total_sc_paid_bill_amt, costCodeData, items]
  );
  const handleDragAndDrop = (items: Array<Partial<ItemSideData>>) => {
    const tempItems: Partial<ItemSideData>[] = items.map((row, index) => ({
      ...row,
      bill_item_no: (index + 1).toString(),
      unit_cost: (Number(row?.unit_cost) * 100).toFixed(2),
      sc_paid_bill_amt: Number(row?.sc_paid_bill_amt) / 100,
      total: Number(row?.total) / 100,
    }));
    let combinedData = tempItems;
    if (tableAllData && tableAllData?.length !== tempItems?.length) {
      const newData = tableAllData?.filter(
        (itemB) => !tempItems?.some((itemA) => itemA.item_id === itemB.item_id)
      );

      const startIndex = tempItems?.length;
      let maxBillItemNo = startIndex;
      newData?.forEach((obj, index) => {
        maxBillItemNo++;
        obj.bill_item_no = maxBillItemNo.toString();
      });

      combinedData = [...tempItems, ...newData];
    }

    setMultipleBillItemsEdit({
      id: Billparams?.id ?? "",
      moduleId: gConfig?.module_id,
      items: combinedData,
      callComplete: () => {
        setManageBillsDetails({
          id: Billparams?.id || "",
        });
      },
    });
  };

  const handleAddData = (formData: Array<Partial<ItemSideData>>) => {
    if (!formData || !Array.isArray(formData)) {
      return;
    }
    const billId = Billparams && Billparams.id ? Billparams.id : "";

    const itemsArray: Partial<ItemSideData>[] = [];
    formData.forEach((formDataItem: Partial<ItemSideData>) => {
      let itemId =
        formDataItem?.equipment_id ||
        formDataItem?.material_id ||
        formDataItem?.labor_id ||
        formDataItem?.contractor_id ||
        formDataItem?.other_item_id ||
        formDataItem?.item_id;
      if (isEmpty(itemId?.toString())) {
        return;
      }
      const items: Partial<ItemSideData> = {
        unit_cost: (Int(formDataItem?.unit_cost) * 1).toString(),
        cost_code_id:
          formDataItem && formDataItem.costCodeId
            ? formDataItem.costCodeId
            : formDataItem.cost_code_id
            ? formDataItem.cost_code_id
            : "0",
        ...(formDataItem?.notes && {
          description: formDataItem?.notes || "",
        }),
        ...(formDataItem?.account_id && {
          account_id: formDataItem?.account_id || "",
        }),
        ...(formDataItem?.unit && {
          unit: formDataItem?.unit || "",
        }),
        ...((formDataItem?.internal_notes ||
          (formDataItem as Partial<CIDBItemSideData>)?.internalNotes) && {
          internal_notes:
            formDataItem?.internal_notes ||
            (formDataItem as Partial<CIDBItemSideData>)?.internalNotes,
        }),
        item_type: formDataItem?.item_type ? formDataItem?.item_type : "0",
        bill_id: billId.toString(),
        subject: formDataItem?.name || "",
        quantity: formDataItem?.quantity ?? "0",
        item_category: "item",
        total: Number(formDataItem?.total ?? 0) * 1,
        apply_global_tax: gSettings?.tax_all_items as unknown as string,
        reference_item_id: itemId,
      };
      itemsArray?.push(items);
    });

    const prevBillData = tableAllData?.map(
      (data: Partial<ItemSideData>) => data.reference_item_id
    );

    const newData = itemsArray?.map(
      (data: Partial<ItemSideData>) => data.reference_item_id
    );

    const totalDeleteItems = tableAllData?.filter(
      (item: Partial<ItemSideData>) =>
        !newData.includes(item.reference_item_id) &&
        (item?.reference_module_item_id as unknown as number) === 0
    );

    const totalInsertData = itemsArray.filter(
      (item: Partial<ItemSideData>) =>
        !prevBillData?.includes(item.reference_item_id)
    );

    // Delete items
    if (totalDeleteItems?.length) {
      totalDeleteItems.forEach((selectItem) => {
        if (
          !!selectItem?.item_id ||
          (selectItem?.item_id == "0" && !!selectItem?.bill_id)
        ) {
          if (selectItem?.item_type_name !== "Other") {
            handleDelete(selectItem?.item_id);
          }
        }
      });
    }

    // Add new items
    if (totalInsertData?.length) {
      setAddBillItems({
        bill_id: billId.toString(),
        module_id: gConfig?.module_id,
        items: totalInsertData,
        callComplete: () => {
          resetItemFilter();
          setManageBillsDetails({
            id: Billparams?.id || "",
          });
        },
      });
    }

    closeModalHandler();
  };
  useEffect(() => {
    if (filteredItems?.length) {
      filterBilldetailItems(filteredItems);
    } else {
      filterBilldetailItems([]);
    }
  }, [filteredItems, filter]);

  return (
    <>
      <GradientIconCard
        title="Lump Sum/Item Costs"
        iconProps={{
          icon: faSackDollar,
          containerClassName:
            "bg-[linear-gradient(180deg,#42DD9B1a_0%,#3CB9B31a_100%)]",
          id: "bills_item_costs_card_icon",
          colors: ["#42DD9B", "#3CB9B3"],
        }}
        rightSideClassName="md:w-[calc(100%-218px)] w-full"
        headerRightButton={
          <div className="flex sm:items-center items-start justify-end gap-2">
            <div className="flex items-center gap-1.5 bg-blue-50 text-primary-900 py-[5px] px-[9px] rounded dark:bg-dark-500 dark:text-white/90 w-fit whitespace-nowrap">
              <FontAwesomeIcon
                className="w-[18px] h-[18px] text-primary-900 dark:text-white/90"
                icon="fa-duotone fa-solid fa-money-check-dollar"
              />
              <CFTypography
                title="small"
                className="block text-sm font-medium text-primary-900 dark:text-white/90"
              >
                {formatter(formatAmount(Int(totalSum) / 100)).value_with_symbol}
              </CFTypography>
            </div>
            <div className="flex sm:flex-row flex-col sm:items-center items-end gap-2">
              {/* will remove after testing */}
              {/* {!(Number(due_balance ?? 0) <= 0 && Number(total ?? 0) > 0) && */}
              {!gConfig?.module_read_only && (
                <div className="max-w-fit relative z-[98] flex ml-auto bg-blue-50 dark:bg-dark-500 shadow-[0px_3px_20px] shadow-black/5 rounded">
                  <CFDropdown
                    contentClassName="max-h-[46vh] !overflow-y-auto add-items-drop-down"
                    iconClass="text-white"
                    options={addItemBill
                      ?.filter((item) =>
                        gSettings.qb_country !== "US"
                          ? true
                          : item.value !== "tax_total"
                      )
                      ?.map((item) => ({
                        ...item,
                        onClick: () => {
                          handleSelectChange(item.value);
                        },
                      }))}
                    footerOptionText={false}
                    buttonClass="!w-fit !h-fit"
                  >
                    <div className="py-1 px-2.5 bg-[#EBF1F9] rounded flex items-center gap-[5px]">
                      <CFTypography
                        title="small"
                        className="text-primary-900 text-sm"
                      >
                        {_t(`Add Item to ${gConfig?.module_singular_name}`)}
                      </CFTypography>
                      <FontAwesomeIcon
                        className="w-3 h-3 text-primary-900"
                        icon="fa-regular fa-chevron-down"
                      />
                    </div>
                  </CFDropdown>
                </div>
              )}
              <ModuleItemsFilter
                onOpenChange={(newOpen: boolean) => setOpen(newOpen)}
                filter={filter}
                onChangeFilter={setFilter}
                openFilter={open}
              />
            </div>
          </div>
        }
      />
      <div className="pt-2">
        {isLoading ? (
          <div className="mt-5">
            <CFSpin />
          </div>
        ) : (
          <CFStaticTable
            rowHeight={42}
            headers={headers}
            suppressDragLeaveHidesColumns={true}
            data={filteredItems}
            onGridReady={onGridReady}
            suppressRowClickSelection={true}
            rowSelection={"multiple"}
            onRowDragDropEnd={handleDragAndDrop}
            onCellValueChanged={onCellValueChanged}
            noRowsOverlayComponent={() => (
              <NoRecords
                image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
              />
            )}
          />
        )}
      </div>
      <CFConfirmModal
        open={openConfirmModal}
        bodyClassName="flex text-center justify-center"
        message="Are you sure you want to delete this Item?"
        handelModal={() => setOpenConfirmModal(false)}
        header={
          <CFConfirmHeader
            title="delete"
            icon={
              <FontAwesomeIcon
                className="w-3.5 h-3.5"
                icon="fa-regular fa-trash-can"
              />
            }
            handelModal={() => setOpenConfirmModal(false)}
          />
        }
        footer={
          <>
            <CFButton
              variant="primary"
              className="justify-center min-w-[75px] primary-btn"
              onClick={() => {
                handleDelete();
                setOpenConfirmModal(false);
              }}
            >
              {_t("Yes")}
            </CFButton>
            <CFButton
              variant="default"
              className="min-w-[75px] justify-center hover:!border-primary-900 hover:!text-primary-900 active:!border-primary-900 active:!text-primary-900"
              onClick={() => setOpenConfirmModal(false)}
            >
              {_t("No")}
            </CFButton>
          </>
        }
      />
      {itemBox && selectedValue === "new_item" ? (
        <AddBillItem
          setItemBox={setItemBox}
          itemBox={itemBox}
          itemCount={billDetails?.items?.length || 0}
          data={(editForm || isView ? selectItem : {}) as any} // change this in future ( temporary resolve type issue )
          setSelectItem={setSelectItem as any} // change this in future ( temporary resolve type issue )
          editForm={editForm}
          setEditform={setEditform}
          setIsView={setIsView}
          isView={isView}
          closeModalHandler={() => {
            setItemBox(false);
            setSelectedValue(
              addItemBill?.length > 0 ? addItemBill[0]?.value : ""
            );
          }}
          filteredItems={filteredItems}
        />
      ) : (
        <></>
      )}
      {itemBox && selectedValue === "Database" ? (
        <CidbItemDrawer
          closeDrawer={() => setItemBox(false)}
          itemTypes={itemTypes}
          options={costDataBase}
          singleSelecte={false}
          addItem={(value: Partial<CIDBItemSideData>[]) => {
            return handleAddData(value as Array<Partial<ItemSideData>>);
          }}
          data={costItemsFromDatabase}
          openSendEmailSidebar={itemBox}
          cidbModuleVIseIdAndValue={{
            [defaultConfig.material_key]: {
              id: 161,
              value: defaultConfig.material_key,
            },
            [defaultConfig.labor_key]: {
              id: 163,
              value: defaultConfig.labor_key,
            },
            [defaultConfig.equipment_key]: {
              id: 162,
              value: defaultConfig.equipment_key,
            },

            [defaultConfig.subcontractor_key]: {
              id: 164,
              value: defaultConfig.subcontractor_key,
            },
            [defaultConfig.other_items_key]: {
              id: 165,
              value: defaultConfig.other_items_key,
            },
          }}
        />
      ) : (
        <></>
      )}
      {itemBox && selectedValue === "purchase_order" && isProjectExist ? (
        <ImportPurchaseOrderItems
          open={itemBox}
          closeModalHandler={() => {
            setItemBox(false);
            setSelectedValue(
              addItemBill?.length > 0 ? addItemBill[0]?.value : ""
            );
          }}
        />
      ) : (
        <></>
      )}
      {itemBox && selectedValue === "subContract" && isProjectExist ? (
        <ImportSubContractItems
          open={itemBox}
          closeModalHandler={() => {
            setItemBox(false);
            setSelectedValue(
              addItemBill?.length > 0 ? addItemBill[0]?.value : ""
            );
          }}
        />
      ) : (
        <></>
      )}
      {itemBox && selectedValue === "expense" && isProjectExist ? (
        <ImportExpense
          open={itemBox}
          closeModalHandler={() => {
            setItemBox(false);
            setSelectedValue(
              addItemBill?.length > 0 ? addItemBill[0]?.value : ""
            );
          }}
        />
      ) : (
        <></>
      )}
      {itemBox && selectedValue === "new_lum_sum_item" ? (
        <LumpSumTotal
          open={itemBox}
          closeModalHandler={() => {
            setItemBox(false);
            setSelectedValue(
              addItemBill?.length > 0 ? addItemBill[0]?.value : ""
            );
          }}
        />
      ) : (
        <></>
      )}
      {itemBox && selectedValue === "discount_item" ? (
        <BillDiscountItemModal
          open={itemBox}
          closeModalHandler={() => {
            setItemBox(false);
            setSelectedValue(
              addItemBill?.length > 0 ? addItemBill[0]?.value : ""
            );
          }}
        />
      ) : (
        <></>
      )}
      {itemBox &&
      gSettings?.qb_country === "us" &&
      gSettings?.quickbook_sync === "1" &&
      selectedValue === "tax_total" ? (
        <BillTaxItemModal
          open={itemBox}
          closeModalHandler={() => {
            setItemBox(false);
            setItemBox(false);
            setSelectedValue(
              addItemBill?.length > 0 ? addItemBill[0]?.value : ""
            );
          }}
        />
      ) : (
        <></>
      )}
      {itemBox && selectedValue === "credit_item" ? (
        <BillCreditItem
          open={itemBox}
          closeModalHandler={() => {
            setItemBox(false);
            setSelectedValue(
              addItemBill?.length > 0 ? addItemBill[0]?.value : ""
            );
          }}
        />
      ) : (
        <></>
      )}
      {itemBox && selectedValue === "retainage_payment" ? (
        <BillRetainagePaymentModal
          open={itemBox}
          closeModalHandler={() => {
            setItemBox(false);
            setSelectedValue(
              addItemBill?.length > 0 ? addItemBill[0]?.value : ""
            );
          }}
        />
      ) : (
        <></>
      )}
      {itemBox && selectedValue === "freight_charge" ? (
        <BillFreightChargeItemModal
          open={itemBox}
          closeModalHandler={() => {
            setItemBox(false);
            setSelectedValue(
              addItemBill?.length > 0 ? addItemBill[0]?.value : ""
            );
          }}
        />
      ) : (
        <></>
      )}
    </>
  );
};

interface AddItem {
  label: string;
  value: string;
  module_id?: number;
  disabled?: boolean;
}

export default BillsItemCostsCard;

const CheckCellRenderer = (
  params: CostItemTableCellRenderer,
  due_balance: string | undefined,
  total: string | undefined
) => {
  const [checkBoxData, setCheckBoxData] = useState(
    (params?.data as any)?.apply_global_tax === "1" // change this in future ( temporary resolve type issue )
  );
  const [TaxLoadingStatus, setTaxLoadingStatus] = useState<{
    [key: string]: boolean;
  }>({});
  const Billparams: Partial<RouteParams> = useParams();
  const gConfig: GConfig = getGConfig();
  const items = {
    ...params?.data,
  };

  return (
    <CustomCheckBox
      className="gap-0 mx-auto w-4"
      checked={checkBoxData}
      onChange={(e: CheckboxChangeEvent) => {
        const value = e?.target?.checked;
        setCheckBoxData(!checkBoxData);
        setTaxLoadingStatus({
          [params?.data?.item_id]: true,
        });
        setBillItemsEdit({
          id: Billparams?.id ?? "",
          moduleId: gConfig?.module_id,
          items: {
            ...items,
            apply_global_tax: value === true ? "1" : "0",
            sc_paid_bill_amt: +(Number(items?.sc_paid_bill_amt) / 100).toFixed(
              2
            ),
            total: +Number(items?.total).toFixed(2),
            unit_cost: (Number(items?.unit_cost) * 100).toFixed(2),
          },
          callComplete: () => {
            setManageBillsDetails({
              id: Billparams?.id || "",
            });
          },
        });
      }}
      disabled={
        gConfig?.module_read_only
        // will remove after testing
        // || (Number(due_balance ?? 0) <= 0 && Number(total ?? 0) > 0)
      }
      loadingProps={{
        isLoading:
          params && params?.data?.item_id
            ? TaxLoadingStatus[params?.data?.item_id]
            : false,
        className: "bg-[#ffffff]",
      }}
    />
  );
};
