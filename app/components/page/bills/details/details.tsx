import GradientIconCard from "~/components/common/gradient-icon-card";
import { CFCheckBox } from "~/components/third-party/ant-design/cf-checkbox";
import { CFDatePicker } from "~/components/third-party/ant-design/cf-date-picker";
import { CFInlineTextarea } from "~/components/third-party/ant-design/cf-inline-textarea";
import { CFSelect } from "~/components/third-party/ant-design/cf-select";
import { CFTypography } from "~/components/third-party/ant-design/cf-typography";
import { defaultConfig } from "~/data";
import {
  getAddTermTypeConfirmLoading,
  useBillTermType,
} from "~/components/modals/add-term-type/zustand";
import { getGConfig, getGSettings, useGModules } from "~/zustand";
import { memo, useEffect, useMemo, useRef, useState } from "react";
import {
  getBillReferenceDetails,
  getBillsDetails,
} from "~/zustand/pages/manage-bills/store";
import { useParams } from "@remix-run/react";
import {
  Number,
  formatAmount,
  getConvertedLink,
  getDifferenceBetweenDate,
  getFormat,
  replaceDOMParams,
  sanitizeString,
} from "~/helpers/helper";
import isEmpty from "lodash/isEmpty";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
import { useDateFormatter, useTranslation } from "~/hook";
import { getDirectaryKeyById } from "~/components/sidebars/multi-select/customer/zustand/action";
import { routes } from "~/route-services/routes";
import { AddTermTypeConfirmModal } from "~/components/modals/add-term-type";
import dayjs, { Dayjs } from "dayjs";
import {
  setBillReferenceDetails,
  setBillsEdit,
} from "~/zustand/pages/manage-bills/actions";
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";
import { CheckboxChangeEvent } from "antd/es/checkbox";
import { setDirectoryEdit } from "~/zustand/pages/manage-directory/actions";
import { useBillDetailFormStatus } from "./hook";
import { type DefaultOptionType } from "antd/es/select";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { getStatusForField } from "~/shared/utils/helper/common";
import { displayDateFormat } from "~/shared/utils/helper/defaultDateFormat";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
import delay from "lodash/delay";
import { SubContractsFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/subContractsFieldRedirectionIcon";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import FieldRedirectButton from "~/shared/components/molecules/fieldRedirect/fieldRedirectButton/FieldRedirectButton";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";

// Bill detail status fields set as temporary base removed after new structure
const upadateFieldStatus: IFieldStatus[] = [
  {
    field: "due_date",
    status: "button",
  },
  {
    field: "supplier_id",
    status: "button",
  },
];

const BillsDetailsCard = ({
  FilterBalanceDue,
}: {
  FilterBalanceDue?: number;
}) => {
  const { setTermTypes, termTypes, addTermType, billTermLoading } =
    useBillTermType();
  const gConfig: GConfig = getGConfig();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const gSettings: GSettings = getGSettings();
  const billDetails: BillDetails = getBillsDetails();
  const billRefDetail: IBillRefernce[] = getBillReferenceDetails();
  const termTypeLoading: boolean = getAddTermTypeConfirmLoading();
  const { checkModuleAccessByKey, getGModuleByKey } = useGModules();
  const [isSelectVendorOpen, setIsSelectVendorOpen] = useState<boolean>(false);
  const { statusChange, getInputProps, getCheckboxProps } =
    useBillDetailFormStatus();
  const { formatter, inputFormatter, unformatted } = useCurrencyFormatter();
  const dateFormat = useDateFormatter();
  const { _t } = useTranslation();
  const { quickbook_sync, quickbook_desktop_sync }: GSettings = getGSettings();
  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(upadateFieldStatus);
  const [billData, setBillData] = useState<Partial<BillDetails>>({});
  const [newTermValue, setNewTermValue] = useState<string>("");
  const [addNewTermTypeConfirmDialogOpen, setAddNewTermTypeConfirmDialogOpen] =
    useState<boolean>(false);
  const [contactDetailDialogOpen, setContactDetailDialogOpen] =
    useState<boolean>(false);
  const termSelectRef = useRef<HTMLSelectElement | null>(null);
  const addTermConfirmButtonRef = useRef<HTMLButtonElement | null>(null);
  const params: RouteParams = useParams();
  const [selectedProject, setSelectedProject] = useState<
    IProject | undefined | null
  >();
  const supplierKey: string = getDirectaryKeyById(
    Number(billData?.supplier_dir_type),
    gConfig
  );

  const onVendorChange = (
    customer:
      | Partial<CustomerSelectedData>
      | Array<Partial<CustomerSelectedData>>
  ) => {
    if (!customer || (Array.isArray(customer) && customer.length === 0)) {
      setBillData((prev) => {
        return {
          ...prev,
        };
      });
      notification.error({
        description: "Vendor field is required.",
      });
      return;
    }
    let selectedCustomer: Partial<
      CustomerSelectedData & {
        is_billable?: number | string;
      }
    >;
    if (Array.isArray(customer)) {
      selectedCustomer = customer[0];
    } else {
      selectedCustomer = customer;
    }
    if (selectedCustomer) {
      setBillData((prev) => {
        let tempPrev = prev;
        const value: Partial<BillDetails> = {
          supplier_id: selectedCustomer?.user_id?.toString() ?? "0",
          supplier_company_name: selectedCustomer?.display_name || "",
          supplier_name: selectedCustomer?.display_name || "",
          supplier_dir_type: selectedCustomer?.type?.toString() || "",
          supplier_details: { image: selectedCustomer?.image || "" },
          ref_po: "0",
          supplier_contact_id:
            selectedCustomer.contact_id?.toString() !== "0"
              ? selectedCustomer.contact_id?.toString()
              : "0",
        };
        if (
          selectedCustomer?.type_key === CFConfig.vendor_key ||
          selectedCustomer?.type_key === CFConfig.contractor_key
        ) {
          value.is_billable = selectedCustomer?.is_billable == 1 ? "1" : "0";
        }

        setDirectoryEdit(
          value?.supplier_id?.toString() ?? "0",
          user,
          "cardview"
        );
        updateBillDetail({
          field_name: "supplier_id",
          value,
          onError: () => {
            setBillData(prev);
          },
        });
        return {
          ...tempPrev,
          ...value,
          supplier_dir_type: selectedCustomer?.type,
          supplier_address1: selectedCustomer?.address1,
          supplier_address2: selectedCustomer?.address2,
          supplier_city: selectedCustomer?.city,
          supplier_contact_id: selectedCustomer?.contact_id,
          supplier_name: selectedCustomer?.display_name,
          supplier_phone: selectedCustomer?.phone,
          supplier_state: selectedCustomer?.state,
          supplier_zip: selectedCustomer?.zip,
          supplier_details: { image: selectedCustomer?.image || "" },
        } as Partial<BillDetails>;
      });
    }
  };

  const getUpdatedTerms = ({
    order_date,
    due_date,
    term_key,
  }: {
    order_date?: string;
    due_date?: string;
    term_key?: string;
  }) => {
    let billDueDate = due_date;
    let dueDays = 0;
    switch (term_key) {
      //Seema(2020-10-02): If someone select option 'Due on Receipt', set due date to Current Date.
      case "invoice_term_due_on_receipt":
        billDueDate = dateFormat({
          format: getFormat(gSettings?.date_format),
        });
        break;
      case "invoice_term_net_10":
        dueDays = 10;
        break;
      case "invoice_term_net_15":
        dueDays = 15;
        break;
      case "invoice_term_net_30":
        dueDays = 30;
        break;
      case "invoice_term_net_45":
        dueDays = 45;
        break;
      case "invoice_term_net_60":
        dueDays = 60;
        break;
    }

    if (dueDays > 0) {
      billDueDate = dayjs(
        dateFormat({
          date: order_date,
          dateFormat: getFormat(gSettings?.date_format),
          format: "yyyy-MM-dd",
        })
      )
        .add(dueDays, "days")
        .format(gSettings?.date_format);
    }

    let billUpdateObj: TermChangeApiParams = {
      term_key: term_key && term_key.trim() ? term_key.trim() : "",
      term_id: (!isNaN(Number(term_key)) ? Number(term_key) : 0)?.toString(),
    };

    if (billData.due_date !== billDueDate) {
      billUpdateObj = {
        ...billUpdateObj,
        due_date: billDueDate,
      };
    }
    return billUpdateObj;
  };

  const onOrderDateChange = (date: Dayjs | DateValue | null) => {
    if (!Array.isArray(date)) {
      const fieldName = "order_date";
      setBillData((prev) => {
        let tempPrev = prev;
        const dueDate = getDate(prev.due_date) as string;

        if (!!date) {
          const orderDateFormatted = date.format("YYYY-MM-DD");
          if (!prev?.term_key?.includes("invoice_term_net_")) {
            const getDifference = getDifferenceBetweenDate(
              dueDate,
              orderDateFormatted,
              "days"
            );
            if (getDifference < 0) {
              const errorMessage =
                "Due date must be greater than or equal to Bill Date.";
              notification.error({
                description: errorMessage,
                duration: 1, //add for fixed this bug https://app.clickup.com/t/86cycd5er
              });
              statusChange(fieldName, {
                status: "error",
                message: errorMessage,
              });
              return tempPrev;
            }
          }
          const orderDate = !!date ? date?.format(gSettings?.date_format) : "";
          const updatesTermObj = getUpdatedTerms({
            term_key: tempPrev?.term_key,
            due_date: tempPrev?.due_date,
            order_date: orderDate,
          });
          tempPrev = {
            ...tempPrev,
            ...updatesTermObj,
            order_date: orderDate,
          } as Partial<BillDetails>;
          updateBillDetail({
            field_name: fieldName,
            value: { ...updatesTermObj, order_date: orderDate },
            onError: () => {
              setBillData(prev);
            },
          });
        } else {
          tempPrev = {
            ...tempPrev,
            order_date: undefined,
          } as Partial<BillDetails>;
          updateBillDetail({
            field_name: fieldName,
            value: { order_date: null },
            onError: () => {
              setBillData(prev);
            },
          });
        }
        return tempPrev;
      });
    }
  };

  const onDueDateChange = (date: Dayjs | DateValue | null) => {
    if (!Array.isArray(date)) {
      const fieldName = "due_date";
      setBillData((prev) => {
        let tempPrev = prev;
        const orderDate = getDate(tempPrev.order_date) as string;
        if (!!date) {
          const dueDateFormatted = date.format("YYYY-MM-DD");
          const difference = getDifferenceBetweenDate(
            dueDateFormatted,
            orderDate,
            "days"
          );
          if (difference < 0) {
            const errorMessage =
              "Due date must be greater than or equal to Bill Date.";
            notification.error({
              description: errorMessage,
              duration: 1, //add for fixed this bug https://app.clickup.com/t/86cycd5er
            });
            statusChange(fieldName, {
              status: "error",
              message: errorMessage,
            });
            handleChangeFieldStatus({
              field: "due_date",
              status: "error",
              action: "API",
            });
            delay(() => {
              handleChangeFieldStatus({
                field: "due_date",
                status: "button",
                action: "API",
              });
            }, 2000);
            return tempPrev;
          }
          const dueDate = !!date ? date?.format(gSettings?.date_format) : null;
          tempPrev = { ...tempPrev, due_date: dueDate };
          updateBillDetail({
            field_name: fieldName,
            value: {
              due_date: dueDate,
            },
            onError: () => {
              setBillData(prev);
            },
          });
        } else {
          tempPrev = { ...tempPrev, due_date: undefined };
          updateBillDetail({
            field_name: fieldName,
            value: {
              due_date: null,
            },
            onError: () => {
              setBillData(prev);
            },
          });
        }
        return tempPrev;
      });
    }
  };

  const onTermChange = (term?: { term_key: string; term_name: string }) => {
    setBillData((prev) => {
      let tempPrev = prev;
      let updatesTermObj = getUpdatedTerms({
        term_key: term?.term_key ?? "",
        due_date: tempPrev?.due_date,
        order_date: tempPrev?.order_date,
      }) as TermChangeApiParams & { term_name: string };
      updatesTermObj = {
        ...updatesTermObj,
        term_name: term?.term_name ?? "",
      };
      updateBillDetail({
        field_name: "bill_terms",
        value: updatesTermObj,
        onError: () => {
          setBillData(prev);
        },
      });
      return { ...tempPrev, ...updatesTermObj } as Partial<BillDetails>;
    });
  };

  const updateBillDetail = ({
    field_name,
    value,
    onError,
  }: {
    field_name: keyof (BillFormStatusType & BillFormCheckboxStatusType);
    value: any;
    onError: () => void;
  }) => {
    let tempValue = value;
    const dateFieldsKeys = ["order_date", "due_date"];
    const checkboxFieldsKeys = ["is_billable", "is_shared"];
    const isCheckBoxChange = checkboxFieldsKeys?.find(
      (dateFieldKey) => tempValue?.[dateFieldKey]
    );
    if (dateFieldsKeys?.find((dateFieldKey) => tempValue?.[dateFieldKey])) {
      Object.keys(value)?.forEach((valueKey) => {
        if (dateFieldsKeys?.includes(valueKey)) {
          tempValue = {
            ...tempValue,
            [valueKey]: getDate(value?.[valueKey]),
          };
        }
      });
    }
    if (isCheckBoxChange) {
      Object.keys(value)?.forEach((valueKey) => {
        if (checkboxFieldsKeys?.includes(valueKey)) {
          tempValue = {
            ...tempValue,
            [valueKey]: value?.[valueKey] === "1",
          };
        }
      });
    }
    statusChange(
      field_name,
      isCheckBoxChange
        ? "loading"
        : {
            status: "loading",
            message: "",
          }
    );
    if (field_name === "due_date") {
      handleChangeFieldStatus({
        field: "due_date",
        status: "loading",
        action: "API",
      });
    } else if (field_name === "supplier_id") {
      handleChangeFieldStatus({
        field: "supplier_id",
        status: "loading",
        action: "API",
      });
    }
    if (params && params.id) {
      setBillsEdit({
        onError: (message) => {
          statusChange(
            field_name,
            isCheckBoxChange
              ? ""
              : {
                  status: "error",
                  message,
                }
          );
          onError();
        },
        onSuccess: (responseData) => {
          setBillData((prev) => {
            let tempPrev = { ...prev };
            const mergedData = {
              ...tempPrev,
              ...responseData,
            };
            return mergedData;
          });

          // Handle response data here
        },
        module_id: gConfig?.module_id,
        billId: params.id,
        bill: tempValue,
        currentBill: dateFieldsKeys?.find(
          (dateFieldKey) => tempValue?.[dateFieldKey]
        )
          ? value
          : undefined,
        setSuccess: (success: boolean) => {
          if (success) {
            statusChange(
              field_name,
              isCheckBoxChange
                ? ""
                : {
                    status: "success",
                    message: "",
                  }
            );
            if (field_name === "due_date") {
              handleChangeFieldStatus({
                field: "due_date",
                status: "success",
                action: "API",
              });
            } else if (field_name === "supplier_id") {
              handleChangeFieldStatus({
                field: "supplier_id",
                status: "success",
                action: "API",
              });
            }
          }
        },
      });
    } else {
      notification.error({
        description: "Record id not available",
      });
    }
    delay(() => {
      if (field_name === "due_date") {
        handleChangeFieldStatus({
          field: "due_date",
          status: "button",
          action: "API",
        });
      } else if (field_name === "supplier_id") {
        handleChangeFieldStatus({
          field: "supplier_id",
          status: "button",
          action: "API",
        });
      }
    }, 3000);
  };

  const handleEnterPress = (
    e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setNewTermValue(e?.currentTarget?.value);
    if (e.key === "Enter") {
      const value = e?.currentTarget?.value?.trim();
      const newType = onEnterSelectSearchValue(
        e,
        termTypes?.map((type) => ({
          label: type?.name,
          value: "",
        })) || []
      );
      if (newType) {
        setNewTermValue(newType);
        setAddNewTermTypeConfirmDialogOpen(true);
        if (termSelectRef?.current) {
          termSelectRef?.current.blur();
        }
      } else if (value) {
        notification.error({
          description: "Records already exist, no new records were added.",
        });
      }
    }
  };

  const addNewTerm = () => {
    addTermType(newTermValue, (value: NewBillTermType) => {
      setAddNewTermTypeConfirmDialogOpen(false);
      onTermChange({
        term_key: value?.term_id?.toString(),
        term_name: value?.name,
      });
      setNewTermValue("");
    });
  };

  const getDate = (date: string | undefined) => {
    if (!isEmpty(date)) {
      if (date === "00/00/0000") {
        return undefined;
      }
      return dateFormat({
        date,
        dateFormat: getFormat(gSettings?.date_format),
        format: "yyyy-MM-dd",
      });
    }
    return undefined;
  };

  // ref view and redirect icon
  const purchaseOrderModuleAccess: TModuleAccessStatus = checkModuleAccessByKey(
    defaultConfig.purchase_order_module
  );
  const subContractsModuleAccess: TModuleAccessStatus = checkModuleAccessByKey(
    defaultConfig.sub_contracts_module
  );
  const purchaseOrderModule: GModule | undefined = getGModuleByKey(
    defaultConfig.purchase_order_module
  );
  const subContractsModule: GModule | undefined = getGModuleByKey(
    defaultConfig.sub_contracts_module
  );

  const refLink = useMemo(() => {
    const queryParams = `id=${billData?.reference_primary_id}`;
    if (
      purchaseOrderModule?.module_id?.toString() ===
        billData?.reference_module_id?.toString() &&
      purchaseOrderModule?.module_key
    ) {
      return getConvertedLink({
        url: routes?.MANAGE_PURCHASE_ORDERS?.url,
        moduleKey: purchaseOrderModule?.module_key,
        queryParams,
      });
    }
    return "#";
  }, [purchaseOrderModule, billData]);

  const isRefRedirectIconView = useMemo(() => {
    const accessStatusForView: TModuleAccessStatus[] = [
      "full_access",
      "own_data_access",
    ];
    return (
      accessStatusForView?.includes(purchaseOrderModuleAccess) ||
      accessStatusForView?.includes(subContractsModuleAccess)
    );
  }, [purchaseOrderModuleAccess, subContractsModuleAccess]);

  const isValidRef: boolean = useMemo(
    () => !!billData?.ref_po && !["SC0", "PO0", "0"].includes(billData?.ref_po),
    [billData]
  );

  const vendorData: Partial<CustomerSelectedData>[] = useMemo(
    () =>
      billData?.supplier_id?.toString() && billData?.supplier_name?.toString()
        ? [
            {
              display_name: replaceDOMParams(
                sanitizeString(billData?.supplier_name)
              ),
              ...(billData?.supplier_name && {
                user_id: Number(billData?.supplier_id),
              }),
              type: Number(billData?.supplier_dir_type),
              type_key: supplierKey,
              type_name: supplierKey,
              contact_id: Number(billData?.supplier_contact_id),
              image: billData?.supplier_details?.image,
            },
          ]
        : [],
    [billData, supplierKey]
  );

  // selectedContactId change when only user_id change
  const selectedContactId: number | undefined = useMemo(
    () => vendorData?.[0]?.user_id,
    [vendorData?.[0]?.user_id]
  );
  const refOptions: DefaultOptionType[] = useMemo(() => {
    const getRefOption = (data: IBillRefernce) => ({
      label: `${data?.module_prefix} ${data?.prefix_company_primary_id} : ${data?.subject}`,
      value: data?.primary_id ?? "",
    });

    let tempRefOptions: DefaultOptionType[] = [
      {
        label: "Select a reference PO or Sub-Contract",
        value: "PO0",
        disabled: true,
      },
    ];
    const isRefAvail = billRefDetail.filter(
      (ele) => ele.primary_id === billData?.ref_po
    );
    if (
      billData?.ref_po !== "0" &&
      billRefDetail?.length &&
      !isRefAvail.length &&
      isValidRef
    ) {
      tempRefOptions.push({
        label: billData.reference_po_name,
        value: billData.ref_po,
      });
    }
    if (Number(billData.project_id) > 0) {
      tempRefOptions.push(...(billRefDetail?.map(getRefOption) ?? []));
    } else if (isValidRef && billData.project_id === "0") {
      const refOption = billRefDetail.find((data: IBillRefernce) => {
        return data.primary_id === billData?.ref_po;
      });
      if (refOption) {
        tempRefOptions.push(getRefOption(refOption));
      }
    }
    return tempRefOptions;
  }, [billRefDetail, billData, isValidRef]);

  useEffect(() => {
    if (!!billData?.project_id && !!billData?.supplier_id) {
      setBillReferenceDetails({
        projectId: billData.project_id,
        vendorId: billData?.supplier_id ?? "",
      });
    }
  }, [billData?.supplier_id, billData?.project_id]);

  useEffect(() => {
    setTermTypes();
  }, []);

  useEffect(() => {
    const billData = {
      ...billDetails,
      supplier_contact_id: billDetails.supplier_contact_id?.toString(),
    };
    setBillData(billData);
  }, [billDetails]);

  useEffect(() => {
    if (addNewTermTypeConfirmDialogOpen) {
      if (addTermConfirmButtonRef?.current) {
        addTermConfirmButtonRef?.current?.focus();
      }
    }
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.key === "Enter") {
        addTermConfirmButtonRef?.current?.focus();
      }
    };
    document.addEventListener("keydown", handleKeyPress);
    return () => {
      document.removeEventListener("keydown", handleKeyPress);
    };
  }, [addNewTermTypeConfirmDialogOpen]);

  const refValue = refOptions?.find(
    (refOption) => billData?.ref_po === refOption?.value
  )?.value;

  const { status: supplierIdStatus, ...supplierIdStatusProps } =
    getInputProps("supplier_id");

  const supplierOptions = useMemo(() => {
    let options: CustomerTabs[] = [
      defaultConfig.contractor_key,
      defaultConfig.vendor_key,
      defaultConfig.misc_contact_key,
      "by_service",
    ];

    if (billDetails.project_id && billDetails.project_id !== "0") {
      options.push("my_project");
    }
    return options;
  }, [billDetails.pro_id, defaultConfig]);

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );
    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) =>
        prevState.map((item) =>
          item.field === field ? { ...item, status: status } : item
        )
      );
    }
  };

  const quickbookSync = useMemo(
    () =>
      Boolean(Number(gSettings?.quickbook_sync)) ||
      Boolean(Number(gSettings?.quickbook_desktop_sync)),
    [gSettings?.quickbook_sync, gSettings?.quickbook_desktop_sync]
  );

  const defaultIsBillBillable = useMemo(
    () => (quickbookSync ? Boolean(gSettings?.is_bill_billable) : true),
    [gSettings?.is_bill_billable, quickbookSync]
  );

  useEffect(() => {
    if (billData) {
      billData.project_id &&
        setSelectedProject({
          id: billData.project_id?.toString(),
          project_name: billData.project_name,
        } as IProject);
    }
  }, [JSON.stringify(billData)]);

  return (
    <>
      <GradientIconCard
        title="Details"
        headerProps={{
          containerClassName: "!items-start flex-nowrap",
        }}
        iconProps={{
          icon: "fa-solid fa-file-lines",
          containerClassName:
            "bg-[linear-gradient(180deg,#FF868A1a_0%,#FD33331a_100%)]",
          id: "module_details_icon",
          colors: ["#FF868A", "#FD3333"],
        }}
        rightSideClassName="sm:max-w-[calc(100%-110px)] w-full"
        headerRightButton={
          <div className="flex md:flex-row flex-wrap justify-end flex-col md:items-center items-end gap-2.5">
            <div className="flex gap-2">
              <Tooltip
                title={
                  (typeof billData?.is_billable === "boolean"
                    ? billData?.is_billable
                    : billData?.is_billable?.toString() === "1") &&
                  !(getCheckboxProps("is_billable").status === "loading") &&
                  "This Item is Billable to the Customer"
                }
              >
                <CustomCheckBox
                  className="gap-1.5"
                  name="is_billable"
                  onChange={(e: CheckboxChangeEvent) => {
                    setBillData((prev) => {
                      let tempPrev = prev;

                      const value = {
                        is_billable: e?.target?.checked ? "1" : "0",
                      };

                      updateBillDetail({
                        field_name: "is_billable",
                        value,
                        onError: () => {
                          setBillData(prev);
                        },
                      });
                      return { ...tempPrev, ...value } as Partial<BillDetails>;
                    });
                  }}
                  checked={
                    typeof billData?.is_billable === "boolean"
                      ? billData?.is_billable
                      : billData?.is_billable?.toString() === "1"
                  }
                  disabled={gConfig?.module_read_only}
                  loadingProps={{
                    isLoading:
                      getCheckboxProps("is_billable").status === "loading",
                    className: "bg-[#ffffff]",
                  }}
                >
                  {_t("Billable")}
                </CustomCheckBox>
              </Tooltip>
              {Boolean(Number(billData.project_id)) &&
                billData?.show_client_access === "1" &&
                Boolean(Number(billData?.is_access)) && (
                  <CFCheckBox
                    className="gap-1.5 text-primary-900 font-medium dark:text-white/90"
                    label="Share with Client"
                    name="is_shared"
                    checked={
                      typeof billData?.is_shared === "boolean"
                        ? billData?.is_shared
                        : billData?.is_shared?.toString() === "1"
                    }
                    iconView={true}
                    disabled={gConfig?.module_read_only}
                    {...getCheckboxProps("is_shared")}
                    onChange={(e: CheckboxChangeEvent) => {
                      setBillData((prev) => {
                        let tempPrev = prev;
                        const value = {
                          is_shared: e?.target?.checked ? "1" : "0",
                        };
                        updateBillDetail({
                          field_name: "is_shared",
                          value,
                          onError: () => {
                            setBillData(prev);
                          },
                        });
                        return {
                          ...tempPrev,
                          ...value,
                        } as Partial<BillDetails>;
                      });
                    }}
                  />
                )}
            </div>

            <CFTypography
              title="small"
              className="px-2.5 py-[5px] block font-medium rounded bg-blue-50 dark:bg-dark-500 text-sm text-primary-900 dark:text-white/90"
            >
              Balance Due:{" "}
              {Number(FilterBalanceDue) <= 0
                ? formatter(formatAmount(+0)).value_with_symbol
                : formatter(formatAmount(Number(FilterBalanceDue)))
                    .value_with_symbol}
            </CFTypography>
          </div>
        }
      />
      <div className="pt-2">
        <ul className="w-full grid sm:gap-1 gap-2">
          <li className="overflow-hidden">
            <ButtonField
              name="supplier_id"
              label={_t("Vendor")}
              labelPlacement="left"
              placeholder={_t("Select/View Vendor")}
              editInline={true}
              iconView={true}
              onClick={() => {
                setIsSelectVendorOpen(true);
              }}
              readOnly={gConfig?.module_access === "read_only"}
              value={
                (vendorData &&
                  HTMLEntities.decode(
                    sanitizeString(vendorData[0]?.display_name)
                  )) ||
                ""
              }
              avatarProps={
                vendorData?.[0]?.display_name
                  ? {
                      user: {
                        name: HTMLEntities.decode(
                          sanitizeString(vendorData?.[0]?.display_name)
                        ),
                        image:
                          !vendorData?.[0]?.contact_id &&
                          !!vendorData?.[0]?.image
                            ? vendorData?.[0]?.image
                            : "",
                      },
                    }
                  : undefined
              }
              statusProps={{
                status: getStatusForField(loadingStatus, "supplier_id"),
                className: "right-6 flex",
                iconProps: {
                  className: "!w-[15px] !h-[15px]",
                },
              }}
              disabled={
                getStatusForField(loadingStatus, "supplier_id") === "loading"
              }
              mainReadOnlyClassName="sm:w-fit max-w-full"
              headerTooltip={`Vendor: ${HTMLEntities.decode(
                sanitizeString(vendorData[0]?.display_name)
              )}`}
              isDisabled={
                gConfig?.module_access === "read_only" ||
                getStatusForField(loadingStatus, "directory_id") === "loading"
              }
              rightIcon={
                vendorData[0]?.user_id ? (
                  <div className="flex items-center gap-1">
                    <ContactDetailsButton
                      onClick={() => {
                        setContactDetailDialogOpen(true);
                      }}
                    />
                    {vendorData[0]?.user_id && (
                      <DirectoryFieldRedirectionIcon
                        className="!w-5 !h-5"
                        directoryId={vendorData[0]?.user_id.toString()}
                        directoryTypeKey={vendorData[0]?.type_key || ""}
                      />
                    )}
                  </div>
                ) : (
                  <></>
                )
              }
            />
          </li>
          <li>
            <InputNumberField
              label="Amount"
              placeholder="0.00"
              className="!text-primary-900 placeholder:!text-[#bdbdbd]"
              labelPlacement="left"
              disabled={true}
              value={Number(billData?.total) / 100}
              parser={(value) => {
                if (!value) return "";
                const inputValue = unformatted(value.toString());
                return inputValue;
              }}
              formatter={(value) => {
                return Number(value)
                  ? inputFormatter(Number(value)?.toFixed(2)).value
                  : "";
              }}
              prefix={inputFormatter().currency_symbol}
            />
          </li>
          <li className="overflow-hidden">
            <CFSelect
              editInline={true}
              iconView={true}
              label="Ref.#"
              showSearch={false}
              formInputClassName="edit-inline-select"
              labelPlacement="left"
              maxWidthClass="sm:max-w-[calc(100%-171px)]"
              name="ref_po"
              allowClear={true}
              placeholder={"Select Ref.#"}
              options={refOptions}
              readOnly={gConfig?.module_read_only}
              value={isValidRef ? refValue : undefined}
              disabled={billData?.project_id === "0" && isValidRef}
              {...getInputProps("ref_po")}
              onClick={() => {
                if (
                  billData &&
                  billData.project_id !== undefined &&
                  billData.project_id !== null &&
                  billData.project_id.toString() === "0" &&
                  refValue &&
                  ["SC0", "PO0", "0"].includes(refValue.toString())
                ) {
                  notification.error({
                    description: "Please select project to Get Reference #.",
                  });
                }
              }}
              addonBefore={
                Number(billRefDetail?.length) > 0 &&
                isValidRef &&
                billData?.project_id &&
                isRefRedirectIconView &&
                Number(billData?.reference_primary_id) > 0 && (
                  <div className="absolute top-1/2 -translate-y-1/2 right-0 z-20 right-from-square-icon focus-within:bg-[#f0f0f0] rounded focus-visible:outline-none hover:!bg-[#f0f0f0]">
                    {subContractsModule?.module_id?.toString() ===
                      billData?.reference_module_id?.toString() &&
                    subContractsModule?.module_key ? (
                      <SubContractsFieldRedirectionIcon
                        subContractId={billData?.reference_primary_id}
                      />
                    ) : (
                      <FieldRedirectButton
                        href={`${routes.MANAGE_PURCHASE_ORDERS.url}/${billData?.reference_primary_id}`}
                        tooltipTitle={_t("Open the detail in a new tab")}
                      />
                    )}
                  </div>
                )
              }
              onChange={(value) => {
                setBillData((prev) => {
                  let tempPrev = prev;

                  const updatedRefPoObj = {
                    ref_po: value ? value : 0,
                  };

                  updateBillDetail({
                    field_name: "ref_po",
                    value: updatedRefPoObj,
                    onError: () => {
                      setBillData(prev);
                    },
                  });
                  return {
                    ...tempPrev,
                    ...updatedRefPoObj,
                  } as Partial<BillDetails>;
                });
              }}
            />
          </li>
          <li>
            <CFDatePicker
              asSingle={true}
              editInline={true}
              iconView={true}
              label="Bill Date"
              labelPlacement="left"
              name="order_date"
              value={getDate(billData?.order_date)}
              readOnly={gConfig?.module_read_only}
              displayFormat={gSettings?.date_format}
              onChange={onOrderDateChange}
              allowClear={false}
              {...getInputProps("order_date")}
            />
          </li>
          <li>
            <DatePickerField
              label={_t("Due Date")}
              labelPlacement="left"
              name="due_date"
              placeholder={_t("Select Date")}
              editInline={true}
              iconView={true}
              value={
                billData &&
                billData?.due_date &&
                !billData?.due_date?.includes("0000")
                  ? displayDateFormat(
                      billData?.due_date?.toString().trim(),
                      gSettings?.date_format
                    )
                  : undefined
              }
              fixStatus={getStatusForField(loadingStatus, "due_date")}
              readOnly={gConfig?.module_read_only}
              onChange={onDueDateChange}
              format={gSettings?.date_format}
            />
          </li>
          <li className="overflow-hidden">
            <CFSelect
              name="bill_terms"
              label="Terms"
              labelPlacement="left"
              placeholder="Select Terms"
              message={{
                text: "Terms Name & Press Enter",
                icon: "fa-regular fa-plus",
              }}
              allowClear={true}
              options={termTypes
                ?.filter((type: InvoiceTerm) => {
                  const cleanedName = sanitizeString(type?.name).trim();
                  return type?.term_id && cleanedName.length > 0;
                })
                .map((type: InvoiceTerm) => ({
                  label: HTMLEntities.decode(sanitizeString(type?.name)),
                  value: type?.term_id,
                }))}
              ref={termSelectRef}
              value={
                newTermValue
                  ? newTermValue
                  : termTypes?.find(
                      (termType) =>
                        termType?.term_id?.toString() === billData?.term_key
                    )?.term_id
              }
              iconView={true}
              editInline={true}
              className="cf-singal-selection"
              readOnly={gConfig?.module_read_only}
              onInputKeyDown={handleEnterPress}
              {...getInputProps("bill_terms")}
              onChange={(
                value: string,
                options: any // change this in future ( temporary resolve type issue )
              ) => {
                if (isEmpty(value)) {
                  if (termSelectRef?.current) {
                    termSelectRef?.current?.focus();
                  }
                } else if (!Array.isArray(options)) {
                  onTermChange({
                    term_key: options?.value?.toString() ?? "",
                    term_name: options?.label?.toString() ?? "",
                  });
                }
              }}
              onClear={() => {
                onTermChange();
              }}
            />
          </li>
          <li>
            <CFInlineTextarea
              placeholder="Bill Description"
              label="Description"
              iconView={true}
              labelPlacement="left"
              value={billData?.notes}
              readOnly={gConfig?.module_read_only}
              {...getInputProps("notes")}
              onBlur={(value: string) => {
                setBillData((prev) => {
                  let tempPrev = prev;
                  if (value.trim() !== tempPrev?.notes?.trim()) {
                    const updatedRefPoObj = {
                      notes: value,
                    };
                    tempPrev = {
                      ...tempPrev,
                      ...updatedRefPoObj,
                    };
                    updateBillDetail({
                      field_name: "notes",
                      value: updatedRefPoObj,
                      onError: () => {
                        setBillData(prev);
                      },
                    });
                  }
                  return tempPrev;
                });
              }}
            />
          </li>
        </ul>
      </div>
      <ContactDetailsModal
        isOpenContact={contactDetailDialogOpen}
        onCloseModal={() => setContactDetailDialogOpen(false)}
        contactId={selectedContactId}
        readOnly={gConfig?.module_read_only}
        additional_contact_id={
          billDetails?.supplier_contact_id !== "0"
            ? billDetails?.supplier_contact_id
            : 0
        }
      />
      <AddTermTypeConfirmModal
        open={addNewTermTypeConfirmDialogOpen}
        message={_t(
          `This will add "${newTermValue}" to the list. Do you want to add it?`
        )}
        handelModal={() => {
          setAddNewTermTypeConfirmDialogOpen(false);
          setNewTermValue("");
        }}
        buttonRef={addTermConfirmButtonRef}
        loading={termTypeLoading}
        onClick={addNewTerm}
      />
      {isSelectVendorOpen && (
        <SelectCustomerDrawer
          closeDrawer={() => {
            // dispatch(SetActiveField(defaultConfig.employee_key));
            setIsSelectVendorOpen(false);
          }}
          singleSelecte={true}
          options={
            (isSelectVendorOpen
              ? [
                  defaultConfig.contractor_key,
                  defaultConfig.vendor_key,
                  defaultConfig.misc_contact_key,
                  "by_service",
                ]
              : []
            ).concat("my_project") as CustomerEmailTab[]
          }
          projectId={
            selectedProject
              ? Number(selectedProject.id) || Number(selectedProject?.key)
              : 0
          }
          openSelectCustomerSidebar={isSelectVendorOpen}
          setCustomer={(data) => {
            if (isSelectVendorOpen) {
              if (data.length) {
                onVendorChange(data[0]);
              } else {
                notification.error({
                  description: "Vendor field is required.",
                });
              }
            }
          }}
          selectedCustomer={
            isSelectVendorOpen ? (vendorData ? vendorData : []) : []
          }
          groupCheckBox={isSelectVendorOpen ? false : true}
          is_all_tab={true}
        />
      )}
    </>
  );
};

export default memo(BillsDetailsCard);
