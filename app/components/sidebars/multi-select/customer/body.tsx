import { CFButton } from "~/components/third-party/ant-design/cf-button";
import { CFCheckBox } from "~/components/third-party/ant-design/cf-checkbox";
import { CFSearchInput } from "~/components/third-party/ant-design/cf-search-input";
import { CFTypography } from "~/components/third-party/ant-design/cf-typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import isEmpty from "lodash/isEmpty";
import debounce from "lodash/debounce";
import { defaultConfig } from "~/data";
import EmployeeList from "./employee-list";
import MyCrewList from "./my-crew-list";
import MyProject from "./my-project";
import ItemList from "./item-list";
import ByServiceList from "./by-service-list";
import ItemVendorList from "./item-vendor-list";
import { useTranslation } from "~/hook";
import {
  onChangeFavourites,
  resetCustomersByTab,
  setCustomerProject,
  setCustomers,
} from "./zustand/action";
import {
  getCutomerAllData,
  getCutomerFavoritesOnly,
  getCutomerPageNumber,
  getCutomers,
  getCutomerSearch,
  getProjectContacts,
} from "./zustand/store";
import { getGConfig, getSingleProjectData } from "~/zustand";
import { CheckboxChangeEvent } from "antd/es/checkbox";
import { customerFavoritTabs } from "./zustand/utils";
import ListOfSeletedCustomers from "./list-of-seleted-customers";
import CustomerList from "./customer-list";
// molecules
import { IconButton } from "~/shared/components/molecules/iconButton";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { ACTIVE_FIELD_NAME } from "~/shared/components/molecules/selectCustomerDrawer/constants";
import { getGlobalUser } from "~/zustand/global/user/slice";

interface CustomerSelectBodyProps {
  selectedOption: CustomerTabs;
  items: Array<Partial<CustomerSelectedData>>;
  options?: CustomerTabs[];
  // selectedData?:
  //   | Partial<CustomerSelectedData>
  //   | Array<Partial<CustomerSelectedData>>;
  selectedData?: any; // it will update in future
  currentSelected?: CustomerSelectOption<CustomerTabs>;
  onClickItem: (item: Partial<CustomerSelectedData>) => void;
  onSave: (items: Partial<CustomerSelectedData>[]) => void;
  singleSelecte: boolean;
  isMail?: boolean;
  buttonText: string;
  showDirectoryContact?: boolean;
  app_access?: boolean;
  additionalContactDetails?: number;
  appUsers?: number;
}

let isFetching = false;
const CustomerSelectBody = ({
  selectedOption,
  items,
  currentSelected,
  options,
  onClickItem,
  onSave,
  singleSelecte,
  buttonText,
  isMail,
  selectedData,
  showDirectoryContact,
  additionalContactDetails,
  app_access,
  appUsers = 1,
}: CustomerSelectBodyProps) => {
  const [searchString, setSearchString] = useState<string>(" ");
  const [viewSearch, setViewSearch] = useState<boolean>(false);
  const [favoriteTabs, setFavoriteTabs] = useState<{
    [tabId: string]: boolean;
  }>({});
  const search: string = getCutomerSearch(selectedOption);
  const favoritesOnly: boolean | undefined =
    getCutomerFavoritesOnly(selectedOption);
  const [loading, setLoading] = useState<boolean>(false);
  let { _t } = useTranslation();

  const [page, setPage] = useState<number>(1);
  const gotAllData: boolean = getCutomerAllData(selectedOption, page);
  const [bottomSheetOpen, setBottomSheetOpen] = useState<boolean>(false);
  const dataList: CustomerSideData[] = getCutomers(selectedOption, page);
  const customerLastPageNumber: number = getCutomerPageNumber(selectedOption);
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const gConfig: GConfig = getGConfig();
  const project = getSingleProjectData();
  const projectContact = getProjectContacts();
  const setData = (params?: SetCustomersData) => {
    isFetching = true;
    setLoading(isFetching);
    if (selectedOption === "my_project") {
      let projectId = project?.id?.toString();
      if (projectId) {
        setCustomerProject(selectedOption, {
          gUser: user,
          projectId,
          search: params?.search ?? "",
          additionalContactDetails: additionalContactDetails,
          callComplete: () => {
            isFetching = false;
            setLoading(isFetching);
          },
        });
      }
    } else {
      const userID = (selectedData || [])
        .filter(
          (customerData) =>
            customerData?.contact_id == "0" ||
            customerData?.contact_id == undefined
        )
        .map((customerData) => customerData?.user_id?.toString())
        .filter((userId): userId is string => Boolean(userId));
      const contactId = (selectedData || [])
        .filter(
          (customerData) =>
            customerData?.contact_id && customerData.contact_id != "0"
        )
        .map((customerData) => customerData?.contact_id?.toString());
      setCustomers(selectedOption, {
        gUser: user,
        search: params?.search ?? "",
        gConfig,
        appUsers: appUsers,
        page: params?.page ?? 1,
        callComplete: () => {
          isFetching = false;
          setLoading(isFetching);
        },
        selected_users: params?.selectedData?.length
          ? contactId.length > 0
            ? {
                contact_id: String(contactId),
                ...(userID?.length > 1
                  ? { user_id: userID ? String(userID) : undefined }
                  : {}),
              }
            : { user_id: userID ? String(userID) : undefined }
          : {},
        app_access: app_access,
        is_favorite: favoriteTabs[selectedOption] || false,
        additionalContactDetails,
      });
    }
  };

  useEffect(() => {
    if (selectedOption === "my_project") {
      isFetching = true;
      setLoading(isFetching);
      let projectId = project?.id?.toString();
      if (projectId) {
        setCustomerProject(selectedOption, {
          gUser: user,
          projectId,
          search: searchString,
          additionalContactDetails: additionalContactDetails,
          callComplete: () => {
            isFetching = false;
            setLoading(isFetching);
          },
        });
      }
    }
  }, [selectedOption, user, project]);

  const setCustomerData = useCallback(
    (
      isFavorite: boolean | undefined,
      page: number,
      search: string,
      selectedData: Array<Partial<CustomerSelectedData>>
    ) => {
      setData({ is_favorite: isFavorite, page, search, selectedData });
    },
    [customerLastPageNumber, selectedOption, selectedData, favoriteTabs]
  );

  useEffect(() => {
    setCustomerData(favoriteTabs[selectedOption], page, search, selectedData);
  }, [gConfig, user, favoriteTabs, selectedData]);

  useEffect(() => {
    setSearchString(search);
  }, [search]);

  useEffect(() => {
    setPage(1);
    setSearchString("");
    setCustomerData(false, 1, "", selectedData);
  }, [selectedOption]);

  const onChangeFav = (data: Customer) => {
    if (!!data) {
      onChangeFavourites(selectedOption, {
        data,
        favoritesOnly: favoriteTabs[selectedOption],
      });
    }
  };

  const handleSearch = (value: number | string) => {
    resetCustomersByTab(selectedOption);
    setData({
      search: value as string,
      is_favorite: favoriteTabs[selectedOption] || false,
      selectedData,
    });
    setPage(1);
  };
  const debounceOnChange = React.useCallback(debounce(handleSearch, 800), [
    gConfig,
    user,
    selectedOption,
    favoriteTabs[selectedOption] || false,
  ]);

  const observer = useRef<IntersectionObserver | null>();
  const lastElementRef = useCallback(
    (node: HTMLDivElement | null) => {
      if (loading || gotAllData) return;
      if (observer.current) observer.current.disconnect();
      observer.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting) {
          if (customerLastPageNumber < page + 1) {
            setData({
              search,
              is_favorite: favoriteTabs[selectedOption] || false,
              page: page + 1,
              selectedData,
            });
          }
          setPage(page + 1);
        }
      });
      if (node) observer.current.observe(node);
    },
    [
      loading,
      gotAllData,
      gConfig,
      user,
      search,
      page,
      customerLastPageNumber,
      favoriteTabs,
      favoritesOnly,
      selectedData,
    ]
  );

  const getSelectedCom = () => {
    const listProps = {
      items,
      onClickItem,
      fetching: loading,
      lastElementRef,
      getAllData: gotAllData,
      page,
      noRecordsData: (
        <div className="md:h-[calc(100dvh-120px)] h-[calc(100dvh-200px)] flex justify-center items-center">
          <NoRecords
            image={`${window.ENV.CDN_URL}assets/images/no-records-contact.svg`}
          />
        </div>
      ),
    };

    const filteredDataList = showDirectoryContact
      ? dataList
      : dataList.filter((item) => item.type_key !== "contact"); // Filter out 'contact' type

    if (selectedOption === defaultConfig.employee_key) {
      return (
        <EmployeeList {...listProps} data={filteredDataList as Customer[]} />
      );
    } else if (selectedOption === "my_crew") {
      let newCrew = dataList;
      newCrew = newCrew.sort((a, b) =>
        a.group_name.localeCompare(b.group_name)
      );
      newCrew.forEach((group: GroupEmailDetail, i) => {
        newCrew[i].employees = group?.employees.filter((employee) =>
          options?.includes(employee?.type_key as string)
        );
      });
      return (
        <MyCrewList
          {...listProps}
          data={newCrew as GroupDetail[]}
          singleSelecte={singleSelecte}
        />
      );
    } else if (selectedOption === "my_project") {
      return (
        <MyProject {...listProps} data={projectContact as projectContact[]} />
      );
    } else if (selectedOption === defaultConfig.lead_key) {
      return (
        <ItemList
          {...listProps}
          data={filteredDataList as Customer[]}
          onChangeFav={onChangeFav}
          activeField={selectedOption}
        />
      );
    } else if (selectedOption === defaultConfig.customer_key) {
      return (
        <CustomerList {...listProps} data={filteredDataList as Customer[]} />
      );
    } else if (customerFavoritTabs?.includes(selectedOption)) {
      return (
        <ItemVendorList
          {...listProps}
          data={filteredDataList as Customer[]}
          onChangeFav={onChangeFav}
          activeField={selectedOption}
        />
      );
    } else if (selectedOption === "by_service") {
      return (
        <ByServiceList
          {...listProps}
          data={filteredDataList as DirectoryService[]}
          singleSelecte={singleSelecte}
          options={options}
        />
      );
    }
    return <></>;
  };

  const handleFavoriteChange = (tabId: string, isChecked: boolean) => {
    setFavoriteTabs(() => {
      const setAllTabs = (value: boolean) => {
        const updatedFavorites: { [tabId: string]: boolean } = {};
        options?.forEach((id) => {
          if (
            id === defaultConfig.vendor_key ||
            id === defaultConfig.contractor_key ||
            id === defaultConfig.misc_contact_key ||
            id === defaultConfig.lead_key
          ) {
            updatedFavorites[id] = value;
          }
        });
        return updatedFavorites;
      };
      if (!isChecked) {
        return setAllTabs(false);
      }
      return setAllTabs(true);
    });
  };

  return (
    <div className="flex lg:flex-row flex-col h-full">
      <div className="w-full xl:max-w-[630px] md:flex-[1_0_0%] border-r border-gray-300 dark:border-white/10">
        <div className="py-3 sm:pl-[15px] sm:pr-[15px] pl-1.5 pr-3.5 flex items-center justify-between whitespace-nowrap gap-3">
          <div className="w-full flex items-center whitespace-nowrap gap-3">
            <div className="flex items-center justify-center w-10 md:w-full">
              <IconButton
                htmlType="button"
                className="w-7 !m-0 group/handle-visible-change max-w-[28px] max-h-[28px] block md:hidden"
                variant="text"
                onClick={() => {
                  setViewSearch((prev) => !prev);
                }}
              >
                <FontAwesomeIcon
                  className="text-base w-5 h-5 !text-primary-gray-80 dark:!text-white/90"
                  icon="fa-regular fa-magnifying-glass"
                />
              </IconButton>
              <div
                className={`dashboard-header-search md:static md:translate-x-0 absolute w-full z-10 md:!bg-transparent bg-white dark:bg-[#1E2732] -translate-x-full ease-in left-0 ${
                  viewSearch ? "translate-x-0" : ""
                }`}
              >
                <div className="flex items-center md:px-0 px-2.5">
                  <IconButton
                    htmlType="button"
                    className="w-6 group/handle-visible-change max-w-[24px] max-h-[24px] md:hidden mr-2.5"
                    variant="text"
                    onClick={() => {
                      setViewSearch((prev) => !prev);
                    }}
                  >
                    <FontAwesomeIcon
                      className="text-base w-[18px] h-[18px] text-primary-900/80 group-hover/handle-visible-change:text-primary-900 dark:!text-white/90"
                      icon="fa-regular fa-angle-left"
                    />
                  </IconButton>
                  <CFSearchInput
                    value={searchString}
                    placeholder={_t(
                      `Search for ${
                        ACTIVE_FIELD_NAME[
                          currentSelected?.value as CustomerEmailTab
                        ]
                      }`
                    )}
                    onChange={(e) => {
                      const { value } = e.target;
                      setSearchString(value);
                      debounceOnChange(value);
                    }}
                    beforeName=""
                    className="border border-solid border-gray-200 focus:!border-primary-900 hover:!border-primary-900 focus-within:!border-primary-900 search-with-border"
                  />
                </div>
              </div>
            </div>
            {(selectedOption === defaultConfig.vendor_key ||
              selectedOption === defaultConfig.contractor_key ||
              selectedOption === defaultConfig.misc_contact_key ||
              selectedOption === defaultConfig.lead_key) && (
              <CFCheckBox
                className="gap-1.5 text-primary-900"
                label="Show Favourites Only"
                name="show_only"
                checked={favoriteTabs[selectedOption] || false}
                onChange={async (e: CheckboxChangeEvent) => {
                  // Wait for handleFavoriteChange to complete
                  await handleFavoriteChange(selectedOption, e.target.checked);
                  resetCustomersByTab(selectedOption);
                  setPage(1);
                }}
              />
            )}
          </div>
          <CFTypography
            title="h5"
            className="!text-[15px] !mb-0 block lg:hidden"
            onClick={() => {
              setBottomSheetOpen(true);
            }}
          >
            {_t("Selected")} {items?.length > 0 ? `(${items?.length})` : ""}
          </CFTypography>
        </div>
        {getSelectedCom()}
        {!singleSelecte && (
          <div className="sidebar-footer flex items-center justify-center w-full p-[15px] lg:hidden">
            <CFButton
              variant="primary"
              className="w-full primary-btn justify-center"
              onClick={() => onSave(items)}
            >
              {_t(buttonText)}
            </CFButton>
          </div>
        )}
      </div>
      <div
        className={`z-20 xl:w-[340px] xl:min-w-[340px] xl:max-w-[340px] lg:w-[280px] lg:min-w-[280px] lg:max-w-[280px] w-full lg:bg-white bg-primary-gray-20/60 dark:bg-dark-800 lg:dark:bg-[#15202B] flex-[1_0_0%] max-md:overflow-hidden ${
          bottomSheetOpen ? "fixed top-0 h-full" : "lg:h-full h-0"
        }`}
      >
        {bottomSheetOpen && (
          <div
            className="lg:hidden block fixed left-0 bg-black/20 h-full w-full"
            onClick={() => {
              setBottomSheetOpen(false);
            }}
          ></div>
        )}
        <div
          className={`transition-all ease-in-out duration-300 bg-white lg:rounded-none rounded-t-lg max-lg:fixed w-full z-10 ${
            bottomSheetOpen
              ? "h-[calc(100vh-150px)] bottom-0 left-0"
              : "lg:h-full h-0 max-lg:-bottom-[100%]"
          }`}
        >
          <div className="py-[13px] px-[15px] flex items-center relative before:absolute before:left-0 before:bottom-0 before:w-full before:h-px before:bg-[linear-gradient(90deg,#3f4c653d_24%,#3f4c6500_100%)] dark:before:bg-[linear-gradient(90deg,#343f54_24%,#1e2732_100%)]">
            <div className="flex items-center justify-center rounded-full mr-[7px] w-9 h-9 bg-gray-200/50 dark:bg-dark-500">
              <FontAwesomeIcon
                className="w-[18px] h-[18px] text-primary-900 dark:text-white/90"
                icon="fa-regular fa-box-check"
              />
            </div>
            <div>
              <CFTypography
                title="h5"
                className="!text-[15px] !mb-0 !text-primary-900 dark:!text-white/90"
              >
                {_t("Currently Selected")}
                {items?.length > 0 ? ` (${items?.length})` : ""}
              </CFTypography>
              <CFTypography className="text-[11px] opacity-60 text-primary-900 dark:text-white/90 !mb-0 font-normal">
                {_t("Selected contacts will appear here!")}
              </CFTypography>
            </div>
          </div>
          <div className="lg:h-[calc(100dvh-175px)] h-[calc(100dvh-218px)] lg:overflow-y-hidden overflow-y-auto">
            <ListOfSeletedCustomers
              employees={items}
              onClickEmployee={onClickItem}
              isMail={isMail}
            />
          </div>
          {!singleSelecte && (
            <div className="sidebar-footer flex items-center justify-center w-full p-[15px] max-lg:hidden">
              <CFButton
                variant="primary"
                className="w-full primary-btn justify-center"
                onClick={() => onSave(items)}
              >
                {_t(buttonText)}
              </CFButton>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CustomerSelectBody;
