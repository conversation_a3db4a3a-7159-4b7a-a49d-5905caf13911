import { create } from "zustand";
import { selectedCustomersMoveFirst } from "./helper";
import { customerGroupTabs, customersDataLimit } from "./utils";

export const customerInitialState: InitialStateCustomers = {
  customers: {
    all: {
      list: [],
      pageNumber: 0,
      gotAllData: false,
      search: "",
    },
    employee: {
      list: [],
      pageNumber: 0,
      gotAllData: false,
      search: "",
    },
    my_crew: {
      list: [],
      pageNumber: 0,
      gotAllData: false,
      search: "",
    },
    customer: {
      list: [],
      pageNumber: 0,
      gotAllData: false,
      search: "",
    },
    contractor: {
      list: [],
      pageNumber: 0,
      gotAllData: false,
      search: "",
      favoritesOnly: false,
    },
    vendor: {
      list: [],
      pageNumber: 0,
      gotAllData: false,
      search: "",
      favoritesOnly: false,
    },
    misc_contact: {
      list: [],
      pageNumber: 0,
      gotAllData: false,
      search: "",
      favoritesOnly: false,
    },
    lead: {
      list: [],
      pageNumber: 0,
      gotAllData: false,
      search: "",
    },
    by_service: {
      list: [],
      pageNumber: 0,
      gotAllData: false,
      search: "",
    },
    my_project: {
      list: [],
      pageNumber: 0,
      gotAllData: false,
      search: "",
    },
  },
  directories: [],
  existingCustomers: {},
  projectContacts: [],
  sendEmail: false,
};

export const useCutomers = create<InitialStateCustomers>(() => ({
  ...customerInitialState,
}));

// below comment code will remove when apply new component
export const getCutomers = (
  tab: CustomerTabs,
  // selectedData:
  //   | undefined
  //   | Partial<CustomerSelectedData>
  //   | Partial<CustomerSelectedData>[] = [],
  page: number
) => {
  const customersList = useCutomers((s) => s.customers[tab]?.list);
  // return customersList
  //   ? !customerGroupTabs.includes(tab)
  //     ? selectedCustomersMoveFirst({
  //         selectedData,
  //         dataItems: customersList?.slice(0, page * customersDataLimit) as (
  //           | Customer
  //           | GroupDetail
  //         )[],
  //         tab,
  //       })
  //     : customersList?.slice(0, page * customersDataLimit)
  //   : [];
  return customersList;
};

export const getCutomerPageNumber = (tab: CustomerTabs) => {
  return useCutomers((s) => s.customers[tab]?.pageNumber);
};
export const getCutomerSearch = (tab: CustomerTabs) => {
  return useCutomers((s) => s.customers[tab]?.search);
};
export const getCutomerFavoritesOnly = (tab: CustomerTabs) => {
  return useCutomers((s) => s.customers[tab]?.favoritesOnly);
};

export const getCutomerAllData = (tab: CustomerTabs, page: number) => {
  const gotAllData = useCutomers((s) => s.customers[tab]?.gotAllData);
  const dataLength = useCutomers((s) => s.customers[tab]?.list?.length);
  const requestedDataCount = page * customersDataLimit;
  // Backend side in service data pagination not implemented yet because of dependecy issue, that why i commont this line
  // If gotAllData flag is true, no need to fetch
  if (gotAllData) {
    return true;
  }

  if (requestedDataCount > dataLength) {
    return true;
  }
  // if (gotAllData) {
  //   return page * customersDataLimit > dataLength;
  // }
  return gotAllData;
};

export const useDirectory = () => {
  const directories = useCutomers((s) => s.directories);
  return {
    getDirectory: (id: number) =>
      directories?.find((directory: Directory) => directory?.user_id === id),
  };
};

export const getProjectContacts = () => {
  return useCutomers((s) => s.projectContacts);
};
export const getSendEmailOpen = () => {
  return useCutomers((s) => s.sendEmail);
};
