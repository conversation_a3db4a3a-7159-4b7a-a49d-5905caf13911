// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { getUniqueIdDirData } from "~/shared/components/molecules/sendEmailDrawer/utils";
// Other
import { CFDrawer } from "~/components/third-party/ant-design/cf-drawer";
import { CFIconButton } from "~/components/third-party/ant-design/cf-iconbutton";
import { useEffect, useMemo, useState } from "react";
import { defaultConfig } from "~/data";
import isEmpty from "lodash/isEmpty";
import SelectOptions from "../select-options";
import CustomerSelectBody from "./body";
import { CFCloseButton } from "~/components/third-party/ant-design/cf-close-button";
import SidebarSelectHeader from "../header";
import { useExistingCustomers } from "./zustand";
import AddMultiselectDirectorySide from "../../add-multiselect-directory";
import { resetCustomerFilter, setExistingCustomers } from "./zustand/action";
import { getGConfig, useGModules } from "~/zustand";
import { useTranslation } from "~/hook";
import { getGlobalUser } from "~/zustand/global/user/slice";

interface MultiSelectSideProps {
  buttonText?: string;
  selectedOption: CustomerTabs | "";
  setSelectedOption: (selectedOption: CustomerTabs | "") => void;
  onSelected: (
    value: Partial<CustomerSelectedData> | Array<Partial<CustomerSelectedData>>
  ) => void;
  options?: CustomerTabs[];
  isMail?: boolean;
  singleSelecte: boolean;
  data?: Partial<CustomerSelectedData> | Partial<CustomerSelectedData>[];
  closeDrawer?: () => void;
  resetAgencySelected?: number | null;
  showDirectoryContact?: boolean;
  showNewDirectoryAddButton?: boolean;
  app_access?: boolean;
  additionalContactDetails?: number;
  appUsers?: number;
}
let isFetching = false;
const MultiSelectSide = ({
  buttonText = "Save",
  selectedOption,
  setSelectedOption,
  onSelected,
  options,
  isMail = false,
  singleSelecte = false,
  data,
  closeDrawer = () => {},
  resetAgencySelected,
  showDirectoryContact = true,
  showNewDirectoryAddButton = true,
  additionalContactDetails = 1,
  app_access,
  appUsers = 1,
}: MultiSelectSideProps) => {
  const { _t } = useTranslation();
  const [items, setItems] = useState<Partial<CustomerSelectedData>[]>([]);
  const [sideMenuOpen, setSideMenuOpen] = useState(false);
  const [addMultiselectDirectoryOpen, setAddMultiselectDirectoryOpen] =
    useState(false);
  const [currentSelected, setCurrentSelected] =
    useState<CustomerSelectOption<CustomerTabs>>();
  // this is will NF once testing done it will be merge on dev
  // const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
  //   useState<boolean>(false);
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const gConfig: GConfig = getGConfig();

  const { getGModule, checkModuleAccess } = useGModules();
  let { getExistingUsers, getExistingUsersWithApi } = useExistingCustomers();

  const sidebarOpen = useMemo(() => !isEmpty(selectedOption), [selectedOption]);

  const setUniqueItems = (data: Partial<CustomerSelectedData>[]) => {
    setItems((prev) => {
      return [
        ...prev,
        ...(data?.filter(
          (dataItem) =>
            !Boolean(
              prev?.find(
                (item) =>
                  getUniqueIdDirData(item) === getUniqueIdDirData(dataItem)
              )
            )
        ) ?? []),
      ];
    });
  };

  useEffect(() => {
    if (sidebarOpen) {
      let userIds: number[] = [];
      let contactIDs: string[] = [];
      if (Array.isArray(data)) {
        userIds =
          (data
            ?.map((customerData) => customerData?.user_id)
            ?.filter((userId: number | undefined) => userId) as number[]) || [];
        contactIDs =
          (data
            ?.map((customerData) => customerData?.contact_id?.toString())
            ?.filter(
              (contactId: string | undefined) => contactId
            ) as string[]) || [];
      } else if (!isEmpty(data)) {
        userIds = data?.user_id ? [data?.user_id] : [];
        contactIDs = data?.contact_id?.toString()
          ? [data?.contact_id?.toString()]
          : [];
      }

      if (userIds?.length > 0 || contactIDs?.length > 0) {
        const tempExistingUsers: Partial<CustomerSelectedData>[] =
          getExistingUsers({
            user_id: userIds,
            contact_id: contactIDs,
          });
        if (tempExistingUsers?.length > 0) {
          setUniqueItems(tempExistingUsers);
        }
        const tempUserIds = userIds?.filter(
          (userId) =>
            !Boolean(
              tempExistingUsers?.find(
                (existingUser) => existingUser?.user_id === userId
              )
            )
        );

        const tempConIds = contactIDs?.filter(
          (userId) =>
            !Boolean(
              tempExistingUsers?.find(
                (existingUser) => existingUser?.contact_id === userId
              )
            )
        );

        if (
          (tempUserIds?.length > 0 || tempConIds?.length > 0) &&
          !isFetching
        ) {
          getExistingUsersWithApi({
            usersIds: userIds?.map((customer) => customer)?.join(","),
            contactIDs: contactIDs?.map((customer) => customer)?.join(","),
            apiDataReturn: (customers: Partial<CustomerSelectedData>[]) => {
              setUniqueItems(customers);
              isFetching = false;
            },
            options,
          });
        }
      }
    }
  }, [data, sidebarOpen]);

  useEffect(() => {
    if (
      typeof data === "undefined" ||
      (Array.isArray(data) && data.length === 0) ||
      resetAgencySelected
    ) {
      setItems([]);
    }
  }, [data, resetAgencySelected]);

  const onClickItem = (value: Partial<CustomerSelectedData>) => {
    const customerDetail = {
      ...value,
      contact_id:
        typeof value.contact_id === "string"
          ? Number(value.contact_id)
          : value.contact_id,
    };
    setItems((prev: Partial<CustomerSelectedData>[]) => {
      const itemExists = prev?.some(
        (selectedItem: Partial<CustomerSelectedData>) =>
          selectedItem &&
          customerDetail &&
          getUniqueIdDirData(selectedItem) ===
            getUniqueIdDirData(customerDetail)
      );

      if (singleSelecte) {
        if (itemExists) {
          onSave([]);
        } else {
          onSave(customerDetail);
          return [customerDetail];
        }
      }
      if (itemExists) {
        return prev?.filter(
          (selectedItem: Partial<CustomerSelectedData>) =>
            getUniqueIdDirData(selectedItem) !==
            getUniqueIdDirData(customerDetail)
        );
      } else {
        return [...prev, customerDetail];
      }
    });
  };

  const onSave = (
    value: Partial<CustomerSelectedData> | Array<Partial<CustomerSelectedData>>
  ) => {
    let tempValue: Array<Partial<CustomerSelectedData>> = [];

    if (Array.isArray(value)) {
      tempValue = value;
    } else if (!isEmpty(value)) {
      tempValue.push(value);
    }
    setExistingCustomers(tempValue as Customer[]);
    onSelected(tempValue);

    setSideMenuOpen(false);
    setItems([]);

    setSelectedOption("");
    resetCustomerFilter();
  };

  const selectedData = useMemo(() => {
    const normalizedData = Array.isArray(data) ? data : [data];

    return items.filter((item) =>
      normalizedData.some(
        (dataItem) => getUniqueIdDirData(dataItem) === getUniqueIdDirData(item)
      )
    );
  }, [data, items]);

  const optionsModuleIds: Partial<OptionsModuleIds> = {
    contractor: 24,
    customer: 22,
    employee: 23,
    lead: 71,
    misc_contact: 38,
    vendor: 25,
  };

  const defaultOptions = useMemo(
    () =>
      customerSelectOptions
        .filter((dataItem: CustomerSelectOption<CustomerTabs>) => {
          const optionId = optionsModuleIds?.[dataItem.value];
          if (optionId) {
            const gModule = getGModule(optionId);
            return gModule?.hide_tab_in_directory_popup === "0";
          }
          return true;
        })
        .map((dataItem: CustomerSelectOption<CustomerTabs>) => {
          const optionId = optionsModuleIds?.[dataItem.value];
          if (optionId) {
            const moduleAccess: TModuleAccessStatus =
              checkModuleAccess(optionId);
            if (moduleAccess !== "no_access") {
              return {
                ...dataItem,
                addButton:
                  moduleAccess !== "read_only" && showNewDirectoryAddButton,
              };
            }
          }
          return dataItem;
        }),
    []
  );

  useEffect(() => {
    setCurrentSelected(
      defaultOptions?.find((option) => selectedOption === option?.value)
    );
  }, [defaultOptions, selectedOption, setCurrentSelected]);
  // this is will NF once testing done it will be merge on dev
  // const isFormModified = useMemo(() => {
  //   return JSON.stringify(items) !== JSON.stringify(selectedData);
  // }, [items, selectedData]);

  // const closeConfirmationModal = () => {
  //   sidebarOpen;
  //   setIsConfirmDialogOpen(false);
  // };

  // const handleAlertBox = async () => {
  //   setIsConfirmDialogOpen(false);
  //   setSelectedOption("");
  //   resetCustomerFilter();
  //   closeDrawer();
  //   setItems([]);
  // };

  // const handleDrawerClose = () => {
  //   if (!isFormModified) {
  //     setSelectedOption("");
  //     resetCustomerFilter();
  //     closeDrawer();
  //     setItems([]);
  //   } else {
  //     setIsConfirmDialogOpen(true);
  //   }
  // };

  return (
    <>
      <CFDrawer
        open={sidebarOpen}
        size={1210}
        drawerBody="!h-screen !overflow-hidden"
      >
        <div className="sidebar-body">
          <div className="flex">
            <div
              className={`w-60 md:min-w-[240px] md:max-w-[240px] md:relative absolute flex-[1_0_0%] z-20 ${
                sideMenuOpen
                  ? "h-screen w-full"
                  : "md:h-screen md:w-full w-0 h-0"
              }`}
            >
              <div
                className={`md:hidden block absolute bg-black/20 ${
                  sideMenuOpen ? "h-full w-full" : "h-0 w-0"
                }`}
                onClick={() => setSideMenuOpen(false)}
              ></div>
              <div
                className={`overflow-y-auto h-screen relative w-[240px] md:bg-gray-200/50 bg-gray-200 dark:bg-dark-800 transition-all ease-in-out duration-300 ${
                  sideMenuOpen ? "left-0" : "md:left-0 -left-[100%]"
                }`}
              >
                {!isEmpty(selectedOption) && (
                  <SelectOptions<CustomerTabs>
                    defaultOptions={defaultOptions}
                    options={options}
                    selectedOption={selectedOption}
                    // addFunction={addFunction}
                    addFunction={() => setAddMultiselectDirectoryOpen(true)}
                    onClick={(value: CustomerTabs) => {
                      setSelectedOption(value);
                    }}
                  />
                )}
              </div>
            </div>
            <div className="flex flex-[1_0_0%] overflow-hidden flex-col">
              <div className="md:px-4 pl-10 pr-5 py-2.5 border-b border-gray-300 dark:border-white/10 flex items-center relative justify-start">
                <CFIconButton
                  htmlType="button"
                  className={`md:hidden flex w-6 max-w-[24px] max-h-[24px] !absolute left-2.5`}
                  variant="text"
                  onClick={() => setSideMenuOpen((prev) => !prev)}
                >
                  <FontAwesomeIcon
                    className="text-base w-[18px] h-[18px] text-primary-gray-80 dark:text-white/90"
                    icon="fa-regular fa-bars"
                  />
                </CFIconButton>
                {currentSelected && (
                  <SidebarSelectHeader<CustomerTabs>
                    currentSelected={currentSelected}
                    headerTitle={
                      singleSelecte
                        ? _t("Select Contact")
                        : _t("Select Contacts")
                    }
                    headerTitleIcon="fa-regular fa-id-badge"
                  />
                )}
                <div className="md:relative md:right-[18px] !absolute right-2.5">
                  <CFCloseButton
                    onClick={() => {
                      setSelectedOption("");
                      resetCustomerFilter();
                      closeDrawer();
                      setItems([]);
                    }}
                    // this is will NF once testing done it will be merge on dev
                    // onClick={() => handleDrawerClose()}
                    iconClassName="!w-[18px] !h-[18px]"
                  />
                </div>
              </div>
              {!isEmpty(selectedOption) &&
                customerSelectOptions?.some(
                  (selectOption: CustomerSelectOption<CustomerTabs>) =>
                    selectOption?.value === selectedOption &&
                    options?.includes(selectOption?.value)
                ) && (
                  <CustomerSelectBody
                    selectedOption={selectedOption}
                    onClickItem={onClickItem}
                    items={items}
                    onSave={onSave}
                    singleSelecte={singleSelecte}
                    selectedData={selectedData}
                    buttonText={buttonText}
                    options={options}
                    currentSelected={currentSelected}
                    isMail={isMail}
                    app_access={app_access}
                    showDirectoryContact={showDirectoryContact}
                    additionalContactDetails={additionalContactDetails}
                    appUsers={appUsers}
                  />
                )}
            </div>
          </div>
        </div>
      </CFDrawer>
      {!isEmpty(currentSelected) && (
        <AddMultiselectDirectorySide
          setAddMultiselectDirectoryOpen={setAddMultiselectDirectoryOpen}
          addMultiselectDirectoryOpen={addMultiselectDirectoryOpen}
          selectOption={currentSelected}
          onClickItem={onClickItem}
        />
      )}
      {/* this is will NF once testing done it will be merge on dev */}
      {/* {isConfirmDialogOpen && (
        <ConfirmModal
          isOpen={isConfirmDialogOpen}
          modalIcon="fa-regular fa-file-check"
          modaltitle={_t("Confirmation")}
          description={_t(
            `Do you really want to leave this page and lose your unsaved changes?`
          )}
          onCloseModal={closeConfirmationModal}
          onAccept={() => {
            handleAlertBox();
          }}
          onDecline={closeConfirmationModal}
        />
      )} */}
    </>
  );
};

export default MultiSelectSide;

export const customerSelectOptions: CustomerSelectOption<CustomerTabs>[] = [
  {
    name: "All",
    value: "all",
    icon: <FontAwesomeIcon className="w-5 h-5" icon="fa-solid fa-id-badge" />,
    displayIcon: (
      <FontAwesomeIcon
        className="w-4 h-4 text-primary-900 dark:text-white/90"
        icon="fa-regular fa-id-badge"
      />
    ),
    addButton: false,
  },
  {
    name: "Employees",
    addTooltipContent: "Employee",
    value: defaultConfig.employee_key,
    icon: <FontAwesomeIcon className="w-5 h-5" icon="fa-solid fa-user-vneck" />,
    displayIcon: (
      <FontAwesomeIcon
        className="w-4 h-4 text-primary-900 dark:text-white/90"
        icon="fa-regular fa-user-vneck"
      />
    ),
    addButton: false,
  },
  {
    name: "My Crew",
    value: "my_crew",
    icon: <FontAwesomeIcon className="w-5 h-5" icon="fa-solid fa-user-group" />,
    displayIcon: (
      <FontAwesomeIcon
        className="w-[18px] h-[18px] text-primary-900 dark:text-white/90"
        icon="fa-regular fa-user-group"
      />
    ),
    addButton: false,
  },
  {
    name: "Customers",
    addTooltipContent: "Customer",
    value: defaultConfig.customer_key,
    icon: <FontAwesomeIcon className="w-5 h-5" icon="fa-solid fa-user-large" />,
    displayIcon: (
      <FontAwesomeIcon
        className="w-4 h-4 text-primary-900 dark:text-white/90"
        icon="fa-regular fa-user-large"
      />
    ),
    addButton: false,
  },
  {
    name: "Leads",
    addTooltipContent: "Lead",
    value: defaultConfig.lead_key,
    icon: (
      <FontAwesomeIcon
        className="w-5 h-5"
        icon="fa-solid fa-book-open-reader"
      />
    ),
    displayIcon: (
      <FontAwesomeIcon
        className="w-4 h-4 text-primary-900 dark:text-white/90"
        icon="fa-regular fa-book-open-reader"
      />
    ),
    addButton: false,
  },
  {
    name: "Contractors",
    addTooltipContent: "Contractor",
    value: defaultConfig.contractor_key,
    icon: (
      <FontAwesomeIcon
        className="w-5 h-5"
        icon="fa-solid fa-user-helmet-safety"
      />
    ),
    displayIcon: (
      <FontAwesomeIcon
        className="w-4 h-4 text-primary-900 dark:text-white/90"
        icon="fa-regular fa-user-helmet-safety"
      />
    ),
    addButton: false,
  },
  {
    name: "Vendors",
    addTooltipContent: "Vendor",
    value: defaultConfig.vendor_key,
    icon: (
      <FontAwesomeIcon className="w-5 h-5" icon="fa-solid fa-person-digging" />
    ),
    displayIcon: (
      <FontAwesomeIcon
        className="w-5 h-5 text-primary-900 dark:text-white/90"
        icon="fa-regular fa-person-digging"
      />
    ),
    addButton: false,
  },
  {
    name: "Misc. Contacts",
    addTooltipContent: "Misc. Contact",
    value: defaultConfig.misc_contact_key,
    icon: (
      <FontAwesomeIcon className="w-5 h-5" icon="fa-solid fa-address-book" />
    ),
    displayIcon: (
      <FontAwesomeIcon
        className="w-4 h-4 text-primary-900 dark:text-white/90"
        icon="fa-regular fa-address-book"
      />
    ),
    addButton: false,
  },
  {
    name: "By Service",
    value: "by_service",
    icon: (
      <FontAwesomeIcon className="w-5 h-5" icon="fa-solid fa-person-shelter" />
    ),
    displayIcon: (
      <FontAwesomeIcon
        className="w-4 h-4 text-primary-900 dark:text-white/90"
        icon="fa-regular fa-person-shelter"
      />
    ),
    addButton: false,
  },
  {
    name: "My Project",
    value: "my_project",
    icon: (
      <FontAwesomeIcon
        className="w-5 h-5"
        icon="fa-solid fa-building-circle-arrow-right"
      />
    ),
    displayIcon: (
      <FontAwesomeIcon
        className="w-4 h-4 text-primary-900 dark:text-white/90"
        icon="fa-regular fa-building-circle-arrow-right"
      />
    ),
    addButton: false,
  },
];
