interface AuthDecodedPayload {
  username: string;
  user_id: number;
  company_id: number;
  email: string;
  fullName: string;
  role_id: number;
  cf_token: string;
  iat: number;
  exp: number;
}

interface IAuthorizeActionContext {
  form: any;
  user: Omit<AuthDecodedPayload, "role_id" | "cf_token" | "iat" | "exp">;
  token: string;
}

interface PermitParams {
  id: string;
}

interface SsoToken {
  sso_token: string;
  generate_date: string;
}

interface DefaultResponse {
  project_id?(request: Request, project_id: any, authSession: string): unknown;
  success: string;
  message: string;
  data: ObjType | Array<any>;
  errors?: any;
}

interface GConfig {
  default_lead_sales_status: number;

  user_id: number;

  employee_key: string;
  employee_id: number;
  employee_text: string;

  customer_key: string;
  customer_id: number;
  customer_text: string;

  contractor_key: string;
  contractor_id: number;
  contractor_text: string;

  project_type_key_id: number;
  project_type_key: string;
  project_status_type_id: number;
  project_status_type_key: string;
  custom_estimate_status_key: string;

  vendor_key: string;
  vendor_id: number;
  vendor_text: string;

  misc_contact_key: string;
  misc_contact_id: number;
  misc_contact_text: string;

  lead_key: string;
  lead_id: number;
  lead_text: string;

  estimate_module_id: number;
  estimate_module_key: string;
  estimate_module_name: string;

  todo_open_status_id: number;
  todo_incomplete_status_id: number;
  todo_completed_status_id: number;

  project_permit_key_id: number;

  module_key: string;
  module_id: number;
  module_can_read: number;
  module_can_write: number;
  module_access: TModuleAccessStatus;
  module_is_enabled: boolean;
  module_read_only: boolean;
  module_name: string;
  module_singular_name: string;
  module_vimeo_url: string;
  module_kb_url_name: string;
  module_icon: string;

  authorization: string;
  sso_token: string;

  pay_pending_review: string;
  pay_verified: string;
  pay_in_review: string;
  equipment_company_type_id: number;

  custom_estimate_status_id: number;

  company_items: string;

  bill_type_id: number;
  bill_key_id: number;

  is_iframe_loading: boolean;
  page_is_iframe: boolean;
}

interface GMenuModule {
  web_page: string;
  name: string;
  singular_name: string;
  add_permission: boolean;
  app_request: string;
  icon: string;
  module_key: string;
  kb_url_name: string;
  module_id: number;
  vimeo_url: string | string | null;
  can_read: boolean;
  can_write: boolean;
  access_to_all_data: boolean;
  shortcut: boolean;
  section_title: string;
  module_vimeo_url?: string;
  section_item_order: number;
  is_enabled: boolean;
}

interface GCompanyData {
  user_id: string;
  auth_token: string;
  company_name: string;
  display_name: string;
  header_logo: string;
  image: string;
}

interface GCompanySettings {
  angilead_entity_id: number;
  angilead_status: number;
  chargebee_customer_id: string;
  city: string;
  company_employee: number;
  company_id: number;
  company_name: string;
  company_note: string;
  company_primary_industry: string;
  company_size: number;
  company_type: string;
  copy_from_company: number;
  copy_s3_files: number;
  country: string;
  date_added: string;
  date_modified: string;
  description: string;
  fax: string;
  header_logo: string;
  image: string;
  industry_code: string;
  industry_code_type: string;
  is_active: number;
  license: string;
  logo_height: number;
  logo_width: number;
  module_group_id: number;
  one_build_cost_code: number;
  one_build_description: number;
  one_build_import_cost_code: string;
  one_build_item_name: number;
  one_build_lat: string;
  one_build_lng: string;
  one_build_location: number;
  one_build_photo: number;
  one_build_price: number;
  one_build_state_county: string;
  one_build_unit: number;
  organization_type: number;
  phone: string;
  phone_code: string;
  phone_type: string;
  slogan: string;
  state: string;
  street: string;
  tax_ein: string;
  website: string;
  zapier_token: string;
  zip_code: string;
}

declare type GUser = IUser;

interface GModule {
  access_to_all_data: string;
  allow_modify_timecard: string;
  allow_view_all_activities: string;
  app_request: string;
  can_read: string;
  can_write: string;
  has_company_access: string;
  has_new_option: number;
  has_project: number;
  has_student_permission: string;
  header: string;
  hide_tab_in_directory_popup: string;
  icon: string;
  is_enabled: boolean;
  is_module: number;
  is_sortable: number;
  kb_url_name: string;
  module_id: number;
  module_key: string;
  module_name: string;
  original_module_name: string;
  parent_module_id: number;
  plural_name?: string;
  prompt_generate_daily_log: string;
  section_item_order: number;
  section_order: number;
  section_title: string;
  sort_order: number;
  vimeo_url: null | string;
  visible_app: number;
  web_page: string;
  view_st_billing_tab?: string;
  view_st_custom_tab?: string;
  view_st_history_tab?: string;
}

interface GAdminSettings {
  training_session_bottom: string;
  training_session_header: string;
  training_session_top: string;
}

interface GType {
  default_color: null | string;
  display_name: string;
  does_sync_qb: string;
  key: string;
  name: string;
  sort_order: string;
  type: string;
  type_id: string;
}

interface GSettings {
  AccountsPayable_id: number;
  AccountsPayable_name: string;
  AccountsReceivable_id: number;
  AccountsReceivable_name: string;
  BankAccount_id: number;
  BankAccount_name: string;
  BillAccount_id: number;
  BillAccount_name: string;
  BillPaymentBankCreditAccount_id: number;
  BillPaymentBankCreditAccount_name: string;
  CreditCardAccount_id: number;
  CreditCardAccount_name: string;
  DefaultItem_id: number;
  DefaultItem_name: string;
  DefaultTerms_cf_id: string;
  DefaultTerms_id: number;
  DefaultTerms_name: string;
  DepositsAccount_id: number;
  DepositsAccount_name: string;
  DiscountAccount_id: number;
  DiscountAccount_name: string;
  ExpenseAccount_cf_id: number;
  ExpenseAccount_id: number;
  ExpenseAccount_name: string;
  ExpensePaidThrough_id: number;
  ExpensePaidThrough_name: string;
  ExpenseServiceID: number;
  ExpenseServiceName: string;
  ExpensesProductsServices_id: number;
  ExpensesProductsServices_name: string;
  IncomeProductsServices_id: number;
  IncomeProductsServices_name: string;
  IncomeServiceID: number;
  IncomeServiceName: string;
  ItemCategoriesFeature: number;
  MasterContactRecord: string;
  OfferingSku: string;
  PaymentAccount_id: number;
  PaymentAccount_name: string;
  PayrollFeature: number;
  RetainageAccountReceivable_id: number;
  RetainageAccountReceivable_name: string;
  RetentionsReceivable_id: number;
  RetentionsReceivable_name: string;
  SyncPreference: string;
  UncategorizedExpenseAccountId: number;
  UncategorizedIncomeAccountId: number;
  ach_payment_limit: string;
  additional_users: number;
  all_topics: number;
  allow_ach_payment: number;
  allow_client_financing: number;
  allow_clock_in: number;
  allow_creditcard_payment: number;
  allow_geofence: number;
  allow_online_payment: number;
  allow_search_phone_contact: number;
  allow_service_ticket_in_timecard: number;
  allow_timecard_tags: number;
  angilead_entity_id: number;
  angilead_status: number;
  app_rule_id: number;
  app_setting_id: number;
  apply_auto_timecard_break: string;
  apply_overtime_rule: number;
  auto_save_data: number;
  auto_schedule_appointment: number;
  bid_item_description_source: string;
  bill_retainage_cost_code: number;
  break_after: number;
  break_cost_code: number;
  break_duration: number;
  break_rule_name: string;
  can_have_qb_access: string;
  certificate_reminder_option: number;
  certificate_reminder_value: string;
  city: string;
  client_portal_project_manager: number;
  co_to_sov: number;
  comment_order: string;
  company_employee: number;
  company_id: number;
  company_name: string;
  company_note: string;
  company_primary_industry: string;
  company_size: number;
  company_type: string;
  consolidate_invoice: number;
  contact_reminder_option: null | string;
  contact_reminder_value: null | any;
  contractor_as_vendor: number;
  copy_from_company: number;
  copy_s3_files: number;
  cost_code_sort_order: string;
  cost_code_display_order: string;
  country: string;
  country_name: string;
  crew_disclaimer: string;
  currency: string;
  currency_code: string;
  currency_id: number;
  currency_symbol: string;
  custom_bill_id: string;
  custom_change_orders_id: string;
  custom_compliance_notice_id: string;
  custom_estimate_id: string;
  custom_incident_id: string;
  custom_inspection_id: string;
  custom_invoice_id: string;
  custom_opportunity_id: string;
  custom_project_id: string;
  custom_purchase_orders_id: string;
  custom_purchase_order_id: string;
  custom_rfi_id: string;
  custom_schedule_notice_id: string;
  custom_service_tickets_id: string;
  custom_sub_contract_id: string;
  custom_submittal_id: string;
  custom_work_orders_id: string;
  daily_hours: string;
  date_added: string;
  date_format: string;
  date_modified: string;
  decimal_separator: string;
  default_cover_sheet: string;
  default_currency: string;
  default_equipment_markup_percent: string;
  default_geofence_radius: number;
  default_invoice_term_key: string;
  default_item_view: number;
  default_labor_markup_percent: string;
  default_material_markup_percent: string;
  default_other_item_markup_percent: string;
  default_project_status: string;
  default_project_status_key: string;
  default_sub_contractor_markup_percent: string;
  default_tax_rate: number;
  default_term_id: number;
  default_term_key: string;
  default_undefined_markup_percent: string;
  demo_mode: number;
  description: string;
  dir_certificate_manager: number;
  directory_ssn_number_format: string;
  disable_client_when_project_completed: number;
  do_not_track_gps_roles: string;
  do_notify_running_timecard: number;
  emp_jobsite_accident: string;
  emp_tc_verification: string;
  emp_time_card_note: string;
  emp_time_off_request: string;
  enable_labor_markup_billing_rate: number;
  enable_labor_markup_burden_rate: number;
  enable_labor_markup_wage_rate: number;
  enable_old_settings: number;
  estimate_auto_expiration: number;
  estimate_company_name: string;
  estimate_convert_lead_to_customer: number;
  estimate_copy_item_to_sov: number;
  estimate_email: string;
  estimate_first_name: string;
  estimate_generate_invoice: number;
  estimate_generate_project: number;
  estimate_last_name: string;
  estimate_msg: string;
  estimate_phone: null | string;
  estimate_selected_label: string;
  estimate_signture: null | string;
  estimate_slogan: null | string;
  estimate_term: string;
  estimate_term_message: string;
  estimate_to_sov: number;
  estimate_website: null | string;
  expense_default_tax_rate: number;
  fax: string;
  first_week_day: number;
  geofence_roles: string;
  google_email: string;
  google_sync: number;
  google_sync_token: string;
  google_token: string;
  gps_crew_card_location: number;
  group_id: number;
  group_name: string;
  group_safety_meeting: number;
  has_copied_demo_data: number;
  has_labor_tc_cost_codes: number;
  has_project_based_cost_codes: number;
  header_logo: string;
  hide_item_with_total_zero: number;
  id: number;
  image: string;
  image_resolution: string;
  individual_safety_meeting: number;
  industry_code: string;
  industry_code_type: string;
  inv_sub_billing_option: string;
  inv_total_balance_due: number;
  invoice_company_name: string;
  invoice_email: string;
  invoice_first_name: string;
  invoice_last_name: string;
  invoice_msg: string;
  invoice_phone: null | string;
  invoice_signture: null | string;
  invoice_slogan: null | string;
  invoice_term: string;
  invoice_term_message: string;
  invoice_website: null | string;
  is_active: number;
  is_bill_billable: number;
  is_cidb_auto_save: number;
  is_client_pay_transaction_fee: number;
  is_companycam_connected: number;
  is_custom_bill_id: number;
  is_custom_change_orders_id: number;
  is_custom_compliance_notice_id: number;
  is_custom_estimate_id: number;
  is_custom_incident_id: number;
  is_custom_inspection_id: number;
  is_custom_invoice_id: number;
  is_custom_opportunity_id: number;
  is_custom_project_id: number;
  is_custom_purchase_order_id: number;
  is_custom_rfi_id: number;
  is_custom_schedule_notice_id: number;
  is_custom_service_tickets_id: number;
  is_custom_sub_contract_id: number;
  is_custom_submittal_id: number;
  is_custom_work_orders_id: number;
  is_deleted: number;
  is_multi_currency_enabled: number;
  is_quickbook_sandbox: string;
  is_shared_calendar: number;
  is_stripe_activate_from_admin: number;
  is_taxable_equipment_items: number;
  is_taxable_labor_items: number;
  is_taxable_material_items: number;
  is_taxable_other_items: number;
  is_taxable_subcontractor_items: number;
  is_wepay_activate_from_admin: number;
  job_cost_wage_field: string;
  last_company_bill_id: number;
  last_company_change_order_id: number;
  last_company_compliance_notice_id: number;
  last_company_estimate_id: number;
  last_company_incident_id: number;
  last_company_inspection_id: number;
  last_company_invoice_id: number;
  last_company_opportunity_id: number;
  last_company_project_id: number;
  last_company_purchase_order_id: number;
  last_company_rfi_id: number;
  last_company_schedule_notice_id: number;
  last_company_service_ticket_id: number;
  last_company_sub_contract_id: number;
  last_company_submittal_id: number;
  last_company_work_order_id: number;
  lead_capture_form_option: number;
  lead_recipients: string;
  license: string;
  logo_height: number;
  logo_width: number;
  losing_bid_notification: string;
  maximum_geofence_radius: number;
  minimum_geofence_radius: number;
  mixed_daily_hours: string;
  mixed_weekly_hours: string;
  module_group_id: number;
  modules: number;
  need_project_prefix: number;
  notes: string;
  notify_for_timecards: number;
  notify_timecard_roles: string;
  onboard_experience: string;
  onboard_training: string;
  one_build_cost_code: number;
  one_build_description: number;
  one_build_import_cost_code: string;
  one_build_item_name: number;
  one_build_lat: string;
  one_build_lng: string;
  one_build_location: number;
  one_build_photo: number;
  one_build_price: number;
  one_build_state_county: string;
  one_build_unit: number;
  online_payment: number;
  only_allow_ach_payment: number;
  organization_type: number;
  outlook_delta_token: string;
  outlook_email: string;
  outlook_office_delta_token: string;
  outlook_office_email: string;
  outlook_office_sync: number;
  outlook_office_token: string;
  outlook_office_token_obj: string;
  outlook_sync: number;
  outlook_token: string;
  outlook_token_obj: string;
  overtime_option: string;
  pdf_footer_image: string;
  pdf_header_image: string;
  pdf_language: string;
  phone: string;
  phone_code: string;
  phone_format: string;
  phone_type: string;
  prefix_project_name_with_project_id: number;
  prefix_project_to_change_order: number;
  prefix_project_to_compliance_notice: number;
  prefix_project_to_invoice: number;
  prefix_project_to_purchase_order: number;
  prefix_project_to_rfi: number;
  prefix_project_to_schedule_notice: number;
  prefix_project_to_sub_contract: number;
  prefix_project_to_submittal: number;
  prefix_project_to_work_order: number;
  prefix_project_to_opportunity: number;
  project_prefix: string;
  purchase_licences: null | string;
  qb_account_sync_flag: number;
  qb_country: string;
  qb_default_ot_payroll: number;
  qb_default_payroll: number;
  qb_previous_realm_id: string;
  is_non_united_state_qb_country?: boolean; // find value after api response get
  quickbook_access_token: string;
  quickbook_access_token_expires_at: string;
  quickbook_automatic_sync: number;
  quickbook_company_name: string;
  quickbook_desktop_sync: string;
  quickbook_realm_id: string;
  quickbook_refresh_token: string;
  quickbook_refresh_token_expires_at: string;
  quickbook_sync: string;
  require_crew_card_injury: number;
  require_crew_sheet_injury: number;
  require_injury: number;
  retain_original_image: number;
  retainage_cost_code: number;
  safety_topic_language: string;
  save_a_copy_of_sent_pdf: number;
  schedule_break_end: number;
  schedule_break_start: number;
  schedule_first_hour: number;
  schedule_last_hour: number;
  schedule_projects: string;
  scheduled_safety_meeting: number;
  send_automatic_reminder: number;
  send_automatic_reminder_hours: number;
  show_crew_card_injury: number;
  show_crew_sheet_injury: number;
  show_email_to_client_portal: number;
  show_image_footer_pdf: number;
  show_image_header_pdf: number;
  show_injury: number;
  show_on_signup: number;
  show_phone_to_client_portal: number;
  show_resource_pane: number;
  show_service_ticket_timer_section: number;
  show_weekend_timecard: number;
  slogan: string;
  sort_order: number;
  state: string;
  street: string;
  sub_contract_term_message: string;
  sync_flag: number;
  tax_all_items: number;
  tax_ein: string;
  tax_format: string;
  taxable_retainage: string;
  tc_auto_clock_out_admin: string;
  tc_auto_clock_out_hours: number;
  tc_notify_hours: number;
  temperature_scale: string;
  thousand_separator: string;
  timecard_sort_cost_codes_order: string;
  track_gps_for_time_cards: number;
  unit_progressive_billing: number;
  use_gps_for_time_card: number;
  use_master_cost_code: number;
  use_tax_label: number;
  user_limit: number;
  user_option_wizard: number;
  verify_time_by_employee: number;
  verify_time_by_supervisor: number;
  website: string;
  weekend_schedule: number;
  weekly_hours: string;
  weekly_schedule: number;
  wepay_activated: string;
  winning_bid_confirmation: string;
  wo_term_message: string;
  wo_to_sov: number;
  working_hours: string;
  zapier_token: string;
  zip_code: string;
  stripe_activated: string;
  pdf_template_text_color: string;
  pdf_template_color: string;
}
interface GProject {
  project_id: string;
  project_name: string;
  view_in_timecard: string;
  project_selection: string;
}

interface ModuleStatus {
  item_id?: string;
  key?: string;
  name?: string;
  status_color?: string;
  default_color?: string;
  does_sync_qb?: number;
  is_custom_item?: number;
  is_deleted?: number;
  orig_name?: string;
  status__color?: string;
  status_name?: string;
  sort_order?: string;
  show_in_progress_bar?: string;
  is_status?: string;
}

interface ModuleSetting {
  weekend_schedule?: string;
  module_status?: Array<ModuleStatus>;
  bid_do_not_show_with_projects: string;
  bid_do_not_show_with_approve_estimate: string;
  bid_do_not_show_with_deadline: string;
  winning_bid_confirmation: string;
  losing_bid_notification: string;
  bid_item_description_source: string;
  project_module_status: Array<any>; // change type
  project_module_type_color: Array<any>; // change type
  project_client_access_settings: Array<any>; // change type
  custom_field_form_json: string;
  custom_field_form_json_decode: Array<any>; // change type
  modify_module_name: string;
  original_module_name: string;
  modify_module_name_plural: string;
  custom_bill_id: string;
}

interface GModuleDashboard {
  module_setting: ModuleSetting;
}

declare type GModuleFilterData =
  | Partial<ProjectPermitFilter>
  | Partial<DirectoryModuleFilter>
  | Partial<EstimateModuleFilter>
  | Partial<BillModuleFilter>
  | Partial<EquipmentLogsFilter>
  | Partial<ToDoFilter>
  | Partial<PunchListFilter>
  | Partial<ProjectFilter>
  | Partial<PurchaseOrdersFilter>
  | Partial<PaymentsFilter>
  | Partial<WorkOrdersFilter>
  | Partial<LeadsFilter>
  | Partial<InvoiceFilter>
  | Partial<IncidentsFilter>
  | Partial<VhicalLogsFilter>
  | Partial<IExpenseFilter>
  | Partial<FormsChecklistsFilter>
  | Partial<InspectionsFilter>
  | Partial<SafetyMeetingsFilter>
  | Partial<STFilter>
  | Partial<IChangeOrderListFilters>
  | Partial<SubmittalFilter>;

interface GModuleFilter {
  company_id: string;
  date_added: string;
  date_modified: string;
  filter: GModuleFilterData;
  filter_id: string;
  module_id: string | number;
  user_id: string;
}

interface ProjectPermitFilter {
  sort_by_field: number;
  sort_by_order: string;
  filter_my_list: string;
  expire_start_date: string;
  expire_end_date: string;
  status: string;
  permit_type: string;
  project_names: string;
  project: string;
  agency: string;
  agency_names: string;
  permit_type_names: string;
}

interface FormsChecklistsFilter {
  project: string;
  start_date: string;
  end_date: string;
  status: string;
  status_names: string;
}

interface InspectionsFilter {
  end_date: string;
  inspection_status: string;
  inspection_status_kanban: string;
  project: string;
  project_names: string;
  start_date: string;
}

interface SafetyMeetingsFilter {
  project: string;
  project_names: string;
  status: string;
  status_names: string;
  directory: string;
  topic_type: string;
  topic_type_names: string;
  filter_my_list: string | number;
}

interface DirectoryModuleFilter {
  assignee: string;
  directory_type: string;
  directory_type_names: string;
  group: string;
  group_names: string;
  project_type: string;
  quality: string;
  referral_source: string;
  service: string;
  service_names: string;
  stage: string;
  status: string;
  tags: string;
  tags_names: string;
  billing_status?: string;
  billing_status_kanban?: string;
  billing_status_kanban_names?: string;
  billing_status_names?: string;
}

interface EstimateModuleFilter {
  sort_by_field: number;
  sort_by_order: string;
  filter_my_list: string;
  expire_start_date: string;
  expire_end_date: string;
  start_date: string;
  end_date: string;
  status: string;
  permit_type: string;
  project_names: string;
  project: string;
  agency: string;
  agency_names: string;
  permit_type_names: string;
  customer: string;
  customer_names: string;
  project_contact: string;
  project_contact_names: string;
  approval_type_names: string;
  approval_type: string;
  estimate_project_type: string;
  estimate_project_type_names: string;
  approval_type_kanban_names: string;
  approval_type_kanban: string;
  estimate_template_names: string;
  estimate_template_only: string;
}

interface BillModuleFilter {
  sort_by_field: number;
  sort_by_order: string;
  filter_my_list: string;
  start_date: string;
  end_date: string;
  status: string;
  project_names: string;
  project: string;
  directory: string;
  directory_names: string;
  tab: string | number;
  response_status: string | number;
  is_own_data: string | number;
  job_status: string | number;
}

interface DailyLogModuleFilter {
  sort_by_field: number;
  sort_by_order: string;
  filter_my_list: string;
  start_date: string;
  end_date: string;
  status: string;
  project_names: string;
  project: string;
  directory: string;
  directory_names: string;
  tab: string | number;
}

interface VehicleLogModuleFilter {
  vehicle: string;
  status: string;
  status_names: string;
  project: string;
  project_names: string;
  employee: string;
}

interface RFIModuleFilter {
  project: string | number;
  project_names: string;
  status: string;
  status_names: string;
  type: string;
  type_names: string;
  tab_status: string;
  filter_my_list?: string;
}

interface EquipmentLogsFilter {
  project: string;
  equipments: string;
  project_names: string;
  equipment_names: string;
  status: string;
  start_date: string;
  end_date: string;
}

interface VhicalLogsFilter {
  employee: string;
  vehicle: string;
  project: string;
  project_names: string;
  employee_names: string;
  vehicle_names: string;
  status: string;
  vehicles: string;
}
interface IncidentsFilter {
  project: string;
  directory: string;
  start_date: string;
  end_date: string;
  project_names: string;
  list_for: string | number | null;
  key: string;
  value: string;
  directory_names: string;
}
interface InvoiceFilter {
  billing_status: string;
  billing_status_kanban: string;
  billing_status_kanban_names: string;
  billing_status_names: string;
  customer: string;
  customer_names: string;
  end_date: string;
  is_aging: number;
  key: string;
  project: string;
  project_names: string;
  start_date: string;
  status: string;
  tab: string | number;
  value: string;
}
interface LeadsFilter {
  // Created
  created_start_date: string;
  created_end_date: string;
  // Stage
  stage: string;
  stage_names: string;
  // Quality
  quality: string;
  quality_names: string;
  // Sales Rep.
  sales_rep: string;
  sales_rep_name: string;
  // Est. Sales Date
  est_sales_start_date: string;
  est_sales_end_date: string;
  // Project Type
  project_type: string;
  project_type_names: string;
  // Value
  value_filter_start: string;
  value_filter_end: string;
  // Referral Source
  referral_source: string;
  referral_source_names: string;
  // Last Contacted
  last_contacted_start_date: string;
  last_contacted_end_date: string;
  // City
  city: string;
  // Record Status
  status: string;

  // sort_by_field: string;
  // sort_by_order: string;
  filter_my_list: string | number;
  // project: string | number;
  // project_names: string;
  is_new_filter_applied?: boolean;
}

interface ToDoFilter {
  assignee: string;
  assignee_names: string;
  is_my_todo: number;
  project: string;
  project_names: string;
  sort_due_date: number;
  status: string;
  status_names: string;
  tab: string | number;
}

interface SubmittalFilter {
  is_my_submittals?: number;
  project?: string;
  project_names?: string;
  status?: string;
  submittal_status?: string;
  tab?: string;
}
interface STFilter {
  start_date?: string;
  end_date?: string;
  customer?: string;
  employee?: string;
  employee_names?: string;
  project?: string;
  project_names?: string;
  status?: string;
  priority?: string;
  priority_names?: string;
  job_status?: string;
  job_status_names?: string;
  job_status_kanban?: string;
  response_status?: string | number;
  is_own_data?: string | number;
  is_my_ST?: string | number;
  sort_due_date?: string | number;
  tab?: string;
}

interface PunchListFilter {
  project: string;
  status: string;
  completed_status: number;
  tab: string | number;
  project_names: string;
  open: string;
}
interface PurchaseOrdersFilter {
  billing_status: string;
  billing_status_kanban: string;
  billing_status_kanban_names: string;
  billing_status_names: string;
  directory: string;
  directory_names: string;
  project: string;
  project_names: string;
  status: string;
  tab: string | number;
  is_multiple_suppliers?: string;
}
interface PaymentsFilter {
  customer: string;
  customer_names: string;
  payment_status: string;
  project: string;
  project_name: string;
  project_names: string;
  status: string;
  tab: string | number;
}
interface WorkOrdersFilter {
  customer: string;
  customer_names: string;
  end_date: string;
  module_status: string;
  project: string;
  project_names: string;
  start_date: string;
  status: string;
  status_names: string;
  work_order_status_kanban: string;
  is_my_wo?: string;
}
interface ProjectFilter {
  customer: string;
  customer_contact_id: string;
  customer_names: string;
  end_date: string;
  project: string;
  project_contacts: string;
  project_contact_id?: string;
  project_contacts_names: string;
  project_manager: string;
  project_manager_names: string;
  project_names: string;
  project_status: string;
  project_status_kanban: string;
  project_status_kanban_names: string;
  project_status_names: string;
  project_type: string;
  project_type_names: string;
  record_type: string;
  sales_rep: string;
  sales_rep_names: string;
  start_date: string;
  status: string;
  tab: string | number;
}

interface Project {
  id: number;
  project_id: string;
  project_name: string;
  customer_id: number;
  billed_to: number;
  billed_to_contact: number;
  prj_record_type: string;
  billed_to_name: string | null;
  view_in_calendar: string;
  view_in_schedule: string;
  retention: string | null;
  project_manager_id: number;
  view_in_timecard: number;
  project_status_key: string;
  project_type_key: string;
  billing_option: string;
  allow_overbilling: string;
  is_assigned_project: string;
  show_client_access: string;
  default_tax_rate_id: string;
  allow_online_payment: number;
  prj_type_name: string;
  project_status_name: string;
  project_type_name: string;
  start_date: string | null;
  end_date: string | null;
  date_added: string;
  date_modified: string;
  progress: string;
  customer_name: string;
  customer_name_only: string;
  cust_image: string;
  project_color: string;
  project_manager_name: string;
  customer_tax_id: string;
  project_default_material_markup_percent: string;
  project_default_equipment_markup_percent: string;
  project_default_labor_markup_percent: string;
  project_default_sub_contractor_markup_percent: string;
  project_default_other_item_markup_percent: string;
  project_default_undefined_markup_percent: string;
  project_enable_labor_markup_wage_rate: string;
  project_enable_labor_markup_billing_rate: string;
  project_enable_labor_markup_burden_rate: string;
  customer_contract: string;
  key?: string;
  name?: string;
}

interface IProjectModules {
  company_id: string;
  time_cards: string;
  notes: string;
  daily_logs: string;
  todos: string;
  estimates: string;
  service_tickets: string;
  change_orders: string;
  work_orders: string;
  punchlists: string;
  safety_meetings: string;
  correspondences: string;
  incidents: string;
  vehicle_logs: string;
  equipment_logs: string;
  inspections: string;
  expenses: string;
  bills: string;
  purchase_orders: string;
  forms_checklist: string;
  sub_contracts: string;
  submittals: string;
  invoice_merge: string;
  corporate_calendar: string;
  crew_schedule: string;
  project_permits: string;
}
interface BarChartData {
  x: Array<number | string>;
  y: Array<number | string>;
  status_color?: Array<string>;
}

interface LineChartData {
  x_data: Array<number | string>;
  this_year_expense: Array<number | string>;
  status_color?: Array<string>;
}

interface AreaChartData {
  data?: Array<any>;
  x: Array<number | string>;
  y: Array<number | string>;
}

interface DashboardWidget {
  w: number;
  min_w?: number;
  max_w?: number;
  h: number;
  min_h?: number;
  max_h?: number;
  id: string;
  children: string | JSX.Element | JSX.Element[];
  widget_key: string | Array<string>;
}

interface SProject {
  id: number;
  project_id: string;
  project_name: string;
  customer_id: number;
  billed_to: number;
  billed_to_contact: number;
  prj_record_type: string;
  progress: string;
  view_in_calendar: number;
  view_in_schedule: number;
  retention: string | null;
  project_manager_id: number;
  view_in_timecard: number;
  project_status_key: string;
  project_type_key: string;
  billing_option: string;
  allow_overbilling: number;
  is_assigned_project: number;
  show_client_access: number;
  default_tax_rate_id: number;
  allow_online_payment: number;
  prj_type_name: string;
  project_status_name: string;
  customer_name: string;
  customer_name_only: string;
  cust_image: string;
  customer_tax_id: number;
  project_color: string;
  project_manager_name: string;
  project_type_name: string;
  start_date: string;
  end_date: string;
  date_added: string;
  date_modified: string;
}

interface SProjectModuleStatus {
  item_id: number;
  key: string;
  orig_name: string;
  does_sync_qb: number;
  is_custom_item: number;
  name: string;
  status_name: string;
  is_deleted: number;
  default_color: string;
  status__color: string;
  status_color: string;
}
interface ObjType {
  [key: string]:
    | string
    | number
    | boolean
    | Array<ObjType | string | number | boolean>
    | ObjType;
}

// Google Street Field
interface LatLongItf {
  latitide?: string | number;
  longitude?: string | number;
}

// as per atom structure
interface IModuleStatus {
  sort_order: string;
  status_color: string;
  item_id: number;
  key: string;
  column_id: string;
  is_custom_item: string;
  name: string;
  orig_name: string;
  item_type: string;
  qb_full_name: string;
  qb_account_type: string;
  qb_account_sub_type: string;
  quickbook_category_id: string;
  status_name: string;
  is_deleted: string;
  is_status: string;
  sorting_id: number;
  show_in_progress_bar: string;
}

interface IGetProjectsApiResponseDataGlobalProject {
  project_id: number;
  project_name: string;
  view_in_timecard: string;
  project_selection: string;
}

interface IChangeOrderListFilters {
  is_kanban: number;
  need_status_color: number;
  filter_primary_id: number;
  global_project: number;
  project_names: string;
  project: string;
  type: string;
  start_date: string;
  end_date: string;
  status: string;
  billing_status: string;
  billing_status_kanban: string;
}

interface IChangeOrderListFiltersPayload {
  company_id: number;
  user_id: number;
  module_id: number;
  filter_id: number;
  filter: Partial<IChangeOrderListFilters>;
  search?: string;
}

interface IChangeOrderListComponentActions {
  reload: () => void;
  updateDefaultView: (
    isChecked: boolean
  ) => Promise<IKanbanSettingApiRes | null>;
}

interface CommonError {
  message: string;
}

interface ISubContratsModuleFilter {
  status: string;
  project_names: string;
  project: string;
  directory: string;
  directory_names: string;
  sub_contract_status: string;
  start_date: string;
  end_date: string;
}

interface IInvoiceModuleFilter {
  project: string;
  project_names: string;
  customer: string;
  customer_names: string;
  status?: string;
  status_names: string;
  billing_status: string;
  billing_status_names: string;
  billing_status_kanban: string;
  billing_status_kanban_names: string;
  start_date: string;
  end_date: string;
  tab: string;
  key: string;
  value: string;
}

interface IPaymentModuleFilter {
  project: string;
  project_names: string;
  customer: string;
  contact_id: string;
  customer_contact_id?: string;
  customer_names: string;
  status?: string;
  payment_status?: string;
  tab?: string;
}

interface ISafetyMeetingsModuleFilter {
  project_names: string;
  project: string;
  employee: string;
  employee_names: string;
  leader_id: string;
  topic: string;
  topic_names: string;
  status: string;
}

interface IPunchlistModuleFilter {
  project_names: string;
  project: string;
  completed_status: string;
  status: string;
  tab: string;
}
