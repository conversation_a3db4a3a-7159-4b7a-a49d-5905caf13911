import { useEffect, useMemo, useState } from "react";
import dayjs from "dayjs";
import {
  getCustomField,
  getDirectoriesForms,
  getEquipmentsForms,
  getProjects,
} from "~/redux/action/customFieldAction";
import {
  filterAndMapDirectory,
  filterAndMapEquipment,
  filterAndMapProject,
} from "./helper";
import {
  otherOptionRadio,
  otherOptionCheckBox,
} from "~/shared/components/organisms/customField/const";
import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";
import { apiRoutes } from "~/route-services/routes";

export const useCustomField = (
  { directory, directoryKeyValue }: IDirectoryFormCustomField,
  { moduleId, recordId, userType }: IRequestCustomField
) => {
  const [componentList, setComponentList] = useState<ICustomFieldKey[]>([]);
  const [customFieldId, setCustomFieldId] = useState<string>("");
  const [loadingCustomField, setLoadingCustomFields] = useState<boolean>(true);
  const [apiError, setApiError] = useState<string>("");
  useEffect(() => {
    const fetchData = async () => {
      if (directory && directoryKeyValue && moduleId && recordId) {
        try {
          const allData = (await Promise.all([
            getDirectoriesForms({ directory, directoryKeyValue }),
            getEquipmentsForms({ module_id: moduleId }),
            getProjects({
              globalCall: 1, // this needed because of no access permission
            }),
            getCustomField({
              moduleId,
              recordId,
              userType: !!userType ? userType : undefined,
            } as IRequestCustomField),
          ])) as [
            Partial<IGetDirectoriesFormsRes>,
            Partial<IGetEquipmentFormsRes>,
            Partial<IGetProjectsRes>,
            Partial<IGetCustomFieldRes>
          ];

          const projectList: ISelectList[] = filterAndMapProject(
            allData[2]?.data ?? []
          );
          const equipmentList: ISelectList[] = filterAndMapEquipment(
            allData[1]?.data ?? []
          );
          const directoryList: IDirectoriesForms[] = allData[0]?.data ?? [];

          const customFieldData: Partial<IGetCustomFieldResponseData> =
            allData[3]?.data ?? {};

          const objectValue: TypeId | null =
            customFieldData?.fieldValuesData?.data_value ?? null;

          const getCustomFieldId: string =
            customFieldData?.fieldValuesData?.custom_field_id?.toString() ?? "";

          const mergedArrayData: ICustomFieldKey[] = await Promise.all(
            (customFieldData?.customFieldFormJson ?? []).map(
              async (item: ICustomFieldKey) => {
                const { name, label, type } = item;
                const required = item.required || false;
                const multiple = item.multiple || false;
                const other = item.other || false;
                let value: any;
                let previousValue: any;
                if (type === "select") {
                  if (multiple) {
                    value =
                      !!objectValue?.[name] && objectValue?.[name] !== "[]"
                        ? Array.isArray(objectValue[name])
                          ? objectValue[name]
                          : toArray(objectValue[name])
                        : [];
                    previousValue =
                      !!objectValue?.[name] && objectValue?.[name] !== "[]"
                        ? Array.isArray(objectValue[name])
                          ? objectValue[name]
                          : toArray(objectValue[name])
                        : [];
                  } else {
                    value = !!objectValue?.[name] ? objectValue[name] : "";
                    previousValue = !!objectValue?.[name]
                      ? objectValue[name]
                      : "";
                  }
                } else if (type === "checkbox-group") {
                  value = toArray(objectValue?.[name]);
                  previousValue = toArray(objectValue?.[name]);
                } else if (type === "date") {
                  value = !!objectValue?.[name]
                    ? dayjs(objectValue[name], ["YYYY-MM-DD"], true).isValid()
                      ? objectValue[name]
                      : dayjs(
                          objectValue[name],
                          [
                            "DD.MM.YYYY",
                            "MM/DD/YYYY",
                            "DD/MM/YYYY",
                            "DD/MMM/YYYY",
                          ],
                          true
                        ).format("YYYY-MM-DD")
                    : "";
                  previousValue = !!objectValue?.[name]
                    ? dayjs(objectValue[name], ["YYYY-MM-DD"], true).isValid()
                      ? objectValue[name]
                      : dayjs(
                          objectValue[name],
                          [
                            "DD.MM.YYYY",
                            "MM/DD/YYYY",
                            "DD/MM/YYYY",
                            "DD/MMM/YYYY",
                          ],
                          true
                        ).format("YYYY-MM-DD")
                    : "";
                } else {
                  value = !!objectValue?.[name] ? objectValue[name] : "";
                  previousValue = !!objectValue?.[name]
                    ? objectValue[name]
                    : "";
                }
                if (
                  type === "select" &&
                  ![
                    "Misc. Contact",
                    "Misc Contact",
                    "Customer",
                    "Employee",
                    "Contractor",
                    "Customer &amp; Lead",
                    "Equipment",
                    "Project",
                  ].includes(label)
                ) {
                  if (
                    multiple &&
                    required &&
                    (value == "" || value.length == 0)
                  ) {
                    let selectedValue: string[] = [];
                    const selectedItem = (item?.values || []).filter(
                      (selectData) => Boolean(selectData?.selected) == true
                    );
                    if (selectedItem.length > 0) {
                      for (
                        let index = 0;
                        index < selectedItem.length;
                        index++
                      ) {
                        selectedValue.push(
                          HTMLEntities.decode(selectedItem[index].value)
                        );
                      }
                    }
                    return {
                      ...item,
                      value: value !== "" ? selectedValue : [],
                      previousvalue: value !== "" ? selectedValue : [],
                      required,
                      multiple,
                      status: "button" as IStatus,
                    };
                  } else {
                    return {
                      ...item,
                      value: value,
                      required,
                      multiple,
                      status: "button" as IStatus,
                      previousvalue: value,
                    };
                  }
                } else if (label === "Customer") {
                  return {
                    ...item,
                    value: value,
                    required,
                    multiple,
                    status: "button" as IStatus,
                    values: filterAndMapDirectory(directoryList, [
                      directoryKeyValue.customer,
                    ]),
                    previousvalue: value,
                  };
                } else if (label === "Employee") {
                  return {
                    ...item,
                    value: value,
                    required,
                    multiple,
                    status: "button" as IStatus,
                    values: filterAndMapDirectory(directoryList, [
                      directoryKeyValue.employee,
                    ]),
                    previousvalue: value,
                  };
                } else if (label === "Contractor") {
                  return {
                    ...item,
                    value: value,
                    required,
                    multiple,
                    status: "button" as IStatus,
                    values: filterAndMapDirectory(directoryList, [
                      directoryKeyValue.contractor,
                      directoryKeyValue.vendor,
                    ]),
                    previousvalue: value,
                  };
                } else if (label === "Customer &amp; Lead") {
                  return {
                    ...item,
                    value: value,
                    required,
                    multiple,
                    status: "button" as IStatus,
                    values: filterAndMapDirectory(directoryList, [
                      directoryKeyValue.lead,
                      directoryKeyValue.customer,
                    ]),
                    previousvalue: value,
                  };
                } else if (["Misc. Contact", "Misc Contact"].includes(label)) {
                  return {
                    ...item,
                    value: value,
                    required,
                    multiple,
                    status: "button" as IStatus,
                    values: filterAndMapDirectory(directoryList, [
                      directoryKeyValue.misc_contact,
                    ]),
                    previousvalue: value,
                  };
                } else if (label === "Equipment") {
                  return {
                    ...item,
                    value: multiple
                      ? value
                      : equipmentList?.find((p) => p.value === value)?.value ??
                        "",
                    required,
                    multiple,
                    status: "button" as IStatus,
                    values: equipmentList,
                    previousvalue: value,
                  };
                } else if (label === "Project") {
                  return {
                    ...item,
                    value: multiple
                      ? value
                      : projectList?.find((p) => p.value === value)?.value ??
                        "",
                    required,
                    multiple,
                    status: "button" as IStatus,
                    values: projectList,
                    previousvalue: value,
                  };
                } else if (type === "checkbox-group") {
                  const checkboxList = (item?.values || []).filter(
                    (checkboxData) => checkboxData.label
                  );

                  const checkboxListValue = checkboxList.map(
                    (checkboxData) => checkboxData.value
                  );

                  if (other) {
                    item?.values?.push(otherOptionCheckBox);
                  }

                  let selectedCheckBox: Array<string> = [];

                  let selectedCheckBoxs: Array<string> = Array.isArray(value)
                    ? (value as unknown as Array<string>)
                    : JSON.parse(value as unknown as string);

                  let uniqueArray = [...new Set(selectedCheckBoxs)];

                  let inputValueOther;
                  for (let index = 0; index < uniqueArray.length; index++) {
                    if (checkboxListValue.includes(uniqueArray[index])) {
                      selectedCheckBox.push(
                        HTMLEntities.decode(uniqueArray[index])
                      );
                    } else {
                      if (uniqueArray.length - 1 === index && other) {
                        inputValueOther = uniqueArray[index];
                        selectedCheckBox.push(
                          HTMLEntities.decode(uniqueArray[index])
                        );
                      }
                    }
                  }

                  if (selectedCheckBoxs.length === 0) {
                    const selectedList = (item?.values || []).filter(
                      (checkboxData) => Boolean(checkboxData?.selected) == true
                    );
                    if (
                      objectValue?.[name] !== "[]" &&
                      objectValue?.[name] !== ""
                    ) {
                      for (
                        let index = 0;
                        index < selectedList.length;
                        index++
                      ) {
                        selectedCheckBox.push(
                          HTMLEntities.decode(selectedList[index].value)
                        );
                      }
                    }
                  }

                  if (other && inputValueOther !== undefined) {
                    if (selectedCheckBox.length) {
                      inputValueOther = selectedCheckBox?.find(
                        (selectedValue: string) =>
                          !checkboxList?.some(
                            (checkBox) => checkBox?.value === selectedValue
                          )
                      );
                    }
                  }

                  return {
                    ...item,
                    value: value !== "" ? selectedCheckBox : [],
                    previousvalue:
                      value !== ""
                        ? Array.isArray(value)
                          ? (value as unknown as Array<string>)
                          : JSON.parse(value as unknown as string)
                        : [],
                    required,
                    inputValueOther: other ? inputValueOther : undefined,
                    other,
                    values: item?.values || [],
                    status: "button" as IStatus,
                    view: item?.inline ? item?.inline : false,
                    description: item?.description,
                  };
                } else if (type === "radio-group") {
                  const radioGroupList = (item?.values || []).map((rglist) => ({
                    label: HTMLEntities.decode(rglist.label),
                    value: HTMLEntities.decode(rglist.value),
                  }));
                  const radioList = (radioGroupList || []).filter(
                    (radioData) => radioData.label
                  );

                  let inputValueOther;
                  if (other) {
                    (radioGroupList || []).push(otherOptionRadio);
                    if (
                      !radioList.some(
                        (radio) => radio.value.toString() === value?.toString()
                      )
                    ) {
                      inputValueOther = value as string;
                    }
                  }

                  if (!value) {
                    const selectedValue = (item?.values || []).find(
                      (radioboxData) => Boolean(radioboxData?.selected) == true
                    );
                    value = selectedValue?.value;
                  }

                  return {
                    ...item,
                    value: value,
                    inputValueOther,
                    other,
                    values: radioGroupList || [],
                    previousvalue: previousValue,
                    status: "button" as IStatus,
                    required,
                  };
                } else if (type === "button") {
                  if (!!value) {
                    const isGetUrl = await isCheckUrl(value);
                    return {
                      ...item,
                      value: isGetUrl,
                      previousvalue: isGetUrl,
                      status: "button" as IStatus,
                      action: "ML" as IAction,
                      required,
                    };
                  } else {
                    return {
                      ...item,
                      value,
                      previousvalue: previousValue,
                      status: "button" as IStatus,
                      action: "ML" as IAction,
                      required,
                    };
                  }
                }
                return {
                  ...item,
                  value,
                  previousvalue: previousValue,
                  status: "button" as IStatus,
                  action: "ML" as IAction,
                  required,
                };
              }
            )
          );
          setCustomFieldId(getCustomFieldId);
          setComponentList(mergedArrayData as ICustomFieldKey[]);
        } catch (error) {
          setComponentList([]);
          setApiError((error as Error).message);
        } finally {
          setLoadingCustomFields(false);
        }
      }
    };

    fetchData();
  }, [
    JSON.stringify(directory),
    JSON.stringify(directoryKeyValue),
    moduleId,
    recordId,
  ]);

  return useMemo(
    () => ({
      componentList,
      setComponentList,
      loadingCustomField,
      setLoadingCustomFields,
      apiError,
      setApiError,
      customFieldId,
    }),
    [componentList, loadingCustomField]
  );
};

function toArray(value: unknown): string[] {
  if (Array.isArray(value)) {
    return value.map(String); // Ensure all elements are strings
  }

  if (typeof value === "string") {
    try {
      const parsed = JSON.parse(value);
      if (Array.isArray(parsed)) {
        return parsed.map(String); // parsed JSON array
      }
    } catch {
      // Not a JSON string – treat as comma-separated
      return value
        .split(",")
        .map((s) => s.trim())
        .filter(Boolean);
    }
  }

  return []; // fallback for null, undefined, number, object, etc.
}

async function isCheckUrl(url: string): Promise<string> {
  try {
    const response = await fetch(url, { method: "GET" });

    // If the original URL works, return it
    if (
      response.ok &&
      response.headers.get("Content-Type")?.startsWith("image/")
    ) {
      return url;
    }

    // Fallback to signed URL if original fails
    return await getSignedURL(url);
  } catch {
    // In case of network or CORS errors, fallback
    return await getSignedURL(url);
  }
}

async function getSignedURL(url: string): Promise<string> {
  try {
    const fileData = {
      file_path: url,
      expirationTime: 0,
    };

    const params = await getWebWorkerApiParams({
      otherParams: fileData,
    });

    const response: any = await webWorkerApi<any>({
      url: apiRoutes.COMMON.exchange_signed_token,
      method: "post",
      data: params,
    });

    return response?.data?.exchanged_file_path || "";
  } catch {
    return "";
  }
}

export default useCustomField;
