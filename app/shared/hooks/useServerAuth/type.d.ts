import { Session, SessionData, TypedResponse } from "@remix-run/node";

declare global {
  interface RefreshAuthTokenResponse extends IApiCallResponse {
    data?: {
      accessToken?: string;
    };
  }

  type RedirectOnPagedHandler = (
    redirectInit?: number | ResponseInit
  ) => TypedResponse<never> | undefined;

  type SetAuthTokenHandler = (
    token: string,
    headers?: Headers
  ) => Promise<Headers>;
  type DestroySiteSessionHandler = (headers?: Headers) => Promise<Headers>;

  type DecodeAuthTokenHandler = (token: string) => any; //this type change in future

  interface RefreshAuthTokenFuncResponse {
    headers: Headers;
    token: string;
    is_refresh_token: boolean;
    isRefreshTokenUnauthorize?: boolean;
  }

  type RefreshAuthTokenHandler = (
    token: string,
    headers?: Headers
  ) => Promise<RefreshAuthTokenFuncResponse>;

  interface RefreshCSFRTokenFuncResponse {
    headers: Headers;
    csrfToken: string;
  }
  type RefreshCSFRTokenHandler = (
    headers?: Headers
  ) => Promise<RefreshCSFRTokenFuncResponse>;

  interface UseServerAuthResponse {
    redirectOnPage: RedirectOnPagedHandler;
    setAuthToken: SetAuthTokenHandler;
    decodeAuthToken: DecodeAuthTokenHandler;
    refreshAuthToken: RefreshAuthTokenHandler;
    destroySiteSession: DestroySiteSessionHandler;
    authorization: string | undefined;
    refreshToken: string | undefined;
    session: Session<SessionData, SessionData>;
    csrfToken?: string;
    RefreshCSFRTokenHandler: RefreshCSFRTokenHandler;
  }

  type UseServerAuthHandler = (
    request: Request
  ) => Promise<UseServerAuthResponse>;
}
