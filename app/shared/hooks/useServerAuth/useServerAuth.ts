import { redirect } from "@remix-run/node";

// helpers
import { removeLeadingSlash } from "~/helpers/custom.helper";
import { JwtDecoded } from "~/helpers/helper.server";

// shared
import {
  isURLModuleDeveloped,
  isURLUnderMaintenance,
} from "~/shared/utils/common";
import { isAuthURL } from "~/shared/utils/helper/authRoute";

// other
import { routes } from "~/route-services/routes";
import { authenticator } from "~/services/auth.server";
import {
  commitSession,
  destroySession,
  getSession,
  sessionPrefs,
} from "~/services/session.server";
import { getRefreshTokenResponse } from "~/shared/controllers/authController.server";
import { randomBytes } from "crypto";

export const useServerAuth: UseServerAuthHandler = async (request) => {
  const headerCookie = request.headers.get("Cookie");
  const authKey = authenticator.sessionKey;

  // get session
  let session = await getSession(headerCookie);
  let authorization = session.get(authKey)?.trim() as string | undefined;
  let refreshToken = session.get("refreshToken")?.trim() as string | undefined;
  let csrfToken = session.get("csrfToken")?.trim() as string | undefined;

  // if session not get default then get from cookie
  if (!authorization) {
    // get cookie
    let cookie = await sessionPrefs.parse(headerCookie);
    if (cookie && cookie.session) {
      session = await getSession(cookie.session);
      authorization = session.get(authKey)?.trim() as string | undefined;
    }
  }

  const redirectOnPage: RedirectOnPagedHandler = (redirectInit) => {
    const redirectFun = (url: string) => {
      if (redirectInit) {
        return redirect(url, redirectInit);
      }
      return redirect(url);
    };
    // check site is on maintenance
    if (!isURLUnderMaintenance(request.url)) {
      const newUrl = new URL(request.url);
      if (!isURLModuleDeveloped(request.url)) {
        const returnURL = newUrl.searchParams.get("return_url");
        let page = process.env.PANEL_URL;
        if (returnURL) {
          page = "https://" + returnURL + "/";
        }
        const decodedPathname = decodeURIComponent(newUrl.pathname);
        if (decodedPathname) {
          if (
            decodedPathname === routes.DASHBOARD.url ||
            (authorization && isAuthURL(request.url))
          ) {
            page += "index.php";
          } else {
            page += removeLeadingSlash(
              decodedPathname.replaceAll("-", "_") + ".php"
            );
          }
        }
        if (page) {
          page += newUrl.search;
          return redirectFun(page);
        }
      }
      return redirectFun(newUrl.pathname + newUrl.search);
    }
  };

  const setAuthToken: SetAuthTokenHandler = async (token, headers) => {
    const newHeaders = headers || new Headers();

    authorization = token;

    // get cookie
    let cookie = await sessionPrefs.parse(headerCookie);

    // Ensure cookie is initialized
    cookie = cookie || {};

    // set auth token and refresh token
    session.set(authKey, token);

    // commit session
    const sessionCookie = await commitSession(session, {
      expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 365 * 10), // [( millisecond * second * minute * hours * days * years)]
    });

    // commit auth token set in cookie session
    cookie.session = sessionCookie;

    // serialize cookie
    const sessionPrefsCookie = await sessionPrefs.serialize(cookie, {
      expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 365 * 10), // [( millisecond * second * minute * hours * days * years)]
    });

    newHeaders.append("Set-Cookie", sessionPrefsCookie);
    newHeaders.append("Set-Cookie", sessionCookie);

    return newHeaders;
  };

  const destroySiteSession: DestroySiteSessionHandler = async (headers) => {
    const newHeaders = headers || new Headers();

    // get cookie
    let cookie = await sessionPrefs.parse(headerCookie);
    // Ensure cookie is initialized
    cookie = cookie || {};
    // blank cookie session
    cookie.session = "";

    // serialize cookie
    const sessionPrefsCookie = await sessionPrefs.serialize(cookie);
    // destroy Auth Session
    const destroyAuthSession = await destroySession(session);
    // add session and cookie in header
    newHeaders.append("Set-Cookie", sessionPrefsCookie);
    newHeaders.append("Set-Cookie", destroyAuthSession);
    newHeaders.append("Clear-Site-Data", "cookies");

    return newHeaders;
  };

  const decodeAuthToken: DecodeAuthTokenHandler = (token) =>
    process.env.JWT ? JwtDecoded(token, process.env.JWT) : "";

  const refreshAuthToken: RefreshAuthTokenHandler = async (token, headers) => {
    let newHeaders = headers || new Headers();
    let isRefreshToken = false;
    let isRefreshTokenUnauthorize = false;

    let exp = 0;
    if (token) {
      // Decode the JWT token to extract user information.
      const jwtDecoded = (await JwtDecoded(
        token,
        process.env.JWT || ""
      )) as AuthDecodedPayload;

      // Destructure properties from `jwtDecoded` and omit unnecessary ones
      exp = jwtDecoded?.exp || 0;
    }

    let newToken = "";

    if (exp && exp < Math.floor(Date.now() / 1000)) {
      if (refreshToken) {
        const response = await getRefreshTokenResponse(request);
        if (response.success && response.data) {
          newHeaders = await setAuthToken(
            response.data.accessToken,
            newHeaders
          );
          isRefreshToken = true;
          newToken = response.data.accessToken;
        } else if (response.statusCode?.toString() === "401") {
          isRefreshTokenUnauthorize = true;
        }
      }
    } else {
      isRefreshToken = true;
      newToken = token;
    }

    return {
      headers: newHeaders,
      is_refresh_token: isRefreshToken,
      token: newToken,
      isRefreshTokenUnauthorize,
    };
  };

  const RefreshCSFRTokenHandler: RefreshCSFRTokenHandler = async (headers) => {
    const newHeaders = headers || new Headers();
    const csrfTokenNew = randomBytes(32).toString("hex");

    authorization = csrfTokenNew;
    // get cookie
    let cookie = await sessionPrefs.parse(headerCookie);

    // Ensure cookie is initialized
    cookie = cookie || {};

    // set auth csrfTokenNew and refresh csrfTokenNew
    session.set("csrfToken", csrfTokenNew);

    // commit session
    const sessionCookie = await commitSession(session, {
      expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 365 * 10), // [( millisecond * second * minute * hours * days * years)]
    });

    // commit auth token set in cookie session
    cookie.session = sessionCookie;

    // serialize cookie
    const sessionPrefsCookie = await sessionPrefs.serialize(cookie, {
      expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 365 * 10), // [( millisecond * second * minute * hours * days * years)]
    });

    newHeaders.append("Set-Cookie", sessionPrefsCookie);
    newHeaders.append("Set-Cookie", sessionCookie);

    return { headers: newHeaders, csrfToken: csrfTokenNew };
  };

  return {
    redirectOnPage,
    setAuthToken,
    refreshAuthToken,
    decodeAuthToken,
    destroySiteSession,
    authorization,
    refreshToken,
    session,
    csrfToken,
    RefreshCSFRTokenHandler,
  };
};
