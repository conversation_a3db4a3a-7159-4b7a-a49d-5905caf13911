import axios from "axios";
import { getFileExtension } from "~/modules/document/fileAndPhoto/utils/googleDrivePicker";
import { apiRoutes } from "~/route-services/routes";
import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";

// get blob file from url
export const getBlobFileFromUrl = async (url: string, accessToken?: string) => {
  const file_name = url.split("/").pop();
  const file_ext =
    file_name?.split(".").pop()?.split("?")[0]?.toLowerCase() ?? "";

  const response = await axios.get(url, {
    responseType: "blob",
    headers: accessToken
      ? {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "image/webp",
        }
      : {},
  });

  const newBlob = new Blob([response.data], {
    type:
      file_ext === "webp" || file_ext === "WEBP"
        ? "image/webp"
        : response.data.type,
  });

  return newBlob;
};
export const AWSFileUploadApi = async (
  file: Blob,
  fileName?: string,
  moduleName?: string,
  isFileNameNeeded?: boolean
) => {
  let fileType = file.type;
  if (fileName) {
    const ext = getFileExtension(fileName).toUpperCase();
    fileName = fileName;
    if (ext == "HEIC") {
      fileName = fileName.replace(/\.heic$/i, ".HEIC");
    }

    if (ext == "msg" || ext == "MSG") {
      fileType = "application/vnd.ms-outlook";
    }
    if (ext == "KMZ" || ext == "kmz") {
      fileType = "application/vnd.google-earth.kmz";
    }
    if (ext == "DWG" || ext == "dwg") {
      fileType = "application/acad";
    }
    if (ext == "WEBP" || ext == "webp") {
      fileType = "image/webp";
    }
    if (ext == "XLSM" || ext == "xlsm") {
      fileType = "application/vnd.ms-excel.sheet.macroEnabled.12";
    }
  }

  let file_name;
  try {
    file_name = decodeURI(fileName as string);
  } catch (error) {
    notification.error({
      key: "decode-uri-error", // This is the important part
      description: `Invalid file name`,
    });
    file_name = fileName;
  }

  // get aws signed url
  let signedUrlData = {
    moduleName,
    thumb: file.type.startsWith("image/") ? true : false,
    fileType: fileType,
    fileName: file_name || "",
    isFileNameNeeded: isFileNameNeeded,
  };

  const signedUrlParams = await getWebWorkerApiParams({
    otherParams: {
      ...signedUrlData,
    },
  });

  const signedUrlResponse = (await webWorkerApi({
    url: apiRoutes.COMMON.upload_file,
    method: "post",
    data: signedUrlParams,
  })) as IFileUploadApiResponseData;

  if (signedUrlResponse && signedUrlResponse.data) {
    // upload file to aws
    await axios.put(signedUrlResponse.data.signedUrl, file, {
      headers: {
        "Content-Type": fileType, // Set the content type of the file
      },
    });
  }

  return signedUrlResponse;
};

export const handleFileAddFiles = async (
  file_add_files: IAddFileDataToServer
) => {
  const final_data_params = await getWebWorkerApiParams({
    otherParams: {
      ...file_add_files,
    },
  });

  const new_file_added_response = (await webWorkerApi({
    url: apiRoutes.COMMON.add_file,
    method: "post",
    data: final_data_params,
  })) as IFileAddFilesResponseData;

  return new_file_added_response;
};
