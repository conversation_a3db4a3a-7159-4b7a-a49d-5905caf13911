// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

import { memo, useMemo } from "react";
import { CFAvatar } from "~/components/third-party/ant-design/cf-avatar";
import { CFTypography } from "~/components/third-party/ant-design/cf-typography";
import { AnimateCheckmark } from "~/components/common/animate/animate-checkmark";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { useTranslation } from "~/hook";
import { dirTypeIds } from "~/modules/people/directory/utils/constasnts";
import { defaultConfig } from "~/data";
import { useGModules } from "~/zustand";
import { MLESOITEMS_SELECTOPTION_TAB_KEYS } from "~/modules/settings/costItemsDatabase/utils/constants";
import { useGlobalModule } from "~/zustand/global/modules/slice";

const SingleItem = ({
  active,
  avatar,
  name,
  title,
  unitCostPriceUnit,
  favoriteIcon = false,
  itemPriceUnit = false,
  onClick = () => {},
  onClickManageFav = () => {},
  isFavorite,
  isViewAvatar = true,
  activeField,
  item,
}: // supplier_name, will remove once get confirmation
// supplier_type,
ICidbSingleItemSendEmailProps<ObjType>) => {
  const { _t } = useTranslation();
  const { checkModuleAccessByKey } = useGModules();

  const module_key = useMemo(() => {
    if (activeField !== defaultConfig.group_key) return null;
    switch (item?.item_type_display_name) {
      case "MTL":
        return "material";
      case "EQUIP":
        return "equipment";
      case "LBR":
        return "labor";
      case "SUB":
        return "sub_contractor";
      case "Other":
        return "other";
      default:
        return "groups";
    }
  }, [activeField, item?.item_type_display_name]);

  const { getGlobalModuleByKey } = useGlobalModule();

  const module = useMemo(() => {
    if (!module_key) return null;
    const key = (() => {
      switch (module_key) {
        case MLESOITEMS_SELECTOPTION_TAB_KEYS.material:
          return CFConfig.material_module;
        case MLESOITEMS_SELECTOPTION_TAB_KEYS.labor:
          return CFConfig.labour_module;
        case MLESOITEMS_SELECTOPTION_TAB_KEYS.equipment:
          return CFConfig.equipment_module;
        case "sub_contractor":  
        case MLESOITEMS_SELECTOPTION_TAB_KEYS.sub_contractor:
          return CFConfig.sub_contractor_module;
        case MLESOITEMS_SELECTOPTION_TAB_KEYS.other:
          return CFConfig.otherItems_module;
        default:
          return CFConfig.group_module;
      }
    })();
    return getGlobalModuleByKey(key);
  }, [module_key]);

  const isCidReadOnly = useMemo(() => {
    if (!module?.module_key) return false;
    return checkModuleAccessByKey(module.module_key) === "read_only";
  }, [module?.module_key]);

  return (
    <div
      className={`flex items-center justify-between hover:bg-gray-200/30 dark:hover:bg-[#2e3b47] ${
        favoriteIcon ? "pr-[15px]" : "pr-0"
      } ${active ? "bg-gray-200/30 dark:bg-[#2e3b47]" : ""}`}
    >
      <div
        className={`flex items-center justify-between py-[5px] text-primary-900 dark:text-white/90 cursor-pointer ${
          favoriteIcon
            ? "pl-[15px] pr-0 w-[calc(100%-20px)]"
            : "px-[15px] w-full"
        }`}
        onClick={onClick}
      >
        <div className="flex items-center justify-between gap-3 w-[calc(100%-26px)]">
          <div
            className={`flex items-center gap-3 ${
              itemPriceUnit ? "w-[calc(100%-115px)]" : "w-full"
            }`}
          >
            {isViewAvatar && <CFAvatar user={{ image: avatar, name: name }} />}
            <div
              className={`text-13 ${
                isViewAvatar
                  ? "max-w-[calc(100%-50px)]"
                  : "max-w-[calc(100%-20px)]"
              }`}
              style={{ wordBreak: "break-word" }}
            >
              <div className="inline">
                {name}
                <b>{title && `${!name ? "" : ","} ${title}`}</b>
              </div>
              {/* will remove once get confirmation */}
              {/* {supplier_name && (
                <Tooltip title={`${_t("Supplier")}: ${supplier_name}`}>
                  {supplier_type != dirTypeIds.contractors ? (
                    <FontAwesomeIcon
                      className="text-base w-3.5 h-3.5 text-primary-900 inline pl-1"
                      icon="fa-regular fa-helmet-safety"
                    />
                  ) : (
                    <FontAwesomeIcon
                      className="text-base w-3.5 h-3.5 text-primary-900 inline pl-1"
                      icon="fa-regular fa-tags"
                    />
                  )}
                </Tooltip>
              )} */}
            </div>
          </div>
          {itemPriceUnit && (
            <CFTypography
              title="small"
              className="text-primary-900 dark:text-white/90 font-light text-13 break-words"
            >
              {unitCostPriceUnit}
            </CFTypography>
          )}
        </div>
        <div className="w-5 h-[18px]">
          <div
            className={`transition-all ease-in-out duration-100 overflow-hidden ${
              active ? "block" : "hidden"
            }`}
          >
            <AnimateCheckmark circleClass="hidden" />
          </div>
        </div>
      </div>
      <div
        className={`flex  ${
          isCidReadOnly ? "cursor-not-allowed" : "cursor-pointer fsdfsdfas"
        }`}
      >
        {favoriteIcon && (
          <FontAwesomeIcon
            className={"w-4 h-4 text-deep-orange-500"}
            icon={
              isFavorite !== "0" ? "fa-solid fa-star" : "fa-regular fa-star"
            }
            onClick={() => {
              if (isCidReadOnly) return;
              onClickManageFav(item as ObjType);
            }}
          />
        )}
      </div>
    </div>
  );
};

export default memo(SingleItem);
