// Hooks
import { useTranslation } from "~/hook";

// Atoms
import { Paragraph } from "~/shared/components/atoms/paragraph";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Button } from "~/shared/components/atoms/button";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { FilePreview } from "~/shared/components/molecules/filePreview";
import { ThumbnailView } from "~/shared/components/molecules/thumbnailView";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { MarkupModal } from "~/shared/components/molecules/markupModal";

import { deleteFiles } from "~/modules/document/fileAndPhoto/redux/action/filePhotoRightAction";
import { useEffect, useMemo, useState } from "react";
import { uploadFile } from "~/redux/action/fileAttachmentAction";
import { getGConfig, getGSettings } from "~/zustand";
import { useAppDispatch } from "~/redux/store";
import { putFilesToSignedUrl } from "~/shared/utils/helper/putFilesToSignedUrl";
import { getMimeType } from "~/shared/utils/helper/getMimeType";
import { useFilePreview } from "~/shared/hooks/useFilePreview";
import { updateMarkupFile } from "~/redux/action/fileEditAction";
import { VALID_EXTENSIONS_MARKUP } from "../galleryFilePhotos/constants";
import cloneDeep from "lodash/cloneDeep";
import { faUpRightAndDownLeftFromCenter } from "@fortawesome/pro-solid-svg-icons/faUpRightAndDownLeftFromCenter";
import { sanitizeString } from "~/helpers/helper";
import { IAttachedFileListProps } from "../../molecules/attachedFileList/type";
import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";
import { apiRoutes } from "~/route-services/routes";

const AttachedFileList = ({
  isReadOnly,
  file,
  editView,
  onDeleteFile,
  isShowDeleteMenu,
  fetchFileDetails,
  closeAddFileDrawer,
  setUpdatedData,
  uploadFileCallBack,
  notesAttach = false,
  handleShareWithClient,
  isSharing,
  selectedTab,
  isNotes,
  setNotes,
  filesData,
  setFilesData,
  projectid,
  isReview = false,
  setIsFileDeleted,
  setDeletedFile,
  isFileSharable = true,
  strictllyAllowShareWithClient = false,
  attachmentsFromEmailDrawer,
  filtered_filesData = [],
}: IAttachedFileListProps) => {
  const dispatch = useAppDispatch();
  const { _t } = useTranslation();
  const { module_key }: GConfig = getGConfig();
  const { retain_original_image }: GSettings = getGSettings();
  // This will change after design
  const isMenuOpen = false;
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState<boolean>(false);
  const [isMarkupAddFileOpen, setIsMarkupAddFileOpen] =
    useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [isNoLoading, setIsNoLoading] = useState<boolean>(false);
  const [menuOpen, setMenuOpen] = useState<boolean>(false);
  const [isMarkupModalOpen, setIsMarkupModalOpen] = useState<boolean>(false);
  const [isEditConfirmModalOpen, setIsEditConfirmModalOpen] =
    useState<boolean>(false);
  const [markupedFileData, setMarkupedFileData] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [markupLoading, setMarkupLoading] = useState(false);
  const [Extenstion, setExtention] = useState<boolean>(false);
  const [isMarkupApply, setIsMarkupApply] = useState<boolean>(false);
  const [imgSrc, setImgSrc] = useState<string>("");
  let {
    file_ext = "",
    file_name,
    file_path,
    image_id,
    is_image,
    original_url,
    is_file_shared,
    id,
    annotation_data,
    show_client_access,
    signedUrl,
    thumb_file_path,
    isheic,
    cdnUrl,
    fileUrl,
    original_image_id,
  } = file;

  // file image download function s3 bucket and cdn URL
  // Note:- in future this code will be convert into helper fun

  async function download(url: string) {
    const a = document.createElement("a");
    if (url.includes("/thumb/")) {
      url = url.replace("/thumb/", "/large/");
    }
    const dataUrl = await toDataURL(url);
    const filename = url.substring(url.lastIndexOf("/") + 1).split("?")[0];
    a.href = dataUrl;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  }

  function toDataURL(url: string) {
    return fetch(url)
      .then((response) => {
        return response.blob();
      })
      .then((blob) => {
        return URL.createObjectURL(blob);
      });
  }

  const {
    handleFilePreview,
    previewUrlOpen,
    previewUrl,
    previewUrlLoding,
    setPreviewUrlOpen,
  } = useFilePreview();

  const LIST_OPTIONS: IDropdownMenuOption[] = [
    {
      label: "Download",
      icon: "fa-regular fa-download",
      key: "0",
      onClick: async () => {
        if (editView) {
          if (
            VALID_EXTENSIONS_MARKUP.includes(
              file_ext?.toString()?.toLowerCase()
            )
          ) {
            try {
              const fileData = {
                file_path: Array.isArray(file_path)
                  ? file_path[0] ?? ""
                  : file_path ?? "",
                expirationTime: 0,
              };

              const data = await getWebWorkerApiParams({
                otherParams: fileData,
              });

              // it will update in future
              const response: any = await webWorkerApi<any>({
                url: apiRoutes.COMMON.exchange_signed_token,
                method: "post",
                data: data,
              });
              if (!!response?.data?.exchanged_file_path) {
                download(`${response.data.exchanged_file_path}`);
              }
            } catch (error) {
              console.error("Error while editing file preview:", error);
            }
          } else {
            download(
              Array.isArray(file_path) ? file_path[0] ?? "" : file_path ?? ""
            );
          }
        } else {
          if (file_path || cdnUrl) {
            // URL
            const filePath =
              notesAttach || !editView
                ? cdnUrl ||
                  (Array.isArray(file_path) ? file_path[0] : file_path)
                : Array.isArray(file_path)
                ? file_path[0]
                : file_path;

            download(filePath);
          }
        }
        setMenuOpen(false);
      },
    },
    {
      label: "Markup",
      icon: "fa-regular fa-paintbrush-pencil",
      key: "1",
      onClick: () => {
        if (VALID_EXTENSIONS_MARKUP.includes(file_ext as string)) {
          handleFilePreview(
            notesAttach || !editView
              ? !!cdnUrl
                ? cdnUrl
                : file_path
              : Array.isArray(file_path) && file_path.length === 1
              ? (file_path[0] as string)
              : ((file_path ?? signedUrl) as string)
          );
        } else {
          if (editView) {
            setIsMarkupModalOpen(true);
          } else {
            setIsMarkupAddFileOpen(true);
          }
        }
        setMenuOpen(false);
      },
    },
    {
      label: "Delete",
      icon: "fa-regular fa-trash-can",
      key: "2",
      onClick: () => {
        deleteModalOpen();
        setMenuOpen(false);
      },
    },
  ];

  const [OPTIONS, setOPTIONS] = useState<IDropdownMenuOption[]>(LIST_OPTIONS);

  useEffect(() => {
    let listOption = cloneDeep(LIST_OPTIONS);
    if (!isShowDeleteMenu) {
      listOption = listOption.filter((ele) => {
        return ele?.key !== "2";
      });
    }

    if (
      !VALID_EXTENSIONS_MARKUP.includes(file_ext as string) &&
      !is_image &&
      file_ext !== "pdf"
    ) {
      listOption = listOption.filter((ele) => {
        return ele?.key !== "1";
      });
    }
    setOPTIONS(listOption);
  }, [file, fetchFileDetails]);

  const deleteModalOpen = () => {
    setIsConfirmModalOpen(true);
  };
  const handleDeleteFile = async () => {
    setLoading(true);
    if (!notesAttach) {
      let deleteParams;
      if (image_id) {
        const imageIdNumber = image_id as number;
        deleteParams = { fileIds: [imageIdNumber] };
      } else {
        const filePath = file_path as string;
        deleteParams = { fileUrl: [filePath] };
      }
      if (isNotes) {
        if (!image_id) {
          const fileterData =
            filesData &&
            filesData?.filter((data: IFile) => data?.file_name !== file_name);
          setMarkupedFileData({});
          setIsMarkupApply(false);
          setFilesData(fileterData);
          setLoading(false);
          setIsConfirmModalOpen(false);
          await deleteFiles(deleteParams);
        } else if (image_id) {
          const fileterData = filesData?.filter(
            (data: IFile) => data?.image_id !== image_id
          );
          setFilesData(fileterData);
          setLoading(false);
          setIsConfirmModalOpen(false);
        }
      } else {
        const response = await deleteFiles(deleteParams);
        if (response) {
          setLoading(false);
          setIsConfirmModalOpen(false);

          if (!image_id) {
            onDeleteFile({ file_path });
          } else {
            onDeleteFile({ image_id, response });
          }
        }
      }
    } else {
      if (!image_id) {
        const fileterData = filesData?.filter(
          (data: IFile) => data?.file_path != file_path
        );
        setMarkupedFileData({});
        setIsMarkupApply(false);
        setIsFileDeleted?.(true);
        setDeletedFile?.(id);
        setFilesData(fileterData);
        await deleteFiles({ fileUrl: [file_path] });
      } else {
        onDeleteFile({ image_id });
        await deleteFiles({ fileUrl: [file_path] });
      }
      setLoading(false);
      setIsConfirmModalOpen(false);
    }
    if (closeAddFileDrawer) {
      closeAddFileDrawer();
    }
  };

  const closeModalSaveOrAddFile = () => {
    if (editView && setIsMarkupModalOpen) {
      setIsMarkupModalOpen(false);
      setIsMarkupApply(false);
    } else if (!editView) {
      setIsMarkupAddFileOpen(false);
    }
    setPreviewUrlOpen(false);
  };

  const handleSaveAddFile = async (
    info: ISavedImageData | IPdfjsExpressPropsOnSaveValue
  ) => {
    if (!file) return;
    const fileExt = getMimeType(file_ext);
    const isThumbRequired = [
      "jpg",
      "jpeg",
      "png",
      "gif",
      "bmp",
      "webp",
    ].includes(file_ext?.toLowerCase());

    let fileName;
    try {
      fileName = decodeURI(file_name as string);
    } catch (error) {
      notification.error({
        key: "decode-uri-error",
        description: `Invalid file name`,
      });
      fileName = file_name;
    }

    // Construct payload conditionally
    const uploadPayload: any = {
      moduleName: module_key,
      fileName: fileName,
      fileType: fileExt || undefined,
      saveAsNew:
        retain_original_image === 0
          ? 0
          : filtered_filesData?.some(
              (item) =>
                item?.original_image_id === image_id ||
                item?.image_id === original_image_id
            )
          ? 0
          : retain_original_image,
      isThumbRequired: isThumbRequired,
      isMarkedUp: true,
    };
    // Only add fileId if not a note
    if (!isNotes && !editView) {
      uploadPayload.fileId = image_id;
    }

    const response = (await dispatch(uploadFile(uploadPayload))) as {
      payload: IGetUploadFileRes;
    };
    if (response?.payload?.data?.signedUrl) {
      const signedUrl = response.payload.data.signedUrl;
      uploadFileCallBack(response.payload.data, file);
      await putFilesToSignedUrl(
        signedUrl,
        info as ISavedImageData | IPdfjsExpressPropsOnSaveValue
      );
      setIsMarkupApply(true);
      setMarkupedFileData(response.payload.data);
      closeModalSaveOrAddFile();
    } else {
      setIsMarkupApply(false);
      closeModalSaveOrAddFile();
    }
    setMarkupLoading(false);
  };

  const handleSave = async (
    info: ISavedImageData | IPdfjsExpressPropsOnSaveValue
  ) => {
    onAcceptOrDecline(info);
    if (!Object.keys(fetchFileDetails ?? {}).length) {
      return;
    }
  };

  const updateFile = async (
    fetchFileDetails: IFile,
    info: ISavedImageData | IPdfjsExpressPropsOnSaveValue,
    signedUrl: string
  ) => {
    const urlObject = new URL(signedUrl);
    const baseUrl = `${urlObject.origin}${urlObject.pathname}`;

    let fileIdToSend = fetchFileDetails.image_id;
    let fileUrlToSend = baseUrl;

    // ✅ Detect if this is updating original image (so we must refer to the markup's ID/url)
    const isUpdatingOriginal = filtered_filesData?.some(
      (item) => item.original_image_id === fetchFileDetails.image_id
    );

    const markupFile = filtered_filesData?.find(
      (item) => item.original_image_id === fetchFileDetails.image_id
    );

    const originalFile = filtered_filesData?.find(
      (item) => item.image_id === fetchFileDetails.original_image_id
    );

    // ✅ If updating original → use markup file's ID + fileUrl
    if (isUpdatingOriginal && markupFile) {
      fileIdToSend = markupFile.image_id;
      fileUrlToSend = markupFile.fileUrl || baseUrl;
    }

    // ✅ Else, if it's a markup being updated → use current file info (already default)

    const response = (await dispatch(
      updateMarkupFile({
        projectId:
          projectid && Number(projectid) > 0 ? Number(projectid) : undefined,
        fileUrl: fileUrlToSend,
        fileId: fileIdToSend,
        annotationData: "xfdf" in info ? info.xfdf : undefined,
      })
    )) as { payload: IgetUpdatedFileRes };
    if (setUpdatedData) {
      if (response?.payload) {
        response.payload.isAddNew =
          retain_original_image === 0
            ? 0
            : filtered_filesData?.some(
                (item) =>
                  item?.original_image_id === fetchFileDetails.image_id ||
                  item?.image_id === fetchFileDetails.original_image_id
              )
            ? 0
            : retain_original_image;
      }
      setUpdatedData(response?.payload);
      setMarkupedFileData(response?.payload?.data);
    }
  };

  const onAcceptOrDecline = async (
    info: ISavedImageData | IPdfjsExpressPropsOnSaveValue
  ) => {
    let saveAsNew =
      retain_original_image === 0
        ? 0
        : filtered_filesData?.some(
            (item) =>
              item?.original_image_id === image_id ||
              item?.image_id === original_image_id
          )
        ? 0
        : retain_original_image;

    try {
      const fullFileName = `${(fetchFileDetails as IFile)?.file_name}.${
        (fetchFileDetails as IFile)?.file_ext
      }`;
      const fileExt = getMimeType(
        (fetchFileDetails as IFile)?.file_ext?.toString() ?? ""
      );
      const isThumbRequired = [
        "jpg",
        "jpeg",
        "png",
        "gif",
        "bmp",
        "webp",
      ].includes((fetchFileDetails as IFile).file_ext?.toLowerCase());

      const isUpdatingOriginal = filtered_filesData?.some(
        (item) => item.original_image_id === image_id
      );

      const markupFile = filtered_filesData?.find(
        (item) => item.original_image_id === image_id
      );

      const originalFile = filtered_filesData?.find(
        (item) => item.image_id === original_image_id
      );

      // ✅ Determine: Upload fileId should be from markup (if updating original)
      let fileIdToUpload =
        isUpdatingOriginal && markupFile?.image_id
          ? markupFile.image_id
          : image_id;

      const response = (await dispatch(
        uploadFile({
          moduleName: module_key,
          fileName: fullFileName,
          fileType: fileExt,
          saveAsNew,
          isThumbRequired,
          fileId: fileIdToUpload,
        })
      )) as { payload: IGetUploadFileRes };

      if (response?.payload?.data?.signedUrl) {
        const signedUrl = response.payload.data.signedUrl;
        // ✅ For file content, always use the ORIGINAL file
        const fileToSend =
          isUpdatingOriginal && originalFile ? originalFile : fetchFileDetails;

        await putFilesToSignedUrl(
          signedUrl,
          info as ISavedImageData | IPdfjsExpressPropsOnSaveValue
        );

        await updateFile(
          fileToSend as IFile, // send original file info here
          info as ISavedImageData | IPdfjsExpressPropsOnSaveValue,
          signedUrl
        );
      }
      if (setIsEditConfirmModalOpen) setIsEditConfirmModalOpen(false);
      if (setIsMarkupModalOpen) {
        setIsMarkupModalOpen(false);
        setIsMarkupApply(false);
      }
    } catch (error) {
      if (setIsEditConfirmModalOpen) setIsEditConfirmModalOpen(false);
      if (setIsMarkupModalOpen) {
        setIsMarkupModalOpen(false);
        setIsMarkupApply(false);
      }
    } finally {
      setLoading(false);
      setIsNoLoading(false);
      setMarkupLoading(false);
    }
  };

  const handleAccept = () => {
    handleDeleteFile();
  };

  const handleDecline = () => {
    setIsConfirmModalOpen(false);
  };

  const handleCloseModel = () => {
    setIsConfirmModalOpen(false);
  };

  const handleSaveOrAddFile = async (
    info: ISavedImageData | IPdfjsExpressPropsOnSaveValue
  ) => {
    setMarkupLoading(true);
    if (editView && handleSave) {
      await handleSave(info);
    } else if (!editView) {
      setLoading(true);
      await handleSaveAddFile(info);
      setLoading(false);
    }
    // setMarkupLoading(false);
  };

  const getFileExtension = (path: string | string[]): string => {
    let targetPath = "";

    if (typeof path === "string") {
      targetPath = path;
    } else if (Array.isArray(path) && typeof path[0] === "string") {
      targetPath = path[0];
    }
    const parts = targetPath.split(".");
    return parts.length > 1 ? parts.pop()!.split("?")[0].toUpperCase() : "";
  };

  // Function to extract the file name from the file path
  const getFileName = (path: string): string => {
    const parts = Array.isArray(path) ? path[0].split("/") : path.split("/");
    const fullFileName = parts.length > 0 ? parts.pop()! : "";
    return fullFileName?.includes(".")
      ? fullFileName.split(".")[0]
      : fullFileName;
  };

  // Validate file_ext and extract from file_path if necessary
  const validExtensions = [
    "JPG",
    "JPEG",
    "PNG",
    "GIF",
    "WEBP",
    "JFIF",
    "HEIC",
    "PDF",
    "TXT",
    "DOC",
    "DOCX",
    "RTF",
    "ODT",
    "HTML",
    "SXW",
    "XLS",
    "XLSX",
    "XLSM",
    "ODS",
    "SXC",
    "CSV",
    "TSV",
    "PPT",
    "PPTX",
    "MP4",
    "MSG",
    "ZIP",
    "RAR",
    "KMZ",
    "DWG",
    "MOV",
    "FLV",
    "AVI",
    "WMV",
    "M2TS",
    "MTS",
    "MP3",
    "WEBM",
  ];

  const isValidExtension = (ext: string) => {
    return validExtensions.includes(ext.toUpperCase());
  };

  if (!isValidExtension(file_ext) || file_ext === "") {
    if (file_ext !== "") {
      file_ext = getFileExtension(file_path);
      file_name = getFileName(file_path);
    }
  }

  useEffect(() => {
    const hasExt = getFileExtension(file_name);
    if (!hasExt) {
      setExtention(true);
    }
  }, [file_name]);

  return (
    <div className="w-24 group/upload-file App">
      <div className="w-24 h-24 overflow-hidden relative !rounded-xl flex items-center justify-center bg-gray-200 dark:bg-dark-400">
        <ThumbnailView
          key={thumb_file_path}
          file_ext={file_ext ?? ""}
          file_path={file_path ?? ""}
          image_id={image_id}
          markupedFileData={markupedFileData}
          id={id}
          isLoading={isLoading}
          setIsLoading={setIsLoading}
          selectedTab={selectedTab}
          is_image={is_image}
          thumb_file_path={thumb_file_path}
          isheic={isheic}
          cdnUrl={cdnUrl}
          isMarkup={isMarkupApply}
          attachmentsFromEmailDrawer={attachmentsFromEmailDrawer}
          className={is_image ? "object-cover" : ""}
        />
        {isReview ? (
          <div
            className={`absolute top-0 w-full h-full ease-in opacity-0 ${
              isMenuOpen || menuOpen ? "opacity-100 before:scale-100" : ""
            } before:scale-0 before:rounded-xl group-hover/upload-file:opacity-100 group-hover/upload-file:!visible group-hover/upload-file:before:scale-100 delay-100 before:absolute before:w-full before:h-full before:top-0 before:left-0 before:bg-black/50 before:ease-in before:duration-300 `}
          >
            {!isReadOnly && (
              <div className="flex items-center justify-end w-full h-fit p-1.5">
                <Tooltip title={_t("open")} placement="top">
                  <Button
                    className="border-0 p-0 h-6 w-6 bg-transparent group/button hover:bg-[#c7c7c745]"
                    onClick={() => handleShareWithClient(file)}
                  >
                    <FontAwesomeIcon
                      className="w-3.5 h-3.5 md:text-white text-primary-900 group-hover/button:text-primary-900"
                      icon={faUpRightAndDownLeftFromCenter}
                    />
                  </Button>
                </Tooltip>
              </div>
            )}

            <div className="absolute top-2/4 left-2/4 -translate-x-1/2 -translate-y-1/2 group-hover/upload-file:block hidden">
              <Tooltip
                title={
                  is_file_shared
                    ? _t("Click to Detach Document")
                    : _t("Click to Attach Document")
                }
                placement="top"
              >
                <Button
                  className="border-0 p-0 bg-transparent group/button hover:!bg-transparent"
                  onClick={() => handleShareWithClient(file)}
                >
                  <FontAwesomeIcon
                    className="w-10 h-10 md:text-white text-primary-900 "
                    icon={
                      is_file_shared ? "fa-solid fa-user" : "fa-regular fa-user"
                    }
                  />
                </Button>
              </Tooltip>
            </div>
          </div>
        ) : (
          <div
            className={`absolute top-0 w-full h-full ease-in opacity-0 ${
              isMenuOpen || menuOpen ? "opacity-100 before:scale-100" : ""
            } before:scale-0 before:rounded-xl group-hover/upload-file:opacity-100 group-hover/upload-file:!visible group-hover/upload-file:before:scale-100 delay-100 before:absolute before:w-full before:h-full before:top-0 before:left-0 before:bg-black/50 before:ease-in before:duration-300 `}
          >
            {!isReadOnly && (
              <div className="flex items-center justify-end w-full h-fit p-1.5">
                {strictllyAllowShareWithClient ? (
                  <Tooltip
                    title={
                      is_file_shared
                        ? _t("Shared with Client")
                        : _t("Share with Client")
                    }
                    placement="top"
                  >
                    <Button
                      className="border-0 p-0 h-6 w-6 bg-transparent group/button hover:bg-[#c7c7c745]"
                      onClick={
                        isSharing === file.image_id
                          ? undefined
                          : () => handleShareWithClient(file)
                      }
                    >
                      <FontAwesomeIcon
                        className="w-3.5 h-3.5 md:text-white text-primary-900 group-hover/button:text-primary-900"
                        icon={
                          is_file_shared
                            ? "fa-solid fa-user"
                            : "fa-regular fa-user"
                        }
                      />
                    </Button>
                  </Tooltip>
                ) : (
                  isFileSharable &&
                  show_client_access == 1 && (
                    <Tooltip
                      title={
                        is_file_shared
                          ? _t("Shared with Client")
                          : _t("Share with Client")
                      }
                      placement="top"
                    >
                      <Button
                        className="border-0 p-0 h-6 w-6 bg-transparent group/button hover:bg-[#c7c7c745]"
                        onClick={
                          isSharing === file.image_id
                            ? undefined
                            : () => handleShareWithClient(file)
                        }
                      >
                        <FontAwesomeIcon
                          className="w-3.5 h-3.5 md:text-white text-primary-900 group-hover/button:text-primary-900"
                          icon={
                            is_file_shared
                              ? "fa-solid fa-user"
                              : "fa-regular fa-user"
                          }
                        />
                      </Button>
                    </Tooltip>
                  )
                )}
                <DropdownMenu
                  options={OPTIONS}
                  buttonClass="m-0 hover:!bg-white"
                  iconClassName="text-white group-hover/buttonHover:text-primary-900"
                  icon="fa-regular fa-ellipsis-vertical"
                  onDropdownOpenChange={(open: boolean) => setMenuOpen(open)}
                />
              </div>
            )}
            <div className="absolute top-2/4 left-2/4 -translate-x-1/2 -translate-y-1/2 group-hover/upload-file:block hidden">
              <FilePreview
                imageData={{
                  file_path,
                  file_ext,
                  file_name,
                  image_id,
                  id,
                  original_url,
                  annotation_data,
                  cdnUrl,
                  fileUrl,
                  // thumb_file_path,
                }}
                editView={editView}
                markupedFileData={markupedFileData}
                notesAttach={notesAttach}
                isMarkup={isMarkupApply}
                imgSrc={imgSrc}
                setImgSrc={setImgSrc}
                attachmentsFromEmailDrawer={attachmentsFromEmailDrawer}
              />
            </div>
          </div>
        )}
      </div>

      <Tooltip
        title={HTMLEntities.decode(
          sanitizeString(Extenstion ? file_name + "." + file_ext : file_name)
        )}
        placement="top"
      >
        <Paragraph className="!text-[10px] text-center max-w-24 truncate font-normal !text-gray-500 dark:!text-white/50 mt-1.5 !mb-0">
          {_t(
            HTMLEntities.decode(
              sanitizeString(
                Extenstion ? file_name + "." + file_ext : file_name
              )
            )
          )}
        </Paragraph>
      </Tooltip>

      {(isMarkupModalOpen || isMarkupAddFileOpen || previewUrlOpen) && (
        <MarkupModal
          open={!!(editView ? isMarkupModalOpen : isMarkupAddFileOpen)}
          closeModalHandler={closeModalSaveOrAddFile}
          onSave={handleSaveOrAddFile}
          file={editView ? fetchFileDetails : file}
          loading={loading}
          previewUrlOpen={previewUrlOpen}
          previewUrl={previewUrl as string}
          previewUrlLoading={previewUrlLoding}
          markupLoading={markupLoading}
          notesAttach={notesAttach}
          editView={editView}
        />
      )}

      {isConfirmModalOpen && (
        <ConfirmModal
          isLoading={loading}
          isNoLoading={isNoLoading}
          isOpen={isConfirmModalOpen}
          description={_t("Are you sure you want to delete this file?")}
          yesButtonLabel={_t("Yes")}
          noButtonLabel={_t("No")}
          onAccept={handleDeleteFile}
          onDecline={() => setIsConfirmModalOpen(false)}
          onCloseModal={() => setIsConfirmModalOpen(false)}
        />
      )}

      {isEditConfirmModalOpen && (
        <ConfirmModal
          isOpen={editView ? isEditConfirmModalOpen : isConfirmModalOpen}
          isLoading={editView ? loading : false}
          isNoLoading={editView ? isNoLoading : false}
          description={_t("Are you sure you want to delete this file?")}
          modalIcon="fa-regular fa-trash-can"
          modaltitle={_t("Delete")}
          yesButtonLabel={_t("Yes")}
          noButtonLabel={_t("No")}
          onAccept={handleAccept}
          onDecline={handleDecline}
          onCloseModal={handleCloseModel}
          zIndex={9999}
        />
      )}
    </div>
  );
};

export default AttachedFileList;
