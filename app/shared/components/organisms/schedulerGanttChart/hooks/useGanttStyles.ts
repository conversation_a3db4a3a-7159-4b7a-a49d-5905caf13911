import { useEffect, useMemo } from "react";

export const useGanttStyle = () => {
  const styleInnerHtml = useMemo(() => {
    return `
        .gantt_container {
          overflow-x: auto !important;
          z-index: 2 !important;
        }
    
        /* Grid column headers (WBS, Task Name, etc.) */
        .gantt_grid_head_cell {
          font-size: 12px !important;
          font-weight: 500;
          opacity: 0.5;
          border-color: #cbd5e1 !important;
         }
    
        /* Hide Add button in header */
        .gantt_grid_head_cell.gantt_grid_head_add {
          visibility: hidden !important;
        }

        /* PLUS ICON */
        .gantt_add:before, .gantt_grid_head_add:before {
          display: none;
        }

        .gantt_add, .gantt_grid_head_add {
          height: 100% !important;
          background-position: center center !important;
          background-repeat: no-repeat !important;
          cursor: pointer;
          position: relative;
          background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNiAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NTQ3MjMyMENDNkI0MTFFMjk4MTI5QTg3MDhFNDVDQTkiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NTQ3MjMyMERDNkI0MTFFMjk4MTI5QTg3MDhFNDVDQTkiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo1NDcyMzIwQUM2QjQxMUUyOTgxMjlBODcwOEU0NUNBOSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo1NDcyMzIwQkM2QjQxMUUyOTgxMjlBODcwOEU0NUNBOSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PshZT8UAAABbSURBVHjaYrTdeZmBEsCER+4wEP+H4sPkGGCDg020ARR7gb4GIAcYDKMDdPnDyAbYkGG5DVW9cIQMvUdBBAuUY4vDz8iAcZinA2zgCHqAYQMseAywJcYFAAEGAM+UFGuohFczAAAAAElFTkSuQmCC);
        }
    
        /* Grid row cell text */
        .gantt_grid_data .gantt_cell {
          font-size: 13px;
          font-family: 'Arial';
          color: #374151;
        }
    
        .gantt_tree_content {
          display: flex;
          align-items: center;
          gap: 6px;
        }
    
        .gantt_tree_icon {
          width: 16px !important;
          height: 16px !important;
          display: inline-block;
          margin-right: 4px;
          vertical-align: middle;
        }

        .gantt_tree_icon.gantt_folder_open {
          background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAs0lEQVQ4T62T0Q2EIBBEpQlzuaaMsQoqooqLsSljbMLLmMxmUXBR4U+Qt7Mzi2sqLVeJ00SgEMKWAnvvzYLyAyHfT5sU2fXDJSwCAXK8MI0/UTkva7IIFJsg3NSwnKdFoKtAWOQ1CN7CEqeTotE5L7QyJhmBcklZM4ZgTiAr3iOU3kD93ppO5SkMjB1EeXdBWoSkRql3YeIRe+cGvktS056JR9wsmeBUkujCfNXWCPC8GugPqn5ii/hV+FoAAAAASUVORK5CYII=);
        }
          
        .gantt_tree_icon.gantt_folder_closed {
          background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAfElEQVQ4T2NkoBJgpJI5DCgGTZ8+/T82gzMzMwlaCFcAM0RKQgyrI/0Dg/EahmIQyBB0DRvXr4W78tmLV1gtAbmYoEEgnciG4QpTogzCFyEwSyg2CBS2oCAZNQh3cA+hMAJ5AlcKxuVBlOgnNgVjMwyUrQjmamKLGaoZBAAOTFyLnFFW4wAAAABJRU5ErkJggg==);
        }
          
        .gantt_tree_icon.gantt_file {
          background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAeElEQVQ4T2NkoBJgRDZn+vTp/wmZm5mZiaIHph7DICkJMUJmMfgHBmMYhtUgbAo3rl+L4lp0NUQbBPI2umuRDaPIIFAYwAyjv0HoMQALM5JdhG4QLMxGDcKdyIdoGIE89OzFK4KZF5Rl8EY/QROQFGA1iBQD0NUCAJVjcxO0naAQAAAAAElFTkSuQmCC);
        }

        .gantt_tree_icon.gantt_file, .gantt_tree_icon.gantt_folder_closed, .gantt_tree_icon.gantt_folder_open {
          display: block !important;
          width: 18px !important;
          height: 100% !important;
        }
    
        .gantt_tooltip {
          background-color: white !important;
          border: 1px solid #d1d5db !important;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
          padding: 0;
          z-index: 100;
        }
    
        .gantt_tooltip:before {
          display: none !important;
        }
    
        .gantt_scale_cell {
          font-size: 12px;
          font-weight: 500;
          text-align: center;
        }
    
        /* Timeline scale row (top-right headers) */
        .gantt_scale_line {
          font-size: 12px !important;
          opacity: 0.5;
        }
    
        .gantt_task .weekend,
        .gantt_task_cell.weekend {
          background-color: #e6f0fa !important;
        }

        .gantt_scale_cell.weekend {
          background-color: #e6f0fa !important;
        }
    
        /* Remove background color from month scale row */
        .gantt_task .gantt_scale_line:nth-child(1) .gantt_scale_cell {
          background-color: transparent !important;
        }
    
        .gantt_side_content {
          font-size: 11px;
          opacity: 0.8;
          font-weight: 500;
          margin-left: 20px;
        }
    
        .gantt_task_line {
          border-radius: 2px !important;
          overflow: visible !important;
          z-index: 100 !important;
        }
    
        .gantt_task_content {
          font-size: 12px !important;
        }

        .hide_task_text .gantt_task_content {
          display: none !important;
        }
    
        /* Green Color (For every parent task) */
        .gantt_project {
          background-color: #65c16f !important;
        }
    
        // Folder & File icon class
        .gantt-tree-icon-wrapper {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 16px;
          height: 16px;
          flex-shrink: 0;
        }
    
        .gantt-icon {
          font-size: 13px;
          color: #6b7280;
        }
    
        /* Darken grid column borders */
        .gantt_grid, .gantt_task {
          border-color: #cbd5e1 !important; /* Tailwind's slate-300 */
        }
    
        /* Darken vertical lines inside the grid */
        .gantt_grid_data .gantt_row, 
        .gantt_task_data .gantt_row {
          border-bottom: 1px solid #cbd5e1 !important;
        }

        .gantt_grid_data .gantt_row.gantt_selected, 
        .gantt_grid_data .gantt_row.odd.gantt_selected, 
        .gantt_grid_data .gantt_row:hover, 
        .gantt_grid_data .gantt_row.odd:hover {
          background-color: #FFF3A1;
        }

        .gantt_task .gantt_task_row.gantt_selected {
          background-color: #FFF3A1;
        }
    
        /* Darken column split line between grid and timeline */
        .gantt_split {
          border-left-color: #cbd5e1 !important;
        }
    
        /* Darken the timeline scale border lines */
        .gantt_scale_line {
          border-bottom-color: #cbd5e1 !important;
        }
    
        /* Optional: highlight week/month header separation */
        .gantt_task_scale .gantt_scale_cell {
          border-right-color: #cbd5e1 !important;
        }
    
        /* Highlight Critical Path with red */
        .gantt_critical_task.gantt_task_line {
          background-color: #f44336 !important;
        }
    
        .gantt_critical_task.gantt_project {
          background-color: #65c16f !important;
        }
    
        /** Lighbox CSS */
        .gantt_cal_light {
          top: 214px !important;
        }
        .gantt_cal_larea {
          font-size: 12px !important;
          height: 550px !important;
          overflow-y: auto !important;
        }
    
        .gantt_cal_ltext textarea {
          font-size: 12px !important;
          min-height: 32px !important;
        }
    
        /** Lightbox Header */
        .gantt_cal_ltitle {
          background: #ffff;
          position: relative;
          justify-content: center;
        }
    
        .gantt_cal_ltitle_text {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          white-space: nowrap;
          color: #1f2937; /* text-gray-800 */
        }
    
        .gantt_cal_ltitle_controls {
          position: absolute;
          z-index: 1;
          right: 10px;
        }
    
        .gantt_duration_end_date {
          display: none !important;
        }
    
        /* Slack CSS */
        .slack {
          background: repeating-linear-gradient(45deg,#fff,#fff 5px,#b6b6b6 5px,#b6b6b6 10px);
          opacity: 0.7;
          position: absolute;
          border: none;
          margin-left: -2px;
          border-right: 1px solid #b6b6b6;
          border-radius: 0;
          pointer-events: none;
        }
    
        /** Gantt container css */
        #gantt-fullscreen-wrapper:fullscreen,
        #gantt-fullscreen-wrapper:-webkit-full-screen {
          width: 100vw !important;
          height: 100vh !important;
          display: flex;
          flex-direction: column;
          background: #fff !important;
          z-index: 999999 !important;
        }

        /** Today's marker styling */
        .gantt_marker.today {
          background-color: #ffb6bc;
          width: 2px;
        }

        .gantt_marker.today .gantt_marker_content {
          color: #fff;
          font-weight: bold;
          padding: 4px;
          font-size: 10px;
        }

        /* Link Color */
        .gantt_task_link .gantt_line_wrapper {
          stroke: #faae2a !important;
        }
          
        .gantt_task_link .gantt_link_corner {
          border-color: #faae2a !important;
        }

        .gantt_line_wrapper div {
          background-color: #faae2a !important;
        }

        // Arrowhead Color
        .gantt_task_link div.gantt_link_arrow:before {
          color: #faae2a !important;
        }
          
        .gantt_link_arrow {
          --dhx-gantt-base-colors-icons: #faae2a !important;
        }

        /** For critical link */
        .gantt_critical_link .gantt_task_link .gantt_line_wrapper {
          stroke:#f44336 !important;
        }

        .gantt_critical_link.gantt_task_link .gantt_link_corner {
          border-color: #f44336 !important;
        }

        .gantt_critical_link .gantt_line_wrapper div {
          background-color:#f44336 !important;
        }

        .gantt_critical_link.gantt_task_link div.gantt_link_arrow:before {
          color:#f44336 !important;
        }

        .gantt_critical_link .gantt_link_arrow {
           --dhx-gantt-base-colors-icons: #f44336 !important;
        }

        .gantt_grid_editor_placeholder input {
           outline: none !important;
        }

        .gantt_drag_marker {
          z-index: 2 !important;
        }
 
      `;
  }, []);

  useEffect(() => {
    const style = document.createElement("style");
    style.innerHTML = styleInnerHtml;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style); // Clean up on unmount
    };
  }, []);

  return { styleInnerHtml };
};
