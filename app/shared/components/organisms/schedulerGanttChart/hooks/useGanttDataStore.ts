import { Number } from "~/helpers/helper";
import { useParams } from "@remix-run/react";
import { useCallback, useEffect, useRef } from "react";
import {
  fetchGanttScheduleTasks,
  updateGanttScheduleTasks,
} from "~/redux/action/scehdulerAction";
import { useGantt } from "~/hook/useGantt";
import {
  batchCrudGanttOperation,
  updateGanttPreviousState,
} from "~/redux/slices/schedulerSlice";
import {
  calculateSummaryProgress,
  normalizeGanttDate,
  roundToDecimalPlaces,
} from "../gantt/helper/helper";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { useGanttSettings } from "./useGanttSettings";

export const useGanttDataStore = () => {
  const { selector, dispatch, getGanttInstance } = useGantt();

  const params: RouteParams = useParams();

  const { tasks, links, ganttPreviousState } = selector();
  const { projectId } = useGanttSettings();

  const currentModule = getCurrentMenuModule();
  const { module_key = 0 } = currentModule || {};

  const fetchSchduleTasks = useCallback(() => {
    dispatch(
      fetchGanttScheduleTasks({
        project: projectId,
        module_key: module_key?.toString() || "",
      })
    );
  }, [projectId, module_key]);

  const ganttPreviousStateRef =
    useRef<IGanttScheduleInitialState["ganttPreviousState"]>(
      ganttPreviousState
    );

  const formattedTasks = tasks.map((g) => ({
    ...g,
    id: g.id,
    text: g.text,
    start_date: new Date(g.start_date || ""),
    start_date_only: g.start_date_only,
    end_date_only: g.end_date_only,
    planned_start: g.planned_start ? new Date(g.planned_start) : null,
    planned_end: g.planned_end ? new Date(g.planned_end) : null,
    duration: Number(g.duration),
    progress: g.progress,
    parent: g.parent,
    type: g.type,
    color: g.color,
  }));

  useEffect(() => {
    ganttPreviousStateRef.current = ganttPreviousState;
  }, [ganttPreviousState]);

  const updatePreviousGanttState = useCallback(() => {
    const gantt = getGanttInstance();
    if (!gantt) return;

    // storing snapshot of current gantt state
    setTimeout(() => {
      const previousState = gantt.serialize();
      dispatch(updateGanttPreviousState(previousState));
    }, 100);
  }, [dispatch, getGanttInstance]);

  const updateParentProgressIfChildChanged = (changedTaskIds: string[]) => {
    const gantt = getGanttInstance();
    if (!gantt) return;

    const updatedParentIds: string[] = [];
    const visited = new Set<string>();

    changedTaskIds.forEach((childId) => {
      gantt.refreshTask(childId);
      if (!childId?.toString()?.startsWith("task_")) return;

      gantt.eachParent((parentTask) => {
        if (visited.has(parentTask.id)) return;
        visited.add(parentTask.id);

        // Progress calculation
        const newProgress = calculateSummaryProgress(parentTask, gantt);
        const oldProgress = roundToDecimalPlaces(Number(parentTask.progress));
        const updatedProgress = roundToDecimalPlaces(Number(newProgress));

        // Recalculate start and end dates
        const children = gantt.getChildren(parentTask.id);

        if (!children.length) return;

        let newStart = new Date(gantt.getTask(children[0]).start_date ?? "");
        let newEnd = new Date(gantt.getTask(children[0]).end_date ?? "");

        for (let i = 1; i < children.length; i++) {
          const child = gantt.getTask(children[i]);
          if (child.start_date && child.start_date < newStart)
            newStart = new Date(child.start_date);
          if (child.end_date && child.end_date > newEnd)
            newEnd = new Date(child.end_date);
        }

        // Check if parent needs update
        const needsStartUpdate = +parentTask.start_date !== +newStart;
        const needsEndUpdate = +parentTask.end_date !== +newEnd;
        const needsProgressUpdate = oldProgress !== updatedProgress;

        if (needsStartUpdate || needsEndUpdate || needsProgressUpdate) {
          if (needsStartUpdate) parentTask.start_date = newStart;
          if (needsEndUpdate) parentTask.end_date = newEnd;
          if (needsProgressUpdate) parentTask.progress = updatedProgress;

          gantt.updateTask(parentTask.id);
          updatedParentIds.push(parentTask.id);
        }
      }, childId);
    });

    return updatedParentIds;
  };

  // const updateParentProgressIfChildChanged = (changedTaskIds: string[]) => {
  //   const gantt = getGanttInstance();

  //   if (!gantt) return;

  //   const updatedParentIds: string[] = [];

  //   const visited = new Set<string>();

  //   changedTaskIds.forEach((childId) => {

  //     if (!childId?.toString()?.startsWith("task_")) return;

  //     gantt.eachParent((parentTask) => {
  //       if (visited.has(parentTask.id)) return;

  //       visited.add(parentTask.id);

  //       const newProgress = calculateSummaryProgress(parentTask, gantt);

  //       gantt.open(parentTask.id);

  //       if (
  //         roundToDecimalPlaces(Number(parentTask.progress)) !==
  //         roundToDecimalPlaces(Number(newProgress))
  //       ) {
  //         parentTask.progress = newProgress;
  //         gantt.updateTask(parentTask.id);
  //         updatedParentIds.push(parentTask.id);
  //       }
  //     }, childId);
  //   });

  //   return updatedParentIds;
  // };

  const taskNLinkCrudHandler = useCallback(
    async (data: IGanttBatchChanges) => {
      const isTaskDeleteAction = data?.tasks?.[0]?.action === "delete";
      const isTaskAbleToDelete = data?.tasks?.every((t) =>
        t.data?.id?.toString()?.startsWith("task_")
      );

      if (isTaskDeleteAction && !isTaskAbleToDelete) return;

      const gantt = getGanttInstance();

      const formattedTasks = data.tasks?.map((t) => {
        let {
          assignees_to,
          task_users,
          assignee_name,
          task_user_name,
          contractors,
          employees,
          ...valuesToBeSend
        } = t.data;

        if (t.entity != "link" && t.action != "delete") {
          valuesToBeSend.wbs = gantt?.getWBSCode(
            gantt.getTask(valuesToBeSend.id ?? "")
          );
        }

        return {
          ...t,
          action: "native_action" in t.data ? t?.data?.native_action : t.action,
          data: {
            ...valuesToBeSend,
            open: t.action === "create" ? true : Boolean(Number(t.data?.open)),
            start_date: normalizeGanttDate(t.data.start_date || "") as Date,
            end_date: normalizeGanttDate(t.data.end_date as string) as Date,
            planned_start: t.data?.planned_start
              ? (normalizeGanttDate(t.data?.planned_start) as string)
              : null,
            planned_end: t.data?.planned_end
              ? (normalizeGanttDate(t.data?.planned_end) as string)
              : null,
          },
        };
      });

      const crudRes = (await updateGanttScheduleTasks({
        project_id: params?.id?.toString() ?? "",
        gantt_data: {
          ...data,
          count: (data?.tasks?.length || 0) + (data?.links?.length || 0),
          ...(formattedTasks && { tasks: formattedTasks }),
        },
      })) as IUpdateScheduleTaskApiRresponse;

      if (crudRes.success) {
        dispatch(
          batchCrudGanttOperation({
            tasks: crudRes?.data?.tasks,
            links: crudRes?.data?.links,
          })
        );

        const changedProgressTaskIds =
          (crudRes?.data?.tasks?.map((t) =>
            t.action === "delete" ? t?.id : t.data?.id
          ) as string[]) ?? [];

        crudRes?.data?.tasks?.forEach((ent) => {
          const { data } = ent || {};

          if (ent.action === "create" && gantt && data.parent) {
            if (
              gantt?.getTask(data.parent ?? "")?.type !=
              gantt?.config.types.project
            ) {
              gantt.getTask(data.parent ?? "").type = gantt.config.types
                .project as string;
              gantt.render();
              gantt.updateTask(data.parent ?? "");
            }
          }
        });

        updateParentProgressIfChildChanged(changedProgressTaskIds);
      } else {
        // Revert if API is failed
        const stateToRestore = {
          tasks: ganttPreviousStateRef.current?.data
            ?.filter((t) => isNaN(Number(t.id)))
            ?.map((task) => ({
              ...task,
              // We are getting the date in DD-MM-YYYY HH:mm format after batchSave, so here we need to convert it again to proper Date instance in order to be parsed dates by gantt
              start_date: normalizeGanttDate(task.start_date || "", false),
              end_date: normalizeGanttDate(task.end_date, false),
              planned_start: task.planned_start
                ? normalizeGanttDate(task.planned_start, true)
                : null,
              planned_end: task.planned_end
                ? normalizeGanttDate(task.planned_end, true)
                : null,
            })),
          links: ganttPreviousStateRef.current.links,
        };

        gantt?.clearAll();
        gantt?.parse(stateToRestore);

        notification.error({
          description: crudRes?.message || "Failed to update task entity",
        });
      }
    },
    [params?.id, getGanttInstance, updateParentProgressIfChildChanged]
  );

  return {
    ganttTasks: formattedTasks,
    ganttLinks: links,
    taskNLinkCrudHandler,
    updatePreviousGanttState,
    fetchSchduleTasks,
    updateParentProgressIfChildChanged,
  };
};
