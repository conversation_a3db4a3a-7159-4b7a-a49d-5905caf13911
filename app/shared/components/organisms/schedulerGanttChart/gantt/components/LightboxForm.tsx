import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from "react";
import dayjs from "dayjs";
import { useFormik } from "formik";
import * as Yup from "yup";
import { Form } from "@remix-run/react";
import { InputRef } from "antd";

// Shared
import InputField from "~/shared/components/molecules/inputField/InputField";
import SelectField from "~/shared/components/molecules/selectField/SelectField";
import ButtonField from "~/shared/components/molecules/buttonField/ButtonField";
import { Number, sanitizeString } from "~/helpers/helper";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import { CheckboxChangeEvent } from "antd/es/checkbox";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { Button } from "~/shared/components/atoms/button";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { useTranslation } from "~/hook";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import {
  getDirectaryIdByKey,
  getDirectaryKeyById,
} from "~/components/sidebars/multi-select/customer/zustand/action";
import { filterOptionBySubstring } from "~/shared/utils/helper/common";
import { COLOR_OPTIONS } from "~/modules/projectManagement/pages/project/utils/constants";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import DateFieldWithIncreamentDaysOpt from "./DateFieldWithIncreamentDaysOpt";
import { useGantt } from "~/hook/useGantt";
import { useBoolean } from "~/modules/projectManagement/pages/project/hook/use-boolean";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { useCurrentModuleAccess } from "../../hooks/useCurrentModuleAccess";

const defaultFieldSelector = {
  taskId: "",
  type: "",
};

const LightboxForm: React.FC<CustomLightboxProps<TGanttTaskTypes>> = ({
  data,
  onCancel,
  onSave,
  onDelete,
  isNewTask,
}) => {
  const { _t } = useTranslation();
  const { enoughGanttAccess } = useCurrentModuleAccess();
  const { getGanttInstance } = useGantt();

  const [shouldBaselineShow, setShouldBaselineShow] = useState<boolean>(false);
  const [openFieldSelector, setOpenFieldSelector] =
    useState(defaultFieldSelector);

  const deleteConfirmation = useBoolean();

  const validationSchema = Yup.object().shape({
    text: Yup.string().trim().required("Task name is required."),
  });

  const taskNameRef = useRef<InputRef>(null);

  const formik = useFormik({
    initialValues: {
      ...data,
      type: data?.type || "task",
      color: data?.color || "#1796b0",
    },
    validationSchema,
    onSubmit: async (values) => {
      const gantt = getGanttInstance();
      if (!gantt) return;

      let plannedStartDate: Date | "" = "";
      let plannedEnd: Date | "" = "";

      if (shouldBaselineShow) {
        const strictParsed = dayjs(
          values?.planned_start,
          CFConfig.day_js_date_format,
          true
        );

        if (strictParsed.isValid()) {
          plannedStartDate = strictParsed.toDate();
        } else {
          const fallback = new Date(values?.planned_start ?? "");
          plannedStartDate = isNaN(fallback.getTime()) ? "" : fallback;
        }

        if (plannedStartDate && Number(values.planned_duration ?? 0)) {
          plannedEnd = gantt.calculateEndDate(
            plannedStartDate,
            Number(values.planned_duration ?? 0)
          );
        }
      }

      const newVals = {
        ...values,
        duration: Number(values?.duration || 1),
        wbs: values?.["$wbs"] ?? "",
        end_date: gantt.calculateEndDate(
          new Date(values.start_date as Date),
          Number(values.duration)
        ),
        planned_start: plannedStartDate,
        planned_end: plannedEnd,
        assigned_to:
          values?.contractors?.map((c) => c?.user_id)?.join(",") ?? "",
        task_user_id:
          values?.employees?.map((e) => e?.user_id)?.join(",") ?? "",
      };

      onSave(newVals);
    },
  });

  const { handleSubmit, values, errors, setFieldValue, touched, handleBlur } =
    formik;

  useEffect(() => {
    taskNameRef.current?.focus();
    taskNameRef.current?.select();
    if (!!data?.planned_start && !!data?.planned_end) {
      setShouldBaselineShow(true);
    }
  }, [data]);

  const isTask = useMemo(() => values?.type === "task", [values?.type]);
  const isMilestone = useMemo(
    () => values?.type === "milestone",
    [values?.type]
  );
  const isProject = useMemo(() => values?.type === "project", [values?.type]);

  const isMainProjectTask = useMemo(
    () => data?.id?.toString()?.startsWith("prj_"),
    [data?.id]
  );

  const progressOptions = useMemo(() => {
    return Array.from({ length: 101 }).map((_v, index) => ({
      label: `${index}%`,
      value: (index / 100)?.toString(),
    }));
  }, []);

  const colorOptions = useMemo(() => {
    let defaultOpts = COLOR_OPTIONS.map((c, idx) => ({
      label: (
        <div key={idx} className="flex justify-start items-center gap-1">
          <div
            className="w-[24px] h-[14px] mr-[5px]"
            style={{ backgroundColor: c.key }}
          />
          <div>{c?.label}</div>
        </div>
      ),
      value: c?.key,
    }));

    const isCurrentColorNotExist = defaultOpts?.some(
      (c) => c.value === data?.color
    );
    if (!isCurrentColorNotExist) {
      defaultOpts = [
        {
          label: (
            <div className="flex justify-start items-center gap-1">
              <div
                className="w-[24px] h-[14px] mr-[5px]"
                style={{ backgroundColor: data?.color }}
              />
              <div>{data?.color}</div>
            </div>
          ),
          value: data?.color ?? "",
        },
        ...defaultOpts,
      ];
    }

    return defaultOpts;
  }, [data?.color]);

  const drawerOptions: CustomerEmailTab[] = useMemo(() => {
    return openFieldSelector.type === "contractors"
      ? [
          CFConfig.employee_key,
          CFConfig.customer_key,
          CFConfig.lead_key,
          CFConfig.contractor_key,
          CFConfig.vendor_key,
          CFConfig.misc_contact_key,
        ]
      : [CFConfig.employee_key];
  }, [openFieldSelector]);

  const selectedData = useMemo(() => {
    const getData = (data: Partial<TselectedContactSendMail>[]) => {
      data = data?.filter((d) => Number(d?.user_id)) ?? [];
      return data?.map((user) => ({
        display_name: user?.display_name ?? "",
        // contact_id: Number(user?.contact_id) ?? "",
        user_id: Number(user?.user_id ?? ""),
        type: user.type,
        type_key: getDirectaryKeyById(
          Number(user.type) === 1 ? 2 : Number(user.type ?? ""),
          undefined
        ),
        image: user?.image,
      }));
    };

    return {
      contractors: getData(values.contractors ?? []),
      employees: getData(values.employees ?? []),
    };
  }, [values.contractors, values.employees]);

  const closeDrawer = useCallback(() => {
    setOpenFieldSelector(defaultFieldSelector);
  }, []);

  const getDirId = useCallback((type_key: string) => {
    return getDirectaryIdByKey(type_key as CustomerTabs, undefined);
  }, []);

  const handleDrawerSelection = (
    data: Array<Partial<TselectedContactSendMail>>,
    type: string
  ) => {
    if (Array.isArray(data)) {
      const newData = data.map((c) => {
        const type_key =
          c.type_key !== "contact" ? c.type_key || "" : c.parent_type_key || "";

        return {
          user_id: c.user_id ?? 0,
          display_name: c.display_name ?? "",
          // contact_id: c.contact_id ?? 0,
          type: getDirId(type_key) ?? "",
          image: c.image ?? "",
        };
      });

      if (data?.length > 0) {
        setFieldValue(type, newData);
      } else {
        setFieldValue(type, []);
      }
    }
  };

  const buttonFieldVals = useMemo(() => {
    const contractorVal =
      values.contractors?.length > 1
        ? `${values.contractors.length} Selected`
        : values.contractors?.[0]?.display_name ?? "";

    const empVal =
      values.employees?.length > 1
        ? `${values.employees.length} Selected`
        : values.employees?.[0]?.display_name ?? "";

    return {
      contractorVal,
      empVal,
    };
  }, [values.contractors, values.employees]);

  const closeDeleteConfirmModal = useCallback(() => {
    deleteConfirmation.onFalse();
  }, []);

  return (
    <>
      <Form method="post" onSubmit={handleSubmit} noValidate>
        <div className="grid gap-4 p-4 max-h-[calc(100dvh-150px)] overflow-y-auto">
          <SidebarCardBorder addGap={true}>
            <div className="w-full">
              <InputField
                ref={taskNameRef}
                label={_t("Task name")}
                labelPlacement="top"
                isRequired={true}
                name="text"
                id="text"
                disabled={!enoughGanttAccess || isMainProjectTask}
                value={_t(
                  HTMLEntities.decode(
                    sanitizeString(values.text?.toString() || "")
                  )
                )}
                errorMessage={
                  touched?.text && !values.text ? (errors.text as string) : ""
                }
                autoComplete="off"
                onChange={(e) => {
                  setFieldValue("text", e.target.value.trimStart());
                }}
                onBlur={handleBlur}
              />
            </div>
            <div className="w-full">
              <TextAreaField
                label={_t("Description")}
                labelPlacement="top"
                name="description"
                id="description"
                disabled={!enoughGanttAccess}
                className="md:max-h-[300px] max-h-[250px]"
                value={_t(
                  HTMLEntities.decode(
                    sanitizeString(values.description?.toString() || "")
                  )
                )}
                autoComplete="off"
                onChange={(e) => {
                  setFieldValue("description", e.target.value);
                }}
                onBlur={handleBlur}
                autoSize={{ minRows: 1 }}
              />
            </div>
          </SidebarCardBorder>
          <SidebarCardBorder addGap={true}>
            <div className="grid grid-cols-2 gap-2">
              <ButtonField
                label={_t("Assigned Contractor")}
                placeholder={_t("Select Contact")}
                labelPlacement="top"
                name="assigned_to"
                id="assigned_to"
                key="assigned_to"
                disabled={!enoughGanttAccess}
                onClick={() => {
                  setOpenFieldSelector({
                    type: "contractors",
                    taskId: data?.id as string,
                  });
                }}
                value={HTMLEntities.decode(
                  sanitizeString(`${buttonFieldVals?.contractorVal || ""}`)
                )}
                avatarProps={
                  values.contractors?.length === 1
                    ? {
                        user: {
                          name: HTMLEntities.decode(
                            sanitizeString(values?.contractors[0]?.display_name)
                          ),
                          image: values?.contractors[0]?.image ?? "",
                        },
                      }
                    : {}
                }
              />
              <ButtonField
                label={_t("Employee")}
                placeholder={_t("Select Employee")}
                labelPlacement="top"
                name="task_user_id"
                id="task_user_id"
                key="task_user_id"
                disabled={!enoughGanttAccess}
                onClick={() => {
                  setOpenFieldSelector({
                    type: "employees",
                    taskId: data?.id as string,
                  });
                }}
                value={HTMLEntities.decode(
                  sanitizeString(`${buttonFieldVals?.empVal || ""}`)
                )}
                avatarProps={
                  values.employees?.length === 1
                    ? {
                        user: {
                          name: HTMLEntities.decode(
                            sanitizeString(values?.employees[0]?.display_name)
                          ),
                          image: values?.employees[0]?.image ?? "",
                        },
                      }
                    : {}
                }
              />
            </div>
          </SidebarCardBorder>
          <SidebarCardBorder addGap={true}>
            <div className="w-full">
              <div className="flex justify-start items-center gap-2">
                <div>Send Email?</div>
                <div>
                  <CustomCheckBox
                    className="gap-1.5"
                    name="send_reminder"
                    onChange={(e: CheckboxChangeEvent) => {
                      const newVal = e.target.checked ? "1" : "0";
                      setFieldValue("send_reminder", newVal);
                    }}
                    checked={Number(values.send_reminder) === 1}
                  >
                    {_t("Send updated details via Email once saved?")}
                  </CustomCheckBox>
                </div>
              </div>
            </div>
          </SidebarCardBorder>
          <SidebarCardBorder addGap={true}>
            <div
              className={`grid ${
                values?.type === "project" ? "grid-cols-1" : "grid-cols-3"
              }  gap-2`}
            >
              <SelectField
                label={_t("Type")}
                labelPlacement="top"
                // isRequired={true}
                name="type"
                allowClear={false}
                disabled={!enoughGanttAccess}
                value={values.type}
                onChange={(value) => {
                  setFieldValue("type", value);
                }}
                options={[
                  {
                    label: "Task",
                    value: "task",
                    disabled: isProject,
                  },
                  {
                    label: "Project",
                    value: "project",
                    disabled: isTask || isMilestone,
                  },
                  {
                    label: "Milestone",
                    value: "milestone",
                    disabled: isProject,
                  },
                ]}
              />
              <SelectField
                label={_t("Progress")}
                labelPlacement="top"
                name="progress"
                allowClear={false}
                disabled={!enoughGanttAccess || isProject}
                showSearch
                filterOption={(input, option) =>
                  filterOptionBySubstring(input, option?.label as string)
                }
                value={Number(values.progress)?.toString() ?? "0"}
                onChange={(value) => {
                  setFieldValue("progress", value);
                }}
                options={progressOptions}
              />
              {!isProject && (
                <SelectField
                  label={_t("Color")}
                  labelPlacement="top"
                  name="color"
                  allowClear={false}
                  disabled={!enoughGanttAccess}
                  value={values?.color}
                  onChange={(value) => {
                    setFieldValue("color", value);
                  }}
                  options={colorOptions}
                />
              )}
            </div>
          </SidebarCardBorder>
          <SidebarCardBorder addGap={true}>
            {isProject && (
              <div className="text-[12px] text-red-600">
                The Start & End Date are controlled by the tasks that you assign
                to the project. The Start Date is the date the first task starts
                and the End Date is the date the last task is completed.
              </div>
            )}
            <div className="">Scheduled/Planned</div>

            {!isProject ? (
              <DateFieldWithIncreamentDaysOpt
                date={values?.start_date_only ?? ""}
                duration={Number(values?.duration)}
                field="start_date_only"
                onChangeDate={(newDate) => {
                  setFieldValue("start_date_only", newDate);
                  setFieldValue(
                    "start_date",
                    dayjs(newDate, CFConfig.day_js_date_format).toDate()
                  );
                }}
                onChangeDays={(duration) => {
                  setFieldValue("duration", duration);
                }}
                isDateDisabled={isProject}
                isTask={isTask}
              />
            ) : (
              <div className="text-sm">
                {values?.start_date_only} - {values?.end_date_only}
              </div>
            )}

            {isTask && (
              <>
                <div className="flex justify-between items-center">
                  <div className="">Baseline/Actual</div>
                  <Button
                    htmlType="button"
                    className="w-auto min-w-[75px] justify-center hover:!border-primary-900 hover:!text-primary-900 active:!border-primary-900 active:!text-primary-900"
                    onClick={() => {
                      if (!data?.planned_start && !data?.planned_end) {
                        setFieldValue("planned_start", data?.start_date_only);
                        setFieldValue("planned_duration", data?.duration);
                      }
                      setShouldBaselineShow((prev) => !prev);
                    }}
                  >
                    {shouldBaselineShow ? "Remove" : "Set"}
                  </Button>
                </div>
                {shouldBaselineShow && (
                  <DateFieldWithIncreamentDaysOpt
                    date={
                      values?.planned_start
                        ? dayjs(
                            values?.planned_start,
                            CFConfig.day_js_date_format
                          ).isValid()
                          ? values?.planned_start // Keep original format if it's already in config's format
                          : dayjs(values?.planned_start).format(
                              CFConfig.day_js_date_format
                            )
                        : ""
                    }
                    duration={Number(values?.planned_duration ?? 1)}
                    field="planned_start"
                    onChangeDate={(newDate) => {
                      setFieldValue("planned_start", newDate);
                    }}
                    onChangeDays={(duration) => {
                      setFieldValue("planned_duration", duration);
                    }}
                    isTask={isTask}
                  />
                )}
              </>
            )}
          </SidebarCardBorder>
        </div>
        <div className="sidebar-footer flex items-center justify-center w-full p-4 gap-4">
          <PrimaryButton
            className="min-w-[75px] w-auto"
            htmlType="submit"
            buttonText={_t("Save")}
            isLoading={formik.isSubmitting}
            disabled={formik.isSubmitting}
          />
          <Button
            className="min-w-[75px] justify-center hover:!border-primary-900 hover:!text-primary-900 active:!border-primary-900 active:!text-primary-900"
            htmlType="button"
            onClick={() => {
              data.contractors = [];
              data.employees = [];
              onCancel?.();
            }}
            name="cancel"
          >
            {"Cancel"}
          </Button>
          {!isMainProjectTask && !isNewTask && (
            <Button
              className="min-w-[75px] justify-center !border-red-600 !text-red-600 active:!border-red-600 active:!text-red-600"
              htmlType="button"
              onClick={() => deleteConfirmation.onTrue()}
              name="delete"
            >
              {"Delete"}
            </Button>
          )}
        </div>
      </Form>
      {!!openFieldSelector.type && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={!!openFieldSelector.type}
          closeDrawer={closeDrawer}
          singleSelecte={false}
          setCustomer={(data) => {
            handleDrawerSelection(data, openFieldSelector.type);
          }}
          options={drawerOptions}
          selectedCustomer={
            openFieldSelector.type === "contractors"
              ? selectedData?.contractors
              : selectedData?.employees ?? []
          }
          groupCheckBox={true}
          projectId={Number(data?.project_id)}
          additionalContactDetails={0}
        />
      )}

      {deleteConfirmation.bool && (
        <ConfirmModal
          isOpen={deleteConfirmation.bool}
          modaltitle={_t("Delete")}
          description={_t("Task will be deleted permanently, are you sure?")}
          modalIcon={"fa-regular fa-trash-can"}
          onAccept={() => {
            onDelete?.();
          }}
          onDecline={closeDeleteConfirmModal}
          onCloseModal={closeDeleteConfirmModal}
        />
      )}
    </>
  );
};

export default LightboxForm;
