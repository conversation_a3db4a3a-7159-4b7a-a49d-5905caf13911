import { useEffect, useState } from "react";
// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Button } from "~/shared/components/atoms/button";
import { Progress } from "~/shared/components/atoms/progress";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";

import { useAppDispatch } from "~/modules/document/fileAndPhoto/redux/store";
import { uploadFile } from "~/redux/action/fileAttachmentAction";
import axios, { AxiosProgressEvent, AxiosRequestConfig } from "axios";
import { useDropzone } from "react-dropzone";
import { getFileExtension } from "~/modules/document/fileAndPhoto/utils/googleDrivePicker";
import { ImageResizer } from "~/shared/utils/common";
import { uid } from "~/helpers/helper";
import {
  acceptedFileTypes,
  acceptedFileTypesInNoAccessRole,
  allowUploadExtensions,
} from "~/modules/document/fileAndPhoto/utils/allowedFileExtension";
import { deleteFiles } from "~/modules/document/fileAndPhoto/redux/action/filePhotoRightAction";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { defaultConfig } from "~/data";
import { useGModules } from "~/zustand";

const NewFile = ({
  setSelectedTab,
  setSelectedFileData,
  validationParams,
  addFilesRes,
  load,
  isLoad,
  setAvailableFiles,
  multiple = true,
  setSelectedFilesFromNewFilestab,
}: ICustomFileTabsSelectProps) => {
  const userAgent = navigator.userAgent || "";
  const isAndroid = userAgent.toLowerCase().includes("android");
  const isiOS = /iPhone|iPad|iPod/i.test(userAgent);
  const { _t } = useTranslation();
  const dispatch = useAppDispatch();
  const { checkModuleAccessByKey } = useGModules();
  const fileSupportAccess = checkModuleAccessByKey(
    defaultConfig.file_support_key
  );
  const currentModule = getCurrentMenuModule();
  const { module_key, image_resolution, module_id } = validationParams;
  const [uploadedFiles, setUploadedFiles] = useState<INewFileObj[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<INewFileObj[]>([]);
  useEffect(() => {
    // Any side-effects or additional logic can go here when addFilesRes changes
    if (addFilesRes) {
      if ("success" in addFilesRes) {
        if (addFilesRes?.success) {
          isLoad(false);
          setSelectedTab("");
        }
      }
    }
  }, [addFilesRes]);

  const chunkArray = (array: any[], size: number) => {
    const chunked = [];
    for (let i = 0; i < array.length; i += size) {
      chunked.push(array.slice(i, i + size));
    }
    return chunked;
  };

  const uploadSingleFile = async (
    file: File,
    newFiles: INewFileObj[],
    fileId: string
  ) => {
    const extension = getFileExtension(file.name).toLowerCase();
    if (
      extension === "" ||
      !allowUploadExtensions.includes(extension.toLowerCase())
    ) {
      notification.error({
        description: `Rejected: ${file.name} (Invalid file type or missing extension)`,
      });
      return;
    }

    let resizedFile = file;
    if (
      file.type.startsWith("image/") &&
      image_resolution &&
      image_resolution !== "full"
    ) {
      try {
        const resizedBlob = await ImageResizer(
          file,
          image_resolution as keyof IResolutionAry
        );
        resizedFile = new File([resizedBlob], file.name, {
          type: file.type,
          lastModified: file.lastModified,
        });
      } catch (error) {}
    }

    let fileName = file.name;
    if (extension === "heic") {
      fileName = file.name.replace(/\.heic$/i, `_${Date.now()}.HEIC`);
    } else {
      fileName = file.name.replace(/(\.[a-zA-Z0-9]+)$/, `_${Date.now()}$1`);
    }

    const fileType = resizedFile.type.startsWith("image/heic")
      ? "image/heic"
      : extension === "kmz" || extension === "KMZ"
      ? "application/vnd.google-earth.kmz"
      : extension === "dwg" || extension === "DWG"
      ? "application/acad"
      : extension === "avi" || extension === "AVI"
      ? "video/x-msvideo"
      : extension === "flv" || extension === "FLV"
      ? "video/x-flv"
      : extension === "m2ts" ||
        extension === "M2TS" ||
        extension === "mts" ||
        extension === "MTS"
      ? "video/avchd-stream"
      : extension === "xlsm" || extension === "XLSM"
      ? "application/vnd.ms-excel.sheet.macroEnabled.12"
      : resizedFile.type;

    try {
      setSelectedFiles((prevFiles) =>
        prevFiles.map((f) =>
          f.id === fileId
            ? {
                ...f,
                uploadProgress: 0,
                uploadStatus: "inProgress",
                isUploading: true,
              }
            : f
        )
      );
      setSelectedFilesFromNewFilestab &&
        setSelectedFilesFromNewFilestab((prevFiles) =>
          prevFiles.map((f) =>
            f.id === fileId
              ? {
                  ...f,
                  uploadProgress: 0,
                  uploadStatus: "inProgress",
                  isUploading: true,
                }
              : f
          )
        );
      const response = (await dispatch(
        uploadFile({
          moduleName: module_key,
          fileType,
          isThumbRequired: resizedFile.type.startsWith("image/"),
          fileName: fileName,
        })
      )) as { payload: IGetUploadFileRes };

      if (!response?.payload?.success) {
        setSelectedFiles((prevFiles) =>
          prevFiles.map((f) =>
            f.id === fileId
              ? {
                  ...f,
                  uploadProgress: 0,
                  uploadStatus: "failed",
                  isUploading: false,
                }
              : f
          )
        );
        setSelectedFilesFromNewFilestab &&
          setSelectedFilesFromNewFilestab((prevFiles) =>
            prevFiles.map((f) =>
              f.id === fileId
                ? {
                    ...f,
                    uploadProgress: 0,
                    uploadStatus: "failed",
                    isUploading: false,
                  }
                : f
            )
          );
        notification.error({
          description: response?.payload?.message,
        });
        setSelectedFiles((prevFiles) =>
          prevFiles.filter((f) => f.id !== fileId)
        );
        setSelectedFilesFromNewFilestab &&
          setSelectedFilesFromNewFilestab((prevFiles) =>
            prevFiles.filter((f) => f.id !== fileId)
          );
        return;
      }

      if (response?.payload?.success && response.payload.data?.signedUrl) {
        const contentType = fileType || "application/octet-stream";
        const config: AxiosRequestConfig = {
          headers: {
            "Content-Type": contentType,
          },
          onUploadProgress: (progressEvent: AxiosProgressEvent) => {
            const percentCompleted = Math.min(
              Math.round(
                (100 * progressEvent.loaded) / (progressEvent.total ?? 1)
              )
            );

            setSelectedFiles((prevFiles) =>
              prevFiles.map((f) =>
                f.id === fileId
                  ? {
                      ...f,
                      uploadProgress: percentCompleted,
                    }
                  : f
              )
            );
            setSelectedFilesFromNewFilestab &&
              setSelectedFilesFromNewFilestab((prevFiles) =>
                prevFiles.map((f) =>
                  f.id === fileId
                    ? {
                        ...f,
                        uploadProgress: percentCompleted,
                      }
                    : f
                )
              );
          },
        };

        await axios.put(response.payload.data.signedUrl, resizedFile, config);

        setSelectedFiles((prevFiles) =>
          prevFiles.map((f) =>
            f.id === fileId
              ? {
                  ...f,
                  uploadProgress: 100,
                  isUploading: false,
                  uploadStatus: "completed",
                  fileUrl: response.payload.data.fileUrl,
                }
              : f
          )
        );
        setSelectedFilesFromNewFilestab &&
          setSelectedFilesFromNewFilestab((prevFiles) =>
            prevFiles.map((f) =>
              f.id === fileId
                ? {
                    ...f,
                    uploadProgress: 100,
                    isUploading: false,
                    uploadStatus: "completed",
                    fileUrl: response.payload.data.fileUrl,
                  }
                : f
            )
          );

        const originalFile = newFiles.find((f) => f.id === fileId);
        if (originalFile) {
          const updatedFileObject: INewFileObj = {
            ...originalFile,
            file_url: response.payload.data?.fileUrl,
            signedUrl: response.payload.data.fileUrl,
            fileUrl: response.payload.data.fileUrl,
            cdnUrl: response?.payload?.data?.cdnUrl,
            isUploading: false,
            uploadProgress: 0,
            uploadStatus: "completed",
          };

          setUploadedFiles((prevFiles: INewFileObj[]) => [
            ...prevFiles,
            updatedFileObject,
          ]);
        }
      }
    } catch (error) {
      setSelectedFiles((prevFiles) =>
        prevFiles.map((f) =>
          f.id === fileId
            ? {
                ...f,
                uploadProgress: 0,
                uploadStatus: "failed",
                isUploading: false,
              }
            : f
        )
      );
      setSelectedFilesFromNewFilestab &&
        setSelectedFilesFromNewFilestab((prevFiles) =>
          prevFiles.map((f) =>
            f.id === fileId
              ? {
                  ...f,
                  uploadProgress: 0,
                  uploadStatus: "failed",
                  isUploading: false,
                }
              : f
          )
        );
      alert((error as Error).message);
    }
  };

  const onDrop = async (acceptedFiles: File[]) => {
    // Immediately set selected files before uploading
    const supportedFiles: File[] = [];
    const unsupportedFiles: File[] = [];

    acceptedFiles.forEach((file) => {
      const extension = getFileExtension(file.name).toLowerCase();
      if (
        extension === "" ||
        !allowUploadExtensions.includes(extension.toLowerCase())
      ) {
        unsupportedFiles.push(file);
      } else {
        supportedFiles.push(file);
      }
    });

    unsupportedFiles.forEach((file) => {
      notification.error({
        description: `Rejected: ${file.name} (Invalid file type or missing extension)`,
      });
    });

    if (supportedFiles.length === 0) return;

    const newFiles: INewFileObj[] = supportedFiles.map((file) => {
      const uniqueId = uid();
      return {
        id: uniqueId,
        file_name: file.name,
        file_type: file.type,
        file_upload: file.name,
        file_url: URL.createObjectURL(file),
        isUploading: false,
        uploadProgress: 0,
        signedUrl: "",
        uploadStatus: "inProgress",
      };
    });

    setSelectedFiles((prevFiles: INewFileObj[]) => [...prevFiles, ...newFiles]);
    setSelectedFilesFromNewFilestab &&
      setSelectedFilesFromNewFilestab((prevFiles: INewFileObj[]) => [
        ...prevFiles,
        ...newFiles,
      ]);

    // Upload queue for supported files
    const queue = supportedFiles.map((file, index) => ({
      file,
      fileId: newFiles[index].id,
    }));
    const inProgress = new Set();
    const maxConcurrent = 4;

    const processQueue = async () => {
      // While there are files in the queue and we haven't hit the concurrent limit
      while (queue.length > 0 && inProgress.size < maxConcurrent) {
        const fileItem = queue.shift();
        if (!fileItem) break;

        inProgress.add(fileItem.fileId);

        // Start the upload and remove from inProgress when done
        uploadSingleFile(fileItem.file, newFiles, fileItem.fileId).finally(
          () => {
            inProgress.delete(fileItem.fileId);
            // Process next file if available
            processQueue();
          }
        );
      }
    };

    // Start initial batch of uploads
    await processQueue();
  };

  const handleAddFileChange = () => {
    // loader should their for file & photos, bills and permits, will remove bills and permits once it will revice
    isLoad(true);
    const filesDetails = uploadedFiles.map((file) => ({
      id: uid(),
      file_path: file?.file_url,
      signedUrl: file?.signedUrl,
      fileUrl: file?.fileUrl,
      cdnUrl: file?.cdnUrl,
      is_image: file?.file_type.startsWith("image/") ? 1 : 0,
      file_name: file?.file_name,
      file_ext: getFileExtension(file?.file_name),
    }));

    if (setSelectedFileData) {
      setSelectedFileData(filesDetails);
    }
    if (
      Array.isArray(addFilesRes) ||
      !addFilesRes ||
      Object.keys(addFilesRes).length === 0
    ) {
      setTimeout(() => {
        isLoad(false);
        setSelectedTab("");
      }, 300);
      setTimeout(() => {
        setSelectedTab("");
      }, 500);
    }
  };

  const isUploadComplete =
    selectedFiles.length > 0 &&
    selectedFiles.every(
      (file) => file.uploadProgress === 100 && file?.uploadStatus == "completed"
    );

  const onDropRejected = (fileRejections: any[]) => {
    fileRejections.map((rejection) => {
      const { file } = rejection;
      notification.error({
        description: `Rejected: ${file.name} (Invalid file type or missing extension)`,
      });
      return file;
    });
  };

  // If you want to add specific MIME types, do it like this

  // Use the dropzone
  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    onDropRejected,
    accept:
      currentModule?.module_id === 8 && fileSupportAccess === "no_access"
        ? acceptedFileTypesInNoAccessRole
        : acceptedFileTypes,
    multiple,
  });

  const onRemoveFileClick = (file: INewFileObj) => {
    setUploadedFiles((prev: INewFileObj[]) =>
      prev
        .filter((prevValuee) => prevValuee?.id !== file?.id)
        .map((file) => ({
          ...file,
          isUploaded: 0,
          progress: 0,
          error: "",
        }))
    );
    setSelectedFiles((prev: INewFileObj[]) =>
      prev
        .filter((prevValue: INewFileObj) => prevValue.id !== file.id)
        .map((file) => ({
          ...file,
          isUploaded: 0,
          progress: 0,
          error: "",
        }))
    );
    setSelectedFilesFromNewFilestab &&
      setSelectedFilesFromNewFilestab((prev: INewFileObj[]) =>
        prev
          .filter((prevValue: INewFileObj) => prevValue.id !== file.id)
          .map((file) => ({
            ...file,
            isUploaded: 0,
            progress: 0,
            error: "",
          }))
      );
    const filePath = file?.fileUrl as string;
    let deleteParams = { fileUrl: [filePath] };
    deleteFiles(deleteParams);
  };
  useEffect(() => {
    if (selectedFiles.length > 0) {
      setAvailableFiles(true);
    } else {
      setAvailableFiles(false);
    }
  }, [selectedFiles, setAvailableFiles]);

  const joinWithAnd = (arr: Array<string>) => {
    if (arr.length === 0) return "";
    if (arr.length === 1) return arr[0];

    const allButLast = arr.slice(5, -1).join(", ");

    return `${allButLast}, and ${arr[arr.length - 1]}`;
  };

  return (
    <div className="h-full overflow-hidden">
      <div className="pt-4 new-file-upload-Dragger" {...getRootProps()}>
        <input {...getInputProps()} />
        <div className="flex md:flex-row flex-col md:gap-1.5 gap-1 items-center justify-center mx-4 p-4 border-2 border-dashed border-[#d9d9d9] rounded-lg cursor-pointer hover:border-primary-900">
          <img
            src="https://cdn.contractorforeman.net/assets/images/upload-file.svg"
            alt=""
          />
          <Typography className="sm:text-sm text-xs pl-2.5 text-primary-900 text-center font-semibold">
            {isiOS || isAndroid ? (
              <>
                <Typography className="font-bold">
                  {_t("Browse Files")}
                </Typography>{" "}
                {_t("On Your Computer")}
              </>
            ) : (
              <>
                {_t("Click here or Drop files here to Upload")} <br />{" "}
                {_t("OR")} <br />
                <Typography className="font-bold">
                  {_t("Browse Files")}
                </Typography>{" "}
                {_t("On Your Computer")}
              </>
            )}
          </Typography>
        </div>
      </div>
      <Typography className="pt-2 pb-1 px-4 text-[#616161] md:text-sm text-13 block">
        {_t(
          `Supported Formats: ${allowUploadExtensions.slice(0, 5).join(", ")},`
        )}{" "}
        <Tooltip
          overlayStyle={{ maxWidth: "330px" }}
          title={_t(joinWithAnd(allowUploadExtensions))}
        >
          <Typography className="font-semibold inline-block text-[#616161] md:text-sm text-13 underline underline-offset-1">
            {_t("and more")}
          </Typography>
        </Tooltip>
      </Typography>
      <div className="">
        {selectedFiles.length > 0 && (
          <div className="h-[calc(100dvh-316px)] md:h-[calc(100dvh-263px)] overflow-y-auto">
            <ul className="grid gap-1 px-4 pt-1 pb-2">
              {selectedFiles.map((file) => (
                <li
                  key={file.id}
                  className="flex items-center justify-between px-4 py-2 hover:shadow-[0px_4px_15px] hover:shadow-black/10 rounded-lg transition-all relative first:before:hidden before:absolute before:h-[1px] before:left-0 before:top-[-2px] before:bg-[#e5e7eb80] before:w-full group/upload-file"
                >
                  <div className="flex gap-2 items-center w-[calc(100%-100px)]">
                    <div className="w-12 h-12 flex items-center justify-center">
                      {file?.file_type?.startsWith("image/") &&
                      file?.file_type !== "image/heic" &&
                      file?.file_type !== "image/tiff" ? (
                        // Show image if upload is complete and file is an image
                        <img
                          src={file.file_url || "/placeholder.svg"}
                          alt={file.file_name}
                          className="w-full h-full object-cover mr-2"
                        />
                      ) : file?.file_type === "image/heic" ? (
                        <FontAwesomeIcon
                          icon="fa-regular fa-file"
                          className={`text-primary-900 w-[25px] h-[25px]`}
                        />
                      ) : (
                        // Show file icon if upload is complete for non-image files
                        <FontAwesomeIcon
                          icon="fa-regular fa-file-lines"
                          className={`text-primary-900 w-[25px] h-[25px]`}
                        />
                      )}
                    </div>
                    <div className="grid gap-1 w-full word-wrap-break">
                      <span>{file.file_name}</span>
                      {file.isUploading && file.uploadProgress !== 100 ? (
                        <Progress
                          percent={file.uploadProgress}
                          showInfo={false}
                          size="small"
                        />
                      ) : file.uploadProgress !== 100 &&
                        file.uploadStatus !== "completed" ? (
                        <span>Waiting</span>
                      ) : null}
                    </div>
                  </div>

                  {file.isUploading == false && (
                    <div>
                      <Tooltip title={_t("Delete")} placement="top">
                        <Button
                          className={`w-[18px] !p-0 max-w-[18px] max-h-[18px] xl:opacity-0 hover:!shadow-none group-hover/upload-file:opacity-100 border-0`}
                          onClick={() =>
                            isUploadComplete
                              ? onRemoveFileClick(file)
                              : undefined
                          }
                          disabled={!isUploadComplete || load}
                        >
                          <FontAwesomeIcon
                            className={`text-base w-[18px] h-[18px] text-red-500`}
                            icon="fa-regular fa-trash-can"
                          />
                        </Button>
                      </Tooltip>
                    </div>
                  )}
                </li>
              ))}
            </ul>
          </div>
        )}
        <div className="p-4">
          {selectedFiles.length > 0 && isUploadComplete && (
            <PrimaryButton
              buttonText={_t("Attach")}
              onClick={handleAddFileChange}
              disabled={!isUploadComplete || load}
              isLoading={load}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default NewFile;
