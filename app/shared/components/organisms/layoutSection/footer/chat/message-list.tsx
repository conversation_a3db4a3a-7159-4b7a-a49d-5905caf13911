import { memo, useEffect, useMemo, useRef, useState } from "react";

// atoms
import { LightGalleryModel } from "~/shared/components/atoms/lightGalleryModel";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Mention, MentionInput } from "~/shared/components/atoms/mention";
// molecules
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { IconButton } from "~/shared/components/molecules/iconButton";
import { ThumbnailView } from "~/shared/components/molecules/thumbnailView";
// organisms
import { FileSelect } from "~/shared/components/organisms/fileSelect";
import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";

// Zustand
import {
  addNewChatMessages,
  setChatMessages,
} from "~/zustand/global/chat/action";
import { getChatMessages } from "~/zustand/global/chat/slice";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";
import { useGlobalData } from "~/zustand/global/store";

// redux
import { useAppDispatch, useAppSelector } from "~/redux/store";

// Fire Base
import { set } from "@firebase/database";

// Other
import { useDateFormatter, useTranslation } from "~/hook";
import { globalChat } from "~/route-services/chat.routes";
import { Number, sanitizeString } from "~/helpers/helper";
import { DATE_FORMAT_ISO, defaultDateTimeFormat } from "./constants";
import { DateTime } from "luxon";
import { deleteMessageFirebaseCall, sendMessageFirebaseCall } from "./utils";
import { v4 as uuidv4 } from "uuid";
import { imageExtensions } from "~/modules/document/fileAndPhoto/constant";
import { FilePreview } from "~/shared/components/molecules/filePreview";

const avatars3 = "https://avatars3.githubusercontent.com/u/4948333";

const MessageList = ({
  activeChatData,
  search,
  minimize,
}: IMessageListProps) => {
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const {
    user_id = 0,
    company_id = 0,
    first_name = "",
    last_name = "",
    full_name = "",
    is_main_admin_user = "0",
  } = user || {};
  const { checkGlobalModulePermissionByKey } = useGlobalModule();
  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const {
    module_key = "",
    module_id = 0,
    module_access = "no_access",
  } = currentModule || {};
  const appSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();
  const { date_format = "", image_resolution = "" } = appSettings || {};

  const dispatch = useAppDispatch();

  const chatMessageKey = "id_" + activeChatData.user_id.toString();

  const messages = getChatMessages(chatMessageKey) || [];
  const divRef = useRef<HTMLDivElement | null>(null);

  const { _t } = useTranslation();
  const dateFormat = useDateFormatter();

  const [loading, setLoading] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(false);
  const [deleteConfirmationId, setDeleteConfirmationId] = useState<number>(0);
  const [deleteLoading, setDeleteLoading] = useState<boolean>(false);
  const [message, setMessage] = useState<string>("");
  const [displayMessage, setDisplayMessage] = useState<string>("");
  const [mentions, setMentions] = useState<Array<string>>([]);
  const [sendingMessage, setSendingMessage] = useState<boolean>(false);
  const [fileSelect, setFileSelect] = useState<TFileAttachmentTabsValues | "">(
    ""
  );
  const [selectedFiles, setSelectedFiles] = useState<IFile[]>([]);
  const [load, isLoad] = useState(false);

  const mentionsOption = useMemo(
    () =>
      (activeChatData.group_user || activeChatData.project_user || []).map(
        (groupUserValue: IChatUsersApiResponseRecordGroupUser) => {
          return {
            id: groupUserValue.user_id ?? "",
            display: groupUserValue.first_name ?? "",
            image: groupUserValue.image || avatars3,
          };
        }
      ),
    [activeChatData.group_user]
  );

  let fetching = false;

  const fetchUser = async ({
    start = 0,
  }: { start?: number } | undefined = {}) => {
    setLoading(true);
    const limit = 50;
    try {
      const apiParams = await getWebWorkerApiParams<IFetchChatMessagesParams>({
        otherParams: {
          data: "message",
          ke: activeChatData.user_id,
          tipe: activeChatData.tipe,
          group_id: activeChatData.user_id,
          limit: limit.toString(),
          start: start.toString(),
        },
      });

      const chatMessageResponse = (await webWorkerApi({
        url: globalChat.MESSAGES.list,
        method: "post",
        data: apiParams,
      })) as IChatMessagesApiResponse;
      fetching = false;
      if (chatMessageResponse.success) {
        let modifiedMessages = chatMessageResponse.data;
        if (start) {
          modifiedMessages = [...chatMessageResponse.data, ...messages];
        }
        modifiedMessages = modifiedMessages.sort((a, b) => {
          const firstMessageId = Number(a.m_id);
          const secondMessageId = Number(b.m_id);
          if (firstMessageId < secondMessageId) return -1;
          if (firstMessageId > secondMessageId) return 1;
          return 0;
        });
        setChatMessages({
          [chatMessageKey]: modifiedMessages,
        });

        setHasMore(
          Boolean(chatMessageResponse.data) &&
            !(chatMessageResponse.data.length < limit)
        );
        const date = dateFormat();
        let fireBaseParams: IFireBaseParams = {
          data: "read",
          name: activeChatData.user_id.toString(),
          recive_name: activeChatData.name,
          ke: "Public",
          avatar: activeChatData.avatar,
          user_id: activeChatData.user_id.toString(),
          company_id,
          message: activeChatData.msg,
          images: "",
          image: "",
          tipe: activeChatData.tipe,
          type: activeChatData.tipe,
          mention_ids: "",
          chat_type: "company",
          m_id: modifiedMessages[modifiedMessages.length]?.m_id || "",
          date,
        };
        switch (activeChatData.tipe) {
          case "project":
            fireBaseParams = {
              ...fireBaseParams,
              chat_type: "project",
            };
            break;
          case "client":
            fireBaseParams = {
              ...fireBaseParams,
              chat_type: "client_portal",
            };
            break;
        }

        if (FBDatabase.companyMessageRefValueSet) {
          set(FBDatabase.companyMessageRefValueSet, fireBaseParams);
        }
      }
    } catch (error) {
      notification.error({
        description: (error as Error).message,
      });
    }
    setLoading(false);
  };

  const activeChatsString = JSON.stringify(activeChatData);

  useEffect(() => {
    if (!fetching) {
      fetching = true;
      const keyIsExist =
        chatMessageKey in useGlobalData.getState().chat.message_list;
      if (!keyIsExist) {
        fetchUser();
      }
    }
  }, [activeChatsString]);

  const observer = new IntersectionObserver(async (entries) => {
    if (entries[0].isIntersecting && !loading) {
      fetchUser({
        start: messages.length,
      });
    }
  });

  const messageSendError = (newMessageId: string) => {
    const messages = useGlobalData.getState().chat.message_list[chatMessageKey];
    setChatMessages({
      [chatMessageKey]: messages.map((message) =>
        message.m_id === newMessageId
          ? {
              ...message,
              m_id: message.m_id.replace("new_message", "message_send_error"), // don't remove `new_message` because this keyword is depended
            }
          : message
      ),
    });
  };

  const sendMessage = async () => {
    const chatMessageKey = "id_" + activeChatData.user_id.toString();
    const newMessageID: string = "new_message_" + uuidv4(); // don't remove `new_message` because this keyword is depended
    if (message.trim() && !sendingMessage) {
      setSendingMessage(true);
      try {
        const mention_ids = JSON.stringify(mentions);
        let apiParams = await getWebWorkerApiParams<ISendChatMessageParams>({
          otherParams: {
            data: "send",
            ke: "Public",
            avatar: avatars3,
            message: message.replace(/&/g, "amp;"),
            tipe: activeChatData.tipe,
            images: "[]",
            date: dateFormat({ zone: false }),
            img_type: "img",
            timestamp_string: Date.now().toString(),
            mention_ids,
          },
        });

        switch (activeChatData.tipe) {
          case "project":
            apiParams = {
              ...apiParams,
              name: full_name,
              project_id: activeChatData.user_id,
            };
            break;
          case "client":
            apiParams = {
              ...apiParams,
              name: full_name,
              project_id: activeChatData.user_id,
            };
            break;
          default:
            apiParams = {
              ...apiParams,
              name: activeChatData.user_id,
            };
            break;
        }
        const lastMessage = messages[messages.length - 1];
        addNewChatMessages(chatMessageKey, {
          m_id: newMessageID,
          name: apiParams.name?.toString() || "",
          company_id: company_id?.toString(),
          avatar: apiParams.avatar,
          message: apiParams.message,
          is_allow_delete: "0",
          image: "",
          tipe: apiParams.tipe || "",
          date: apiParams.date,
          selektor: "Public",
          chat_type: "company",
          last_read_msg_id: lastMessage?.last_read_msg_id || "",
          message_type: lastMessage?.message_type || "",
          is_deleted: apiParams.is_deleted?.toString() || "0",
          is_unread_msg: 1,
          u_id: user_id.toString(),
        });
        setMessage("");
        setDisplayMessage("");
        const sendChatMessageResponse = (await webWorkerApi({
          url: globalChat.MESSAGES.send,
          method: "post",
          data: apiParams,
        })) as ISendChatMessageResponse;

        if (sendChatMessageResponse.success) {
          if (sendChatMessageResponse.data) {
            sendMessageFirebaseCall({
              activeChatData,
              mention_ids,
              apiParams,
              messages,
              newMessage: sendChatMessageResponse.data,
              newMessageId: newMessageID,
            });
          } else {
            messageSendError(newMessageID);
            notification.error({
              description: "Something went wrong!",
            });
          }
        } else {
          messageSendError(newMessageID);
          notification.error({
            description:
              sendChatMessageResponse.message || "Something went wrong!",
          });
        }
      } catch (error) {
        messageSendError(newMessageID);
        notification.error({
          description: (error as Error).message,
        });
      }
      setSendingMessage(false);
    }
  };

  const sendAttachment = (files: IFile[]) => {
    if (files.length && !sendingMessage) {
      const chatMessageKey = "id_" + activeChatData.user_id.toString();
      setSendingMessage(true);
      try {
        files.forEach((file) => {
          const newMessageID: string = "new_message_" + uuidv4(); // don't remove `new_message` because this keyword is depended
          const sendFile = async () => {
            let apiParams =
              await getWebWorkerApiParams<ISendCommonChatMessageParams>({
                otherParams: {
                  data: "send_common",
                  ke: "Public",
                  avatar: avatars3,
                  message: "",
                  tipe: activeChatData.tipe,
                  images: file?.file_path,
                  date: dateFormat({ zone: false }),
                  timestamp_string: Date.now()?.toString(),
                  mention_ids: 0,
                  is_allow_delete: 1,
                  m_id: 0,
                },
              });

            switch (activeChatData.tipe) {
              case "project":
                apiParams = {
                  ...apiParams,
                  name: full_name,
                  project_id: activeChatData.user_id,
                  chat_type: "project",
                };
                break;
              case "client":
                apiParams = {
                  ...apiParams,
                  name: full_name,
                  project_id: activeChatData.user_id,
                  chat_type: "client_portal",
                  is_deleted: 0,
                  project_name: activeChatData.name,
                };
                break;

              default:
                apiParams = {
                  ...apiParams,
                  name: activeChatData.user_id,
                  chat_type: "company",
                  image: file?.file_path,
                  project_id: 0,
                  receiver_user_id: activeChatData.user_id?.toString(),
                  recive_name: (first_name + " " + last_name).trim(),
                };
                break;
            }

            const chatMessageKey = "id_" + activeChatData.user_id.toString();
            const lastMessage = messages[messages.length - 1];

            addNewChatMessages(chatMessageKey, {
              m_id: newMessageID,
              name: apiParams.name?.toString() || "",
              company_id: company_id?.toString(),
              avatar: apiParams.avatar,
              message: apiParams.message,
              is_allow_delete: "0",
              image: apiParams.image || "",
              tipe: apiParams.tipe || "",
              date: apiParams.date,
              selektor: "Public",
              chat_type: "company",
              last_read_msg_id: lastMessage?.last_read_msg_id || "",
              message_type: lastMessage?.message_type || "",
              is_deleted: apiParams.is_deleted?.toString() || "0",
              is_unread_msg: 1,
              u_id: user_id.toString(),
            });

            const sendChatMessageResponse = (await webWorkerApi({
              url: globalChat.MESSAGES.send_common,
              method: "post",
              data: apiParams,
            })) as ISendCommonChatMessageResponse;

            if (sendChatMessageResponse.success) {
              if (sendChatMessageResponse.data) {
                sendMessageFirebaseCall({
                  activeChatData,
                  mention_ids: "0",
                  apiParams,
                  messages,
                  newMessage: sendChatMessageResponse.data,
                  newMessageId: newMessageID,
                });
              } else {
                messageSendError(newMessageID);
                throw new TypeError("Something went wrong!");
              }
            } else {
              messageSendError(newMessageID);
              throw new TypeError(
                sendChatMessageResponse.message || "Something went wrong!"
              );
            }
          };
          sendFile();
        });
      } catch (error) {
        notification.error({
          description: (error as Error).message,
        });
      }
      setSendingMessage(false);
    }
  };
  const count = 3;

  const portalRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    // Create a portal div if it doesn't exist
    if (!portalRef.current) {
      const divSuggestions = document.getElementById("mention-suggestions-portal") as HTMLDivElement | null;
      if (divSuggestions) portalRef.current = divSuggestions;
    }
  }, []);

  return (
    <>
      <div
        className={`relative overflow-hidden ${
          minimize ? "max-h-0" : "max-h-[448px]"
        } transition-all duration-300 ease-in-out`}
      >
        <div
          ref={divRef}
          className="user-chat-message-inner h-[418px] overflow-y-auto"
          //   onScroll={onChatScroll}
        >
          {loading && !Boolean(messages.length) ? (
            <>
              <div className="animate-pulse">
                {[...Array(count)].map((_, index) => (
                  <div key={index}>
                    <div className="grid gap-2 p-2">
                      <div
                        className={`bg-black/10 rounded w-[200px] h-5 `}
                      ></div>
                      <div
                        className={`bg-black/10 rounded w-[150px] h-5 `}
                      ></div>
                    </div>
                    <div className="grid gap-2 p-2 justify-end">
                      <div
                        className={`bg-black/10 rounded w-[200px] h-5 `}
                      ></div>
                      <div className="w-full flex justify-end">
                        <div
                          className={`bg-black/10 rounded w-[150px] h-5 flex justify-end`}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          ) : (
            !activeChatData.is_user_deleted && (
              <>
                <LightGalleryModel
                  zoom={true}
                  thumbnail={true}
                  backdropDuration={150}
                  showZoomInOutIcons={true}
                  actualSize={false}
                  mode="lg-slide"
                  alignThumbnails="left"
                  // className="flex flex-col-reverse"
                  mousewheel={true}
                >
                  {hasMore && (
                    <div
                      ref={(ref) => {
                        if (ref) observer.observe(ref);
                      }}
                    >
                      <div className="animate-pulse">
                        <div className="grid gap-2 p-2">
                          <div
                            className={`bg-black/10 rounded w-[200px] h-5 `}
                          ></div>
                          <div
                            className={`bg-black/10 rounded w-[150px] h-5 `}
                          ></div>
                        </div>
                      </div>
                    </div>
                  )}
                  {messages.map((messageData, key) => {
                    const sender = [
                      messageData.u_id?.toString(),
                      messageData.user_id?.toString(),
                    ].includes(user_id.toString());

                    const fileUrl =
                      (messageData.image || "").split("?").shift() || "";

                    const fileExt =
                      fileUrl.split(".").pop()?.split(".").shift() || "";

                    let fileName = fileUrl.split("." + fileExt).shift();

                    if (fileName?.trim()) {
                      fileName =
                        fileName.split("/")[fileName.split("/").length - 1];
                    }

                    // const fileData = getFileType({ file: fileUrl });

                    const messageDate = dateFormat({
                      date: messageData.date,
                      format: DATE_FORMAT_ISO,
                      dateFormat: defaultDateTimeFormat,
                    });

                    const now = DateTime.now();

                    const lastReadMsgId = Number(messageData.last_read_msg_id);
                    const prevMessageId = Number(messages[key - 1]?.m_id);
                    const nextMessageId = Number(messages[key + 1]?.m_id);

                    const isUnreadMessageView: boolean =
                      Boolean(prevMessageId) &&
                      Boolean(lastReadMsgId) &&
                      !sender &&
                      prevMessageId.toString() === lastReadMsgId.toString();
                    let message = messageData.message;
                    if (search.trim()) {
                      message = messageData.message.replace(
                        search,
                        `<span className="!text-blue-500 font-bold">${search}</span>`
                      );
                    }
                    return (
                      <div
                        key={key}
                        className="chat-message group/message flex flex-col pt-1 px-2.5 pb-1 hover:bg-primary-gray-20 dark:hover:bg-dark-800"
                        ref={(ref) => {
                          if (
                            ref &&
                            ((nextMessageId &&
                              lastReadMsgId >= nextMessageId) ||
                              !messages[key + 1])
                          ) {
                            ref.scrollIntoView({ block: "end" });
                          }
                        }}
                      >
                        {isUnreadMessageView && (
                          <div className="text-center my-4 text-xs text-black opacity-50 relative before:absolute before:w-full before:h-[1px] before:bg-[radial-gradient(circle,black_10%,transparent_100%)] before:left-0 before:top-2/4 before:-translate-y-2/4">
                            <Typography className="bg-[#fcfcfa] px-1 relative z-10">
                              {_t("Unread Message")}
                            </Typography>
                          </div>
                        )}
                        <div className={sender ? "flex justify-end" : ""}>
                          {!sender && messageData.tipe !== "users" && (
                            <div className="text-[#30649c] text-xs mb-[5px]">
                              {HTMLEntities.decode(
                                sanitizeString(messageData.name)
                              )}
                            </div>
                          )}
                          <div
                            className={`w-full flex gap-0.5 justify-end ${
                              sender ? "" : "flex-row-reverse"
                            }`}
                          >
                            {messageData.is_allow_delete?.toString() === "1" &&
                              messageData.is_deleted?.toString() === "0" &&
                              (sender ||
                                activeChatData.admin_id?.toString() ===
                                  user_id?.toString() ||
                                is_main_admin_user?.toString() === "1") && (
                                <>
                                  <ButtonWithTooltip
                                    tooltipPlacement="top"
                                    tooltipTitle={_t("Delete")}
                                    icon="fa-regular fa-trash-can"
                                    className="hidden group-hover/message:flex hover:!bg-[#FF00001a]"
                                    iconClassName="text-base !text-red-400 group-hover/buttonHover:!text-[#FF0000] w-3.5 h-3.5"
                                    onClick={() => {
                                      setDeleteConfirmationId(
                                        Number(messageData.m_id)
                                      );
                                    }}
                                  />
                                  {deleteConfirmationId.toString() ===
                                    messageData.m_id.toString() && (
                                    <ConfirmModal
                                      isOpen={Boolean(deleteConfirmationId)}
                                      modalIcon="fa-regular fa-trash-can"
                                      isLoading={deleteLoading}
                                      modaltitle={_t("Delete")}
                                      description={_t(
                                        "Are you sure you want to delete this message?"
                                      )}
                                      onCloseModal={() =>
                                        setDeleteConfirmationId(0)
                                      }
                                      onAccept={async () => {
                                        setDeleteLoading(true);
                                        try {
                                          const apiParams =
                                            await getWebWorkerApiParams<IDeleteChatMessagesParams>(
                                              {
                                                otherParams: {
                                                  message_id: messageData.m_id,
                                                  type: "project",
                                                },
                                              }
                                            );

                                          const chatMessageDeleteResponse =
                                            (await webWorkerApi({
                                              url: globalChat.MESSAGES.delete,
                                              method: "post",
                                              data: apiParams,
                                            })) as IDeleteChatMessagesApiResponse;
                                          if (
                                            chatMessageDeleteResponse.success
                                          ) {
                                            deleteMessageFirebaseCall({
                                              messages,
                                              activeChatData,
                                              chatMessageDeleteResponseData:
                                                chatMessageDeleteResponse.data,
                                            });
                                            setDeleteConfirmationId(0);
                                          } else {
                                            notification.error({
                                              description:
                                                chatMessageDeleteResponse.message ||
                                                "Something went wrong!",
                                            });
                                          }
                                        } catch (error) {
                                          notification.error({
                                            description: (error as Error)
                                              .message,
                                          });
                                        }
                                        setDeleteLoading(false);
                                      }}
                                      onDecline={() =>
                                        setDeleteConfirmationId(0)
                                      }
                                    />
                                  )}
                                </>
                              )}
                            <Typography
                              className={`py-1 whitespace-pre-line relative min-h-[24px] text-[#6c6c6c] dark:text-white/60 border border-solid px-2 text-xs rounded-md after:absolute after:border-[10px] after:top-0 after:border-transparent max-w-[90%] w-fit break-words ${
                                sender
                                  ? "bg-[#eff6e5] dark:bg-dark-500 after:border-t-[#eff6e5] dark:after:border-t-dark-500 after:right-[-10px] border-[#64aa001a]"
                                  : "bg-[#dbebf3] border-[#0072871a] dark:bg-[#333d50] after:border-t-[#dbebf3] dark:after:border-t-[#333d50] after:left-[-10px] flex"
                              }`}
                            >
                              {messageData.is_deleted?.toString() === "1" ? (
                                <div className="flex items-center text-[#999]">
                                  <FontAwesomeIcon icon="fa-regular fa-ban" />{" "}
                                  &nbsp;This message was deleted.
                                </div>
                              ) : messageData.image ? (
                                <LoadMessageImage
                                  fileExt={fileExt}
                                  fileUrl={messageData.image}
                                  imageId={messageData.m_id}
                                  data-sub-html={`<h4>${fileName}</h4>`}
                                />
                              ) : (
                                <span
                                  dangerouslySetInnerHTML={{
                                    __html: HTMLEntities.decode(message),
                                  }}
                                />
                              )}
                            </Typography>
                          </div>
                        </div>
                        <div
                          className={`text-[10px] text-gray-500 pt-1 ${
                            sender ? "flex justify-end items-center" : ""
                          }`}
                        >
                          {messageData.date && (
                            <>
                              {(() => {
                                const isToday =
                                  messageDate === now.toFormat(DATE_FORMAT_ISO);
                                const isYesterday =
                                  messageDate ===
                                  now
                                    .plus({ days: -1 })
                                    .toFormat(DATE_FORMAT_ISO);

                                if (isToday) return "Today";
                                if (isYesterday) return "Yesterday";

                                return dateFormat({
                                  date: messageData.date,
                                  format: CFConfig.luxon_date_format,
                                  dateFormat: defaultDateTimeFormat,
                                });
                              })()}
                              &nbsp;
                              {dateFormat({
                                date: messageData.date,
                                format: "hh:mm a",
                                dateFormat: defaultDateTimeFormat,
                              })}
                              &nbsp;
                            </>
                          )}
                          {messageData.m_id?.includes("new_message") ? (
                            <FontAwesomeIcon
                              className="text-base flex w-2.5 h-2.5"
                              icon="fa-regular fa-clock"
                            />
                          ) : messageData.m_id?.includes(
                              "message_send_error"
                            ) ? (
                            <FontAwesomeIcon
                              className="text-base flex w-2.5 h-2.5 text-red-400"
                              icon="fa-regular fa-xmark"
                            />
                          ) : (
                            messageData.tipe === "users" &&
                            sender &&
                            (messageData.is_unread_msg.toString() === "1" ? (
                              <FontAwesomeIcon
                                className="text-base flex w-2.5 h-2.5"
                                icon="fa-regular fa-check"
                              />
                            ) : (
                              messageData.is_unread_msg.toString() === "0" &&
                              messageData.is_deleted.toString() === "0" && (
                                <FontAwesomeIcon
                                  className="text-base flex w-2.5 h-2.5 text-blue-500"
                                  icon="fa-regular fa-check"
                                />
                              )
                            ))
                          )}
                        </div>
                      </div>
                    );
                  })}
                </LightGalleryModel>
              </>
            )
          )}
        </div>
        <div
          className="absolute bottom-4 left-1/2 -translate-x-1/2 h-fit w-fit hidden"
          id="last-message"
        >
          <PrimaryButton
            icon={
              <FontAwesomeIcon
                className="w-[13px] h-[13px]"
                icon="fa-regular fa-arrow-down"
              />
            }
            buttonText={_t("Last Message")}
            onClick={() => {
              const lastChild = divRef?.current?.lastChild as Element | null;
              if (lastChild) {
                lastChild.scrollIntoView({
                  block: "end",
                  behavior: "smooth",
                });
              }
            }}
          />
        </div>

        {fileSelect !== "" && (
          <FileSelect
            options={["new", "gallery", "url"]}
            selectedTab={fileSelect}
            setSelectedTab={setFileSelect}
            setSelectedFileData={(data) => {
              sendAttachment(data as IFile[]);
            }}
            handleSelectImage={(imageData: IFile) => {
              setSelectedFiles((prevSelectedFiles) => {
                if (
                  prevSelectedFiles.some(
                    (file) => file.image_id === imageData.image_id
                  )
                ) {
                  return prevSelectedFiles.filter(
                    (file) => file.image_id !== imageData.image_id
                  );
                } else {
                  return [...prevSelectedFiles, imageData];
                }
              });
            }}
            selectedFiles={selectedFiles}
            useAppSelector={useAppSelector}
            dispatch={dispatch}
            setSelectedFiles={setSelectedFiles}
            validationParams={{
              date_format,
              file_support_module_access: checkGlobalModulePermissionByKey(
                CFConfig.file_support_key
              ),
              image_resolution,
              module_key,
              module_id,
              module_access,
            }}
            load={load}
            isLoad={isLoad}
          />
        )}
      </div>
      {activeChatData.is_user_deleted ? (
        <Typography className="text-center text-[#ff6767] text-xs block py-[7px] bg-[#f4f8ff]">
          {!Boolean(messages.length)
            ? "You have been removed from this group."
            : "This Group is Deleted."}
        </Typography>
      ) : (
        <div
          className={`items-center justify-end border-t border-blue-gray-50 dark:border-white/10 relative mt-[30px] ${
            minimize ? "hidden" : "flex"
          }`}
        >
          <div className="h-auto min-h-[30px] max-h-[85px] w-full absolute bottom-[31px] border-t border-blue-gray-50 bg-white overflow-auto">
            <MentionInput
              // singleLine
              value={displayMessage}
              placeholder={_t("Type a message")}
              onChange={(e, ...mentionsValue) => {
                setDisplayMessage(e.target.value);
                if (mentionsValue[2].length !== mentions.length) {
                  setMentions(mentionsValue[2].map(({ id }) => id));
                }
                setMessage(
                  mentionsValue[0]
                    .replaceAll("-__^^__", "<strong><span>")
                    .replaceAll("@@@^^^", "</span></strong>")
                );
              }}
              className="chat-input"
              onKeyDown={(e) => {
                if (e.key === "Enter" && !e.shiftKey) {
                  e.preventDefault();
                  sendMessage();
                }
              }}
              style={{
                input: {
                  height: "auto",
                },
              }}
              suggestionsPortalHost={portalRef.current ?? undefined}
            >
              <Mention
                trigger="@"
                data={mentionsOption}
                renderSuggestion={(suggestion) => {
                  const mentionChatUser = suggestion as IMentionChatUser;
                  return (
                    <div className="user-suggestion flex items-center gap-1">
                      <img
                        src={mentionChatUser.image}
                        alt={mentionChatUser.display}
                        className="user-avatar"
                        height={"20px"}
                        width={"20px"}
                      />
                      <div className="user-info">
                        <strong>{mentionChatUser.display}</strong>
                      </div>
                    </div>
                  );
                }}
                markup={`-__^^____display__@@@^^^`}
                displayTransform={(id: string, display: string) => {
                  return display;
                }}
              />
            </MentionInput>
          </div>
          <div id="mention-suggestions-portal"></div>
          <div className="flex gap-0.5 ml-auto">
            <ButtonWithTooltip
              tooltipPlacement="top"
              tooltipTitle={_t("Add attachments")}
              icon="fa-regular fa-paperclip"
              className="!w-[30px] !h-[30px] focus-visible:!outline-0"
              iconClassName="text-base w-4 h-4 !text-primary-900 dark:!text-white/90"
              onClick={() => {
                setFileSelect("new");
              }}
            />
            <IconButton
              className="!w-[30px] !h-[30px] !bg-primary-900"
              onClick={() => {
                sendMessage();
              }}
              variant="primary"
            >
              <FontAwesomeIcon
                className="text-base w-4 h-4 text-white"
                icon="fa-regular fa-paper-plane"
              />
            </IconButton>
          </div>
        </div>
      )}
    </>
  );
};

const LoadMessageImage = ({
  fileExt,
  fileUrl,
  imageId,
  ...props
}: ILoadMessageImageProps) => {
  const [loading, setLoading] = useState(true);
  let fileExtension = fileExt?.toLowerCase();
  const isImage = !!fileExtension && imageExtensions.includes(fileExtension);

  return (
    <>
      {loading && (
        <div className="w-20 h-20 animate-pulse">
          <div className="h-full bg-slate-200 dark:bg-dark-900 rounded-lg"></div>
        </div>
      )}

      {isImage ? (
        <>
          <div className="w-20 h-20 relative overflow-hidden rounded-lg">
            <a href={fileUrl} className="lightGalleryModel block w-full h-full">
              <ThumbnailView
                file_ext={fileExt ?? ""}
                file_path={fileUrl ?? ""}
                image_id={imageId}
                file_name={""}
                className="w-full h-full object-cover hover:scale-110 delay-100 ease-in duration-300"
                isLoading={loading}
                setIsLoading={setLoading}
              />
            </a>
          </div>
        </>
      ) : (
        <a
          {...props}
          href={fileUrl}
          download
          className={loading ? "hidden" : ""}
        >
          <ThumbnailView
            file_ext={fileExt}
            file_path={fileUrl}
            image_id={imageId}
            className="h-20 cursor-pointer max-w-[90px]"
            isLoading={loading}
            setIsLoading={setLoading}
          />
        </a>
      )}
    </>
  );
};

export default memo(MessageList);
