import React, { useEffect, useState } from "react";
import { useNavigate } from "@remix-run/react";

// Zustand
import { getGlobalUser } from "~/zustand/global/user/slice";
import { getGlobalMenuModule } from "~/zustand/global/menuModules/slice";
import { getModuleLink } from "~/zustand/global/menuModules/utils";

// atoms
import { Typography } from "~/shared/components/atoms/typography";
import { Tooltip } from "~/shared/components/atoms/tooltip";
// Molecules
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";

import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";

// Others
import { useDateFormatter, useTranslation } from "~/hook";
import { DateTime } from "luxon";
import { apiRoutes, routes } from "~/route-services/routes";
import { sanitizeString } from "~/helpers/helper";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { EVENT_LOGGER_NAME } from "~/shared/constants/event-logger";

const UserMenu = () => {
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { full_name, user_id, image } = user || {};

  const userSettingModule:
    | IInitialGlobalData["menu"]["modules"][0]
    | undefined = getGlobalMenuModule(CFConfig.user_setting_module);

  let { _t } = useTranslation();
  const { company_id, demo_mode } = user || {};

  const navigate = useNavigate();
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isLoadingInAdditionalButton, setIsLoadingInAdditionalButton] =
    useState<boolean>(false);

  const headerMenuOptions = [
    {
      label: "Reload & Clear Cache",
      icon: "fa-regular fa-database",
      onClick: async () => {
        await webWorkerApi({
          url: apiRoutes.CLEAR_CATCH.url,
          method: "post",
        });
        window.location.reload();
      },
    },
    {
      label: "My Settings",
      icon: "fa-regular fa-user",
      href: userSettingModule ? getModuleLink(userSettingModule) : undefined,
    },
    ...(demo_mode === "1"
      ? [
          {
            label: "Remove Demo Data",
            icon: "fa-regular fa-trash-can !text-white",
            itemClass:
              "!bg-[#ff5400] hover:!bg-[#ff5400] rounded !text-white btn-demo-data w-full ",
            onClick: async () => {
              setIsConfirmModalOpen(true);
            },
          },
        ]
      : []),
    // {
    //   label: themeMode === "dark" ? "Light" : "Dark",
    //   icon: themeMode === "dark" ? faSunBright : faMoon,
    //   onClick: onThemeClick,
    // },
    {
      label: "Logout",
      icon: "fa-regular fa-sign-out",
      onClick: () => {
        navigate(routes.SIGNOUT.url);
      },
    },
  ];

  const handleCloseModal = () => {
    setIsConfirmModalOpen(false);
    setIsLoading(false);
    setIsLoadingInAdditionalButton(false);
  };

  const handleDeleteDemoData = async (data: string) => {
    var delete_sample_cost_codes = 0;
    if (data === "yes") {
      delete_sample_cost_codes = 1;
      setIsLoading(true);
    } else {
      delete_sample_cost_codes = 0;
      setIsLoadingInAdditionalButton(true);
    }
    try {
      const data = await getWebWorkerApiParams({
        otherParams: {
          original_company_id: company_id,
          delete_sample_cost_codes: delete_sample_cost_codes,
        },
      });
      const response = (await webWorkerApi({
        url: apiRoutes.REMOVE_DEMO_DATA.url,
        method: "post",
        data: data,
      })) as IRemoveDemoDataApiResponse;

      if (response) {
        if (response.success) {
          EventLogger.log(EVENT_LOGGER_NAME.removed_demo_data, 1);
          handleCloseModal();
          navigate("/sign-out");
        } else {
          notification.error({
            description: response.message,
          });
        }
      } else {
        notification.error({
          description: _t("Some thing went wrong!"),
        });
      }
    } catch (error: unknown) {
      notification.error({ description: (error as Error)?.message });
    }
  };

  return (
    <>
      <DropdownMenu
        options={headerMenuOptions}
        contentClassName="w-fit mr-0.5"
        iconClassName="text-white"
        buttonClass="!w-auto md:!h-[66px] !h-12 !px-2"
      >
        <div className="flex items-center">
          <div className="text-left md:block hidden">
            <Typography className="text-13 text-[#000011] dark:text-[#dcdcdd] block leading-[1.2em] font-normal">
              {HTMLEntities.decode(sanitizeString(full_name))}
            </Typography>
            <Typography className="text-[11px] text-[#ACACAC] dark:text-white/50 block leading-[1.2em] font-normal">
              {_t("User")} ({user_id})
            </Typography>
            {typeof window !== "undefined" &&
              ["dev", "beta"].includes(window.ENV.PANEL_TYPE) && <LiveTime />}
          </div>
          <div className="md:ml-1.5">
            <AvatarProfile
              user={{
                image,
                name: full_name,
              }}
              className="w-[31px] h-[31px]"
            />
          </div>
        </div>
      </DropdownMenu>
      {isConfirmModalOpen && (
        <ConfirmModal
          isOpen={isConfirmModalOpen}
          isLoading={isLoading}
          isLoadingInAdditionalButton={isLoadingInAdditionalButton}
          description={_t(
            "Do you want to delete the sample Cost Codes as well?"
          )}
          modaltitle={_t("Delete")}
          yesButtonLabel={_t("Yes")}
          noButtonLabel={_t("Cancel")}
          additionalButton={_t("No")}
          onAccept={() => handleDeleteDemoData("yes")}
          onDecline={() => setIsConfirmModalOpen(false)}
          onAdditionButtonClick={() => handleDeleteDemoData("no")}
          onCloseModal={() => handleCloseModal()}
          zIndex={9999}
        />
      )}
    </>
  );
};

// Using React.memo to optimize the component by preventing unnecessary re-renders
const LiveTime = React.memo(() => {
  // Translation function from i18n for internationalization
  const { _t } = useTranslation();

  // Custom date formatter function
  const dateFormat = useDateFormatter();

  // Helper function to format the current time
  const getTime = (currentTime: number) => {
    return dateFormat({
      date: DateTime.fromMillis(currentTime).toFormat("yyyy-MM-dd hh:mm:ss"),
      dateFormat: "yyyy-MM-dd hh:mm:ss",
      format: "d/L hh:mm a", // Final format to display
    });
  };

  // Effect to handle live time updates
  useEffect(() => {
    // Function to update the time in the DOM element
    function getRealTime() {
      const formattedTime = getTime(Date.now()); // Get the current formatted time

      // Update the content of the time display element
      const timeElement = document.getElementById("time-display");
      if (timeElement) {
        timeElement.textContent = formattedTime;
      }
    }

    // Initial update to set the time when the component mounts
    getRealTime();

    // Setting up an interval to update the time every second
    const intervalId = setInterval(() => {
      getRealTime();
    }, 1000);

    // Cleanup function to clear the interval when the component unmounts
    return () => clearInterval(intervalId);
  }, [dateFormat]); // Dependency array ensures effect re-runs if `dateFormat` changes

  return (
    // Tooltip component for additional information
    <Tooltip
      title={
        window.ENV.PANEL_TYPE === "dev"
          ? _t("Show only CFdev") // Translation for dev panel
          : _t("Show only Beta") // Translation for beta panel
      }
      className="rounded max-w-[200px] !text-xs leading-5 text-primary-900"
    >
      <div>
        {/* Typography component to display the time */}
        <Typography
          className="!text-[11px] !text-[#ACACAC] leading-[1.2em] !font-normal"
          id="time-display" // DOM element updated via `document.getElementById`
        >
          {getTime(Date.now())} {/* Fallback for initial rendering */}
        </Typography>
      </div>
    </Tooltip>
  );
});

export default UserMenu;
