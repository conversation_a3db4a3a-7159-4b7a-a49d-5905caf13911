export const defaultResponse: DefaultResponse = {
  success: "0",
  message: "Something went wrong! please try again",
  data: [],
};

export const defaultDateTimeFormat = "yyyy-MM-dd hh:mm:ss";
export const DATE_FORMAT_ISO = "yyyy-MM-dd";

export const defaultConfig: IDefaultConfig = {
  default_lead_sales_status: 216,

  user_id: 1,

  employee_key: "employee",
  employee_id: 2,
  employee_text: "",

  customer_key: "customer",
  customer_id: 3,
  customer_text: "",

  contractor_key: "contractor",
  contractor_id: 4,
  contractor_text: "",

  vendor_key: "vendor",
  vendor_id: 22,
  vendor_text: "",

  misc_contact_key: "misc_contact",
  misc_contact_id: 23,
  misc_contact_text: "",

  lead_key: "lead",
  lead_id: 204,
  lead_text: "",

  material_key: "material",
  material_teb_id: 161,
  material_module_id: 41,
  material_icon: "fa-regular fa-block-brick",

  equipment_key: "equipment",
  equipment_teb_id: 162,
  equipment_module_id: 42,
  equipment_icon: "fa-regular fa-screwdriver-wrench",

  labor_key: "labor",
  labor_teb_id: 163,
  labor_module_id: 63,
  labor_icon: "fa-regular fa-user-helmet-safety",

  subcontractor_key: "subcontractor",
  subcontractor_teb_id: 164,
  subcontractor_module_id: 64,
  subcontractor_icon: "fa-regular fa-file-signature",

  other_items_key: "other_items",
  other_items_teb_id: 165,
  other_items_module_id: 72,
  other_items_icon: "fa-regular fa-boxes-stacked",

  group_key: "groups",
  group_id: 0,
  group_icon: "fa-regular fa-layer-group",

  item_mixed_key: "item_mixed",
  item_mixed_tab_id: 0, // this possiblity to change in future

  sub_contract_module_id: 67,

  purchase_order_module_id: 66,

  expense_module_id: 12,
  defaultTaxId: "874",

  project_module: "projects",
  appointment_module: "appointments",
  corporate_notes_module: "corporate_notes",
  change_order_module: "change_orders",
  directory_module: "directories",
  todo_module: "todos",
  invoice_module: "invoices",
  invoice_merge_module_key: "invoice_merge",
  advance_invoice_module: "advance_invoice",
  marge_invoice_module: "invoice_merge",
  estimate_module: "estimates",
  notes_module: "notes",
  time_card_module: "time_cards",
  weekly_time_sheet_module: "weekly_time_sheet",
  crew_card_module: "crew_card",
  files_module: "files",
  punch_list_module: "punchlists",
  vehicle_log_module: "vehicle_logs",
  equipment_log_module: "equipment_logs",
  inspection_module: "inspections",
  // incident_employee_writeup_module : 'incident_write_up_merge',
  incident_employee_writeup_module: "incidents", // for new incident & write-up merge module
  incident_module: "incidents",
  employee_writeup_module: "employee_writeups",
  safety_meeting_module: "safety_meetings",
  safety_meeting_module_group: "safety_meetings_group",
  safety_meeting_module_individual: "safety_meetings_individual",
  daily_log_module: "daily_logs",
  correspond_module: "correspondences",
  expense_module: "expenses",
  payment_module: "payments",
  report_module: "reports",
  project_report_module: "project_reports",
  project_schedule_gantt_module: "project_schedule_gantt",
  project_document_tab: "project_document_tab",
  resource_module: "resources",
  suggestion_module: "make_suggestion",
  submit_bug_module: "submit_bug",
  refer_us_module: "refer_us",
  support_module: "support",
  training_videos_module: "training_videos",
  add_more_users_module: "add_more_users",
  company_module: "company_setting",
  enable_disable_module: "enable_disable_features",
  user_setting_module: "user_setting",
  app_setting_module: "app_setting",
  employee_module: "employees",
  customer_module: "customers",
  contractor_module: "contractors",
  vendor_module: "vendors",
  misc_contact_module: "misc_contacts",
  leads_module: "leads",
  forms_checklist_module: "forms_checklist",
  service_ticket_module: "service_tickets",
  crew_sheet_module: "crew_sheet",
  customer_access_code_module: "customer_access_code",
  employee_wage_module: "employee_wage",
  project_contract_amount_module: "project_contract_amount",
  project_amount_paid_module: "project_amount_paid",
  project_finance_tab_module: "project_finance_tab",
  project_budget_tab_module: "project_budget_tab",
  project_budget_amount_module: "project_budget_amount",
  project_balance_remaining_module: "project_balance_remaining",
  corporate_calendar_module: "corporate_calendar",
  gantt_chart_module: "gantt_chart",
  gantt_chart_update_module: "gantt_chart2",
  work_order_module: "work_orders",
  equipment_module: "equipments",
  vehicle_module: "vehicles",
  labour_module: "labors",
  sub_contractor_module: "sub_contractors",
  otherItems_module: "other_items",
  material_module: "materials",
  tag_categories_module: "tag_categories",
  cost_codes_module: "cost_codes",
  service_module: "services",
  group_module: "groups",
  role_module: "roles",
  purchase_order_module: "purchase_orders",
  sub_contracts_module: "sub_contracts",
  delete_timecard_module: "delete_timecard",
  change_order_billing_status: "change_order_status",
  change_order_request_billing_status: "cor_status",
  default_timecard_project: "timecard_office",
  service_ticket_appointment_status: "service_ticket_appointment_status",
  service_ticket_job_status: "service_ticket_job_status",
  service_ticket_priority: "service_ticket_priority",
  notes_priority: "notes_priority",
  po_billing_status_key: "purchase_order_billing_status",
  po_ship_to_key: "purchase_order_ship_to",
  qb_chart_of_accounts_module: "qb_chart_of_accounts",
  qb_account_sync_module: "qb_account_sync",
  qb_transaction_log_module: "quickbook_log",
  qbd_log_module: "qbd_log",
  mleso_items_module: "database_items",
  project_permit_module: "project_permits",
  project_permit_module_id: 108,
  bill_module: "bills",
  bill_module_id: 78,
  bill_key_id: 243,
  bill_type_id: 232,
  whats_new_module: "whats_new",
  submittal_module: "submittals",
  timecard_reports_module: "timecard_reports",
  timecard_vefication_module: "time_verification",
  opportunity_module: "opportunities",
  opportunity_module_id: 88,
  sales_activity_module: "sales_activity",
  pdf_label_language_module: "pdf_label_language",
  company_chat_module: "company_chat",
  default_setting_module: "default_setting",
  custom_setting_module: "custom_setting",
  financial_setting_module: "financial_setting",
  notification_setting_module: "notification_setting",
  module_log_setting_module: "module_log_setting",
  integration_setting_module: "integration_setting",
  zapierintegration_setting_module: "zapier_integration",
  pdf_template_setting_module: "pdf_templates",
  timecard_setting_tab_module: "timecard_setting_tab",
  estimate_bidding_module: "estimate_bidding",
  project_file_module: "project_file_tab",
  project_budget_tab: "project_budget_tab",
  client_portal_module: "client_portal",
  crew_schedule_module: "crew_schedule",
  procurement_module: "procurement",
  cancellation_request_module: "cancellation_request",
  wepay_module: "wepay_checkout",
  wepay_payment_module: "wepay_payment",
  custom_fields_module: "custom_fields",
  //    settings_module : "settings_module",
  settings_module: "app_setting",
  setting_company_type: "setting_company_type",
  setting_primary_industry: "setting_primary_industry",
  material_company_type_key: "item_material",
  equipment_company_type_key: "item_equipment",
  labour_company_type_key: "item_labour",
  sub_contractor_company_type_key: "item_sub_contractor",
  other_item_company_type_key: "item_other",
  group_item_company_type_key: "item_group",
  mixed_company_type_key: "item_mixed",
  document_writer_module: "document_writer",
  file_support_key: "documents",
  lead_stage_received_status_key: "lead_stage_received",
  send_email_module: "send_email",
  user_access_role_key: "user_access_and_role",

  vimeo_website_url: "https://vimeo.com/",

  estimate_default_bid_status: "est_bid_draft",

  lead_activity_type: "lead_activity_type",
  lead_activity_status: "lead_activity_status",

  company_files_project_id: "company_files",
  unassigned_files_project_id: "unassigned_files",
  company_files_folder_id: 262,
  unassigned_files_folder_id: 336,

  file_photo_module_id: 8,
  projects_module_id: 1,

  todo_open_status_id: 64,
  todo_incomplete_status_id: 65,
  todo_completed_status_id: 66,

  pay_pending_review: "",
  pay_verified: "",
  pay_in_review: "",

  company_items: "company_items",

  project_type_key_id: 188,
  project_type_key: "project_type_key",

  custom_estimate_status_key: "estimate_status_key",
  custom_estimate_status_id: 275,

  project_status_type_id: 226,
  project_status_type_key: "project_status_key",

  project_messaging_module: "project_messaging",

  luxon_date_format: "yyyy-LL-dd",
  day_js_date_format: "YYYY-MM-DD",
  date_format: "",

  project_permit_key_id: 335,

  lead_stage_type_id: 221,
  lead_stage_type_key: "lead_stage_key",

  opportunity_stage_type_id: 421,
  opportunity_stage_type_key: "opportunity_stage_key",

  opportunity_referral_source_type_id: 429,
  opportunity_referral_source_type_key: "opportunity_referral_source_key",

  sensitive_information: "sensitive_information",
};

export const customerSideModuleWiseOptions: CustomerSideModuleWiseOptions = {
  bills: [defaultConfig.employee_key, defaultConfig.contractor_key],
  estimates: [
    defaultConfig.employee_key,
    defaultConfig.contractor_key,
    "my_project",
  ],
  send_mail: [
    defaultConfig.employee_key,
    "my_crew",
    defaultConfig.customer_key,
    defaultConfig.lead_key,
    defaultConfig.contractor_key,
    defaultConfig.vendor_key,
    defaultConfig.misc_contact_key,
    "by_service",
    "my_project",
  ],
};

export const costDataBase: ItemTabs[] = [
  defaultConfig.material_key,
  defaultConfig.labor_key,
  defaultConfig.equipment_key,
  defaultConfig.subcontractor_key,
  defaultConfig.other_items_key,
  defaultConfig.group_key,
];
