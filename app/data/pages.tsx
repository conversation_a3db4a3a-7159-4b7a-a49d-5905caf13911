import ManageEstimates from "~/routes-flat/_module+/manage-estimates+/$id";

interface RemixCompletePage {
  url: string;
  key: string;
  both_view?: boolean;
  default_view?: "remix" | "php";
  plan_view?: boolean;
  // component?: React.ComponentType | JSX.Element;
  component?: React.ComponentType<any>;
}

export const CF_PLANS = {
  basic_v1_to_v4: 11,
  basic_v5: 18,
  standard_v5: 19,
  standard_v1_to_v4_planID: 12,
  default_planID: 1,
  plus_v1_to_v4_planID: 13,
  pro_v1_to_v4_planID: 14,
  enterprise_v1_to_v4_planID: 15,
  unlimited_v1_to_v4_planID: 17,
  standard_v5_planID: 19,
  plus_v5_planID: 20,
  pro_v5_planID: 21,
  unlimited_v5_planID: 22,
  trialAll_v1_to_v4_planID: 16,
};

export const USER_PLANES = {
  basic_v1_to_v4: CF_PLANS.basic_v1_to_v4,
  basic_v5: CF_PLANS.basic_v5,
  standard_v5: CF_PLANS.standard_v5,
  standard_v1_to_v4_planID: CF_PLANS.standard_v1_to_v4_planID,
  default_planID: CF_PLANS.default_planID,
};

export const remixCompletePages: RemixCompletePage[] = [
  {
    url: "manage-project-permits",
    key: "project_permits",
  },
  {
    url: "manage-bills",
    key: "bills",
  },
  {
    url: "manage-files",
    key: "files",
  },
  {
    url: "manage-directory",
    key: "directories",
  },
  {
    url: "manage-change-orders",
    key: "change_orders",
  },
  {
    url: "manage-expenses",
    key: "expenses",
  },
  {
    url: "manage-daily-logs",
    key: "daily_logs",
  },
  {
    url: "manage-work-orders",
    key: "work_orders",
  },
  {
    url: "manage-todo-task",
    key: "todos",
  },
  {
    url: "manage-incidents-employee-writeup",
    key: "incidents",
  },
  {
    url: "manage-service-tickets",
    key: "service_tickets",
  },
  {
    url: "manage-sub-contracts",
    key: "sub_contracts",
  },
  {
    url: "manage-vehicle-logs",
    key: "vehicle_logs",
  },
  {
    url: "manage-opportunities",
    key: "opportunities",
  },
  {
    url: "manage-invoice",
    key: "invoice_merge",
    // both_view: true,
    // default_view: "php"
    plan_view: true,
  },
  {
    url: "manage-estimates",
    key: "estimates",
    // both_view: true,
    // default_view: "php"
    plan_view: true,
    component: ManageEstimates,
  },
  {
    url: "manage-submittals",
    key: "submittals",
  },
  {
    url: "manage-payments",
    key: "payments",
  },
  {
    url: "manage-equipment-logs",
    key: "equipment_logs",
  },
  {
    url: "manage-leads",
    key: "leads",
  },
  {
    url: "manage-mleso-items",
    key: "database_items",
  },
  {
    url: "manage-purchase-orders",
    key: "purchase_orders",
  },
  {
    url: "manage-projects",
    key: "projects",
    both_view: true,
    default_view: "remix",
  },
  {
    url: "manage-safety-meetings",
    key: "safety_meetings",
  },
  {
    url: "manage-inspections",
    key: "inspections",
  },
  {
    url: "manage-rfi-and-notices",
    key: "correspondences",
  },
  // {
  //   url: "manage-schedule",
  //   key: "gantt_chart",
  // },
  // {
  //   url: "manage-punchlists",
  //   key: "punchlists",
  // },
  // {
  //   url: "manage-bids",
  //   key: "estimate_bidding",
  // },
  // {
  //   url: "manage-timecards",
  //   key: "time_cards",
  // },
  // {
  //   url: "manage-calendar",
  //   key: "corporate_calendar",
  // },
  // {
  //   url: "manage-crew-schedule",
  //   key: "crew_schedule",
  // },
  // {
  //   url: "manage-reports",
  //   key: "reports",
  // },
  // {
  //   url: "manage-forms",
  //   key: "forms_checklist",
  // },
  // {
  //   url: "manage-notes",
  //   key: "notes",
  // },
  // {
  //   url: "manage-document-writer",
  //   key: "document_writer",
  // },
  // {
  //   url: "manage-settings",
  //   key: "app_setting",
  // },
  // {
  //   url: "trainings",
  //   key: "training_videos",
  // },
];
