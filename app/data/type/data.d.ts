declare type IDefaultConfig = typeof CFConfig;

namespace CFConfig {
  let default_lead_sales_status: number;

  let user_id: number;

  let employee_key: CustomerTabs;
  let employee_id: number;
  let employee_text: string;

  let customer_key: CustomerTabs;
  let customer_id: number;
  let customer_text: string;

  let sub_contract_module_id: number;

  let purchase_order_module_id: number;

  let expense_module_id: number;
  let defaultTaxId: string;

  let contractor_key: CustomerTabs;
  let contractor_id: number;
  let contractor_text: string;

  let vendor_key: CustomerTabs;
  let vendor_id: number;
  let vendor_text: string;

  let misc_contact_key: CustomerTabs;
  let misc_contact_id: number;
  let misc_contact_text: string;

  let lead_key: CustomerTabs;
  let lead_id: number;
  let lead_text: string;

  let material_key: ItemTabs;
  let material_teb_id: number;
  let material_module_id: number;
  let material_icon: string;

  let equipment_key: ItemTabs;
  let equipment_teb_id: number;
  let equipment_module_id: number;
  let equipment_icon: string;

  let labor_key: ItemTabs;
  let labor_teb_id: number;
  let labor_module_id: number;
  let labor_icon: string;

  let subcontractor_key: ItemTabs;
  let subcontractor_teb_id: number;
  let subcontractor_module_id: number;
  let subcontractor_icon: string;

  let other_items_key: ItemTabs;
  let other_items_teb_id: number;
  let other_items_module_id: number;
  let other_items_icon: string;

  let group_key: ItemTabs;
  let group_id: number;
  let group_icon: string;

  let item_mixed_key: ItemTabs;
  let item_mixed_tab_id: number;

  let project_module: string;
  let appointment_module: string;
  let corporate_notes_module: string;
  let change_order_module: string;
  let directory_module: string;
  let todo_module: string;
  let invoice_module: string;
  let invoice_merge_module_key: string;
  let advance_invoice_module: string;
  let marge_invoice_module: string;
  let estimate_module: string;
  let notes_module: string;
  let time_card_module: string;
  let weekly_time_sheet_module: string;
  let crew_card_module: string;
  let files_module: string;
  let punch_list_module: string;
  let rfi_module: string;
  let vehicle_log_module: string;
  let equipment_log_module: string;
  let inspection_module: string;
  let incident_employee_writeup_module: string;
  let incident_module: string;
  let employee_writeup_module: string;
  let safety_meeting_module: string;
  let safety_meeting_module_group: string;
  let safety_meeting_module_individual: string;
  let daily_log_module: string;
  let correspond_module: string;
  let expense_module: string;
  let payment_module: string;
  let report_module: string;
  let project_report_module: string;
  let project_schedule_gantt_module: string;
  let project_document_tab: string;
  let resource_module: string;
  let suggestion_module: string;
  let submit_bug_module: string;
  let refer_us_module: string;
  let support_module: string;
  let training_videos_module: string;
  let add_more_users_module: string;
  let company_module: string;
  let enable_disable_module: string;
  let user_setting_module: string;
  let app_setting_module: string;
  let employee_module: string;
  let customer_module: string;
  let contractor_module: string;
  let vendor_module: string;
  let misc_contact_module: string;
  let leads_module: string;
  let forms_checklist_module: string;
  let service_ticket_module: string;
  let crew_sheet_module: string;
  let customer_access_code_module: string;
  let employee_wage_module: string;
  let project_contract_amount_module: string;
  let project_amount_paid_module: string;
  let project_finance_tab_module: string;
  let project_budget_tab_module: string;
  let project_budget_amount_module: string;
  let project_balance_remaining_module: string;
  let corporate_calendar_module: string;
  let gantt_chart_module: string;
  let gantt_chart_update_module: string;
  let work_order_module: string;
  let equipment_module: string;
  let vehicle_module: string;
  let labour_module: string;
  let sub_contractor_module: string;
  let otherItems_module: string;
  let material_module: string;
  let tag_categories_module: string;
  let cost_codes_module: string;
  let service_module: string;
  let group_module: string;
  let role_module: string;
  let purchase_order_module: string;
  let sub_contracts_module: string;
  let delete_timecard_module: string;
  let change_order_billing_status: string;
  let change_order_request_billing_status: string;
  let default_timecard_project: string;
  let service_ticket_appointment_status: string;
  let service_ticket_job_status: string;
  let service_ticket_priority: string;
  let notes_priority: string;
  let po_billing_status_key: string;
  let po_ship_to_key: string;
  let qb_chart_of_accounts_module: string;
  let qb_account_sync_module: string;
  let qb_transaction_log_module: string;
  let qbd_log_module: string;
  let mleso_items_module: string;
  let project_permit_module: string;
  let project_permit_module_id: number;
  let bill_module: string;
  let bill_module_id: number;
  let bill_key_id: number;
  let bill_type_id: number;
  let whats_new_module: string;
  let submittal_module: string;
  let timecard_reports_module: string;
  let timecard_vefication_module: string;
  let opportunity_module: string;
  let opportunity_module_id: number;
  let sales_activity_module: string;
  let pdf_label_language_module: string;
  let company_chat_module: string;
  let default_setting_module: string;
  let custom_setting_module: string;
  let financial_setting_module: string;
  let notification_setting_module: string;
  let module_log_setting_module: string;
  let integration_setting_module: string;
  let zapierintegration_setting_module: string;
  let pdf_template_setting_module: string;
  let timecard_setting_tab_module: string;
  let estimate_bidding_module: string;
  let project_file_module: string;
  let project_budget_tab: string;
  let client_portal_module: string;
  let crew_schedule_module: string;
  let procurement_module: string;
  let cancellation_request_module: string;
  let wepay_module: string;
  let wepay_payment_module: string;
  let custom_fields_module: string;
  //    settings_module : string,
  let settings_module: string;
  let setting_company_type: string;
  let setting_primary_industry: string;
  let material_company_type_key: string;
  let equipment_company_type_key: string;
  let labour_company_type_key: string;
  let sub_contractor_company_type_key: string;
  let other_item_company_type_key: string;
  let group_item_company_type_key: string;
  let mixed_company_type_key: string;
  let document_writer_module: string;
  let lead_stage_received_status_key: string;
  let send_email_module: string;
  let user_access_role_key: string;

  let vimeo_website_url: string;

  let estimate_default_bid_status: string;

  let lead_activity_type: string;
  let lead_activity_status: string;
  let file_support_key: string;

  let company_files_project_id: string;
  let unassigned_files_project_id: string;
  let company_files_folder_id: number;
  let unassigned_files_folder_id: number;
  let file_photo_module_id: number;
  let projects_module_id: number;

  let todo_open_status_id: number;
  let todo_incomplete_status_id: number;
  let todo_completed_status_id: number;

  let pay_pending_review: string;
  let pay_verified: string;
  let pay_in_review: string;

  let company_items: string;

  let project_type_key_id: number;
  let project_type_key: string;

  let custom_estimate_status_id: number;
  let custom_estimate_status_key: string;

  let project_status_type_id: number;
  let project_status_type_key: string;

  let project_messaging_module: string;

  let projects_module_id: number;

  let day_js_date_format: string;
  let luxon_date_format: string;
  let date_format: string;

  let project_permit_key_id: number;

  let lead_stage_type_id: number;
  let lead_stage_type_key: string;

  let opportunity_stage_type_id: number;
  let opportunity_stage_type_key: string;

  let opportunity_referral_source_type_id: number;
  let opportunity_referral_source_type_key: string;
  let sensitive_information: string;
}

interface CustomerSideModuleWiseOptions {
  bills: CustomerTabs[];
  estimates: CustomerTabs[];
  send_mail: CustomerTabs[];
}
