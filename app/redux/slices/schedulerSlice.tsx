// This is the demo
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import isEmpty from "lodash/isEmpty";
import {
  fetchGanttScheduleTasks,
  fetchProjectListApi,
} from "../action/scehdulerAction";
import { Number } from "~/helpers/helper";

const initialState: IGanttScheduleInitialState = {
  schedulerTab: "calendar",
  projectId: "",
  tasks: [],
  links: [],
  projectList: [],
  settings: {
    company_id: false,
    critical_path: false,
    auto_scheduling: false,
    slack: false,
    baseline: false,
    date_added: "",
    date_modified: "",
    show_wbs_column: false,
    show_task_column: false,
    show_start_date_column: false,
    show_end_date_column: false,
    show_progress_column: false,
    show_duration_column: false,
    show_new_option_column: false,
    show_predecessors_column: false,
    zoom_to_fit: false,
    exclude_weekend_days: 0,
  },
  company_date_format: "",
  isGanttDataLoading: false,
  ganttPreviousState: { data: [], links: [] },
};

export const projectScheduleSlice = createSlice({
  name: "proDetails",
  initialState,
  reducers: {
    setProjectId: (state, { payload }) => {
      state.projectId = payload;
    },
    updateSchedulerTab: (state, { payload }) => {
      state.schedulerTab = payload;
    },
    updateGanttPreviousState: (state, { payload }) => {
      state.ganttPreviousState = payload;
    },
    batchCrudGanttOperation: (
      state,
      action: PayloadAction<{
        tasks?: IUpdateTaskEntity[];
        links?: IUpdateLinkEntity[];
      }>
    ) => {
      const { tasks = [], links = [] } = action.payload;

      let currentTasks = [...state.tasks];
      let currentLinks = [...state.links];

      // Process Tasks
      tasks.forEach((taskData) => {
        const { action: taskAction, data: task, request } = taskData || {};

        const { end_date, ...updatedData } = task || {};

        switch (taskAction) {
          case "create":
            currentTasks.push(updatedData as TGanttTaskTypes);
            break;

          case "update": {
            const index = currentTasks.findIndex((t) => t.id === task.id);
            if (index !== -1) {
              currentTasks[index] = { ...currentTasks[index], ...updatedData };
            }
            break;
          }

          case "delete":
            currentTasks = currentTasks.filter(
              (t) => t.id?.toString() !== taskData.id?.toString()
            );

            // Check and update parent if it has no more children
            const parentId = request?.parent;
            if (parentId) {
              const stillHasChildren = currentTasks.some(
                (t) => t.parent?.toString() === parentId?.toString()
              );

              if (!stillHasChildren) {
                const parentIndex = currentTasks.findIndex(
                  (t) => t.id?.toString() === parentId?.toString()
                );

                if (
                  parentIndex !== -1 &&
                  !currentTasks[parentIndex]?.id?.toString()?.startsWith("prj_")
                ) {
                  currentTasks[parentIndex] = {
                    ...currentTasks[parentIndex],
                    type: "task",
                  };
                }
              }
            }

            break;
        }
      });

      // Process Links
      links.forEach(({ action: linkAction, data: link, id, request }) => {
        switch (linkAction) {
          case "create":
            currentLinks.push(link);
            break;

          case "update": {
            const index = currentLinks.findIndex((t) => t.id === id);
            if (index !== -1) {
              currentLinks[index] = {
                ...currentLinks[index],
                ...request,
                lag: Number(request.lag),
              };
            }

            break;
          }

          case "delete":
            currentLinks = currentLinks.filter(
              (l) => l.id?.toString() !== id?.toString()
            );
            break;
        }
      });

      state.tasks = currentTasks;
      state.links = currentLinks;
    },

    updateSettings: (
      state,
      action: { payload: { newSettings: Partial<IGanttSetting> } }
    ) => {
      state.settings = { ...state.settings, ...action.payload.newSettings };
    },
    resetGantt: () => initialState,
  },

  extraReducers: (builder) => {
    builder.addCase(fetchGanttScheduleTasks.pending, (state) => {
      state.isGanttDataLoading = true;
    });
    builder.addCase(fetchGanttScheduleTasks.fulfilled, (state, { payload }) => {
      const {
        success,
        data,
        collections,
        company_date_format,
        company_schedule_weekend,
      } = payload as IGetScheduleTasksApiRes;

      const booleanSettings = isEmpty(collections.settings)
        ? initialState.settings
        : (Object.fromEntries(
            Object.entries(collections.settings).map(([key, value]) => {
              if (key === "date_added" || key === "date_modified") {
                return [key, value];
              } else if (key === "exclude_weekend_days") {
                return [key, Number(value)];
              }
              return [key, Boolean(Number(value))];
            })
          ) as IGanttSetting);

      if (success) {
        state.tasks = data;
        state.links = collections.links;
        state.settings = {
          ...booleanSettings,
          exclude_weekend_days: Number(company_schedule_weekend),
        };
        state.company_date_format = company_date_format?.replace("%b", "%M");
      }
    });
    builder.addCase(fetchGanttScheduleTasks.rejected, (state) => {
      state.isGanttDataLoading = false;
    });

    // Project list API to use in Import from Existing Project Modal
    builder.addCase(fetchProjectListApi.pending, (state) => {});
    builder.addCase(fetchProjectListApi.fulfilled, (state, { payload }) => {
      const { success, data } = payload as IGetProjectsApiResponse;
      const reversedProjects = data?.projects?.reverse();
      if (success) {
        state.projectList = reversedProjects?.filter(
          (p) =>
            Number(p.view_in_schedule) === 1 && p.prj_record_type == "project"
        );
      }
    });
    builder.addCase(fetchProjectListApi.rejected, (state) => {
      state.isGanttDataLoading = false;
    });
  },
});

export const {
  setProjectId,
  updateSchedulerTab,
  updateSettings,
  batchCrudGanttOperation,
  updateGanttPreviousState,
  resetGantt,
} = projectScheduleSlice.actions;

export default projectScheduleSlice.reducer;
