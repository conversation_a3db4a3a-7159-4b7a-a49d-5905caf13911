import { json, redirect } from "@remix-run/node";
import { routes } from "~/route-services/routes";

// Shared
import { useServerAuth } from "~/shared/hooks/useServerAuth";
import { isURLModuleDeveloped } from "~/shared/utils/common";
import { getDefaultAppData } from "~/shared/controllers/rootController.server";
import {
  DBUserLogCreate,
  DBUserLogUpdate,
  softDeleteLogByBody,
} from "~/shared/services/mongo/userLogs";
import { getRemixErrorLogModel } from "~/shared/services/mongo/remixErrorLogs";

// Other
import { getSession } from "./session.server";
import { JwtDecoded } from "~/helpers/helper.server";

const DEFAULT_RESPONSE = {
  NODE_API3_URL: process.env.NODE_API3_URL,
  PHP_API_URL: process.env.PHP_API_URL,
  PANEL_URL: process.env.PANEL_URL,
  CLIENT_PANEL_URL: process.env.CLIENT_PANEL_URL,
  PANEL_TYPE: process.env.PANEL_TYPE,
  FEATURE_REQUEST_URL: process.env.FEATURE_REQUEST_URL,
  // this variable change when we need to put over site on maintenance
  GOOGLE_DRIVE_CLIENT_SECRET: process.env.GOOGLE_DRIVE_CLIENT_SECRET,
  GOOGLE_DRIVE_OAUTH_SCOPE: process.env.GOOGLE_DRIVE_OAUTH_SCOPE,
  GOOGLE_DRIVE_API_KEY: process.env.GOOGLE_DRIVE_API_KEY,
  GOOGLE_DRIVE_APP_ID: process.env.GOOGLE_DRIVE_APP_ID,
  GOOGLE_DRIVE_CLIENT_ID: process.env.GOOGLE_DRIVE_CLIENT_ID,
  DROPBOX_APP_KEY: process.env.DROPBOX_APP_KEY,
  PDFJS_EXPRESS_KEY: process.env.PDFJS_EXPRESS_KEY,
  PADDLE_CHURN_KEY: process.env.PADDLE_CHURN_KEY,
  CHARGBEE_CHURN_KEY: process.env.CHARGBEE_CHURN_KEY,
  PADDLE_CHECKOUT_URL: process.env.PADDLE_CHECKOUT_URL,
  CDN_URL: process.env.CDN_URL,
  AWS_URL_PREFIX_REGION: process.env.AWS_URL_PREFIX_REGION,
  QUICKBOOK_URL: process.env.QUICKBOOK_URL,
  FROALA_EDITOR_KEY: process.env.FROALA_EDITOR_KEY,
  STRIPE_PUBLISHABLE_KEY: process.env.STRIPE_PUBLISHABLE_KEY,
  ALLOW_EXTERNAL_SCRIPT: process.env.ALLOW_EXTERNAL_SCRIPT || "",
  ALLOW_MIXPANEL_SCRIPT: process.env.ALLOW_MIXPANEL_SCRIPT || "",
  ALLOW_CHURNZERO_SCRIPT: process.env.ALLOW_CHURNZERO_SCRIPT || "",
  MAIN_PAGE_URL: process.env.MAIN_PAGE_URL || "",
  ENABLE_UNIT_DROPDOWN: process.env.ENABLE_UNIT_DROPDOWN || 0,
  ENABLE_ALL_CLICK: process.env.ENABLE_ALL_CLICK || 0,
};

export const mainLoader: TLoaderFunction = async ({
  request,
  ...otherParams
}) => {
  if (
    request.url.includes("authorize_token") &&
    request.url.includes("iframecall")
  ) {
    return iframeLoader({ request, ...otherParams });
  }
  return loader({ request, ...otherParams });
};

const iframeLoader: TLoaderFunction = async ({ request }) => {
  const { setAuthToken } = await useServerAuth(request);
  let headers = new Headers();
  const requestURL = new URL(request.url);
  const authorization = requestURL.searchParams.get("authorize_token")?.trim();
  if (requestURL.searchParams.get("iframecall") === "1" && authorization) {
    headers = await setAuthToken(authorization, headers);
    const { headers: reasponseHeaders, ...reasponseData } =
      await getDefaultAppData({
        token: authorization,
        request,
        headers,
      });
    headers = reasponseHeaders || new Headers();
    DBUserLogCreate(
      authorization,
      {
        timeline: ["Login from Root > Iframe."],
        ip_address: REMIX_TABLES.clientIp || "Unknown IP", // Store client IP
      },
      "#root.server.ts line 80"
    );
    if (
      (Object.entries(reasponseData) as [string, IApiCallResponse][]).some(
        ([_, value]) => {
          if (
            value.statusCode === 401 &&
            value.error &&
            value.error.need_logout?.toString() === "1"
          ) {
            return true;
          }
          return false;
        }
      )
    ) {
      return redirect(routes.SIGNOUT.url);
    }

    if (reasponseData.user) {
      if (reasponseData.user.success && reasponseData.user.data) {
        if (
          reasponseData.user.data.subscription_status === "Cancelled" ||
          reasponseData.user.data.paddle_admin_account_status?.includes("Bill")
        ) {
          return redirect(routes.SIGNIN.url);
        }
      }
    }
    let expired = 0;
    if (authorization) {
      // Decode the JWT token to extract user information.
      const { exp } = (await JwtDecoded(
        authorization,
        process.env.JWT || ""
      )) as AuthDecodedPayload;

      expired = exp;
    }
    const complies_flag = await getCompliesFlag(request);
    return json(
      {
        ...DEFAULT_RESPONSE,
        authorization_expired: expired,
        authorization,
        PAGE_IS_IFRAME: true,
        complies_flag,
        ...reasponseData,
      },
      {
        headers,
      }
    );
  }
  return redirect(routes.SIGNIN.url);
};

const loader: TLoaderFunction = async ({ request }) => {
  const { authorization, refreshAuthToken, redirectOnPage } =
    await useServerAuth(request);

  if (authorization) {
    let headers = new Headers();
    const refreshAuthTokenResponse: RefreshAuthTokenFuncResponse =
      await refreshAuthToken(authorization, headers);
    if (refreshAuthTokenResponse.is_refresh_token) {
      const { token: newToken } = refreshAuthTokenResponse;
      if (authorization !== newToken && authorization) {
        DBUserLogUpdate(
          authorization,
          {
            timeline: ["Refresh Token from Root."],
            body: newToken,
          },
          "#root.server.ts line 153"
        );
      }
      const { headers: reasponseHeaders, ...reasponseData } =
        await getDefaultAppData({
          token: newToken,
          request,
          headers,
        });
      headers = reasponseHeaders || new Headers();
      if (reasponseData.user) {
        let redirectUrl = "";
        if (reasponseData.user.success && reasponseData.user.data) {
          const {
            company_id,
            paddle_customer_id,
            role_parent_id,
            paddle_subscription_id,
          } = reasponseData.user.data;
          if (reasponseData.user.data.subscription_status === "Cancelled") {
            redirectUrl = `${
              routes.HANDLER.url
            }?type=${company_id}&role=${false}`;
          } else if (
            reasponseData.user.data.paddle_admin_account_status?.includes(
              "Bill"
            )
          ) {
            if (
              role_parent_id &&
              (role_parent_id.toString() === "1" ||
                role_parent_id.toString() === "2")
            ) {
              redirectUrl = `${routes.PAYMENT_METHOD.url}?sub_id=${paddle_subscription_id}&cust_id=${paddle_customer_id}`;
            } else {
              redirectUrl = `${
                routes.HANDLER.url
              }?type=${company_id}&role=${true}`;
            }
          }
        } else if (
          reasponseData.user.statusCode === 401 &&
          reasponseData.user.error &&
          Boolean(reasponseData.user.error.invalid_cf_token)
        ) {
          return redirect(routes.SIGNOUT.url);
        }
        if (redirectUrl) {
          return redirect(redirectUrl, {
            headers,
          });
        } else if (!isURLModuleDeveloped(request.url)) {
          return redirectOnPage({
            headers,
          });
        } else {
          let expired = 0;
          if (authorization) {
            // Decode the JWT token to extract user information.
            const { exp } = (await JwtDecoded(
              authorization,
              process.env.JWT || ""
            )) as AuthDecodedPayload;

            expired = exp;
          }
          if (
            (
              Object.entries(reasponseData) as [string, IApiCallResponse][]
            ).some(([_, value]) => {
              if (
                value.statusCode === 401 &&
                value.error &&
                value.error.need_logout?.toString() === "1"
              ) {
                return true;
              }
              return false;
            })
          ) {
            return redirect(routes.SIGNOUT.url);
          }
          const complies_flag = await getCompliesFlag(request);
          return json(
            {
              ...DEFAULT_RESPONSE,
              authorization_expired: expired,
              authorization,
              PAGE_IS_IFRAME: false,
              complies_flag,
              ...reasponseData,
            },
            {
              headers,
            }
          );
        }
      }
    } else {
      (async function () {
        if (REMIX_TABLES.connected) {
          try {
            await softDeleteLogByBody(authorization, "Sign Out from Root.");
          } catch (error) {
            const remixErrorLog = getRemixErrorLogModel();
            await remixErrorLog.create({
              body: {
                fuc: "mainLoader > step_3",
                authorization,
                message: (error as Error).message,
              },
              url: request.url || null,
            });
          }
        } else {
          console.error(
            `\n File: #root.server.ts -> Line: #263 -> connected false`
          );
        }
      })();
    }

    if (
      !refreshAuthTokenResponse.is_refresh_token &&
      refreshAuthTokenResponse.isRefreshTokenUnauthorize
    ) {
      return redirect(routes.SIGNOUT.url);
    }
  } else {
    console.error(
      `File: #root.server.ts -> Line: #271 ->  `,
      JSON.stringify({ message: "not auth", url: request.url }, null, 2)
    );
  }

  return redirect(routes.SIGNIN.url);
};

const getCompliesFlag = async (request: Request) => {
  const session = await getSession(request.headers.get("Cookie"));
  return (session.get("companies_data") as string) || "";
};
