import isEmpty from "lodash/isEmpty";
import groupBy from "lodash/groupBy";
import orderBy from "lodash/orderBy";
import isEqual from "lodash/isEqual";

// Hook
import { useDateFormatter, useTranslation } from "~/hook";

// Atoms
import { Button } from "~/shared/components/atoms/button";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { RadioGroup } from "~/shared/components/atoms/radioGroup";
import { Radio } from "~/shared/components/atoms/radioButton";

// molecules
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { InputField } from "~/shared/components/molecules/inputField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { FieldLabel } from "~/shared/components/molecules/fieldLabel";
import { Text<PERSON>reaField } from "~/shared/components/molecules/textAreaField";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { InfoTitle } from "~/shared/components/molecules/infoTitle";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";

// Organisms
import { AttachmentCard } from "~/shared/components/organisms/attachmentCard";
import {
  getDifferenceBetweenDate,
  getFormat,
  Number,
  sanitizeString,
} from "~/helpers/helper";
import { getGConfig, getGSettings, useGModules } from "~/zustand";
import { useSubmittalItemAction } from "../../../hooks/useSubmittalItemAction";
import { useDetailUpdate } from "../../../hooks/useDetailUpdate";
import { Form } from "@remix-run/react";
import { displayDateFormat } from "~/shared/utils/helper/defaultDateFormat";
import dayjs, { Dayjs } from "dayjs";
import { useAppSubSelector } from "../../../redux/store";
import { useCallback, useEffect, useMemo, useState } from "react";
import { defaultConfig } from "~/data";
import {
  getSubmittalItemAttachments,
  getSubmittalItemTimeline,
} from "../../../redux/action/detailsAction";
import SubmittalItemAuditLog from "./SubmittalItemAuditLog";
import { removeDuplicatesFile } from "~/shared/utils/helper/removeDuplicatesFile";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";

const AddEditSubmittalItem = ({
  drawerOpen,
  setDrowerOpen,
  isItemAdd,
  isItemEditable,
  itemToEdit,
  seSubmittalItemDrawerAction,
}: ISubmittalItemsProps) => {
  const { _t } = useTranslation();
  const {
    module_singular_name,
    module_id,
    module_access,
    module_key,
  }: GConfig = getGConfig();
  const { date_format, image_resolution }: GSettings = getGSettings();
  const { checkModuleAccessByKey } = useGModules();
  const dateFormat = useDateFormatter();
  const { isReadOnly, isValidNumChar } = useDetailUpdate();

  const { submittal_item_status } = useAppSubSelector(
    (state) => state.getStatusList
  );
  const [isConfirmDrawer, setIsConfirmDrawer] = useState<boolean>(false);
  const [selectedFilesData, setSelectedFilesData] = useState<IFile[]>([]);

  const {
    details: { submittal_data },
    submittal_items,
  } = useAppSubSelector((state) => state.submittalDetails);

  const [itemAuditLog, setItemAuditLog] = useState<
    Record<string, IAuditLogsData[]>
  >({});

  const onCloseDrawer = () => {
    resetForm();
    setDrowerOpen(false);
  };

  const {
    formik,
    formik: {
      values,
      touched,
      errors,
      handleBlur,
      setFieldValue,
      handleSubmit,
      resetForm,
      isSubmitting,
      setSubmitting,
    },
    record_id,
    setDefaultItemAttachments,
    onSubmitItem,
  } = useSubmittalItemAction({ isItemAdd, itemToEdit, onCloseDrawer });

  const getItem = useCallback((item: Partial<TSubmittalItem>) => {
    return {
      item_number: item?.item_number || null,
      item_name: item?.item_name || null,
      submittal_item_plan_sheet_number:
        item?.submittal_item_plan_sheet_number || null,
      manufacturer: item?.manufacturer || null,
      submittal_item_specifications:
        item?.submittal_item_specifications || null,
      description: item?.description || null,
      status: Number(item?.status) || null,
      item_submitted_date: item?.item_submitted_date || null,
      response_date: item?.response_date || null,
      response_note: item?.response_note || null,
    };
  }, []);

  const isItemChanged = useMemo(() => {
    const initItem = getItem(itemToEdit);
    const latestItem = getItem(values);

    const isItemSame = isEqual(initItem, latestItem);

    if (!isItemSame) {
      return true;
    }
    return false;
  }, [itemToEdit, values]);

  const getItemAttachment = useCallback(async () => {
    if (!isItemAdd) {
      const submittalItemAttachmentRes = (await getSubmittalItemAttachments({
        module_key,
        record_id: Number(record_id),
        item_id: itemToEdit.item_id?.toString() ?? "",
      })) as IAddSubmittalFilePhotoRes;

      if (submittalItemAttachmentRes.success) {
        setDefaultItemAttachments(submittalItemAttachmentRes.data.aws_files);
        setFieldValue("aws_files", submittalItemAttachmentRes.data.aws_files);
      }
    }
  }, [isItemAdd, record_id, itemToEdit]);

  useEffect(() => {
    if (!selectedFilesData?.length) return;

    const updatedAwsFiles = formik.values.aws_files.map((existingFile) => {
      const updatedFile = selectedFilesData.find(
        (newFile) => newFile.file_name === existingFile.file_name
      );
      return updatedFile ?? existingFile;
    });

    formik.setFieldValue("aws_files", updatedAwsFiles);
  }, [selectedFilesData]);

  const getItemTimeline = useCallback(async () => {
    if (!isItemAdd) {
      const itemAuditRes = (await getSubmittalItemTimeline({
        moduleId: module_id || 0,
        recordId: Number(record_id),
      })) as IAuditLogApiFormApiRes;

      if (itemAuditRes?.success) {
        let timelineObj = {};
        if (Array.isArray(itemAuditRes?.data?.logs)) {
          const filteredLogs = itemAuditRes?.data?.logs?.filter(
            (l) => l.item_id?.toString() === itemToEdit.item_id?.toString()
          );
          timelineObj = groupBy(
            orderBy(filteredLogs, "date_added", "desc"),
            "insert_date"
          );
        }
        setItemAuditLog(timelineObj);
      } else {
        notification.error({
          description: itemAuditRes?.message,
        });
      }
    }
  }, [isItemAdd, record_id, itemToEdit]);

  useEffect(() => {
    getItemAttachment();
    getItemTimeline();
  }, [drawerOpen, isItemAdd, itemToEdit]);

  const isOnlineApproval = useMemo(() => {
    return submittal_data.online_offline_approval?.toString() === "1";
  }, [submittal_data]);

  const isOfflineApproval = useMemo(() => {
    return submittal_data.online_offline_approval?.toString() === "2";
  }, [submittal_data]);

  const isReadOnlyField = useMemo(() => {
    const itemHasStatus = !!Number(itemToEdit.status);
    return isReadOnly || (!isItemEditable && itemHasStatus);
  }, [isReadOnly, isItemEditable, itemToEdit]);

  const submittalApprovalOptions = useMemo(
    () =>
      submittal_item_status && submittal_item_status.length
        ? submittal_item_status.map((item) => {
            return {
              label: `${HTMLEntities.decode(sanitizeString(item.name))}`,
              value: String(item.type_id),
            };
          })
        : [],
    [submittal_item_status]
  );

  const getDate = (date: string | undefined) => {
    if (!isEmpty(date)) {
      if (date === "00/00/0000") {
        return undefined;
      }
      return dateFormat({
        date,
        dateFormat: getFormat(date_format),
        format: "yyyy-MM-dd",
      });
    }
    return undefined;
  };

  const onSentDateChange = (date: Dayjs | DateValue | null) => {
    if (!Array.isArray(date)) {
      const currentRecieveDate = getDate(
        values?.response_date?.toString()
      ) as string;

      if (!!date) {
        const newSentDate = date.format("YYYY-MM-DD");
        if (currentRecieveDate) {
          const difference = getDifferenceBetweenDate(
            currentRecieveDate,
            newSentDate,
            "days"
          );

          if (difference < 0) {
            notification.error({
              description:
                "Sent date should be less than or equal to Receive date",
            });
            return;
          }
        }
        setFieldValue("item_submitted_date", date.format(date_format));
      } else {
        setFieldValue("item_submitted_date", "");
      }
    }
  };

  const onReceiveDateChange = (date: Dayjs | DateValue | null) => {
    if (!Array.isArray(date)) {
      const currentSentDate = getDate(
        values?.item_submitted_date?.toString()
      ) as string;

      if (!!date) {
        const newSentDate = date.format("YYYY-MM-DD");
        if (currentSentDate) {
          const difference = getDifferenceBetweenDate(
            newSentDate,
            currentSentDate,
            "days"
          );

          if (difference < 0) {
            notification.error({
              description:
                "Received date should be greater than or equal to sent date",
            });
            return;
          }
        }
        setFieldValue("response_date", date.format(date_format));
      } else {
        setFieldValue("response_date", "");
      }
    }
  };

  const handleChangeApprovalStatus = (value: string | string[]) => {
    setFieldValue("status", value);

    if (value?.toString() === "339") {
      // it means, statut is submitted
      const todayFormattedDate = dayjs().format(date_format);

      setFieldValue("item_submitted_date", todayFormattedDate);

      if (values?.response_date) {
        const currentReceiveDate = getDate(
          values?.response_date?.toString()
        ) as string;
        const currentSentDate = getDate(
          values?.item_submitted_date?.toString()
        ) as string;

        const sentDateToCheck = dayjs(currentSentDate).startOf("day");
        const recieveDateToCheck = dayjs(currentReceiveDate).startOf("day");

        const isPastDate =
          recieveDateToCheck?.isSame(sentDateToCheck) ||
          recieveDateToCheck?.isBefore(sentDateToCheck);

        if (isPastDate) {
          setFieldValue("response_date", todayFormattedDate);
        }
      }
    } else {
      setFieldValue("item_submitted_date", "");
      setFieldValue("response_date", "");
    }
  };

  const currenItemIndex = useMemo(() => {
    return submittal_items.findIndex(
      (i) => i.item_id?.toString() === itemToEdit?.item_id?.toString()
    );
  }, [submittal_items, itemToEdit]);

  const validateFormBeforeNavigation = async () => {
    const errors = await formik.validateForm();
    return Object.keys(errors).length === 0;
  };

  const onClickNext = async () => {
    const isFormValid = await validateFormBeforeNavigation();
    if (currenItemIndex !== submittal_items.length - 1 && isFormValid) {
      if (isItemChanged) {
        setSubmitting(true);
        await onSubmitItem(values, false);
        setSubmitting(false);
      }

      const nextItemToEdit = submittal_items[currenItemIndex + 1];

      seSubmittalItemDrawerAction({
        open: true,
        action: "edit",
        itemToEdit: nextItemToEdit,
      });
    }
  };

  const onClickPrevious = async () => {
    const isFormValid = await validateFormBeforeNavigation();
    if (currenItemIndex !== 0 && isFormValid) {
      const prevItemToEdit = submittal_items[currenItemIndex - 1];

      if (isItemChanged) {
        setSubmitting(true);
        await onSubmitItem(values, false);
        setSubmitting(false);
      }

      seSubmittalItemDrawerAction({
        open: true,
        action: "edit",
        itemToEdit: prevItemToEdit,
      });
    }
  };
  const handleEnterKeyPress = (
    event: React.KeyboardEvent<HTMLInputElement>
  ) => {
    event.preventDefault();
  };
  const isSRRFieldDisabled = useMemo(() => {
    return !isItemAdd && isOnlineApproval && !!itemToEdit.status;
  }, [isItemAdd, isOnlineApproval, values.status]);

  return (
    <Drawer
      open={drawerOpen}
      rootClassName="drawer-open"
      width={722}
      maskClosable={false}
      push={false}
      classNames={{
        body: "!p-0 !overflow-hidden",
      }}
      title={
        <div className="flex items-center">
          <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
            <FontAwesomeIcon
              className="w-4 h-4"
              icon="fa-regular fa-clipboard-list-check"
            />
          </div>
          <div className="flex justify-between w-full">
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {_t(
                (!isItemAdd ? "" : "Add ") +
                  HTMLEntities.decode(sanitizeString(module_singular_name)) +
                  " Item"
              )}
            </Header>
            {!isItemAdd && (
              <div className="flex items-center sm:gap-2 gap-0 pr-2">
                <ButtonWithTooltip
                  tooltipTitle={_t("Previous")}
                  tooltipPlacement="top"
                  icon="fa-regular fa-chevron-left"
                  className="item-pre-next-button disabled:bg-transparent"
                  onClick={onClickPrevious}
                  disabled={currenItemIndex === 0 || isSubmitting}
                />
                <ButtonWithTooltip
                  tooltipTitle={_t("Next")}
                  tooltipPlacement="top"
                  icon="fa-regular fa-chevron-right"
                  className="item-pre-next-button disabled:bg-transparent"
                  onClick={onClickNext}
                  disabled={
                    currenItemIndex === submittal_items.length - 1 ||
                    isSubmitting
                  }
                />
              </div>
            )}
          </div>
        </div>
      }
      closeIcon={<CloseButton onClick={onCloseDrawer} />}
    >
      <Form method="post" className="py-4" onSubmit={handleSubmit} noValidate>
        <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
          <div className="flex flex-col gap-4">
            <SidebarCardBorder addGap={true}>
              <div className="grid md:grid-cols-2 gap-5">
                <div className="w-full">
                  <InputField
                    label={_t("Item") + " #"}
                    labelPlacement="top"
                    isRequired={true}
                    name="item_number"
                    id="item_number"
                    value={_t(
                      HTMLEntities.decode(
                        sanitizeString(values.item_number?.toString() || "")
                      )
                    )}
                    errorMessage={
                      touched?.item_number &&
                      !String(values.item_number || "").trim()
                        ? errors.item_number
                        : ""
                    }
                    disabled={isReadOnlyField}
                    onChange={(e) => {
                      setFieldValue("item_number", e.target.value);
                    }}
                    onBlur={handleBlur}
                    autoComplete="off"
                    onPressEnter={handleEnterKeyPress}
                  />
                </div>
                <div className="w-full">
                  <InputField
                    label={_t("Name")}
                    labelPlacement="top"
                    isRequired={true}
                    name="item_name"
                    id="item_name"
                    value={_t(
                      HTMLEntities.decode(
                        sanitizeString(values.item_name?.toString() || "")
                      )
                    )}
                    errorMessage={
                      touched?.item_name &&
                      !String(values.item_name || "").trim()
                        ? errors.item_name
                        : ""
                    }
                    disabled={isReadOnlyField}
                    onChange={(e) => {
                      setFieldValue("item_name", e.target.value);
                    }}
                    onBlur={handleBlur}
                    autoComplete="off"
                    onPressEnter={handleEnterKeyPress}
                  />
                </div>
              </div>
              <div className="grid md:grid-cols-2 gap-5">
                <div className="w-full">
                  <InputField
                    label={_t("Plan Sheet Numbers")}
                    labelPlacement="top"
                    name="submittal_item_plan_sheet_number"
                    id="submittal_item_plan_sheet_number"
                    maxLength={30}
                    value={_t(
                      HTMLEntities.decode(
                        sanitizeString(
                          values.submittal_item_plan_sheet_number?.toString() ||
                            ""
                        )
                      )
                    )}
                    errorMessage={
                      touched?.submittal_item_plan_sheet_number
                        ? errors.submittal_item_plan_sheet_number
                        : ""
                    }
                    disabled={isReadOnlyField}
                    onChange={(e) => {
                      const newVal = e.target.value;
                      const validVal = isValidNumChar(newVal);
                      if (!validVal) {
                        return;
                      }
                      setFieldValue(
                        "submittal_item_plan_sheet_number",
                        newVal.trimStart()
                      );
                    }}
                    onPressEnter={handleEnterKeyPress}
                    onKeyDown={(e) => {
                      const target = e.target as HTMLInputElement;
                      const value = target.value.trim();
                      if (e.key === "Enter" && value == "") {
                        e.preventDefault();
                      }
                    }}
                    onBlur={handleBlur}
                    autoComplete="off"
                  />
                </div>
                <div className="w-full">
                  <InputField
                    label={_t("Manufacturer")}
                    labelPlacement="top"
                    name="manufacturer"
                    id="manufacturer"
                    value={_t(
                      HTMLEntities.decode(
                        sanitizeString(values.manufacturer?.toString() || "")
                      )
                    )}
                    disabled={isReadOnlyField}
                    onChange={(e) => {
                      setFieldValue("manufacturer", e.target.value);
                    }}
                    onKeyDown={(e) => {
                      const target = e.target as HTMLInputElement;
                      const value = target.value.trim();
                      if (e.key === "Enter" && value == "") {
                        e.preventDefault();
                      }
                    }}
                    onPressEnter={handleEnterKeyPress}
                    onBlur={handleBlur}
                    autoComplete="off"
                  />
                </div>
              </div>
              <div className="w-full">
                <TextAreaField
                  label={_t("Spec Section")}
                  labelPlacement="top"
                  name="submittal_item_specifications"
                  id="submittal_item_specifications"
                  value={_t(
                    HTMLEntities.decode(
                      sanitizeString(
                        values.submittal_item_specifications?.toString() || ""
                      )
                    )
                  )}
                  disabled={isReadOnlyField}
                  onChange={(e) => {
                    setFieldValue(
                      "submittal_item_specifications",
                      e.target.value
                    );
                  }}
                  onBlur={handleBlur}
                  autoComplete="off"
                />
              </div>
              <div className="w-full">
                <TextAreaField
                  label={_t("Description")}
                  labelPlacement="top"
                  name="description"
                  id="description"
                  value={_t(
                    HTMLEntities.decode(
                      sanitizeString(values.description?.toString() || "")
                    )
                  )}
                  disabled={isReadOnlyField}
                  onChange={(e) => {
                    setFieldValue("description", e.target.value);
                  }}
                  onBlur={handleBlur}
                  autoComplete="off"
                />
              </div>
              <div className="grid gap-3.5">
                <FieldLabel children={_t("Files")} />
                <AttachmentCard
                  files={values?.aws_files || []}
                  projectid={submittal_data.project_id ?? 0}
                  isReadOnly={isReadOnlyField}
                  isShowDeleteMenu={true}
                  onDeleteFile={(data: {
                    file_path?: string;
                    image_id?: number;
                    response?: any;
                  }) => {
                    const keyToCheck = data?.file_path
                      ? "file_path"
                      : "image_id";

                    if (data?.file_path || data?.image_id) {
                      const newFileData = (values?.aws_files || []).filter(
                        (item) => item?.[keyToCheck] !== data?.[keyToCheck]
                      );

                      setFieldValue("aws_files", newFileData);
                    }
                  }}
                  onAddAttachment={(data) => {
                    console.log("datadata", data);

                    const newFileData = [...(values.aws_files ?? []), ...data];
                    const filterFile = removeDuplicatesFile(newFileData);
                    const updatedAwsFilesUrl =
                      filterFile?.length &&
                      filterFile?.map((file) => ({
                        ...file,
                        isheic: file.file_ext.toLowerCase() === "heic" ? 1 : 0, // Add 'isheic' key
                      }));
                    setFieldValue("aws_files", updatedAwsFilesUrl);
                  }}
                  // will work on it
                  // setUpdatedData={(data) => {
                  //   console.log("data", data);
                  // }}
                  isAddAllow={!isReadOnlyField}
                  editView={!isItemAdd}
                  validationParams={{
                    date_format,
                    file_support_module_access: checkModuleAccessByKey(
                      defaultConfig.file_support_key
                    ),
                    image_resolution,
                    module_key,
                    module_id,
                    module_access,
                  }}
                  isShowAddIcon={true}
                  setIsFileDeleted={() => {}}
                  setDeletedFile={undefined}
                  setSelectedFilesData={setSelectedFilesData}
                />
              </div>
            </SidebarCardBorder>
            <SidebarCardBorder
              addGap={true}
              cardTitle={_t("External Approver Response")}
            >
              {!isItemAdd &&
                Number(submittal_data.online_offline_approval) !== 0 &&
                !!values?.status && (
                  <div className="grid gap-2.5">
                    <div className="flex gap-5">
                      <FieldLabel
                        children={_t("Approved Via")}
                        labelClass="max-w-24"
                      />
                      <div className="w-full">
                        <RadioGroup
                          name="txn_type"
                          value={submittal_data.online_offline_approval?.toString()}
                          disabled
                        >
                          <Radio
                            id="online_group"
                            name="online_group"
                            value="1"
                          >
                            {_t("Online")}
                          </Radio>
                          <Radio
                            id="offline_group"
                            name="offline_group"
                            value="2"
                          >
                            {_t("Offline")}
                          </Radio>
                        </RadioGroup>
                      </div>
                    </div>
                    <div className="flex gap-3.5 w-full overflow-hidden">
                      <div className="flex gap-3.5 submittal-approved-via">
                        {submittal_data.ip_address && (
                          <InfoTitle
                            icon="fa-regular fa-location-dot"
                            title={submittal_data.ip_address?.toString()}
                          />
                        )}
                        {submittal_data.date_approval && (
                          <InfoTitle
                            icon="fa-regular fa-calendar"
                            title={submittal_data.date_approval?.toString()}
                          />
                        )}
                        {submittal_data.time_approval && (
                          <InfoTitle
                            icon="fa-regular fa-clock"
                            title={submittal_data.time_approval?.toString()}
                          />
                        )}
                      </div>
                      {submittal_data.online_offline_approval === 1 &&
                        submittal_data?.external_approved_by_name && (
                          <div className="w-full overflow-hidden">
                            <InfoTitle
                              icon="fa-regular fa-user"
                              textClassName="truncate"
                              tooltipShow={true}
                              title={HTMLEntities.decode(
                                sanitizeString(
                                  submittal_data.external_approved_by_name
                                )
                              )}
                            />
                          </div>
                        )}
                      {/* {submittal_data.online_offline_approval === 2 &&
                        submittal_data?.customer_name && (
                          <div className="w-full overflow-hidden">
                            <InfoTitle
                              icon="fa-regular fa-user"
                              textClassName="truncate"
                              tooltipShow={true}
                              title={HTMLEntities.decode(
                                sanitizeString(submittal_data?.customer_name)
                              )}
                            />
                          </div>
                        )} */}
                    </div>
                  </div>
                )}
              <div>
                <SelectField
                  label={_t("Status")}
                  labelPlacement="top"
                  // isRequired={true}
                  name="status"
                  onClear={() => {
                    setFieldValue("status", "");
                  }}
                  allowClear={!!values?.status}
                  disabled={isReadOnlyField || isSRRFieldDisabled}
                  value={values?.status ? values.status.toString() : ""}
                  onChange={handleChangeApprovalStatus}
                  onBlur={handleBlur}
                  options={submittalApprovalOptions}
                />
              </div>
              <div className="grid md:grid-cols-2 md:gap-4 gap-7">
                <div className="w-full">
                  <DatePickerField
                    label={_t("Date Sent")}
                    labelPlacement="top"
                    placeholder=""
                    id="item_submitted_date"
                    value={
                      displayDateFormat(
                        values?.item_submitted_date?.toString().trim(),
                        date_format
                      ) ?? null
                    }
                    onChange={onSentDateChange}
                    format={date_format}
                    disabled={isReadOnlyField}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault();
                      }
                    }}
                  />
                </div>
                <div className="w-full">
                  <DatePickerField
                    label={_t("Date Received")}
                    labelPlacement="top"
                    placeholder=""
                    id="response_date"
                    value={
                      displayDateFormat(
                        values?.response_date?.toString().trim(),
                        date_format
                      ) ?? null
                    }
                    onChange={onReceiveDateChange}
                    format={date_format}
                    disabled={isReadOnlyField || isSRRFieldDisabled}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault();
                      }
                    }}
                  />
                </div>
              </div>
              <div className="w-full">
                <TextAreaField
                  label={_t("Response Note")}
                  labelPlacement="top"
                  name="response_note"
                  id="response_note"
                  value={_t(
                    HTMLEntities.decode(
                      sanitizeString(values.response_note?.toString() || "")
                    )
                  )}
                  disabled={isReadOnlyField || isSRRFieldDisabled}
                  onChange={(e) => {
                    setFieldValue("response_note", e.target.value);
                  }}
                  onBlur={handleBlur}
                  autoComplete="off"
                />
              </div>
            </SidebarCardBorder>

            {!isItemAdd && (
              <CollapseSingleTable
                title={_t("Timeline")}
                className="!bg-[#FBFBFB]"
                onChange={() => {}}
                children={<SubmittalItemAuditLog itemAuditLog={itemAuditLog} />}
              />
            )}
          </div>
        </div>
        <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
          <PrimaryButton
            htmlType="button"
            buttonText={_t("Confirm")}
            isLoading={isSubmitting}
            onClick={() => {
              if (values.status?.toString() === "339") {
                setIsConfirmDrawer(true);
              } else {
                handleSubmit();
              }
            }}
            disabled={isReadOnly || isSubmitting || isReadOnlyField}
          />
        </div>
      </Form>

      {isConfirmDrawer && (
        <ConfirmModal
          isOpen={isConfirmDrawer}
          modalIcon="fa-regular fa-file-check"
          modaltitle={_t("Confirmation")}
          description={_t(
            `Submitting this item will also mark all other related items and the submittal as Submitted. Are you sure you want to submit this item?`
          )}
          isLoading={isSubmitting}
          onCloseModal={() => setIsConfirmDrawer(false)}
          onAccept={() => {
            handleSubmit();
          }}
          onDecline={() => setIsConfirmDrawer(false)}
        />
      )}
    </Drawer>
  );
};

export default AddEditSubmittalItem;
