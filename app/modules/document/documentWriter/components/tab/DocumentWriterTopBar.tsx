import { useMemo } from "react";
// Hooks
import { useTranslation } from "~/hook";
// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { IconButton } from "~/shared/components/molecules/iconButton";
import { InputField } from "~/shared/components/molecules/inputField";
// Other
import { getGModuleDashboard, setCommonSidebarCollapse } from "~/zustand"; // In future this code move in redux, developer change this code
import { DOC_ADD_STATUS } from "../../utils/constants";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { getStatusForField } from "~/shared/utils/helper/common";
import { sanitizeString } from "~/helpers/helper";
import { useDocDetailUpdate } from "../../hooks/useDocDetailUpdate";
import { Dropdown } from "~/shared/components/atoms/dropDown";
import { FieldStatus } from "~/shared/components/molecules/fieldStatus";
import { Typography } from "~/shared/components/atoms/typography";
import { MenuProps } from "antd";
import { TopBarSkeleton } from "~/shared/components/molecules/topBarSkeleton";
import { useDocAppSelector } from "../../redux/store";
import { useCommonData } from "../../hooks/useCommonData";
import { UsedDocumentDropdownOptios } from "../dashboard/UsedDocumentDropdownOptios";
import { useSearchParams } from "@remix-run/react";

type TDocumentWriterTopBar = {
  sidebarCollapse: boolean;
  onReloadDetails: () => void;
};

const DocumentWriterTopBar = ({
  sidebarCollapse,
  onReloadDetails,
}: TDocumentWriterTopBar) => {
  const { _t } = useTranslation();
  const [searchParams] = useSearchParams();
  const mode = searchParams.get("mode");

  const { isDetailLoading } = useDocAppSelector(
    (state) => state.docWriterDetail
  );

  const { isPreviewMode } = useCommonData();

  const gModuleDashboard: Partial<GModuleDashboard> = getGModuleDashboard();
  const StatusbarOption: Array<ModuleStatus> | undefined =
    gModuleDashboard?.module_setting?.module_status;

  const {
    details,
    isReadOnly,
    inputVals,
    loadingStatus,
    onChangeInputField,
    onFocusUpdateFieldStatus,
    onMouseEnterUpdateFieldStatus,
    onMouseLeaveUpdateFieldStatus,
    updateInputFieldOnBlur,
    handleUpdateField,
  } = useDocDetailUpdate();

  const docStatus = useMemo(() => {
    const statusList = DOC_ADD_STATUS?.map((item) => ({
      label: HTMLEntities.decode(sanitizeString(item.label)),
      key: item?.value?.toString() ?? "",
      icon: (
        <FontAwesomeIcon
          icon="fa-solid fa-square"
          className="h-3.5 w-3.5"
          style={{
            color: item?.status_color,
          }}
        />
      ),
    }));

    const getSelectStatus = DOC_ADD_STATUS?.find(
      (item) => item.value === details.status?.toString()
    );

    const selectStatus = (
      <Tooltip
        title={
          HTMLEntities.decode(sanitizeString(getSelectStatus?.label)) || ""
        }
      >
        <div
          className={`py-0.5 rounded flex items-center justify-center w-full status-dropdown-block group/status-dropdown px-2.5 ${
            isReadOnly ? "" : "hover:px-1 cursor-pointer"
          }`}
          style={{
            backgroundColor: getSelectStatus?.status_color + "1d",
          }}
        >
          <Typography
            style={{
              color: getSelectStatus?.status_color,
            }}
            className="text-xs whitespace-nowrap truncate"
          >
            {HTMLEntities.decode(sanitizeString(getSelectStatus?.label)) || ""}
          </Typography>
          {!isReadOnly && !isPreviewMode && (
            <FontAwesomeIcon
              className="w-2.5 h-2.5 pl-0.5 group-hover/status-dropdown:flex hidden ease-in-out duration-300"
              style={{
                color: getSelectStatus?.status_color,
              }}
              icon="fa-regular fa-chevron-down"
            />
          )}
        </div>
      </Tooltip>
    );
    return { statusList, selectStatus, getSelectStatus };
  }, [StatusbarOption, details?.status]);

  const handleStatus: MenuProps["onClick"] = (e) => {
    if (e.key?.toString() !== details?.status?.toString()) {
      const selectedOption = docStatus?.statusList?.find(
        (option) => option.key === e.key?.toString()
      );

      if (selectedOption) {
        handleUpdateField({
          data_to_send_in_api: {
            is_active: e.key?.toString(),
          },
          data_to_update_in_store: {
            status: e.key?.toString(),
          },
        });
      } else {
        const description = "Selected option not found in statusList";
        notification.error({
          description,
        });
      }
    }
  };

  return (
    <div
      className={`flex items-center bg-white dark:bg-dark-800 py-[5px] px-2.5 m-[15px] shadow-[0_4px_24px_0] shadow-[#22292f1a] rounded-md`}
    >
      {isDetailLoading ? (
        <TopBarSkeleton statusList={false} num={5} />
      ) : (
        <div className="w-full flex sm:flex-row flex-col items-center justify-between ">
          <div className="flex items-center  md:mt-0 mt-2.5 sm:order-1 order-2 sm:mr-0 !mr-auto xl:w-[calc(40%-175px)] w-full">
            <div className="w-11 h-11 flex items-center justify-center bg-[#00B9A3] rounded-full relative before:absolute before:w-[39px] before:h-[39px] before:top-1/2 before:left-1/2 before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full before:border-2 before:border-white">
              <FontAwesomeIcon
                className="w-[18px] h-[18px] text-white"
                icon="fa-regular fa-memo-circle-check"
              />
            </div>
            <div className="pl-2.5 flex flex-col gap-0.5 xl:w-[calc(100%-44px)]">
              <div>
                <Tooltip
                  title={_t(inputVals?.document_name || "")}
                  placement="topLeft"
                >
                  <InputField
                    placeholder={_t("Document Name")}
                    labelPlacement="left"
                    name="document_name"
                    formInputClassName="ellipsis-input-field max-w-[580px]"
                    className="h-6 py-0 !text-base !font-medium"
                    readOnlyClassName="text-base h-6 font-medium whitespace-nowrap truncate block"
                    labelClass="hidden"
                    inputStatusClassName="!w-[15px] !h-[15px]"
                    iconClassName="!w-3 !h-3"
                    maxLength={200}
                    disabled={isReadOnly || isPreviewMode}
                    readOnly={isReadOnly || isPreviewMode}
                    editInline={true}
                    iconView={true}
                    value={HTMLEntities.decode(
                      sanitizeString(inputVals?.document_name || "")
                    )}
                    fixStatus={getStatusForField(
                      loadingStatus,
                      "document_name"
                    )}
                    onChange={(e) => {
                      const newVal = e.target.value?.trimStart();
                      onChangeInputField({
                        field: "document_name",
                        value: newVal,
                      });
                    }}
                    onFocus={() => {
                      onFocusUpdateFieldStatus({ field: "document_name" });
                    }}
                    onMouseEnter={() => {
                      onMouseEnterUpdateFieldStatus({ field: "document_name" });
                    }}
                    onMouseLeaveDiv={() => {
                      onMouseLeaveUpdateFieldStatus({ field: "document_name" });
                    }}
                    onBlur={(e) => {
                      const value = HTMLEntities.encode(
                        e?.target?.value.replace(/\s+/g, " ").trim()
                      );
                      updateInputFieldOnBlur({
                        field: "document_name",
                        value,
                        message: "Document Name is required.",
                        required: true,
                      });
                    }}
                  />
                </Tooltip>
              </div>
              <div className="flex gap-2 items-center">
                <Dropdown
                  menu={{
                    items: docStatus.statusList,
                    selectable: true,
                    selectedKeys: details.status
                      ? [details.status?.toString()]
                      : [],
                    onClick: handleStatus,
                  }}
                  disabled={isReadOnly || isPreviewMode}
                  trigger={["click"]}
                  overlayClassName="dropdown-color-option-block !min-w-40"
                >
                  {docStatus.selectStatus}
                </Dropdown>
                {["loading", "success", "error"].includes(
                  getStatusForField(loadingStatus, "is_active")
                ) && (
                  <FieldStatus
                    className="flex items-center"
                    iconProps={{
                      className: "!w-[15px] !h-[15px]",
                    }}
                    status={getStatusForField(loadingStatus, "is_active")}
                  />
                )}
              </div>
            </div>
          </div>

          <div className="flex justify-end  gap-2.5 sm:order-3 order-1 sm:ml-0 !ml-auto w-[calc(40%-175px)]">
            <IconButton
              htmlType="button"
              variant="default"
              className="md:!hidden group/module-menu relative w-[34px] min-w-[34px] h-[34px] !border-0 !shadow-[0px_2px_3px] !shadow-primary-300 hover:!bg-deep-orange-500/5"
              iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
              icon="fa-regular fa-bars"
              onClick={() => setCommonSidebarCollapse(!sidebarCollapse)}
            />

            <ul className="flex items-center justify-end gap-2.5">
              <li>
                <ButtonWithTooltip
                  tooltipTitle={_t("Refresh")}
                  tooltipPlacement="top"
                  icon="fa-regular fa-arrow-rotate-right"
                  iconClassName={`!text-primary-900
                    ${
                      isDetailLoading
                        ? "group-hover/buttonHover:text-primary-900 fa-spin"
                        : "group-hover/buttonHover:!text-deep-orange-500"
                    }`}
                  className={`!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !shadow-primary-200 ${
                    isDetailLoading
                      ? "hover:bg-transparent"
                      : "hover:!bg-deep-orange-500/5"
                  }`}
                  onClick={onReloadDetails}
                />
              </li>
              {mode && mode === "view" && !isReadOnly && !isDetailLoading && (
                <li>
                  <UsedDocumentDropdownOptios
                    documentData={details}
                    className="!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !rounded !shadow-primary-200 hover:!bg-deep-orange-500/5"
                    iconClassName="text-primary-900 group-hover/buttonHover:text-deep-orange-500"
                    isDetailView={true}
                    footerText={true}
                  />
                </li>
              )}
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentWriterTopBar;
