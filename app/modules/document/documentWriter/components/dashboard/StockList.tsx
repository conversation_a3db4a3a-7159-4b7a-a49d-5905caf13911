import isEqual from "lodash/isEqual";

import { useCallback, useEffect, useRef, useState } from "react";
// hooks
import { useTranslation } from "~/hook";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { StockView } from "../sidebar/stockView";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import useTableGridData from "~/shared/hooks/useTableGridData";
import { useModuleDataSearch } from "~/zustand/global-module-filter/hook";
import { GridReadyEvent, SortChangedEvent } from "ag-grid-community";
import { escapeHtmlEntities, Number, sanitizeString } from "~/helpers/helper";
import { getMasterDocumentListApi } from "../../redux/action/dashAction";
import { DynamicTable } from "~/shared/components/molecules/dynamicTable";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { useBoolean } from "~/modules/projectManagement/pages/project/hook/use-boolean";
import { AddDocumentWriter } from "../sidebar";

let timeout: NodeJS.Timeout;

const StockList = () => {
  const { _t } = useTranslation();
  const [drowerOpen, setDrowerOpen] = useState<boolean>(false);
  const [stockDetail, setStockDetail] = useState<MasterDataResponse>();
  const [copyDocumentDrawerOpen, setCopyDocumentDrawerOpen] =
    useState<boolean>(false);

  const stockLoading = useBoolean();

  const { datasource, gridRowParams } = useTableGridData();

  const currentMenuModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const { module_access } = currentMenuModule || {};

  const { search }: IModuleDataSearchParams = useModuleDataSearch(
    CFConfig.document_writer_module
  );

  const onGridReady = (gridParams: GridReadyEvent) => {
    gridParams?.api?.setServerSideDatasource(datasource);
  };

  const onSortChanged = async (params: SortChangedEvent) => {
    params.api.setServerSideDatasource({ getRows: () => {} });
    params.api.setServerSideDatasource(datasource);
  };

  const previousValues = useRef({
    search,
  });

  useEffect(() => {
    if (gridRowParams?.changeGridParams) {
      if (timeout) {
        clearTimeout(timeout);
      }
      timeout = setTimeout(() => {
        fetchStockMasterDocuments();
      }, 500);
    }
  }, [gridRowParams?.changeGridParams]);

  useEffect(() => {
    const currentValues = {
      search,
    };

    if (!isEqual(previousValues.current, currentValues)) {
      previousValues.current = currentValues;
      refreshAgGrid();
    }
  }, [search]);

  const refreshAgGrid = () => {
    const gridParams = gridRowParams?.gridParams;
    if (gridParams) {
      gridParams.api.setServerSideDatasource({ getRows: () => {} });
      gridParams.api.setServerSideDatasource(datasource);
    }
  };

  const fetchStockMasterDocuments = useCallback(async () => {
    let gridData: { rowCount?: number; rowData: MasterDocument[] } = {
      rowData: [],
    };
    const { changeGridParams, gridParams } = gridRowParams ?? {};
    const limit = changeGridParams?.length ?? 0;
    const { start, order_by_name, order_by_dir } = changeGridParams || {};

    let dataParams: IMasterDocumentListApiParams = {
      limit,
      ignore_filter: 1,
      order_by_col_name: order_by_name,
      order_by_dir: order_by_dir,
      status: "2",
      search: escapeHtmlEntities(search),
      start: start ? Math.floor(start / limit) : 0,
    };

    if (search === "") {
      delete dataParams.search;
    }

    if (!order_by_name || !order_by_dir) {
      delete dataParams.order_by_dir;
      delete dataParams.order_by_col_name;
    }

    try {
      gridParams?.api.hideOverlay();

      const resData = (await getMasterDocumentListApi(
        dataParams
      )) as IGetStockMasterDataApiRes;

      const rowCount = gridParams?.api?.getDisplayedRowCount() ?? 0;
      if (resData?.data?.masterData?.length < limit) {
        gridData = {
          ...gridData,
          rowCount: rowCount + (resData?.data?.masterData?.length ?? 0) - 1,
        };
      }
      gridData = { ...gridData, rowData: resData?.data?.masterData ?? [] };
      gridParams?.success(gridData);

      if (
        (!resData?.success || gridData.rowData.length <= 0) &&
        dataParams?.start === 0
      ) {
        gridParams?.api.showNoRowsOverlay();
      } else if (resData?.success && gridData.rowData.length > 0) {
        gridParams?.api.hideOverlay();
      }
    } catch (err) {
      gridParams?.success({ rowCount: 0, rowData: [] });
      gridParams?.api.showNoRowsOverlay();
      gridParams?.fail();
    }
  }, [gridRowParams, search]);

  const fetchSingleMasterDocData = useCallback(async (docId: number) => {
    stockLoading.onTrue();

    const docData = (await getMasterDocumentListApi({
      master_document_id: docId,
    })) as IGetStockMasterDataApiRes;

    setStockDetail(docData.data);

    stockLoading.onFalse();
  }, []);

  const columnDefs = [
    {
      headerName: "#",
      field: "has",
      sortable: false,
      minWidth: 170,
      maxWidth: 170,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellClass: "ag-center-aligned-cell",
      cellRenderer: (params: IStockMasterDocCellRendererParams) => {
        const { data } = params;

        return (
          <div
            className="flex items-center m-auto gap-1.5 bg-blue-100 text-primary-900 py-1 px-[9px] rounded dark:bg-dark-800 dark:text-white/90 whitespace-nowrap w-fit"
            onClick={async () => {
              setDrowerOpen(true);
              fetchSingleMasterDocData(Number(data?.master_document_id));
            }}
          >
            <Typography className="text-13 font-medium">
              {_t("Copy to My Templates")}
            </Typography>
          </div>
        );
      },
    },
    {
      headerName: _t("Name"),
      field: "document_name",
      sortable: true,
      minWidth: 120,
      flex: 1,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: (params: IStockMasterDocCellRendererParams) => {
        const { data } = params;
        const documentName =
          _t(HTMLEntities.decode(sanitizeString(data?.document_name))) ?? "";

        return (
          <Tooltip title={documentName}>
            <Typography className="table-tooltip-text">
              {documentName || "-"}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Description"),
      field: "description",
      flex: 2,
      minWidth: 120,
      sortable: true,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: (params: IStockMasterDocCellRendererParams) => {
        const { data } = params;
        const description =
          _t(HTMLEntities.decode(sanitizeString(data?.description))) ?? "";

        return (
          <Tooltip title={description}>
            <Typography className="table-tooltip-text">
              {description || "-"}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Module"),
      field: "module_name",
      flex: 2,
      minWidth: 120,
      maxWidth: 200,
      sortable: true,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: (params: IStockMasterDocCellRendererParams) => {
        const { data } = params;
        const moduleName =
          _t(HTMLEntities.decode(sanitizeString(data?.module_name ?? ""))) ??
          "";

        return (
          <Tooltip title={moduleName}>
            <Typography className="table-tooltip-text">
              {moduleName || "-"}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: "",
      field: "",
      maxWidth: 50,
      minWidth: 50,
      suppressMenu: true,
      cellClass: "!cursor-auto",
      cellRenderer: (params: IStockMasterDocCellRendererParams) => {
        const { data } = params;

        return (
          <div className="flex items-center gap-3 justify-center">
            <ButtonWithTooltip
              tooltipTitle={_t("Document Details")}
              tooltipPlacement="top"
              icon="fa-solid fa-eye"
              onClick={async () => {
                setDrowerOpen(true);
                fetchSingleMasterDocData(Number(data?.master_document_id));
              }}
            />
          </div>
        );
      },
    },
  ];

  return (
    <>
      <div className="md:h-[calc(100vh-292px)] h-[calc(100vh-270px)] ag-theme-alpine ag-grid-cell-pointer">
        <DynamicTable
          columnDefs={columnDefs}
          onGridReady={onGridReady}
          onSortChanged={onSortChanged}
          noRowsOverlayComponent={() => (
            <NoRecords
              rootClassName="w-full max-w-[280px]"
              image={`${window.ENV.CDN_URL}assets/images/create-record-directory.svg`}
              imageWSize="280"
              imageHSize="227"
              text={
                module_access === "full_access" ||
                module_access === "own_data_access" ? (
                  <div>
                    <Typography
                      onClick={() => {}}
                      className="sm:text-base text-xs underline underline-offset-1 text-black font-bold cursor-pointer"
                    >
                      {_t("Click here")}
                    </Typography>
                    <Typography className="sm:text-base text-xs text-black font-semibold">
                      {_t(" to Create a New Record")}
                    </Typography>
                  </div>
                ) : (
                  <Typography className="sm:text-base text-xs text-black font-semibold">
                    {_t("No Record Found")}
                  </Typography>
                )
              }
            />
          )}
          enableOpenInNewTab={window.ENV.ENABLE_ALL_CLICK}
          // generateOpenInNewTabUrl={(data: { work_order_id?: number }) =>
          //   `${routes.MANAGE_WORKORDER.url}/${data?.work_order_id}`
          // }
        />
      </div>
      <StockView
        drowerOpen={drowerOpen}
        setDrowerOpen={setDrowerOpen}
        stockLoading={stockLoading.bool}
        stockDetail={stockDetail?.document_html ?? ""}
        copyDocHandler={() => {
          setDrowerOpen(false);
          setCopyDocumentDrawerOpen(true);
        }}
      />

      {copyDocumentDrawerOpen && (
        <AddDocumentWriter
          drowerOpen={copyDocumentDrawerOpen}
          setDrowerOpen={setCopyDocumentDrawerOpen}
          mode="copy"
          defaultValues={{
            module_id: stockDetail?.module_id?.toString() || "",
            document_id: "0",
            document_name: `${stockDetail?.document_name} copy` || "",
            document_html: stockDetail?.document_html || "",
            is_link_attached: "1",
            is_active: stockDetail?.is_active?.toString() || "1",
            is_single_image_pdf: 0,
            add_header: 0,
            add_footer: 0,
            attached_files: [],
          }}
        />
      )}
    </>
  );
};

export default StockList;
