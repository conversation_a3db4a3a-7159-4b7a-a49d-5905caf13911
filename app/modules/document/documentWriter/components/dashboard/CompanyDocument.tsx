// hooks
import { useTranslation } from "~/hook";
// Atoms
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { StaticTable } from "~/shared/components/molecules/staticTable";
// Other
import { TEMPLATES_OPTIONS } from "../../utils/constants";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { DynamicTable } from "~/shared/components/molecules/dynamicTable";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { GridReadyEvent } from "ag-grid-community";
import useTableGridData from "~/shared/hooks/useTableGridData";
import { SortChangedEvent } from "ag-grid-community";
import {
  escapeHtmlEntities,
  getApiDefaultParams,
  Number,
  sanitizeString,
} from "~/helpers/helper";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useModuleDataSearch } from "~/zustand/global-module-filter/hook";
import isEqual from "lodash/isEqual";
import { getApiData } from "~/helpers/axios-api-helper";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { documentWriterRoutes } from "~/route-services/document-writer.routes";
import { TemplateViewDocDropdownOptions } from "./TemplateViewDocDropdownOptions";
import { useBoolean } from "~/modules/projectManagement/pages/project/hook/use-boolean";
import UseThisTemplateDocumentView from "../sidebar/templateView/UseThisTemplates/UseThisTemplateDocumentView";
import { Button } from "~/shared/components/atoms/button";

let timeout: NodeJS.Timeout;

const CompanyDocumentList = ({ myTemplatesStatus }: ICompanyDocumentProps) => {
  const { _t } = useTranslation();
  const { datasource, gridRowParams } = useTableGridData();
  const user: IInitialGlobalData["user"] = getGlobalUser();

  const singleTemplateDataLoading = useBoolean();
  const [useThisTemplateDocView, setUseThisTemplateDocView] =
    useState<boolean>(false);
  const [templateDetail, setTemplateDetail] =
    useState<IMyTemplateDocumentDetails>();

  const { search }: IModuleDataSearchParams = useModuleDataSearch(
    CFConfig.document_writer_module
  );

  const currentMenuModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const { module_access } = currentMenuModule || {};

  const isReadOnly = useMemo(
    () => module_access === "read_only",
    [module_access]
  );

  const fetchMyTemplates = useCallback(async () => {
    let gridData: { rowCount?: number; rowData: IMyTemplatesData[] } = {
      rowData: [],
    };
    const { changeGridParams, gridParams } = gridRowParams ?? {};
    const limit = changeGridParams?.length ?? 0;
    const { start, order_by_name, order_by_dir } = changeGridParams || {};

    let dataParams: IMyTemplatesApiParams = {
      length: limit,
      ignore_filter: 1,
      order_by: order_by_name,
      order_by_dir: order_by_dir,
      search: escapeHtmlEntities(search),
      start: start ? start : 0,
      status: myTemplatesStatus,
    };

    if (search === "") {
      delete dataParams.search;
    }

    if (myTemplatesStatus === "3") {
      delete dataParams.status;
    }

    if (!order_by_name || !order_by_dir) {
      delete dataParams.order_by_dir;
      delete dataParams.order_by;
    }
    try {
      gridParams?.api.hideOverlay();

      getApiData({
        url: documentWriterRoutes.getDocuments,
        method: "post",
        data: getApiDefaultParams({
          op: "get_documents",
          user,
          otherParams: {
            ...dataParams,
          },
        }),
        success: (response: { data: IMyTemplatesData[]; success: boolean }) => {
          const rowCount = gridParams?.api?.getDisplayedRowCount() ?? 0;
          if (response?.data?.length < limit) {
            gridData = {
              ...gridData,
              rowCount: rowCount + (response?.data?.length ?? 0) - 1,
            };
          }
          gridData = { ...gridData, rowData: response?.data ?? [] };
          gridParams?.success(gridData);

          if (
            (!response?.success || gridData.rowData.length <= 0) &&
            dataParams?.start === 0
          ) {
            gridParams?.api.showNoRowsOverlay();
          } else if (response?.success && gridData.rowData.length > 0) {
            gridParams?.api.hideOverlay();
          }
        },
        error: (err) => {
          gridParams?.success({ rowCount: 0, rowData: [] });
          gridParams?.api.showNoRowsOverlay();
          gridParams?.fail();
        },
      });
    } catch (err) {
      gridParams?.success({ rowCount: 0, rowData: [] });
      gridParams?.api.showNoRowsOverlay();
      gridParams?.fail();
    }
  }, [gridRowParams, search]);

  const onSortChanged = async (params: SortChangedEvent) => {
    params.api.setServerSideDatasource({ getRows: () => {} });
    params.api.setServerSideDatasource(datasource);
  };

  const onGridReady = (gridParams: GridReadyEvent) => {
    gridParams.api.setServerSideDatasource(datasource);

    requestAnimationFrame(() => {
      setTimeout(() => {
        gridParams.columnApi.applyColumnState({
          state: [
            {
              colId: "date_added",
              sort: "desc",
            },
          ],
          applyOrder: true,
        });
      }, 0);
    });
  };

  const previousValues = useRef({
    search,
    myTemplatesStatus,
  });

  useEffect(() => {
    if (gridRowParams?.changeGridParams) {
      if (timeout) {
        clearTimeout(timeout);
      }
      timeout = setTimeout(() => {
        fetchMyTemplates();
      }, 500);
    }
  }, [gridRowParams?.changeGridParams]);

  useEffect(() => {
    const currentValues = {
      search,
      myTemplatesStatus,
    };

    if (!isEqual(previousValues.current, currentValues)) {
      previousValues.current = currentValues;
      refreshAgGrid();
    }
  }, [search, myTemplatesStatus]);

  const refreshAgGrid = () => {
    const gridParams = gridRowParams?.gridParams;
    if (gridParams) {
      gridParams.api.setServerSideDatasource({ getRows: () => {} });
      gridParams.api.setServerSideDatasource(datasource);
    }
  };

  const fetchSingleMasterDocData = useCallback(
    async (docId: number) => {
      singleTemplateDataLoading.onTrue();

      let dataParams: IMyTemplatesApiParams = {
        document_id: docId,
      };

      getApiData({
        url: documentWriterRoutes.getDocuments,
        method: "post",
        data: getApiDefaultParams({
          op: "get_documents",
          user,
          otherParams: {
            ...dataParams,
          },
        }),
        success: (response: {
          data: IMyTemplateDocumentDetails;
          success: boolean;
        }) => {
          if (response.success) {
            setTemplateDetail(response.data);
          }
          singleTemplateDataLoading.onFalse();
        },
        error: (err) => {
          notification.error({
            description: err,
          });
          singleTemplateDataLoading.onFalse();
        },
      });
    },
    [singleTemplateDataLoading]
  );

  const columnDefs = [
    {
      headerName: "#",
      field: "has",
      sortable: false,
      minWidth: 170,
      maxWidth: 170,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellClass: "ag-center-aligned-cell",
      cellRenderer: (params: { data: IMyTemplatesData }) => {
        const { data } = params;
        return (
          <div
            className={`flex items-center m-auto gap-1.5 bg-blue-100 text-primary-900 py-1 px-[9px] rounded dark:bg-dark-800 dark:text-white/90 whitespace-nowrap w-fit ${
              Number(data?.is_single_image_pdf) === 1
                ? "!cursor-not-allowed opacity-80"
                : "cursor-pointer"
            }`}
            onClick={() => {
              if (isReadOnly || Number(data?.is_single_image_pdf) === 1) {
                return;
              }
              setUseThisTemplateDocView(true);
              fetchSingleMasterDocData(Number(data?.document_id));
            }}
          >
            <Tooltip
              title={
                Number(data?.is_single_image_pdf) === 1
                  ? _t(
                      "Use of this document is restricted. Since this is only an image, there are no text or fields to populate."
                    )
                  : ""
              }
            >
              <Typography className="text-13 font-medium">
                {_t("Use This Document")}
              </Typography>
            </Tooltip>
          </div>
        );
      },
    },
    {
      headerName: _t("Name"),
      field: "document_name",
      sortable: true,
      minWidth: 120,
      flex: 2,
      suppressMenu: true,
      resizable: true,
      suppressMovable: false,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: (params: { data: IMyTemplatesData }) => {
        const { data } = params;
        const name = _t(
          HTMLEntities.decode(sanitizeString(data.document_name))
        );
        return name ? (
          <Tooltip title={name}>
            <Typography className="table-tooltip-text text-center">
              {name}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Module"),
      field: "module_name",
      minWidth: 120,
      maxWidth: 200,
      flex: 2,
      sortable: true,
      suppressMenu: true,
      resizable: true,
      suppressMovable: false,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: (params: { data: IMyTemplatesData }) => {
        const { data } = params;
        const moduleName = HTMLEntities.decode(
          sanitizeString(data.module_name)
        );

        return moduleName ? (
          <Tooltip title={moduleName}>
            <Typography className="table-tooltip-text text-center">
              {moduleName}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Date"),
      field: "date_added",
      sortable: true,
      minWidth: 135,
      maxWidth: 150,
      flex: 2,
      suppressMenu: true,
      suppressMovable: false,
      cellRenderer: (params: { data: IMyTemplatesData }) => {
        const { data } = params;
        return (
          <>
            {data.date_added ? (
              <DateTimeCard format="date" date={data.date_added} />
            ) : (
              <div className="text-center table-tooltip-text">-</div>
            )}
          </>
        );
      },
    },
    {
      headerName: _t("Status"),
      field: "status",
      sortable: true,
      maxWidth: 120,
      minWidth: 120,
      suppressMenu: true,
      suppressMovable: false,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      cellRenderer: (params: { data: IMyTemplatesData }) => {
        const { data } = params;
        const status = data.status;
        return status ? (
          <Tooltip title={status}>
            <div className="text-center">
              <Tag
                color="#EEF8E9"
                className="!text-[#4FB91D] mx-auto text-13 type-badge common-tag"
              >
                {status}
              </Tag>
            </div>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: "",
      field: "",
      maxWidth: 50,
      minWidth: 50,
      suppressMenu: true,
      cellClass: "!cursor-auto",
      cellRenderer: (params: { data: IMyTemplatesData }) => {
        const { data } = params;

        return (
          <div className="flex items-center gap-1.5 justify-center">
            <TemplateViewDocDropdownOptions
              templateData={data}
              refreshTable={refreshAgGrid}
            />
          </div>
        );
      },
    },
  ];

  return (
    <>
      <div className="md:h-[calc(100vh-292px)] h-[calc(100vh-270px)] ag-theme-alpine">
        <DynamicTable
          columnDefs={columnDefs}
          onGridReady={onGridReady}
          onSortChanged={onSortChanged}
          className="static-table"
          noRowsOverlayComponent={() => (
            <NoRecords
              rootClassName="w-full max-w-[280px]"
              image={`${window.ENV.CDN_URL}assets/images/create-record-directory.svg`}
              imageWSize="280"
              imageHSize="227"
              text={
                module_access === "full_access" ||
                module_access === "own_data_access" ? (
                  <div>
                    <Typography
                      onClick={() => {}}
                      className="sm:text-base text-xs underline underline-offset-1 text-black font-bold cursor-pointer"
                    >
                      {_t("Click here")}
                    </Typography>
                    <Typography className="sm:text-base text-xs text-black font-semibold">
                      {_t(" to Create a New Record")}
                    </Typography>
                  </div>
                ) : (
                  <Typography className="sm:text-base text-xs text-black font-semibold">
                    {_t("No Record Found")}
                  </Typography>
                )
              }
            />
          )}
          enableOpenInNewTab={window.ENV.ENABLE_ALL_CLICK}
          // generateOpenInNewTabUrl={(data: { work_order_id?: number }) =>
          //   `${routes.MANAGE_WORKORDER.url}/${data?.work_order_id}`
          // }
        />

        {useThisTemplateDocView && (
          <UseThisTemplateDocumentView
            detailLoading={singleTemplateDataLoading.bool}
            drowerOpen={useThisTemplateDocView}
            setDrowerOpen={setUseThisTemplateDocView}
            templateDetail={templateDetail}
          />
        )}
      </div>
    </>
  );
};

export default CompanyDocumentList;
