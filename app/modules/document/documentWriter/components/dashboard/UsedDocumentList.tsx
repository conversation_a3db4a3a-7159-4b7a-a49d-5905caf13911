import isEqual from "lodash/isEqual";

// hooks
import { useTranslation } from "~/hook";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
// Other
import { memo, useCallback, useEffect, useRef, useState } from "react";
import {
  activeArchiveDocumentApi,
  getBatchRecordDetails,
  getCompanyDocumentListApi,
  sendBatchEmail,
} from "../../redux/action/dashAction";
import { DynamicTable } from "~/shared/components/molecules/dynamicTable";
import { GridReadyEvent, SortChangedEvent } from "ag-grid-community";
import useTableGridData from "~/shared/hooks/useTableGridData";
import { escapeHtmlEntities, Number, sanitizeString } from "~/helpers/helper";
import { useModuleDataSearch } from "~/zustand/global-module-filter/hook";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { UsedDocumentDropdownOptios } from "./UsedDocumentDropdownOptios";
import { useNavigate } from "@remix-run/react";
import { CellClickedEvent } from "ag-grid-community";
import UsedDocumentPdfView from "./usedDocumentPdfView";
import { routes } from "~/route-services/routes";

let timeout: NodeJS.Timeout;

const UsedDocumentList: React.FC<IUsedDocProps> = ({ tab }) => {
  const { _t } = useTranslation();
  const { datasource, gridRowParams } = useTableGridData();
  const navigate = useNavigate();
  const [isDocumentViewModalOpen, setIsDocumentViewModalOpen] =
    useState<boolean>(false);
  const [documentToBeView, setDocumentToBeView] = useState<string>("");
  const [sendDocumentEmailData, setSendDocumentEmailData] = useState<
    Partial<IGetBatchRecordSendEmailData>
  >({});
  const [batchDocumentRecordData, setBatchDocumentRecordData] = useState<
    IBatchRecordData[]
  >([]);
  const [
    isBatchDocumentRecordDataLoading,
    setIsBatchDocumentRecordDataLoading,
  ] = useState<boolean>(false);

  const currentMenuModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const { module_access, singular_name } = currentMenuModule || {};

  const { search }: IModuleDataSearchParams = useModuleDataSearch(
    CFConfig.document_writer_module
  );

  const onGridReady = (gridParams: GridReadyEvent) => {
    gridParams?.api?.setServerSideDatasource(datasource);
  };

  const onSortChanged = async (params: SortChangedEvent) => {
    params.api.setServerSideDatasource({ getRows: () => {} });
    params.api.setServerSideDatasource(datasource);
  };

  const previousValues = useRef({
    search,
    tab,
  });

  useEffect(() => {
    if (gridRowParams?.changeGridParams) {
      if (timeout) {
        clearTimeout(timeout);
      }
      timeout = setTimeout(() => {
        fetchUsedDocuments();
      }, 500);
    }
  }, [gridRowParams?.changeGridParams]);

  useEffect(() => {
    const currentValues = {
      search,
      tab,
    };

    if (!isEqual(previousValues.current, currentValues)) {
      previousValues.current = currentValues;
      refreshAgGrid();
    }
  }, [search, tab]);

  const refreshAgGrid = () => {
    const gridParams = gridRowParams?.gridParams;
    if (gridParams) {
      gridParams.api.setServerSideDatasource({ getRows: () => {} });
      gridParams.api.setServerSideDatasource(datasource);
    }
  };

  const fetchUsedDocuments = useCallback(async () => {
    let gridData: { rowCount?: number; rowData: IUsedDocumentDetail[] } = {
      rowData: [],
    };
    const { changeGridParams, gridParams } = gridRowParams ?? {};
    const limit = changeGridParams?.length ?? 0;
    const { start, order_by_name, order_by_dir } = changeGridParams || {};

    let dataParams: IUsedDocApiParams = {
      limit,
      ignore_filter: 1,
      order_by_name: order_by_name,
      order_by_dir: order_by_dir,
      search: escapeHtmlEntities(search),
      start: start ? Math.floor(start / limit) : 0,
      selected_tab: tab,
    };

    if (search === "") {
      delete dataParams.search;
    }

    if (!order_by_name || !order_by_dir) {
      delete dataParams.order_by_dir;
      delete dataParams.order_by_name;
    }

    try {
      gridParams?.api.hideOverlay();

      const resData = (await getCompanyDocumentListApi(
        dataParams
      )) as IUsedDocApiRes;

      const rowCount = gridParams?.api?.getDisplayedRowCount() ?? 0;
      if (resData?.data?.length < limit) {
        gridData = {
          ...gridData,
          rowCount: rowCount + (resData?.data?.length ?? 0) - 1,
        };
      }
      gridData = { ...gridData, rowData: resData?.data ?? [] };
      gridParams?.success(gridData);

      if (
        (!resData?.success || gridData.rowData.length <= 0) &&
        dataParams?.start === 0
      ) {
        gridParams?.api.showNoRowsOverlay();
      } else if (resData?.success && gridData.rowData.length > 0) {
        gridParams?.api.hideOverlay();
      }
    } catch (err) {
      gridParams?.success({ rowCount: 0, rowData: [] });
      gridParams?.api.showNoRowsOverlay();
      gridParams?.fail();
    }
  }, [gridRowParams, search, tab]);

  const columnDefs = [
    {
      headerName: _t("Document"),
      field: "document_name",
      minWidth: 120,
      sortable: true,
      flex: 1,
      suppressMenu: true,
      resizable: true,
      suppressMovable: false,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: (params: IUsedDocCellRendererParams) => {
        const { data } = params;
        const documentName =
          _t(HTMLEntities.decode(sanitizeString(data?.document_name))) ?? "";

        return (
          <Tooltip title={documentName}>
            <Typography className="table-tooltip-text text-center">
              {documentName || "-"}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Module"),
      field: "module_name",
      flex: 1,
      minWidth: 120,
      maxWidth: 200,
      sortable: true,
      suppressMenu: true,
      resizable: true,
      suppressMovable: false,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: (params: IUsedDocCellRendererParams) => {
        const { data } = params;
        const moduleName =
          _t(HTMLEntities.decode(sanitizeString(data?.module_name))) ?? "";

        return (
          <Tooltip title={moduleName}>
            <Typography className="table-tooltip-text text-center">
              {moduleName || "-"}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Date"),
      field: "date_added",
      sortable: true,
      maxWidth: 150,
      minWidth: 150,
      suppressMenu: true,
      suppressMovable: false,
      cellRenderer: (params: IUsedDocCellRendererParams) => {
        const { data } = params;

        return data.date_added ? (
          <div>
            <DateTimeCard format="date" date={data.date_added} />
          </div>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: "",
      field: "",
      minWidth: 50,
      maxWidth: 50,
      suppressMenu: true,
      cellClass: "!cursor-auto",
      cellRenderer: (params: IUsedDocCellRendererParams) => {
        const { data } = params;
        return (
          <div className="flex items-center gap-1.5 justify-center">
            <UsedDocumentDropdownOptios
              documentData={data}
              refreshTable={refreshAgGrid}
            />
          </div>
        );
      },
    },
  ];

  const options: CustomerEmailTab[] = [
    CFConfig.employee_key,
    "my_crew",
    CFConfig.customer_key,
    CFConfig.contractor_key,
    CFConfig.vendor_key,
    CFConfig.misc_contact_key,
    "by_service",
    "my_project",
  ];

  const handleEmailApiCall = async (
    tempFormData: SendEmailFormDataWithApiDefault,
    closeSendMailSidebar: () => void,
    ccMailCopy: boolean
  ) => {
    try {
      const paramsData: ISendBatchEmailApiParams = {
        file_path: sendDocumentEmailData.email_attachment || "",
        custom_msg: tempFormData.custom_msg,
        custom_subject: tempFormData.custom_subject,
        emails: tempFormData.emails,
        method_to_send: tempFormData.method_to_send,
        send_me_copy: ccMailCopy ? 1 : 0,
      };

      const sendEmailResponse = await sendBatchEmail({ ...paramsData });

      if (!sendEmailResponse.success) {
        notification.error({
          description: sendEmailResponse.message || "Something went wrong!",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error).message || "Something went wrong!",
      });
    }
  };

  const getRecordDetails = async (docID: number) => {
    setIsBatchDocumentRecordDataLoading(true);
    try {
      const response = (await getBatchRecordDetails({
        company_document_id: docID,
      })) as IGetBatchRecordDetailsApiResponse;

      if (response.success) {
        setBatchDocumentRecordData(response.data);
        setDocumentToBeView(response.sent_email_data.document_pdf_link);
        setSendDocumentEmailData(response.sent_email_data);
        setIsBatchDocumentRecordDataLoading(false);
      } else {
        setIsDocumentViewModalOpen(false);
        setBatchDocumentRecordData([]);
        setDocumentToBeView("");
        setSendDocumentEmailData({});
        setIsBatchDocumentRecordDataLoading(false);
        notification.error({
          description: response.message || "Something went wrong!",
        });
      }
    } catch (error) {
      setIsDocumentViewModalOpen(false);
      setBatchDocumentRecordData([]);
      setDocumentToBeView("");
      setSendDocumentEmailData({});
      setIsBatchDocumentRecordDataLoading(false);
      notification.error({
        description: (error as Error).message || "Something went wrong!",
      });
    }
  };

  const handleDownload = async () => {
    try {
      const response = await fetch(documentToBeView);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);

      const a = document.createElement("a");
      a.href = url;
      a.download = "sample.pdf"; // Set desired filename
      document.body.appendChild(a);
      a.click();
      a.remove();

      window.URL.revokeObjectURL(url);
    } catch (error) {
      notification.error({
        description: (error as Error).message || "",
      });
    }
  };

  const handleArchiveDocument = async () => {
    const payload = {
      status: sendDocumentEmailData.is_deleted === 1 ? 0 : 1,
      company_document_id:
        Number(sendDocumentEmailData.company_document_id) || 0,
      is_archive: sendDocumentEmailData.is_deleted === 1 ? 0 : 1,
    };

    const arcActRes = await activeArchiveDocumentApi(payload);

    if (arcActRes.success) {
      refreshAgGrid();
      setBatchDocumentRecordData([]);
      setDocumentToBeView("");
      setSendDocumentEmailData({});
    } else {
      notification.error({
        description: arcActRes?.message || "Failed to update doc",
      });
    }
  };

  return (
    <>
      <div className="md:h-[calc(100vh-292px)] h-[calc(100vh-270px)] ag-theme-alpine ag-grid-cell-pointer">
        <DynamicTable
          columnDefs={columnDefs}
          onGridReady={onGridReady}
          onSortChanged={onSortChanged}
          noRowsOverlayComponent={() => (
            <NoRecords
              rootClassName="w-full max-w-[280px]"
              image={`${window.ENV.CDN_URL}assets/images/create-record-directory.svg`}
              imageWSize="280"
              imageHSize="227"
              text={
                module_access === "full_access" ||
                module_access === "own_data_access" ? (
                  <div>
                    <Typography
                      onClick={() => {}}
                      className="sm:text-base text-xs underline underline-offset-1 text-black font-bold cursor-pointer"
                    >
                      {_t("Click here")}
                    </Typography>
                    <Typography className="sm:text-base text-xs text-black font-semibold">
                      {_t(" to Create a New Record")}
                    </Typography>
                  </div>
                ) : (
                  <Typography className="sm:text-base text-xs text-black font-semibold">
                    {_t("No Record Found")}
                  </Typography>
                )
              }
            />
          )}
          enableOpenInNewTab={window.ENV.ENABLE_ALL_CLICK}
          // generateOpenInNewTabUrl={(data: { work_order_id?: number }) =>
          //   `${routes.MANAGE_WORKORDER.url}/${data?.work_order_id}`
          // }
          onCellClicked={(params: CellClickedEvent) => {
            const column = params.column;
            if (
              column.getColDef().field !== "" &&
              column.getColDef().field !== "email"
            ) {
              if (params.data.is_record_batch === 1) {
                setIsDocumentViewModalOpen(true);
                getRecordDetails(params.data.company_document_id);
              } else {
                navigate(
                  `${routes.MANAGE_DOCUMENT_WRITER.url}/${params.data.company_document_id}?mode=view`
                );
              }
            }
          }}
        />
      </div>

      {isDocumentViewModalOpen && (
        <UsedDocumentPdfView
          isOpen={isDocumentViewModalOpen}
          onCloseModal={() => {
            setIsDocumentViewModalOpen(false);
            setBatchDocumentRecordData([]);
            setDocumentToBeView("");
            setSendDocumentEmailData({});
          }}
          isLoading={isBatchDocumentRecordDataLoading}
          isViewAttachment={false}
          options={options}
          fileUrl={documentToBeView}
          documentsData={batchDocumentRecordData || []}
          moduleName={singular_name || "Document writer"}
          emailSubject={sendDocumentEmailData.email_subject || ""}
          documentEmailData={sendDocumentEmailData}
          handleDownload={() => handleDownload()}
          handleEmailApiCall={async (...params) => {
            await handleEmailApiCall(...params);
          }}
          handleArchiveDocument={handleArchiveDocument}
        />
      )}
    </>
  );
};

export default memo(UsedDocumentList);
