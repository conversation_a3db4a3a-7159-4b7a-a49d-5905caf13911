import { useEffect, useState } from "react";
// Molecules
import { UploadFileOption } from "~/shared/components/molecules/uploadFile";
// Other
import { loadScript } from "../../utils/loadScript";
import { uploadFileFromUrl } from "../../utils/uploadFileFromUrl";
import { useAppDispatch, useAppSelector } from "../../redux/store";
import { allowUploadExtensions } from "../../constant";
import { defaultConfig } from "~/data";
import { useGModules, getGSettings } from "~/zustand";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { imageExtensions } from "../../utils/allowedFileExtension";

const getFileExtension = (fileName: string) => {
  const nameParts = fileName.split(".");
  let fileExtension = "";
  if (nameParts.length > 1) {
    const ext = nameParts.pop();
    fileExtension = ext ? ext.toLowerCase() : "";
  }
  return fileExtension;
};

const DropboxPicker = () => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const dispatch = useAppDispatch();
  const { checkModuleAccessByKey } = useGModules();
  const currentModule = getCurrentMenuModule();
  const { image_resolution }: GSettings = getGSettings();

  const selectedProject: ISelectedFolder = useAppSelector(
    (state) => state.filePhotoFolderList.selectedFolder
  );

  const { fileList }: IFilePhotoRight = useAppSelector(
    (state) => state.filePhotoRightList
  );
  let dropBoxLoading = false;

  const parentFolderName: string = useAppSelector(
    (state) => state.filePhotoFolderList.parentFolderName
  );

  const fileSupportAccess = checkModuleAccessByKey(
    defaultConfig.file_support_key
  );

  useEffect(() => {
    loadScript(
      "https://www.dropbox.com/static/api/2/dropins.js",
      "dropboxjs",
      window.ENV.DROPBOX_APP_KEY
    ).then(() => setIsLoading(false));
  }, []);
  const dropboxOptions: Dropbox.ChooserOptions = {
    success: async function (files) {
      if (!dropBoxLoading) {
        dropBoxLoading = true;

        const selectedFiles: IselectedFilesUpload[] = [];
        files.forEach(({ name, link }) => {
          const extension = getFileExtension(name);
          if (
            extension === "" ||
            !allowUploadExtensions.includes(extension.toLowerCase())
          ) {
            notification.error({
              description: `Rejected: ${name} (Invalid file type or missing extension)`,
            });
          } else {
            selectedFiles.push({ fileName: name, url: link });

            if (
              currentModule?.module_id === 8 &&
              fileSupportAccess === "no_access" &&
              !imageExtensions.includes(extension.toLowerCase())
            ) {
              notification.error({
                description:
                  "Only image files are allowed for your current access level.",
              });
              dropBoxLoading = false;
              return;
            } else {
              uploadFileFromUrl(
                dispatch,
                selectedProject,
                selectedFiles,
                fileList,
                image_resolution,
                undefined,
                true,
                0,
                parentFolderName,
                fileSupportAccess
              );
            }
          }
        });
      }
    },

    cancel: function () {},

    linkType: "direct", // or "preview"

    multiselect: true, // or false

    folderselect: false,

    sizeLimit: 1024 * 1024 * 100,
    // or any positive number
  };

  return (
    <UploadFileOption
      title="Dropbox"
      className="2xl:p-2 lg:p-2 !p-2 2xl:gap-2 gap-2 justify-center rounded-lg items-center 2xl:flex-row xl:flex-col 2xl:text-left xl:text-left"
      titleClassName="2xl:!text-[13px] xl:!text-[13px] lg:!text-[13px] !text-[13px] sm:flex hidden xl:justify-left justify-center"
      contentClassName="gap-0.5 text-center min-[1370px]:w-fit xl:w-fit"
      imageRootClassName="min-[1370px]:w-fit w-fit"
      imageClassName="max-w-6"
      fileFormatText={false}
      imageUrl={"https://cdn.contractorforeman.net/assets/images/dropbox.png"}
      onClick={() => {
        dropBoxLoading = false;
        !isLoading && window.Dropbox?.choose(dropboxOptions);
      }}
    />
  );
};

export default DropboxPicker;
