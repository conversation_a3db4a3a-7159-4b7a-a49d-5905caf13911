import React, { useRef } from "react";
import { useTranslation } from "~/hook";
// Molecules
import { UploadFileOption } from "~/shared/components/molecules/uploadFile";
import { imageExtensions } from "../../utils/allowedFileExtension";

const Dropzone: React.FC<IDropzoneProps> = ({
  onDrop,
  onClick,
  isFileAndPhotoModule = false,
  fileSupportAccess,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { _t } = useTranslation();
  return (
    <>
      <UploadFileOption
        title="Drag & Drop Files or to Browse"
        className="xl:flex-col justify-center text-center 2xl:gap-2 gap-2 rounded-lg"
        titleClassName="2xl:!text-[13px] xl:!text-[13px] lg:!text-[13px] !text-[13px] whitespace-normal sm:flex justify-center hidden"
        subtitleClassName="xl:text-[10px] text-[10px] sm:flex justify-center hidden"
        contentClassName="gap-0.5 text-center min-[1370px]:w-fit xl:w-fit"
        imageRootClassName="min-[1370px]:w-fit w-fit"
        fileFormatText={false}
        imageUrl={
          "https://cdn.contractorforeman.net/assets/images/upload-file.svg"
        }
        subtitle={_t("JPEG, PNG, GIF, MP4, PDF, Word, PPT")}
        onClick={() => {
          fileInputRef.current && fileInputRef.current.click();
        }}
        onDrop={onDrop}
      />
      <input
        type="file"
        ref={fileInputRef}
        style={{ display: "none" }}
        onChange={onClick}
        multiple={isFileAndPhotoModule && fileSupportAccess !== "no_access"}
        accept={
          fileSupportAccess === "no_access"
            ? `${imageExtensions.join(",image/")}`
            : undefined
        }
      />
    </>
  );
};

export default Dropzone;
