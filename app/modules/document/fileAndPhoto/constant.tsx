// Molecules
import { IFileExtensionsList } from "~/shared/components/molecules/dropdownMenu/type";

export const fileExtensionsList: IFileExtensionsList = {
  docx: { icon: "fa-solid fa-file-word", color: "#004EAE" },
  doc: { icon: "fa-solid fa-file-word", color: "#004EAE" },
  rtf: { icon: "fa-solid fa-file-word", color: "#004EAE" },
  dwg: { icon: "fa-solid fa-file", color: "#5B5B5B" },
  mp4: { icon: "fa-solid fa-music", color: "#FF3E4C" },
  mov: { icon: "fa-solid fa-music", color: "#FF3E4C" },
  flv: { icon: "fa-solid fa-music", color: "#FF3E4C" },
  webm: { icon: "fa-solid fa-music", color: "#FF3E4C" },
  avi: { icon: "fa-solid fa-music", color: "#FF3E4C" },
  mkv: { icon: "fa-solid fa-music", color: "#FF3E4C" },
  m2ts: { icon: "fa-solid fa-music", color: "#FF3E4C" },
  mts: { icon: "fa-solid fa-music", color: "#FF3E4C" },
  ppt: { icon: "fa-solid fa-file-powerpoint", color: "#ff5400" },
  pptx: { icon: "fa-solid fa-file-powerpoint", color: "#ff5400" },
  txt: { icon: "fa-solid fa-file-lines", color: "#5659E9" },
  xlsx: { icon: "fa-solid fa-file-excel", color: "#127F45" },
  xls: { icon: "fa-solid fa-file-excel", color: "#127F45" },
  xlsm: { icon: "fa-solid fa-file-excel", color: "#127F45" },
  xsl: { icon: "fa-solid fa-file-excel", color: "#127F45" },
  zip: { icon: "fa-solid fa-file-zip", color: "#307DBC" },
  rar: { icon: "fa-solid fa-file-zip", color: "#307DBC" },
  pdf: { icon: "fa-solid fa-file-pdf", color: "#FC3830" },
  csv: { icon: "fa-solid fa-file-csv", color: "#31D071" },
  mp3: { icon: "fa-solid fa-music", color: "#FF3E4C" },
};

export const actionMenuOptList: IDropdownMenuOption[] = [
  {
    label: "Send Email",
    icon: "fa-regular fa-envelope",
    key: "send_email",
  },
  {
    label: "Download",
    icon: "fa-regular fa-download",
    key: "download",
  },
  {
    label: "Edit",
    icon: "fa-regular fa-pencil",
    key: "edit",
  },
];

export const imageExtensions = [
  // "heif",
  "heic",
  // "heic-sequence",
  "jpg",
  "jpeg",
  "png",
  "gif",
  // "bmp",
  "webp",
  // "jfif",
  // "tiff",
  // "svg",
];

export const allowUploadExtensions = [
  // Image
  ...imageExtensions,
  // old
  // Document
  // "pdf",
  // "doc",
  // "docx",
  // "xls",
  // "xlsx",
  // "ppt",
  // "pptx",
  // "txt",
  // "csv",
  // "rtf",
  // "ods",
  // "tsv",
  // "sxc",
  // "html",
  // "zip",
  // "rar",
  // "dwg",
  // "odt",
  // "msg",
  // "kmz",
  // "dwg",
  // // video and audio
  // "mp4",
  // "mp3",
  // "wav",
  // "avi",
  // "mov",
  // "flv",
  // "wmv",
  // "3gp",
  // "mkv",
  // "webm",
  // "mkv",
  // "m2ts",
  // "mts",

  //updated
  "txt",
  "mp4",
  "pdf",
  "xls",
  "xlsx",
  "xlsm",
  "docx",
  "zip",
  "gz",
  "rar",
  "msg",
  "kmz",
  "csv",
  "ppt", // likely meant to be "ppt" or "pptx"
  "pptx",
  "dwg",
  "avi",
  "wmv",
  "mkv",
  "mov",
  "webm",
  "m2ts",
  "mts",
  "flv",
  "mp3",
];

export const fileAddOptions = [
  {
    label: "Create a Folder",
    key: "create_a_folder",
  },
  {
    label: "Add New Files",
    key: "add_new_files",
  },
  {
    label: "Create New File",
    key: "create_new_file",
  },
];
