import { Timeline } from "antd";
import { memo, useState } from "react";

// Atoms
import { LightGalleryModel } from "~/shared/components/atoms/lightGalleryModel";
import { Typography } from "~/shared/components/atoms/typography";
import { TimeLine } from "~/shared/components/atoms/timeline";

// Molecules
import { GalleryFilePhotos } from "~/shared/components/molecules/galleryFilePhotos";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { InfiniteScroll } from "~/shared/components/molecules/InfiniteScroll/InfiniteScroll";

// Other
import DetailsView from "./DetailsView";
import isEmpty from "lodash/isEmpty";
import { toggleSelectedFiles } from "./redux/slices/filePhotoRightSlice";
import { useAppDispatch, useAppSelector } from "./redux/store";
import { getGSettings, useGModules } from "~/zustand";
import { useDateFormatter, useTranslation } from "~/hook";
import { defaultConfig, defaultDateTimeFormat } from "~/data";
import { getFormat } from "~/helpers/helper";
import TimelineViewLoader from "./TimeLineViewLoader";
import { getGConfig } from "~/zustand";
import UploadFilesInNoData from "./components/noData/UploadFilesInNoData.client";
import AddOrEditFile from "./sidebar/AddOrEditFile";

const TimelineView = ({
  loadMore,
  hasMore,
  infiniteScrollHideLoadingComponent,
  isInfiniteScrollLoading,
  changeAddOrEditFileOpen,
  changeMarkupModelOpen,
  changeConfirmModelOpen,
  changeSendEmailDrawerOpen,
}: FilesTimelineProps) => {
  const { module_access, module_id }: GConfig = getGConfig();
  const { checkModuleAccessByKey } = useGModules();

  const fileList: IFilePhotoRight = useAppSelector(
    (state) => state.filePhotoRightList
  );
  const timelineView: IFilePhotoViewSlice["timelineView"] = useAppSelector(
    (state) => state.filePhotoView.timelineView
  );
  const selectedFiles: FilePhotoRightDetail[] = useAppSelector(
    (state) => state.filePhotoRightList.selectedFiles
  );
  const selectedAllFiles: boolean = useAppSelector(
    (state) => state.filePhotoRightList.selectedAllFiles
  );
  const [addOrEditFileOpen, setAddOrEditFileOpen] = useState<boolean>(false);
  const markupFileData: IMarkupData = useAppSelector(
    (state) => state.fileEdit?.markupFileData || {}
  );
  const { _t } = useTranslation();
  const fileSupportAccess = checkModuleAccessByKey(
    defaultConfig.file_support_key
  );
  const { date_format }: GSettings = getGSettings();
  const dateFormat = useDateFormatter();
  const dispatch = useAppDispatch();

  const [activeKeys, setActiveKeys] = useState<number[]>([]);

  const handleTimelineItemCollapse = (id: number) => {
    const index = activeKeys.indexOf(id);
    if (index > -1) {
      setActiveKeys(activeKeys.filter((key) => key !== id));
    } else {
      setActiveKeys([...activeKeys, id]);
    }
  };

  const handleSelectImage = (
    selectedFile: FilePhotoRightDetail,
    is_checked: boolean
  ) => {
    dispatch(
      toggleSelectedFiles({
        selectedFile,
        is_file_checked: !is_checked,
        currentView: "timeline",
      })
    );
  };

  const RenderData = ({
    index,
    element,
    item,
    sameDateMultipleUserIndex,
  }: IRenderComponent) => {
    const displayDate = dateFormat({
      date: item?.[0],
      dateFormat: getFormat(date_format),
      format: defaultDateTimeFormat,
    });
    const options: Intl.DateTimeFormatOptions = {
      weekday: "long",
      month: "short",
      day: "2-digit",
      year: "numeric",
    };
    const photoExtensions = [
      "HEIC-SEQUENCE",
      "HEIC",
      "HEIF",
      "JPG",
      "JPEG",
      "PNG",
      "GIF",
      "BMP",
      "WEBP",
      "JFIF",
    ];
    const isPhoto = (fileExt: string) =>
      photoExtensions.includes(fileExt.toUpperCase());

    const photoCount = element.files.filter((file) =>
      isPhoto(file.file_ext)
    ).length;
    const fileCount = element.files.filter(
      (file) => !isPhoto(file.file_ext)
    ).length;

    return (
      <>
        {element.userInfo ? (
          <>
            {sameDateMultipleUserIndex === 0 && (
              <div className="flex items-center gap-1.5" key={index}>
                <Typography
                  className={` text-base font-semibold ${
                    !activeKeys.includes(index)
                      ? "text-[#1c2c49]"
                      : "text-primary-900"
                  }`}
                >
                  {new Date(displayDate).toLocaleDateString("en-US", options)}
                </Typography>
                <ButtonWithTooltip
                  className="!w-[22px] !h-[22px] hover:!bg-primary-900/15"
                  icon="fa-regular fa-chevron-up"
                  iconClassName={`${
                    !activeKeys.includes(index)
                      ? "!text-primary-900"
                      : "rotate-180 text-[#1c2c49]"
                  }`}
                  onClick={() => handleTimelineItemCollapse(index)}
                  tooltipTitle={
                    !activeKeys.includes(index) ? _t("Collapse") : _t("Expand")
                  }
                  tooltipPlacement="top"
                />
              </div>
            )}
            {!activeKeys.includes(index) && (
              <div className="sm:ease-in-out sm:duration-300">
                <ul className="mt-2 -ml-[33px]">
                  <li>
                    {element.userInfo && (
                      <div className="flex items-center gap-2 pl-px">
                        <AvatarProfile
                          user={{
                            image: element.userInfo?.user_icon,
                            name: element.userInfo?.name,
                          }}
                          className="w-6 h-6"
                          iconClassName="text-xs font-medium"
                        />
                        <Typography className="text-primary-900 text-13 font-semibold dark:text-white/90">
                          {element.userInfo?.name} added{" "}
                          {photoCount > 0 && (
                            <>
                              {photoCount}{" "}
                              {photoCount === 1 ? "Photo" : "Photos"}
                            </>
                          )}
                          {photoCount > 0 && fileCount > 0 && " & "}
                          {fileCount > 0 && (
                            <>
                              {fileCount} {fileCount === 1 ? "File" : "Files"}
                            </>
                          )}
                        </Typography>
                      </div>
                    )}
                    {element.files?.length && (
                      <>
                        <div className="pl-[34px] mt-2">
                          {timelineView.fileStructure === "detail" ? (
                            <div>
                              <div className="common-card p-2 bg-white dark:bg-dark-800 rounded">
                                <DetailsView
                                  imageData={element.files}
                                  currentView="timeline"
                                  selectedFiles={selectedFiles}
                                  selectedAllFiles={selectedAllFiles}
                                  changeAddOrEditFileOpen={
                                    changeAddOrEditFileOpen
                                  }
                                  changeMarkupModelOpen={changeMarkupModelOpen}
                                  changeConfirmModelOpen={
                                    changeConfirmModelOpen
                                  }
                                  changeSendEmailDrawerOpen={
                                    changeSendEmailDrawerOpen
                                  }
                                />
                              </div>
                            </div>
                          ) : (
                            <LightGalleryModel
                              zoom={true}
                              thumbnail={true}
                              backdropDuration={150}
                              showZoomInOutIcons={true}
                              actualSize={false}
                              mode="lg-slide"
                              alignThumbnails="left"
                              className="flex gap-[15px] flex-wrap"
                              mousewheel={true}
                            >
                              <div className="w-full grid gap-2.5 2xl:grid-cols-6 xl:grid-cols-5 lg:grid-cols-4 md:grid-cols-2">
                                {element.files
                                  ?.filter((imageData) => imageData.file_path) // Filter applied here
                                  .map(
                                    (
                                      imageData: FilePhotoRightDetail,
                                      index: number
                                    ) => {
                                      const selectedTags: string[] =
                                        imageData?.tag_names &&
                                        typeof imageData.tag_names === "string"
                                          ? imageData.tag_names
                                              .trim()
                                              .split(",")
                                              .map((tag) => tag.trim())
                                          : [];
                                      return (
                                        <GalleryFilePhotos
                                          key={index}
                                          imageData={imageData}
                                          selectedFiles={selectedFiles}
                                          handleSelectImage={handleSelectImage}
                                          selectedAllFiles={selectedAllFiles}
                                          changeAddOrEditFileOpen={
                                            changeAddOrEditFileOpen
                                          }
                                          changeMarkupModelOpen={
                                            changeMarkupModelOpen
                                          }
                                          changeConfirmModelOpen={
                                            changeConfirmModelOpen
                                          }
                                          changeSendEmailDrawerOpen={
                                            changeSendEmailDrawerOpen
                                          }
                                          selectedTags={selectedTags}
                                          isReadOnly={
                                            module_access === "read_only"
                                          }
                                          dispatch={dispatch}
                                          markupFileData={markupFileData}
                                          module_access={module_access}
                                          fileSupportAccess={fileSupportAccess}
                                          date_format={date_format}
                                          isFileAndPhotoModule={true}
                                        />
                                      );
                                    }
                                  )}
                              </div>
                            </LightGalleryModel>
                          )}
                        </div>
                      </>
                    )}
                  </li>
                </ul>
              </div>
            )}
          </>
        ) : (
          <>
            {element.files?.length && (
              <>
                <div>
                  {timelineView.fileStructure === "detail" ? (
                    <div>
                      <div className="common-card p-2 bg-white dark:bg-dark-800 rounded">
                        <DetailsView
                          imageData={element.files}
                          currentView="timeline"
                          selectedFiles={selectedFiles}
                          selectedAllFiles={selectedAllFiles}
                        />
                      </div>
                    </div>
                  ) : (
                    <div className="w-full grid gap-2.5 2xl:grid-cols-6 xl:grid-cols-5 lg:grid-cols-4 md:grid-cols-2">
                      {element.files
                        ?.filter((imageData) => imageData.file_path) // Filter applied here
                        .map((imageData: FilePhotoRightDetail) => {
                          return (
                            <GalleryFilePhotos
                              imageData={imageData}
                              selectedFiles={selectedFiles}
                              handleSelectImage={handleSelectImage}
                              selectedAllFiles={selectedAllFiles}
                              isReadOnly={module_access === "read_only"}
                              dispatch={dispatch}
                              markupFileData={markupFileData}
                              module_access={module_access}
                              fileSupportAccess={fileSupportAccess}
                              date_format={date_format}
                              isFileAndPhotoModule={true}
                            />
                          );
                        })}
                    </div>
                  )}
                </div>
              </>
            )}
          </>
        )}
      </>
    );
  };
  if (isEmpty(fileList) || !(fileList as IFilePhotoRight).fileTimelineList) {
    return <></>;
  }
  const handleAddNewFile = () => {
    setAddOrEditFileOpen(true);
  };

  return (
    <>
      {!isInfiniteScrollLoading &&
      fileList &&
      Object.keys(fileList.fileTimelineList.data).length === 0 ? (
        <div className="h-[calc(100vh-220px)] flex items-center justify-center overflow-hidden">
          <UploadFilesInNoData handleAddNewFile={handleAddNewFile} />
        </div>
      ) : (
        <InfiniteScroll
          loadMore={loadMore}
          hasMore={hasMore}
          loadingComponent={
            <div
              className={`${
                Object.keys(fileList.fileTimelineList.data).length === 0
                  ? "-mt-[17px] -ml-[5px]"
                  : "-mt-5"
              }`}
            >
              <TimelineViewLoader
                galleryView={!(timelineView.fileStructure === "detail")}
                skeleton={
                  Object.keys(fileList.fileTimelineList.data).length === 0
                    ? 5
                    : 1
                }
              />
            </div>
          }
          isLoading={isInfiniteScrollLoading}
          hideLoadingComponent={infiniteScrollHideLoadingComponent}
        >
          <TimeLine
            className={`${
              infiniteScrollHideLoadingComponent ? "py-0" : "py-1.5"
            } px-1.5 w-full`}
            mode="left"
          >
            {Object.entries(
              (fileList as IFilePhotoRight).fileTimelineList.data
            ).map((item: [string, FilesTimelineDateId], index: number) => {
              // Sorting the elements based on userInfo.name
              const sortedElements = Object.values(item[1]).sort((a, b) => {
                const nameA = a?.userInfo?.name || "";
                const nameB = b?.userInfo?.name || "";
                return nameA.localeCompare(nameB);
              });

              return (
                <Timeline.Item
                  key={`timeline-${index}-${item[0]}`}
                  dot={
                    <div
                      className={`time-line-cicle w-[22px] h-[22px] bg-[#F1F4FB] transition-all duration-300 ease-in-out border border-solid rounded-full relative before:absolute before:w-2.5 before:h-2.5 before:rounded-full before:top-1/2 before:left-1/2 before:-translate-y-1/2 before:-translate-x-1/2 before:transition-all before:duration-300 before:ease-in-out dark:bg-dark-800 ${
                        !activeKeys.includes(index)
                          ? "before:bg-primary-900 border-primary-900 dark:before:bg-white dark:border-white"
                          : "before:bg-[#8994A9] border-gray-300"
                      }`}
                    ></div>
                  }
                >
                  {sortedElements.map(
                    (element: IFilesTimelineFileList, key: number) => {
                      return (
                        <RenderData
                          key={`data-${index}-${key}`}
                          index={index}
                          element={element}
                          item={item}
                          sameDateMultipleUserIndex={key}
                        />
                      );
                    }
                  )}
                </Timeline.Item>
              );
            })}
          </TimeLine>
        </InfiniteScroll>
      )}
      {addOrEditFileOpen && (
        <AddOrEditFile
          addOrEditFileOpen={addOrEditFileOpen}
          setAddOrEditFileOpen={setAddOrEditFileOpen}
          moduleId={module_id}
        />
      )}
    </>
  );
};

export default memo(TimelineView);
