import { useEffect, useState } from "react";
import { FileAndPhoto } from "~/modules/document/fileAndPhoto";
import TimelineView from "./TimeLineView";

import { getFolderList } from "~/modules/document/fileAndPhoto/redux/action/filePhotoLeftAction";
import {
  deleteFiles,
  getFileList,
} from "~/modules/document/fileAndPhoto/redux/action/filePhotoRightAction";
import { FilePhotoHeader } from "./components/filePhotoHeader/FilePhotoHeader";
import { useAppDispatch, useAppSelector } from "./redux/store";
import debounce from "lodash/debounce";
import AddOrEditFile from "./sidebar/AddOrEditFile";

// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// molecules
import { MarkupModal } from "~/shared/components/molecules/markupModal";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { SendEmailDrawer } from "~/shared/components/molecules/sendEmailDrawer";
import { CommonModal } from "~/shared/components/molecules/commonModal";
import { IconButton } from "~/shared/components/molecules/iconButton";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
import { ReadOnlyPermissionMsgForFileSupportedFormat } from "~/shared/components/molecules/readOnlyPermissionMsgForFileSupportedFormat";
import { PdfModal } from "~/shared/components/molecules/pdfModal";
import { ReadOnlyPermissionMsg } from "~/shared/components/molecules/readOnlyPermissionMsg";

// Other
import {
  getGConfig,
  getGModuleFilters,
  getGSettings,
  useGModules,
} from "~/zustand";
import { uploadFile } from "~/redux/action/fileAttachmentAction";
import {
  setImageDataRedux,
  setAddOrEditFileOpenRedux,
  setIsConfirmModalOpenRedux,
  setIsMarkupModalOpenRedux,
  deleteFilesFromList,
  setSendEmailDrawerOpenRedux,
  callAgainFileList as setCallAgainFileList,
  setPunchListPdfModal,
  setCommonModal,
} from "./redux/slices/filePhotoRightSlice";
import { updateFileCount } from "~/modules/document/fileAndPhoto/redux/slices/filePhotoLeftSlice";

import { updateMarkupFile } from "~/redux/action/fileEditAction";
import { defaultConfig } from "~/data";
import {
  setActiveField,
  setSelectedContact,
} from "~/redux/slices/sendEmailSlice";
import { sendCommonEmailApi } from "~/redux/action/sendEmailAction";
import {
  incrementFileListPage,
  incrementFileTimelineListPage,
  setSuccessForDetailView,
} from "./redux/slices/filePhotoRightSlice";
import { putFilesToSignedUrl } from "~/shared/utils/helper/putFilesToSignedUrl";
import { useFilePreview } from "~/shared/hooks/useFilePreview";
import { getMimeType } from "~/shared/utils/helper/getMimeType";
import isEmpty from "lodash/isEmpty";
import FilePhotoPath from "./filePhotoView/FilePhotoPath";
import { type RadioChangeEvent } from "antd";
import {
  changeThumbnailOrDetail,
  changeView,
  setSelectedStructure,
} from "./redux/slices/filePhotoViewSlice";
import { getFolderListAfterFileAdd } from "./utils/getFolderListAfterFileAdd";
import { useTranslation } from "~/hook";

const ManageFilePhotos = () => {
  const { _t } = useTranslation();
  const dispatch = useAppDispatch();
  const [tab, setTab] = useState<string>("files_by_thumbnail_view");
  const [markupedFileData, setMarkupedFileData] = useState({});

  const selectedProjectDropbox: ISelectedFolder = useAppSelector(
    (state) => state.filePhotoFolderList.selectedFolder
  );
  const {
    isPunchListPdfViewOpen,
    selectedFileTitle,
    selectedFileUrl,
    isCommonModalOpen,
    selectedFilePath,
  }: IFilePhotoRight = useAppSelector((state) => state.filePhotoRightList);
  const [timelinefileSearchDebounce, setTimeLineFileSearchDebounce] =
    useState<ITimeLineFileSearchDebounceProps>({
      searchFileDebounce: "",
      FilePage: 0,
    });
  const [folderSearchDebounce, setFolderSearchDebounce] =
    useState<IFolderSearchDebounceProps>({
      searchFolderDebounce: "",
      FolderFilePage: 0,
    });

  const { retain_original_image }: GSettings = getGSettings();
  const { checkModuleAccessByKey } = useGModules();

  // state for modal start
  const [isFileLoader, setIsFileLoader] = useState<boolean>(false);
  const [isNoLoading, setIsNoLoading] = useState<boolean>(false);
  const [mobileMenu, setMobileMenu] = useState<boolean>(false);
  const [updatedData, setUpdatedData] = useState<
    IgetUpdatedFileRes | undefined
  >(undefined);
  const [markupLoading, setMarkupLoading] = useState(false);

  // state for modal end

  const {
    fileTimelineList,
    fileList,
    headerDebounceSearch,
    callAgainFileList,
    search,
    detailViewLength,
  }: IFilePhotoRight = useAppSelector((state) => state.filePhotoRightList);
  const filter = getGModuleFilters() as
    | Partial<IFiledFilePhotoFilter>
    | undefined;

  const selectView: IFilePhotoViewSlice = useAppSelector(
    (state) => state.filePhotoView
  );
  const currentView: IFilePhotoViewSlice["currentView"] = useAppSelector(
    (state) => state.filePhotoView.currentView
  );

  const fileSupportAccess = checkModuleAccessByKey(
    defaultConfig.file_support_key
  );

  const {
    isfileLoading,
    imageData,
    editView,
    isConfirmModalOpen,
    isMarkupModalOpen,
    addOrEditFileOpen,
    sendEmailDrawerOpen,
    selectedFiles,
  }: IFilePhotoRight = useAppSelector((state) => state.filePhotoRightList);

  const { isFolderLoading, callAgainFolderList }: IFilePhotoLeftInitialState =
    useAppSelector((state) => state.filePhotoFolderList);
  const { selectedFolder, folderList }: IFilePhotoLeftInitialState =
    useAppSelector((state) => state.filePhotoFolderList);
  const isCurrentViewTimeline = selectView.currentView === "timeline";
  const isFolderDetailView = selectView.folderView.fileStructure === "detail";
  const isTimelineDetailView =
    selectView.timelineView.fileStructure === "detail";
  const isFolderView = selectView.currentView === "folder";
  const isCurrentViewFolder = selectView.folderView.fileStructure;
  const isShowDetailsViewIcon =
    (isFolderView && isFolderDetailView) ||
    (isCurrentViewTimeline && isTimelineDetailView);
  const isCurrentView = selectView.folderView.fileStructure;
  const {
    module_id: currentModuleId,
    module_access,
    module_key,
  }: GConfig = getGConfig();
  const { date_format, image_resolution, save_a_copy_of_sent_pdf }: GSettings =
    getGSettings();
  // modal code start
  const FILE_PHOTOS_VIEW_TAB = [
    {
      label: (
        <FontAwesomeIcon
          className="text-base w-3.5 h-3.5"
          icon="fa-regular fa-list-tree"
        />
      ),
      value: "timeline_view",
      content: "Timeline View",
      isShow: isFolderView,
      enabled:
        (isEmpty(selectedFolder) &&
          folderList.length &&
          ((!isFolderDetailView && fileList.data.length) ||
            (isFolderDetailView && detailViewLength !== 0))) ||
        (isCurrentViewTimeline && !isEmpty(fileTimelineList.data)),
    },
    {
      label: (
        <FontAwesomeIcon
          className="text-base w-3.5 h-3.5"
          icon="fa-regular fa-folder"
        />
      ),
      value: "files_by_project",
      content: "View files by project folder",
      isShow: !isFolderView,
      enabled:
        (isEmpty(selectedFolder) &&
          folderList.length &&
          ((!isFolderDetailView && fileList.data.length) ||
            (isFolderDetailView && detailViewLength !== 0))) ||
        (isCurrentViewTimeline && !isEmpty(fileTimelineList.data)),
    },
    {
      label: (
        <FontAwesomeIcon
          className="text-base w-3.5 h-3.5"
          icon="fa-regular fa-list"
        />
      ),
      value: "files_by_detailed_view",
      content: "View files by detailed view",
      isShow: !isShowDetailsViewIcon,
      enabled:
        (isCurrentViewFolder &&
          ((!isFolderDetailView && fileList.data.length) ||
            (isFolderDetailView && detailViewLength !== 0))) ||
        (isCurrentViewTimeline && !isEmpty(fileTimelineList.data)),
    },
    {
      label: (
        <FontAwesomeIcon
          className="text-base w-3.5 h-3.5"
          icon="fa-regular fa-table-cells-large"
        />
      ),
      value: "files_by_thumbnail_view",
      content: "View files by thumbnail view",
      isShow: isShowDetailsViewIcon,
      enabled:
        (isCurrentViewFolder &&
          ((!isFolderDetailView && fileList.data.length) ||
            (isFolderDetailView && detailViewLength !== 0))) ||
        (isCurrentViewTimeline && !isEmpty(fileTimelineList.data)),
    },
  ];

  const filtered = Object.fromEntries(
    Object.entries(filter || {})
      .filter(([key, value]) => {
        if (value === "") return false;
        if (key === "showAllFiles" && value !== "1") return false;
        return true;
      })
      .map(([key, value]) => {
        if (key === "tags" && typeof value === "string") {
          const tagsArray = value
            .split(",")
            .map((v) => v.trim())
            .filter(Boolean);
          return [key, tagsArray];
        }
        return [key, value];
      })
  );

  const closeModalHandler = () => {
    dispatch(setIsMarkupModalOpenRedux(false));
    dispatch(setImageDataRedux({}));
    setPreviewUrlOpen(false);
    setMarkupLoading(false);
  };

  const handleSave = async (
    info: ISavedImageData | IPdfjsExpressPropsOnSaveValue
  ) => {
    setMarkupLoading(true);
    onAcceptOrDecline(info);
    if (!Object.keys(imageData).length) {
      return;
    }
  };

  const updateFile = async (
    fetchFileDetails: IFilePhotoRight,
    info: ISavedImageData | IPdfjsExpressPropsOnSaveValue,
    signedUrl: string
  ) => {
    const urlObject = new URL(signedUrl);
    const baseUrl = `${urlObject.origin}${urlObject.pathname}`;
    dispatch(setSuccessForDetailView(true));

    const response = await dispatch(
      updateMarkupFile({
        projectId:
          fetchFileDetails.project_id && fetchFileDetails.project_id > 0
            ? fetchFileDetails.project_id
            : undefined,
        fileUrl: baseUrl,
        fileId: fetchFileDetails.image_id,
        annotationData: "xfdf" in info ? info.xfdf : undefined,
      })
    );
    setMarkupedFileData(response?.payload?.data);
  };

  const onAcceptOrDecline = async (
    info: ISavedImageData | IPdfjsExpressPropsOnSaveValue
  ) => {
    const saveMarkupFileInfo = info as ISavedImageData;
    try {
      let fullFileName;
      try {
        fullFileName = decodeURI(
          saveMarkupFileInfo.fullName ??
            `${(imageData as FilePhotoRightDetail)?.file_name}.${
              (imageData as FilePhotoRightDetail)?.file_ext
            }`
        );
      } catch (error) {
        // If decodeURI fails, use the raw filename without decoding
        fullFileName =
          saveMarkupFileInfo.fullName ??
          `${(imageData as FilePhotoRightDetail)?.file_name}.${
            (imageData as FilePhotoRightDetail)?.file_ext
          }`;
        notification.error({
          description: `Invalid file name`,
        });
      }
      const fileExt = getMimeType(
        (imageData as FilePhotoRightDetail)?.file_ext?.toString() ?? ""
      );
      const isThumbRequired = [
        "jpg",
        "jpeg",
        "png",
        "gif",
        "bmp",
        "webp",
      ].includes((imageData as FilePhotoRightDetail).file_ext?.toLowerCase());

      const response = (await dispatch(
        uploadFile({
          moduleName: module_key,
          fileName: fullFileName,
          fileType: fileExt,
          saveAsNew: retain_original_image ?? undefined,
          isThumbRequired: isThumbRequired,
          fileId: imageData?.image_id,
        })
      )) as { payload: IGetUploadFileRes };

      if (response?.payload?.data?.signedUrl) {
        const signedUrl = response.payload.data.signedUrl;
        await putFilesToSignedUrl(
          signedUrl,
          info as ISavedImageData | IPdfjsExpressPropsOnSaveValue
        );
        await updateFile(
          imageData as IFilePhotoRight,
          info as ISavedImageData | IPdfjsExpressPropsOnSaveValue,
          signedUrl
        );

        setMarkupedFileData(imageData);
        dispatch(setCallAgainFileList());
        setMarkupLoading(false);
      }
    } catch (error) {
      return error;
    } finally {
      setIsFileLoader(false);
      setIsNoLoading(false);
      dispatch(setIsConfirmModalOpenRedux(false));
      dispatch(setIsMarkupModalOpenRedux(false));
      dispatch(setImageDataRedux({}));
      setMarkupLoading(false);
    }
  };

  const handleEmailApiCall = async (
    tempFormData: SendEmailFormDataWithApiDefault,
    closeSendMailSidebar: () => void,
    ccMailCopy: boolean
  ) => {
    const formData: ISendEmailFormDataWithApiDefaultNew = {
      ...tempFormData,
      send_me_copy: ccMailCopy ? 1 : 0,
      send_custom_email: 0,
      op: "pdf_files",
    };
    try {
      const responseApi = (await sendCommonEmailApi(
        formData
      )) as ISendEmailCommonRes;
      if (responseApi?.success) {
        closeSendMailSidebar();
        dispatch(setSelectedContact("reset"));
        dispatch(setActiveField(defaultConfig.employee_key));
      }
      if (!responseApi?.success) {
        notification.error({
          description: responseApi.message,
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error).message || "",
      });
    }
    closeSendMailSidebar();
  };

  const handleAccept = () => {
    onDelete();
  };

  const handleDecline = () => {
    dispatch(setIsConfirmModalOpenRedux(false));
    dispatch(setImageDataRedux({}));
  };

  const handleChangeView = (value: string) => {
    if (
      value === "files_by_detailed_view" ||
      value === "files_by_thumbnail_view"
    ) {
      dispatch(changeThumbnailOrDetail());
      dispatch(
        setSelectedStructure(
          isTimelineDetailView || isFolderDetailView ? "thumbnail" : "detail"
        )
      );
    } else if (value === "timeline_view" || value === "files_by_project") {
      dispatch(changeView());
      dispatch(setSelectedStructure(isCurrentView));
    }
  };

  const [isDeleteOrUpdateFileLoading, setIsDeleteOrUpdateFileLoading] =
    useState<boolean>(false);

  const changeConfirmModelOpen = (
    value: boolean,
    imageData: FilePhotoRightDetail
  ) => {
    dispatch(setImageDataRedux(imageData));
    dispatch(setIsConfirmModalOpenRedux(value));
    if (!value) {
      dispatch(setImageDataRedux({}));
    }
  };

  const {
    handleFilePreview,
    previewUrlOpen,
    previewUrl,
    previewUrlLoding,
    setPreviewUrlOpen,
  } = useFilePreview();

  const changeMarkupModelOpen = (
    value: boolean | "previewUrl",
    imageData: FilePhotoRightDetail
  ) => {
    if (value === "previewUrl") {
      handleFilePreview(imageData.file_path ?? "");
    } else {
      dispatch(setImageDataRedux(imageData));
      dispatch(setIsMarkupModalOpenRedux(value));
      if (!value) {
        dispatch(setImageDataRedux({}));
      }
    }
  };

  const changeAddOrEditFileOpen = (
    { addOrEditFileOpen, editView }: IActionPayloadSetAddOrEditFileOpenRedux,
    imageData: FilePhotoRightDetail
  ) => {
    dispatch(setImageDataRedux(imageData));
    dispatch(
      setAddOrEditFileOpenRedux({
        addOrEditFileOpen: addOrEditFileOpen,
        editView: editView,
      })
    );
    if (!addOrEditFileOpen) {
      dispatch(setImageDataRedux({}));
    }
  };

  const changeSendEmailDrawerOpen = (
    sendEmailDrawerOpen: boolean,
    imageData: FilePhotoRightDetail
  ) => {
    dispatch(setImageDataRedux(imageData));
    dispatch(setSendEmailDrawerOpenRedux(sendEmailDrawerOpen));
    if (!sendEmailDrawerOpen) {
      dispatch(setImageDataRedux({}));
    }
  };

  const onDelete = async () => {
    setIsDeleteOrUpdateFileLoading(true);
    setIsFileLoader(true);
    const response = (await deleteFiles({
      fileIds: [(imageData as FilePhotoRightDetail).image_id],
    })) as IFilePhotoDeleteApiRes;
    if (response?.success) {
      dispatch(setSuccessForDetailView(true));
      dispatch(setIsConfirmModalOpenRedux(false));
      setIsFileLoader(false);
      if (isCurrentViewFolder) {
        const fileLength =
          isCurrentViewFolder === "detail"
            ? detailViewLength ?? 0
            : Number(selectedProjectDropbox?.total_file_count) ?? 0;

        dispatch(setSuccessForDetailView(true));
        dispatch(setIsConfirmModalOpenRedux(false));

        dispatch(
          deleteFilesFromList({
            file_id: (imageData as FilePhotoRightDetail).image_id,
          })
        );
        if (selectedProjectDropbox?.next_request_type === "2") {
          getFolderListAfterFileAdd(dispatch, selectedFolder);
        } else {
          dispatch(
            updateFileCount({
              selectedFolder: selectedProjectDropbox,
              selectedProject: selectedProjectDropbox.project_id,
              fileLength: fileLength ? fileLength - 1 : 0,
              action: "addOrDeleteOrUpdate",
            })
          );
        }
      }
    } else {
      notification.error({
        description: response?.message,
      });
    }
    dispatch(setIsConfirmModalOpenRedux(false));
    dispatch(setImageDataRedux({}));
  };

  // modal code end

  useEffect(() => {
    const data = {
      type: 1,
      allProjectCheck: 0,
      globalProject: "",
      version: "web",
      from: "panel",
      tz: "+5:30",
      tzid: "Asia/Calcutta",
      moduleId: null,
      filter: filtered,
      search:
        HTMLEntities.encode(headerDebounceSearch?.trim()) ||
        filter?.search?.trim() ||
        undefined,
    };
    const promise = dispatch(getFolderList(data));
    return () => {
      // `createAsyncThunk` attaches an `abort()` method to the promise
      promise.abort();
    };
  }, [JSON.stringify(filter), headerDebounceSearch, callAgainFolderList]);

  const loadMore = () => {
    dispatch(incrementFileTimelineListPage());
  };

  const {
    next_request_type,
    module_id,
    project_id,
    folder_id,
    module_key: module_key_folder,
  } = selectedFolder;

  useEffect(() => {
    if (
      selectView.folderView.fileStructure !== "detail" ||
      currentView === "timeline"
    ) {
      let getFileListParams: IGetFileListParam = {
        search:
          HTMLEntities.encode(headerDebounceSearch?.trim()) ||
          filter?.search?.trim() ||
          undefined,
        page:
          selectView.currentView === "folder"
            ? fileList.page
            : fileTimelineList.page,
        limit: 40,
        type: next_request_type ?? 1,
        allProjectCheck: 0,
        from: "panel",
        isTimelineView: selectView.currentView === "folder" ? 0 : 1,
        clientOnlyFiles: false,
        onlyImage: 0,
        staticFolder: selectedFolder.module_key === "static" ? 1 : 0,
        projectId: project_id ?? "",
        module_id: next_request_type === "2" ? null : module_id,
        folderId: next_request_type === "2" ? module_id : folder_id,
        filter: filtered,
      };

      if (selectView.currentView === "folder" && fileList.sort) {
        getFileListParams["sortOrder"] =
          fileList.sort === "by_date" ? "date_added" : "file_name";
      }

      const promise = dispatch(
        getFileList({
          ...getFileListParams,
        })
      );
      return () => {
        // `createAsyncThunk` attaches an `abort()` method to the promise
        promise.abort();
      };
    }
  }, [
    headerDebounceSearch,
    fileList.changeSort,
    filter,
    callAgainFileList,
    fileList.page,
    fileTimelineList.page,
    selectView.currentView,
    selectView.folderView.fileStructure,
    updatedData?.data,
  ]);

  const FolderloadMore = () => {
    dispatch(incrementFileListPage());
  };

  const folderView: IFilePhotoViewSlice["folderView"] = useAppSelector(
    (state) => state.filePhotoView.folderView
  );

  const handleSearch = debounce(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      if (isCurrentViewFolder) {
        setFolderSearchDebounce((prev) => ({
          ...prev,
          FolderFilePage: 0,
          searchFolderDebounce: event.target.value,
        }));
      } else {
        setTimeLineFileSearchDebounce((prev) => ({
          ...prev,
          projectPage: 0,
          searchFileDebounce: event.target.value,
        }));
      }
    },
    1000
  );
  return (
    <>
      <div
        className={`md:pt-[41px] pt-10 hover-scroll overflow-y-auto overflow-hidden ${
          module_access === "read_only"
            ? "md:h-[calc(100dvh-112px)] h-[calc(100dvh-112px)]"
            : "md:h-[calc(100dvh-143px)] h-[calc(100dvh-112px)]"
        }`}
      >
        <FilePhotoHeader handleSearch={handleSearch} />
        <ReadOnlyPermissionMsg view={module_access === "read_only"} />
        {module_access !== "read_only" && (
          <ReadOnlyPermissionMsgForFileSupportedFormat
            view={fileSupportAccess === "read_only"}
          />
        )}
        <div className="pt-2 px-4">
          <div className="flex h-7 items-center justify-between mb-2">
            <div className="sm:w-fit w-[calc(100%-100px)] md:block hidden whitespace-nowrap header-breadcrumb">
              {!folderView.viewAllFiles && isFolderView && (
                <FilePhotoPath title={"Projects & Opportunities"} />
              )}
            </div>
            <IconButton
              htmlType="button"
              variant="default"
              className="md:!hidden group/module-menu relative w-[34px] h-[28px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
              iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
              icon="fa-regular fa-bars"
              onClick={() => setMobileMenu(!mobileMenu)}
            />
            <ListTabButton
              value={tab}
              options={FILE_PHOTOS_VIEW_TAB}
              selectedFiles={selectedFiles.length > 0}
              className="min-w-10"
              onChange={(e: RadioChangeEvent) => {
                handleChangeView(e.target.value);
                setTab(e.target.value);
              }}
              activeclassName="!bg-[#F1F4F9]"
            />
            {/* NF:- https://app.clickup.com/t/2nvue3p need to confirmation */}
            {/* <div className="flex gap-2 items-center">
              {Object.keys(filter).some(
                (key) => key !== "search" || filter.search
              ) || search
                ? `${fileList?.filesCount} Files`
                : null}
              <ListTabButton
                value={tab}
                options={FILE_PHOTOS_VIEW_TAB}
                selectedFiles={selectedFiles.length > 0}
                className="min-w-10"
                onChange={(e: RadioChangeEvent) => {
                  handleChangeView(e.target.value);
                  setTab(e.target.value);
                }}
                activeclassName="!bg-[#F1F4F9]"
              />
            </div> */}
          </div>
          <div
            className={`pb-4 h-[calc(100dvh-199px)] hover-scroll overflow-y-auto overflow-hidden ${
              module_access === "read_only"
                ? "md:h-[calc(100dvh-246px)]"
                : "md:h-[calc(100dvh-230px)]"
            }`}
          >
            {currentView === "folder" && (
              <FileAndPhoto
                loadMore={FolderloadMore}
                mobileMenu={mobileMenu}
                changeAddOrEditFileOpen={changeAddOrEditFileOpen}
                changeMarkupModelOpen={changeMarkupModelOpen}
                changeConfirmModelOpen={changeConfirmModelOpen}
                changeSendEmailDrawerOpen={changeSendEmailDrawerOpen}
                handleClose={() => setMobileMenu(false)}
                markupedFileData={markupedFileData}
              />
            )}
            {currentView === "timeline" && (
              <TimelineView
                loadMore={loadMore}
                hasMore={fileTimelineList.infiniteFlieTimelineScrollhasMore}
                infiniteScrollHideLoadingComponent={fileTimelineList.error}
                isInfiniteScrollLoading={isfileLoading}
                changeAddOrEditFileOpen={changeAddOrEditFileOpen}
                changeMarkupModelOpen={changeMarkupModelOpen}
                changeConfirmModelOpen={changeConfirmModelOpen}
                changeSendEmailDrawerOpen={changeSendEmailDrawerOpen}
              />
            )}
          </div>
          {addOrEditFileOpen && (
            <AddOrEditFile
              addOrEditFileOpen={addOrEditFileOpen}
              editView={editView}
              setAddOrEditFileOpen={(value: boolean) => {
                dispatch(
                  setAddOrEditFileOpenRedux({ addOrEditFileOpen: value })
                );
                if (!value) {
                  dispatch(setImageDataRedux({}));
                }
              }}
              singleFileDetails={imageData as ISingleFileDetail}
              moduleId={module_id}
              setUpdatedData={setUpdatedData}
              selectedProjectDropbox={selectedProjectDropbox}
              isReadOnly={module_access === "read_only"}
            />
          )}
          {(isMarkupModalOpen || previewUrlOpen) && (
            <MarkupModal
              open={isMarkupModalOpen}
              closeModalHandler={closeModalHandler}
              onSave={handleSave}
              previewUrl={previewUrl ?? ""}
              previewUrlLoading={previewUrlLoding}
              previewUrlOpen={previewUrlOpen}
              file={imageData as IAttachedFile}
              markupLoading={markupLoading}
            />
          )}
          {isConfirmModalOpen && (
            <ConfirmModal
              isOpen={isConfirmModalOpen}
              isLoading={isFileLoader}
              isNoLoading={isNoLoading}
              description={_t("Are you sure you want to delete this file?")}
              modalIcon="fa-regular fa-trash-can"
              modaltitle={_t("Delete")}
              yesButtonLabel={_t("Yes")}
              noButtonLabel={_t("No")}
              onAccept={handleAccept}
              onDecline={handleDecline}
              onCloseModal={() => {
                dispatch(setIsConfirmModalOpenRedux(false));
                dispatch(setImageDataRedux({}));
              }}
              zIndex={9999}
            />
          )}
          {sendEmailDrawerOpen && (
            <SendEmailDrawer
              projectId={(imageData as FilePhotoRightDetail).project_id}
              closeDrawer={() => {
                dispatch(setActiveField(defaultConfig.employee_key));
                dispatch(setSendEmailDrawerOpenRedux(false));
                dispatch(setImageDataRedux({}));
              }}
              openSendEmailSidebar={sendEmailDrawerOpen}
              options={[
                defaultConfig.employee_key,
                "my_crew",
                defaultConfig.customer_key,
                defaultConfig.misc_contact_key,
                defaultConfig.contractor_key,
                defaultConfig.vendor_key,
                "by_service",
                "my_project",
              ]}
              singleSelecte={false}
              emailApiCall={handleEmailApiCall}
              groupCheckBox={false}
              customEmailData={{
                subject: "Project Details",
                files: [imageData],
              }}
              validationParams={{
                save_a_copy_of_sent_pdf,
                date_format,
                file_support_module_access: checkModuleAccessByKey(
                  defaultConfig.file_support_key
                ),
                image_resolution,
                module_key,
                module_id: currentModuleId,
                module_access,
              }}
              isShowAddIcon={false}
            />
          )}
          {isPunchListPdfViewOpen && (
            <PdfModal
              isOpen={isPunchListPdfViewOpen}
              modaltitle={selectedFileTitle}
              onCloseModal={() =>
                dispatch(
                  setPunchListPdfModal({
                    isPunchListPdfViewOpen: false,
                    selectedFileTitle: "",
                    selectedFileUrl: "",
                  })
                )
              }
              fileUrl={selectedFileUrl}
            />
          )}
          {isCommonModalOpen && (
            <CommonModal
              isOpen={isCommonModalOpen}
              widthSize="1000px"
              draggable={false}
              onCloseModal={() =>
                dispatch(
                  setCommonModal({
                    isCommonModalOpen: false,
                    selectedFileTitle: "",
                    selectedFilePath: "",
                  })
                )
              }
              modalBodyClass="p-0"
              header={{
                title: selectedFileTitle,
                closeIcon: true,
              }}
            >
              <div className="p-4 h-[calc(100vh-200px)]">
                <iframe
                  id="documentViewer"
                  data-src="'+zohodata['document_url']+'"
                  src={(() => {
                    const baseUrl = "https://drive.google.com/viewerng/viewer";
                    const params = new URLSearchParams({
                      embedded: "true",
                      origin: window.ENV.PANEL_URL,
                      url: selectedFilePath,
                    });

                    return `${baseUrl}?${params.toString()}`;
                  })()}
                  style={{ width: "100%", height: "100%" }}
                />
              </div>
            </CommonModal>
          )}
        </div>
      </div>
    </>
  );
};

export default ManageFilePhotos;
