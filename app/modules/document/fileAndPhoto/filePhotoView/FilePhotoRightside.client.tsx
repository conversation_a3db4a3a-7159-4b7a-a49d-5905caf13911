import React from "react";
import { forwardRef, useMemo, useState } from "react";
import { DropZone } from "~/modules/document/fileAndPhoto/components/dropZone";
import { GoogleDrivePicker } from "~/modules/document/fileAndPhoto/components/googleDrivePicker";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { LightGalleryModel } from "~/shared/components/atoms/lightGalleryModel";
// Molecules
import { GalleryFilePhotos } from "~/shared/components/molecules/galleryFilePhotos";
import { LoadingGallery } from "~/shared/components/molecules/loadingGallery";
import { VirtuosoGridCommon } from "~/shared/components/molecules/virtuosoGrid";
import { NoRecords } from "~/shared/components/molecules/noRecords";
// Other
import {
  toggleSelectedFiles,
  callAgainFileList,
} from "../redux/slices/filePhotoRightSlice";
import { useAppDispatch, useAppSelector } from "../redux/store";
import DropboxPicker from "../components/dropboxPicker/DropboxPicker";
import { deepCopyFiles, uploadLocalFile } from "../utils/uploadLocalFile";
import { getFileExtension } from "../utils/googleDrivePicker";
import { getGConfig, getGSettings, useGModules } from "~/zustand";
import UploadFilesInNoData from "../components/noData/UploadFilesInNoData.client";
import AddOrEditFile from "../sidebar/AddOrEditFile";
import { defaultConfig } from "~/data";
import { addFile, uploadFile } from "~/redux/action/fileAttachmentAction";
import isEmpty from "lodash/isEmpty";
import { type Accept, useDropzone } from "react-dropzone";
import axios from "axios";
import { updateFileCount } from "~/modules/document/fileAndPhoto/redux/slices/filePhotoLeftSlice";
import { useTranslation } from "~/hook";
import {
  acceptedFileTypes,
  acceptedFileTypesInNoAccessRole,
} from "~/modules/document/fileAndPhoto/utils/allowedFileExtension"; // Import the acceptedFileTypes from common file
import { notification } from "antd";
import {
  EVENT_LOGGER_ACTION,
  EVENT_LOGGER_NAME,
} from "~/shared/constants/event-logger";

const gridComponents = {
  List: forwardRef<HTMLDivElement, IFilePhotoVirtusoListProps>(
    ({ style, children, ...props }, ref) => (
      <LightGalleryModel
        zoom={true}
        thumbnail={true}
        backdropDuration={150}
        showZoomInOutIcons={true}
        actualSize={false}
        mode="lg-slide"
        alignThumbnails="left"
        mousewheel={true}
      >
        <div
          ref={ref}
          className="flex gap-[15px] flex-wrap"
          style={{
            ...style,
          }}
        >
          {children}
        </div>
      </LightGalleryModel>
    )
  ),
  Item: forwardRef<HTMLDivElement, IFilePhotoVirtusoListProps>(
    ({ children, ...props }) => (
      <div {...props} className="virtuoso-grid file-folder-list-view">
        {children}
      </div>
    )
  ),
};

const FilePhotoRightSide = ({
  fileList,
  loadMore,
  hasMore,
  infiniteScrollHideLoadingComponent,
  isInfiniteScrollLoading,
  changeAddOrEditFileOpen,
  changeMarkupModelOpen,
  changeConfirmModelOpen,
  changeSendEmailDrawerOpen,
  markupedFileData,
}: FilesDefaultViewProps) => {
  const { _t } = useTranslation();
  const { module_id, module_access, module_key }: GConfig = getGConfig();
  const { date_format, image_resolution }: GSettings = getGSettings();
  const { checkModuleAccessByKey } = useGModules();
  const [isLoadingWhenNoData, setIsLoadingWhenNoData] =
    useState<boolean>(false);
  const [addOrEditFileOpen, setAddOrEditFileOpen] = useState<boolean>(false);

  const fileSupportAccess = checkModuleAccessByKey(
    defaultConfig.file_support_key
  );

  const selectedFiles: FilePhotoRightDetail[] = useAppSelector(
    (state) => state.filePhotoRightList.selectedFiles
  );

  const folderView: IFilePhotoViewSlice["folderView"] = useAppSelector(
    (state) => state.filePhotoView.folderView
  );
  const selectedAllFiles: boolean = useAppSelector(
    (state) => state.filePhotoRightList.selectedAllFiles
  );

  const uploadFileList: IUploadFileList[] = useAppSelector(
    (state) => state.filePhotoRightList.uploadFileList
  );

  const { selectedFolder, parentFolderName }: IFilePhotoLeftInitialState =
    useAppSelector((state) => state.filePhotoFolderList);

  const markupFileData: IMarkupData = useAppSelector(
    (state) => state.fileEdit?.markupFileData || {}
  );

  const folderList: ISelectedFolder[] = useAppSelector(
    (state) => state.filePhotoFolderList.folderList
  );

  const dispatch = useAppDispatch();

  // Extract all allowed extensions from acceptedFileTypes
  const allowUploadExtensions = useMemo(() => {
    const extensions: string[] = [];
    Object.entries(acceptedFileTypes).forEach(([mimeType, exts]) => {
      exts.forEach((ext) => {
        // Remove the dot prefix and add to extensions array if not already present
        const cleanExt = ext.replace(".", "");
        if (!extensions.includes(cleanExt)) {
          extensions.push(cleanExt);
        }
      });
    });
    return extensions;
  }, []);

  const onFileSelectFromClick = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files && event.target.files;

    if (files) {
      const validFiles = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const extension = getFileExtension(file.name).toLowerCase();
        if (allowUploadExtensions.includes(extension)) {
          validFiles.push(file);
        } else {
          notification.error({
            description: `Rejected: ${file.name} (Invalid file type or missing extension)`,
          });
        }
      }

      // Convert the array of Files into a FileList
      const dataTransfer = new DataTransfer();
      validFiles.forEach((file: File) => dataTransfer.items.add(file));
      uploadLocalFile(
        dispatch,
        selectedFolder,
        deepCopyFiles(dataTransfer.files),
        fileList,
        image_resolution,
        parentFolderName,
        fileSupportAccess
      );
    }
    event.target.value = "";
  };

  const handleSelectImage = (
    selectedFile: FilePhotoRightDetail,
    is_checked: boolean
  ) => {
    dispatch(
      toggleSelectedFiles({
        selectedFile,
        is_file_checked: !is_checked,
        currentView: "folder",
      })
    );
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    if (event.dataTransfer.files && event.dataTransfer.files.length > 0) {
      const files = event.dataTransfer.files;

      const validFiles = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const extension = getFileExtension(file.name).toLowerCase();

        if (allowUploadExtensions.includes(extension)) {
          validFiles.push(file);
        } else {
          notification.error({
            description: `Rejected: ${file.name} (Invalid file type or missing extension)`,
          });
        }
      }

      // Convert the array of Files into a FileList
      const dataTransfer = new DataTransfer();
      validFiles.forEach((file: File) => dataTransfer.items.add(file));

      if (files)
        uploadLocalFile(
          dispatch,
          selectedFolder,
          deepCopyFiles(dataTransfer.files),
          fileList,
          image_resolution,
          parentFolderName,
          fileSupportAccess
        );
      event.dataTransfer.clearData();
    }
  };

  const handleAddNewFile = () => {
    setAddOrEditFileOpen(true);
  };

  const handleUploadFileWhenNoData = async (filesData: File[]) => {
    // Ensure the filesData is an array before mapping
    if (!filesData || !Array.isArray(filesData)) return;
    // Use Promise.all to handle asynchronous operations within the map
    const newFilesArray = await Promise.all(
      filesData.map(async (file) => {
        // Extract or set file-specific information here, like fileName and fileExtension
        let fileName = file.name;
        const fileExtension = file.type;
        const extension = getFileExtension(file.name).toLowerCase();
        const thumbValue = file.type?.startsWith("image") ? true : false;
        const fileWithProps = file as File | Blob;
        // Dispatch the uploadFile action and await its response
        if (extension === "heic") {
          fileName = file.name.replace(/\.heic$/i, ".HEIC"); // Convert extension to uppercase
        }
        const response = (await dispatch(
          uploadFile({
            moduleName: module_key,
            fileName: fileName,
            fileType: fileExtension,
            saveAsNew: 1,
            isThumbRequired: thumbValue,
          })
        )) as { payload: IGetUploadFileRes };

        if (
          response &&
          response.payload &&
          response.payload.data &&
          response.payload.statusCode == 200
        ) {
          await fetch(response.payload.data?.fileUrl).then((res) => res.blob());
          let res;

          res = await axios.put(
            response.payload.data?.signedUrl,
            fileWithProps,
            {
              headers: {
                "Content-Type": fileWithProps.type, // Dynamically set content type
              },
            }
          );

          if (response.payload.data?.signedUrl) {
            const newFilesArray = [
              {
                file_url: response.payload.data?.fileUrl,
                is_image: thumbValue ? 1 : 0,
              },
            ];
            if (res?.status === 200) {
              const addFileRes = (await addFile({
                files: newFilesArray,
                module_id:
                  !isEmpty(selectedFolder) &&
                  selectedFolder?.project_id ===
                    defaultConfig.company_files_project_id
                    ? defaultConfig.company_files_folder_id
                    : selectedFolder?.project_id ===
                      defaultConfig.unassigned_files_folder_id
                    ? defaultConfig.unassigned_files_folder_id
                    : selectedFolder.module_id === null ||
                      selectedFolder.module_id == 0
                    ? 1
                    : selectedFolder.module_id ??
                      module_id ??
                      defaultConfig.unassigned_files_folder_id,
                folder_id: isEmpty(selectedFolder)
                  ? 0
                  : selectedFolder?.project_id ===
                      defaultConfig.company_files_project_id ||
                    selectedFolder?.project_id ===
                      defaultConfig.unassigned_files_project_id
                  ? 0
                  : selectedFolder.folder_id,
                project_id: isEmpty(selectedFolder)
                  ? 0
                  : selectedFolder?.project_id ===
                      defaultConfig.company_files_project_id ||
                    selectedFolder?.project_id ===
                      defaultConfig.unassigned_files_project_id
                  ? 0
                  : selectedFolder?.project_id,
                ...(!isEmpty(parentFolderName)
                  ? { file_tags: parentFolderName }
                  : {}),
                ...((!isEmpty(selectedFolder) &&
                  selectedFolder?.project_id ===
                    defaultConfig.company_files_project_id) ||
                selectedFolder?.project_id ===
                  defaultConfig.unassigned_files_project_id
                  ? {
                      static_folder: 1,
                    }
                  : {}),
                ...((!isEmpty(selectedFolder) &&
                  selectedFolder?.project_id ===
                    defaultConfig.company_files_project_id) ||
                selectedFolder?.project_id ===
                  defaultConfig.unassigned_files_project_id
                  ? {
                      module_name: selectedFolder?.project_id,
                    }
                  : {}),
                is_file_shared: 0,
              })) as IAddFileApiRes;
              if (addFileRes.success) {
                setIsLoadingWhenNoData(false);
                dispatch(callAgainFileList());
                EventLogger.log(
                  EVENT_LOGGER_NAME.files_photos + EVENT_LOGGER_ACTION.added,
                  1
                );
                if (fileSupportAccess === "no_access") {
                  const onlyImageData = filesData?.filter(
                    (item) => item.type?.startsWith("image") === true
                  );
                  dispatch(
                    updateFileCount({
                      selectedFolder: selectedFolder,
                      selectedProject: selectedFolder.project_id,
                      fileLength:
                        (Number(selectedFolder?.total_file_count) ?? 0) +
                        (onlyImageData?.length ?? 0),
                      action: "addOrDeleteOrUpdate",
                    })
                  );
                } else {
                  dispatch(
                    updateFileCount({
                      selectedFolder: selectedFolder,
                      selectedProject: selectedFolder.project_id,
                      fileLength:
                        (Number(selectedFolder?.total_file_count) ?? 0) +
                        (filesData?.length ?? 0),
                      action: "addOrDeleteOrUpdate",
                    })
                  );
                }
              }
            }
          }
        }
        // Return whatever structure you want for the newFilesArray
        return response.payload; // Assuming IGetUploadFileRes is the response payload
      })
    );
  };

  // Convert acceptedFileTypes to the format required by react-dropzone
  const dropzoneAcceptedTypes: Accept = useMemo(() => {
    const result: Accept = {};

    if (fileSupportAccess === "no_access") {
      Object.entries(acceptedFileTypesInNoAccessRole).forEach(
        ([mimeType, extensions]) => {
          result[mimeType] = extensions;
        }
      );
    } else {
      Object.entries(acceptedFileTypes).forEach(([mimeType, extensions]) => {
        result[mimeType] = extensions;
      });
    }

    return result;
  }, []);

  const onDropRejected = (fileRejections: any[]) => {
    setIsLoadingWhenNoData(false);
    const newRejectedFiles = fileRejections.map((rejection) => {
      const { file } = rejection;
      notification.error({
        description: `Rejected: ${file.name} (Invalid file type or missing extension)`,
      });
      return file;
    });
    // Update the state by adding new rejected files to the existing ones
  };

  const { getRootProps, getInputProps } = useDropzone({
    onDrop: (acceptedFiles) => {
      // Handle the files dropped here
      setIsLoadingWhenNoData(true);
      requestAnimationFrame(() => {
        // Proceed with file validation and uploading after ensuring UI is updated
        !addOrEditFileOpen && handleUploadFileWhenNoData(acceptedFiles);
      });
    },
    onDropRejected,
    accept: dropzoneAcceptedTypes,
    noClick: true, // To prevent the default file dialog from appearing on click
    noKeyboard: true, // To prevent the default keyboard handling
  });
  const shouldHideComponent =
    selectedFolder?.project_id === defaultConfig.company_files_project_id ||
    selectedFolder?.project_id === defaultConfig.unassigned_files_project_id;

  const fileListData = useMemo(() => {
    if (fileList?.data) {
      let uploadFileListJSX: IFileUploadListURL[] = [];
      if (uploadFileList && uploadFileList.length) {
        uploadFileListJSX = uploadFileList
          .map((file: IUploadFileList, key: number) => {
            return file.loading && { file_url: "loading" };
          })
          .filter(Boolean) as IFileUploadListURL[];
      }

      // Prepend the upload object to the existing file list
      const fileListData_ = [...uploadFileListJSX, ...fileList.data];
      const shouldHide =
        selectedFolder?.project_id === defaultConfig.company_files_project_id ||
        selectedFolder?.project_id ===
          defaultConfig.unassigned_files_project_id;
      if (module_access !== "read_only" && !shouldHide) {
        fileListData_.unshift({ file_url: "upload" });
      }
      // add loading skeleton using prop
      if (hasMore && fileList?.data.length) {
        fileListData_.push({ file_url: "loading" });
      }
      return fileListData_;
    }
    return [];
  }, [fileList?.data, JSON.stringify(uploadFileList)]);

  return (
    <>
      <div
        className={`grid items-start h-full ${
          folderView.viewAllFiles
            ? "gap-0 w-full"
            : `gap-3.5 xl:w-[calc(100%-376px)] md:w-[calc(100%-230px)] w-full`
        }`}
        {...getRootProps()}
      >
        {!isInfiniteScrollLoading &&
        !hasMore &&
        fileList &&
        fileList.data?.length === 0 &&
        uploadFileList.filter((file) => file.loading).length === 0 ? (
          <div
            className={`flex justify-center items-center ${
              fileList.data?.length === 0
                ? "h-full"
                : "2xl:h-[calc(100vh-375px)] xl:h-[calc(100vh-366px)] h-[calc(100vh-385px)]"
            }`}
          >
            {isLoadingWhenNoData && (
              <LoadingGallery skeleton={uploadFileList?.length} />
            )}
            {!isLoadingWhenNoData && !shouldHideComponent ? (
              <UploadFilesInNoData handleAddNewFile={handleAddNewFile} />
            ) : (
              <NoRecords
                className="mx-auto"
                image={`${window.ENV.CDN_URL}assets/images/no-records-files.svg`}
              />
            )}
          </div>
        ) : (
          <>
            {fileList &&
            fileList.data?.length === 0 &&
            hasMore &&
            !infiniteScrollHideLoadingComponent ? (
              <div className="grid gap-3.5 file-photos-folder-view">
                <LoadingGallery skeleton={40} />
              </div>
            ) : (
              <VirtuosoGridCommon<
                | FilePhotoRightDetail
                | {
                    file_url: string;
                  }
              >
                className={`w-full ${
                  !folderView.viewAllFiles
                    ? `w-full md:max-h-[calc(100dvh-240px)] h-[calc(100dvh-240px)] ${
                        fileList && fileList?.data?.length === 0
                          ? "md:overflow-hidden"
                          : "hover-scroll md:overflow-y-auto md:overflow-hidden"
                      }`
                    : ""
                }`}
                components={gridComponents}
                loadMore={loadMore}
                data={Array.isArray(fileListData) ? fileListData : []}
                itemContent={(index, imageData) => {
                  if (imageData.file_url === "upload") {
                    return (
                      <div key={index} className="w-full h-full">
                        <div className="grid gap-2.5 h-full">
                          <DropZone
                            onClick={onFileSelectFromClick}
                            onDrop={handleDrop}
                            isFileAndPhotoModule={true}
                            fileSupportAccess={fileSupportAccess}
                          />
                          <div className="grid grid-cols-2 gap-2.5">
                            <Tooltip title="This feature is temporarily disabled while Google is issuing the certificate. We hope to have it enabled soon">
                              <div className="opacity-40 cursor-no-drop">
                                <GoogleDrivePicker />
                              </div>
                            </Tooltip>
                            <DropboxPicker />
                          </div>
                        </div>
                      </div>
                    );
                  }

                  if (imageData.file_url === "loading") {
                    return (
                      <div key={index} className="w-full">
                        <LoadingGallery skeleton={1} />
                      </div>
                    );
                  }

                  imageData = imageData as FilePhotoRightDetail;

                  const selectedTags: string[] = !imageData.file_tags
                    ? []
                    : imageData?.tag_names && imageData.tag_names.trim().length
                    ? imageData.tag_names.trim().split(",")
                    : parentFolderName && parentFolderName.trim().length
                    ? parentFolderName.trim().split(",")
                    : [];
                  return (
                    <div key={imageData.image_id} className="w-full">
                      <GalleryFilePhotos
                        handleSelectImage={handleSelectImage}
                        selectedFiles={selectedFiles}
                        imageData={imageData}
                        selectedAllFiles={selectedAllFiles}
                        changeAddOrEditFileOpen={changeAddOrEditFileOpen}
                        changeMarkupModelOpen={changeMarkupModelOpen}
                        changeConfirmModelOpen={changeConfirmModelOpen}
                        changeSendEmailDrawerOpen={changeSendEmailDrawerOpen}
                        selectedTags={selectedTags}
                        isReadOnly={module_access === "read_only"}
                        dispatch={dispatch}
                        markupFileData={markupFileData}
                        module_access={module_access}
                        fileSupportAccess={fileSupportAccess}
                        date_format={date_format}
                        markupedFileData={markupedFileData}
                        isFileAndPhotoModule={true}
                      />
                    </div>
                  );
                }}
              />
            )}
          </>
        )}
        {fileList?.data?.length === 0 && isLoadingWhenNoData && (
          <LoadingGallery skeleton={uploadFileList?.length} />
        )}
        {addOrEditFileOpen && (
          <AddOrEditFile
            addOrEditFileOpen={addOrEditFileOpen}
            setAddOrEditFileOpen={setAddOrEditFileOpen}
            moduleId={module_id}
          />
        )}
      </div>
    </>
  );
};

export default FilePhotoRightSide;
