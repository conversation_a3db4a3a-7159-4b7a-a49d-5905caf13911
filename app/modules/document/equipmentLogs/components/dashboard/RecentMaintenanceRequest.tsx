// Atoms
import { Toolt<PERSON> } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import { StaticTableRowLoading } from "~/shared/components/molecules/staticTableRowLoading";
// Other
import { useTranslation } from "~/hook";

import { useEffect, useState } from "react";
import { useELAppDispatch, useELAppSelector } from "../../redux/store";
import { fetchEquipLogDashboardApi } from "../../redux/action/equipLogDashAction";
import { sanitizeString } from "~/helpers/helper";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { useNavigate } from "@remix-run/react";
import { routes } from "~/route-services/routes";

const RecentMaintenanceRequest = () => {
  const [isRecentMaintReqLoading, setIsRecentMaintReqLoading] =
    useState<boolean>(false);
  const { _t } = useTranslation();
  const navigate = useNavigate();
  const userAgent = navigator.userAgent || "";
  const isAndroid = userAgent.toLowerCase().includes("android");
  const isiOS = /iPhone|iPad|iPod/i.test(userAgent);
  const {
    repair_maintenance_request,
    repairMaintenanceRequestLastRefreshTime,
    isInitialLoad,
  } = useELAppSelector((state) => state.equipmentLogsDashboard);

  const dispatch = useELAppDispatch();
  const [rowData, setRowData] = useState<
    IRepairMaintenanceRequestCellRenderer[]
  >([]);
  const handleClickRefresh = async () => {
    setIsRecentMaintReqLoading(true);
    setRowData([]);
    await dispatch(
      fetchEquipLogDashboardApi({
        refreshType: "repair_maintenance_request",
      })
    );
    setIsRecentMaintReqLoading(false);
  };
  useEffect(() => {
    if (!isRecentMaintReqLoading && repair_maintenance_request) {
      setRowData(repair_maintenance_request);
    }
  }, [repair_maintenance_request, isRecentMaintReqLoading]);
  const columnDefs = [
    {
      headerName: _t("Date"),
      field: "date",
      minWidth: rowData?.length > 0 ? 135 : 60,
      maxWidth: rowData?.length > 0 ? 135 : 60,
      suppressMenu: true,
      cellRenderer: (params: IRepairMaintenanceRequestCellRenderer) => {
        const { data } = params;
        return data?.created_date ? (
          <DateTimeCard format="date" date={data?.created_date ?? ""} />
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Equipment"),
      field: "name",
      minWidth: rowData?.length > 0 ? 120 : 90,
      flex: 2,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      suppressMenu: true,
      cellRenderer: (params: IRepairMaintenanceRequestCellRenderer) => {
        const { data } = params;
        const name = HTMLEntities.decode(sanitizeString(data.name)) ?? "";
        return (
          <Tooltip title={_t(name)}>
            <Typography className="table-tooltip-text text-center">
              {_t(name) || "-"}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Reported By"),
      field: "reported_by",
      maxWidth: 100,
      minWidth: 100,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      cellRenderer: (params: IRepairMaintenanceRequestCellRenderer) => {
        const { data } = params;
        const name =
          HTMLEntities.decode(sanitizeString(data?.assignee_name)) || "";

        return name || data?.image ? (
          <Tooltip title={name}>
            <div className="w-fit mx-auto">
              <AvatarProfile
                user={{
                  name,
                  image: data?.image,
                }}
                className="m-auto"
                iconClassName="text-[11px] font-semibold"
              />
            </div>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Notes"),
      field: "items_needing_repair",
      minWidth: rowData?.length > 0 ? 80 : 60,
      maxWidth: rowData?.length > 0 ? 80 : 60,
      suppressMenu: true,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellRenderer: (params: IRepairMaintenanceRequestCellRenderer) => {
        const { data } = params;
        const notes = HTMLEntities.decode(sanitizeString(data.notes)) ?? "";

        return notes ? (
          <Tooltip title={_t(notes)}>
            <FontAwesomeIcon
              className="w-4 h-4 text-primary-900"
              icon="fa-regular fa-memo"
            />
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
  ];
  const noRowsOverlay = () => <StaticTableRowLoading columnDefs={columnDefs} />;
  const noData = () => (
    <NoRecords
      image={`${window.ENV.CDN_URL}assets/images/no-records-upcoming-insurance-renewals.svg`}
    />
  );
  return (
    <>
      <DashboardCardHeader
        title={_t("Recent Maintenance Request")}
        showRefreshIcon={true}
        isRefreshing={isRecentMaintReqLoading}
        refreshIconTooltip={repairMaintenanceRequestLastRefreshTime}
        onClickRefresh={handleClickRefresh}
      />
      <div className="py-2 px-2.5">
        <div className="ag-theme-alpine h-[209px] ag-grid-cell-pointer">
          <StaticTable
            className="static-table"
            columnDefs={columnDefs}
            key={isInitialLoad ? "loading" : "loaded"}
            rowData={rowData}
            noRowsOverlayComponent={
              isInitialLoad || isRecentMaintReqLoading ? noRowsOverlay : noData
            }
            enableOpenInNewTab={window.ENV.ENABLE_ALL_CLICK}
            generateOpenInNewTabUrl={(data: { equipment_id?: number }) =>
              `${routes.MANAGE_EQUIPMENT_LOGS.url}/${data?.equipment_id}${
                isAndroid ? "?from=android" : isiOS ? "?from=ios" : ""
              }`
            }
          />
        </div>
      </div>
    </>
  );
};
export default RecentMaintenanceRequest;
