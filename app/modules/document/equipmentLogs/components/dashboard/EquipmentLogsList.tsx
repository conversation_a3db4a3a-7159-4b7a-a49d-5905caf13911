import { useNavigate } from "@remix-run/react";
import type { GridReadyEvent, SortChangedEvent } from "ag-grid-community";
import { useEffect, useMemo, useRef, useState } from "react";
import isEmpty from "lodash/isEmpty";
import isEqual from "lodash/isEqual";
// hooks
import { useTranslation } from "~/hook";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { DynamicTable } from "~/shared/components/molecules/dynamicTable";
// Other
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { getGConfig, getGModuleFilters } from "~/zustand";
import useTableGridData from "~/shared/hooks/useTableGridData";
import { useModuleDataSearch } from "~/zustand/global-module-filter/hook";
import { defaultConfig } from "~/data";
import { escapeHtmlEntities, Number, sanitizeString } from "~/helpers/helper";
import { getEquipmentLogsListApi } from "../../redux/action/equipLogDashAction";
import { EquipmentLogsTableDropdownItems } from "./EquipmentLogsTableDropdownItems";
import { routes } from "~/route-services/routes";
import AddEquipmentLogs from "../sidebar/AddEquipmentLogs";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import { STATUS_CODE } from "~/shared/constants";

let timeout: NodeJS.Timeout;

const EquipmentLogsList = () => {
  const { _t } = useTranslation();
  const { module_access }: GConfig = getGConfig();
  const { datasource, gridRowParams } = useTableGridData();
  const navigate = useNavigate();
  const [drowerOpen, setDrowerOpen] = useState<boolean>(false);
  const filterSrv: Partial<EquipmentLogsFilter> | undefined =
    getGModuleFilters() as Partial<EquipmentLogsFilter> | undefined;
  const { search }: IModuleDataSearchParams = useModuleDataSearch(
    defaultConfig.equipment_log_module
  );

  const filter = useMemo(() => {
    return {
      project: filterSrv?.project || "",
      status: filterSrv?.status !== "2" ? filterSrv?.status : "",
      equipments: filterSrv?.equipments || "",
      start_date: filterSrv?.start_date,
      end_date: filterSrv?.end_date,
    };
  }, [JSON.stringify(filterSrv)]);

  const onGridReady = (gridParams: GridReadyEvent) => {
    gridParams?.api?.setServerSideDatasource(datasource);
  };

  const onSortChanged = async (params: SortChangedEvent) => {
    params.api.setServerSideDatasource({ getRows: () => {} });
    params.api.setServerSideDatasource(datasource);
  };

  const userAgent = navigator.userAgent || "";
  const isAndroid = userAgent.toLowerCase().includes("android");
  const isiOS = /iPhone|iPad|iPod/i.test(userAgent);
  const fetchequipmentLogsList = async () => {
    // if (!filterSrv) {
    //   return;
    // }
    const { changeGridParams, gridParams } = gridRowParams ?? {};
    const { start, order_by_name, order_by_dir } = changeGridParams || {};
    const length = changeGridParams?.length ?? 0;
    const filterObj: Partial<EquipmentLogsFilter> | undefined = !isEmpty(filter)
      ? getValuableObj(filter)
      : undefined;

    if (filterObj?.status === STATUS_CODE.ALL) {
      delete filterObj.status;
    }

    let dataParams: IEquipmentsListParmas = {
      search: !!search ? escapeHtmlEntities(search || "") : undefined,
      filter: !isEmpty(filterObj) ? filterObj : undefined,
      limit: length,
      start: changeGridParams?.start
        ? Math.floor(changeGridParams?.start / length)
        : 0,
      ignore_filter: 1,
      order_by_name: order_by_name,
      order_by_dir: order_by_dir,
    };

    if (!order_by_name || !order_by_dir) {
      delete dataParams.order_by_dir;
      delete dataParams.order_by_name;
    }

    try {
      gridParams?.api.hideOverlay();
      const resData = (await getEquipmentLogsListApi(
        dataParams
      )) as IEquipmentLogsListApiRes;

      const equitmentArr = resData?.data?.equipment_logs || [];

      // Check if we got less data than requested - indicates last page
      const isLastPage = equitmentArr.length < length;

      // Calculate total based on current page and data length
      const currentTotal = isLastPage
        ? (start ? start : 0) + equitmentArr.length
        : (start ? start : 0) + length + 1; // +1 indicates there might be more

      gridParams?.success({
        rowData: equitmentArr,
        rowCount: currentTotal,
      });

      // Handle no records case
      if (
        (!resData?.success || equitmentArr.length <= 0) &&
        dataParams?.start === 0
      ) {
        gridParams?.api.showNoRowsOverlay();
      } else {
        gridParams?.api.hideOverlay();
      }
    } catch (err) {
      gridParams?.success({ rowCount: 0, rowData: [] });
      gridParams?.api.showNoRowsOverlay();
      gridParams?.fail();
    }
  };

  const refreshAgGrid = () => {
    const gridParams = gridRowParams?.gridParams;
    if (gridParams) {
      gridParams.api.setServerSideDatasource({ getRows: () => {} });
      gridParams.api.setServerSideDatasource(datasource);
    }
  };

  const previousValues = useRef({
    filter: JSON.stringify(filterSrv),
    search,
  });

  useEffect(() => {
    const currentValues = {
      filter: JSON.stringify(filter),
      search,
    };

    if (
      !isEqual(previousValues.current, currentValues) &&
      !isEmpty(filterSrv)
    ) {
      previousValues.current = currentValues;
      refreshAgGrid();
    }
  }, [search, JSON.stringify(filter)]);

  useEffect(() => {
    if (gridRowParams?.changeGridParams) {
      if (timeout) {
        clearTimeout(timeout);
      }
      timeout = setTimeout(() => {
        if (!isEmpty(filterSrv)) {
          fetchequipmentLogsList();
        }
      }, 500);
    }
  }, [gridRowParams?.changeGridParams]);

  const columnDefs = [
    {
      headerName: _t("Equipment"),
      field: "equipment_name",
      minWidth: 120,
      flex: 1,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      sortable: true,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: (params: { data: IEquipmentLogData }) => {
        const { data } = params;

        return data.equipment_name ? (
          <Tooltip
            title={HTMLEntities.decode(sanitizeString(data.equipment_name))}
          >
            <Typography className="table-tooltip-text text-center">
              {HTMLEntities.decode(sanitizeString(data.equipment_name || "-"))}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Project"),
      field: "project_name",
      minWidth: 150,
      flex: 2,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      sortable: true,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: (params: { data: IEquipmentLogData }) => {
        const { data } = params;

        return data.project_name ? (
          <Tooltip
            title={HTMLEntities.decode(sanitizeString(data.project_name))}
          >
            <Typography className="table-tooltip-text text-center">
              {HTMLEntities.decode(sanitizeString(data.project_name)) || "-"}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Date"),
      field: "used_date",
      sortable: true,
      maxWidth: 135,
      minWidth: 135,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: (params: { data: IEquipmentLogData }) => {
        const { data } = params;
        return (
          <div>
            {data.used_date ? (
              <DateTimeCard format="date" date={data.used_date} />
            ) : (
              "-"
            )}
          </div>
        );
      },
    },
    {
      headerName: _t("Operator"),
      field: "operator_name",
      sortable: true,
      maxWidth: 105,
      minWidth: 105,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      cellRenderer: (params: { data: IEquipmentLogData }) => {
        const { data } = params;
        return data?.operator_name ? (
          <Tooltip
            title={HTMLEntities.decode(sanitizeString(data?.operator_name))}
          >
            <div>
              <AvatarProfile
                user={{
                  name: HTMLEntities.decode(
                    sanitizeString(data?.operator_name)
                  ),
                  image: data?.image ?? "",
                }}
                className="m-auto"
                iconClassName="text-[11px] font-semibold"
              />
            </div>
          </Tooltip>
        ) : (
          <div>-</div>
        );
      },
    },
    {
      headerName: _t("Hours"),
      field: "e_hours_used",
      sortable: true,
      minWidth: 120,
      maxWidth: 120,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-right",
      cellClass: "ag-right-aligned-cell",
      cellRenderer: (params: { data: IEquipmentLogData }) => {
        const { data } = params;

        const [hours = "0", minutes = "0"] = (data?.e_hours_used || "00:00")
          ?.toString()
          ?.split(":");

        const formattedHours = Number(hours);

        const formattedHoursnMins = `${formattedHours}:${minutes
          ?.toString()
          ?.padStart(2, "0")}`;

        return formattedHoursnMins === "0:00" ||
          formattedHoursnMins === "00:00" ? (
          "-"
        ) : (
          <Tooltip title={formattedHoursnMins}>
            <Typography className="table-tooltip-text text-center">
              {formattedHoursnMins}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Engine Hours"),
      field: "oil_changed_time",
      sortable: true,
      minWidth: 132,
      maxWidth: 132,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-right",
      cellClass: "ag-right-aligned-cell",
      cellRenderer: (params: { data: IEquipmentLogData }) => {
        const { data } = params;
        const oil_changed_time = data.oil_changed_time;

        return oil_changed_time ? (
          <Tooltip title={oil_changed_time}>
            <Typography className="table-tooltip-text text-center">
              {oil_changed_time}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Last Oil Change"),
      field: "oil_changed_date",
      sortable: true,
      maxWidth: 150,
      minWidth: 150,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: (params: { data: IEquipmentLogData }) => {
        const { data } = params;

        return data.oil_changed_date ? (
          <div>
            <DateTimeCard format="date" date={data.oil_changed_date} />
          </div>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: "",
      field: "",
      minWidth: 50,
      maxWidth: 50,
      suppressMenu: true,
      cellStyle: { textAlign: "center" },
      headerClass: "ag-header-center",
      cellClass: "!cursor-auto",
      cellRenderer: (params: { data: IEquipmentLogData }) => {
        const { data } = params;

        return (
          (module_access === "full_access" ||
            module_access === "own_data_access") && (
            <div className="flex items-center gap-3 justify-center">
              {data ? (
                <EquipmentLogsTableDropdownItems
                  data={data}
                  icon="fa-regular fa-ellipsis-vertical"
                  className="m-0 hover:!bg-[#0000000f]"
                  iconClassName="text-primary-900/80 group-hover/buttonHover:text-primary-900"
                  refreshTable={() => {
                    refreshAgGrid();
                  }}
                />
              ) : null}
            </div>
          )
        );
      },
    },
  ];

  return (
    <div
      className={`list-view-table ag-grid-cell-pointer ag-theme-alpine ${
        module_access === "read_only"
          ? "h-[calc(100dvh-294px)]"
          : "h-[calc(100dvh-260px)]"
      }`}
    >
      <DynamicTable
        columnDefs={columnDefs}
        onGridReady={onGridReady}
        onSortChanged={onSortChanged}
        noRowsOverlayComponent={() => (
          <NoRecords
            rootClassName="w-full max-w-[280px]"
            image={`${window.ENV.CDN_URL}assets/images/create-record-list-view.svg`}
            imageWSize="280"
            imageHSize="227"
            text={
              module_access === "full_access" ||
              module_access === "own_data_access" ? (
                <div>
                  <Typography
                    className="sm:text-base text-xs underline underline-offset-1 text-black font-bold cursor-pointer"
                    onClick={() => {
                      setDrowerOpen(true);
                    }}
                  >
                    {_t("Click here")}
                  </Typography>
                  <Typography className="sm:text-base text-xs text-black font-semibold">
                    {_t(" to Create a New Record")}
                  </Typography>
                </div>
              ) : (
                <Typography className="sm:text-base text-xs text-black font-semibold">
                  {_t("No Record Found")}
                </Typography>
              )
            }
          />
        )}
        enableOpenInNewTab={window.ENV.ENABLE_ALL_CLICK}
        generateOpenInNewTabUrl={(data) =>
          `${routes.MANAGE_EQUIPMENT_LOGS.url}/${data?.log_id}${
            isAndroid ? "?from=android" : isiOS ? "?from=ios" : ""
          }`
        }
        restrictOpenInNewTabFields={["email"]}
      />

      {drowerOpen && (
        <AddEquipmentLogs
          drowerOpen={drowerOpen}
          setDrowerOpen={setDrowerOpen}
        />
      )}
    </div>
  );
};

export default EquipmentLogsList;
