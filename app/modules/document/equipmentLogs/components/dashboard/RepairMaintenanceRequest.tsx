// Atoms
import { Toolt<PERSON> } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import { StaticTableRowLoading } from "~/shared/components/molecules/staticTableRowLoading";
// Hook
import { useTranslation } from "~/hook";
import { useEffect, useState } from "react";
import { useELAppDispatch, useELAppSelector } from "../../redux/store";
import { fetchEquipLogDashboardApi } from "../../redux/action/equipLogDashAction";
import { sanitizeString } from "~/helpers/helper";
import { routes } from "~/route-services/routes";
import { useNavigate } from "@remix-run/react";

const RepairMaintenanceRequest = () => {
  const [isRepairMaintenanceLoading, setIsRepairMaintenanceLoading] =
    useState<boolean>(false);
  const { _t } = useTranslation();
  const navigate = useNavigate();
  const userAgent = navigator.userAgent || "";
  const isAndroid = userAgent.toLowerCase().includes("android");
  const isiOS = /iPhone|iPad|iPod/i.test(userAgent);
  const {
    repair_maintenance,
    repairMaintenanceLastRefreshTime,
    isInitialLoad,
  } = useELAppSelector((state) => state.equipmentLogsDashboard);

  const dispatch = useELAppDispatch();
  const [rowData, setRowData] = useState<IRepairMaintananceTableCellRenderer[]>(
    []
  );

  useEffect(() => {
    if (!isRepairMaintenanceLoading && repair_maintenance) {
      setRowData(repair_maintenance);
    }
  }, [repair_maintenance, isRepairMaintenanceLoading]);

  const handleClickRefresh = async () => {
    setIsRepairMaintenanceLoading(true);
    setRowData([]);
    await dispatch(
      fetchEquipLogDashboardApi({
        refreshType: "repair_maintenance",
      })
    );
    setIsRepairMaintenanceLoading(false);
  };

  const columnDefs = [
    {
      headerName: _t("Date"),
      field: "date_added",
      minWidth: 135,
      maxWidth: 135,
      suppressMenu: true,
      cellRenderer: (params: IRepairMaintananceTableCellRenderer) => {
        const { data } = params;
        return data?.date_added ? (
          <DateTimeCard format="date" date={data?.date_added ?? ""} />
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Equipment"),
      field: "name",
      minWidth: 120,
      flex: 1,
      suppressMenu: true,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      cellRenderer: (params: IRepairMaintananceTableCellRenderer) => {
        const { data } = params;
        const displayName = HTMLEntities.decode(sanitizeString(data.name));

        return (
          <Tooltip title={_t(displayName)}>
            <Typography className="table-tooltip-text text-center">
              {_t(displayName) || "-"}
            </Typography>
          </Tooltip>
        );
      },
    },
  ];
  const noRowsOverlay = () => <StaticTableRowLoading columnDefs={columnDefs} />;
  const noData = () => (
    <NoRecords
      image={`${window.ENV.CDN_URL}assets/images/no-records-repair-maintenance.svg`}
    />
  );
  return (
    <>
      <DashboardCardHeader
        title={_t("Recently Used Equipment")}
        showRefreshIcon={true}
        isRefreshing={isRepairMaintenanceLoading}
        refreshIconTooltip={repairMaintenanceLastRefreshTime}
        onClickRefresh={handleClickRefresh}
      />
      <div className="py-2 px-2.5">
        <div className="ag-theme-alpine h-[209px] ag-grid-cell-pointer">
          <StaticTable
            className="static-table"
            columnDefs={columnDefs}
            rowData={rowData}
            key={isInitialLoad ? "loading" : "loaded"}
            noRowsOverlayComponent={
              isInitialLoad || isRepairMaintenanceLoading
                ? noRowsOverlay
                : noData
            }
            enableOpenInNewTab={window.ENV.ENABLE_ALL_CLICK}
            generateOpenInNewTabUrl={(data: { log_id?: number }) =>
              `${routes.MANAGE_EQUIPMENT_LOGS.url}/${data?.log_id}${
                isAndroid ? "?from=android" : isiOS ? "?from=ios" : ""
              }`
            }
          />
        </div>
      </div>
    </>
  );
};
export default RepairMaintenanceRequest;
