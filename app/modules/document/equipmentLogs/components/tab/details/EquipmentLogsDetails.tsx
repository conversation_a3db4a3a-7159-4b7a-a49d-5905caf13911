// Molecules
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { SelectField } from "~/shared/components/molecules/selectField";
// Hook
import { useTranslation } from "~/hook";
import { useELAppSelector } from "../../../redux/store";
import { useCallback, useMemo, useRef, useState } from "react";
import { Number, sanitizeString } from "~/helpers/helper";
import { useEquipmentLogDetail } from "../../../hook/useEquipmentLogDetail";
import {
  filterOptionBySubstring,
  getStatusForField,
} from "~/shared/utils/helper/common";
import { InputPhoneNumber } from "~/shared/components/molecules/inputPhoneNumber";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import {
  calculated_new_hours,
  getTotalMinutes,
  isAllZero,
  parseTimeValue,
} from "../../../utils/hour-calculation";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";

const EquipmentLogsDetails = () => {
  const { _t } = useTranslation();
  const { details } = useELAppSelector((state) => state.equipmentLogsDetail);
  const { codeCostData }: IGetCostCodeList = useELAppSelector(
    (state) => state.costCode
  );
  const appSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();

  const HOURS_MASK = useMemo(() => {
    return "99999:99";
    // const separator = appSettings?.thousand_separator;
    // return separator
    //   ? separator === "."
    //     ? "99.999:99"
    //     : "99,999:99"
    //   : "99.999:99";
  }, []);

  const [totalHoursConfirmationOpen, setTotalHoursConfirmationOpen] = useState<
    Record<string, boolean | null | string>
  >({
    open: false,
    value: null,
  });

  const beignHrRef = useRef<HTMLDivElement | null>(null);
  const endHrRef = useRef<HTMLDivElement | null>(null);
  const totalHrRef = useRef<HTMLDivElement | null>(null);

  const {
    handleUpdateField,
    onMouseEnterUpdateFieldStatus,
    onMouseLeaveUpdateFieldStatus,
    onFocusUpdateFieldStatus,
    updateInputFieldOnBlur,
    onBlurUpdateFieldStatus,
    onChangeInputField,
    loadingStatus,
    isReadOnly,
    inputVals,
    updateDataInStore,
  } = useEquipmentLogDetail();

  const costCodeOptions = useMemo(() => {
    let costCodeOpts = codeCostData.map((item: ICostCode) => {
      const codeName = HTMLEntities.decode(sanitizeString(item.cost_code_name));
      const csiCode = HTMLEntities.decode(sanitizeString(item.csi_code));
      const label = _t(
        `${codeName}${item.csi_code ? ` (${csiCode})` : ""}${
          item.is_deleted === 1 ? ` (Archived)` : ""
        }`
      );

      return {
        label: label,
        value: item.code_id,
      };
    });

    if (details.cost_code_id) {
      const is_cost_code_avail_in_list = codeCostData.some(
        (c) => c.code_id?.toString() === details.cost_code_id?.toString()
      );
      if (!is_cost_code_avail_in_list) {
        const codeName = HTMLEntities.decode(
          sanitizeString(details.cost_code_name ?? "")
        );
        const csiCode = HTMLEntities.decode(
          sanitizeString(details.csi_code ?? "")
        );

        const label = _t(
          `${codeName}${details.csi_code ? ` (${csiCode})` : ""}${
            details.is_deleted === 1 ? ` (Archived)` : ""
          }`
        );

        const newCostCodeToAdd = {
          label: label,
          value: details.cost_code_id?.toString(),
        };
        costCodeOpts.unshift(newCostCodeToAdd);
      }
    }

    return costCodeOpts;
  }, [codeCostData]);

  const getHoursValue = useCallback(
    (hour_key: "e_begin_hours" | "e_end_hours" | "e_hours_used") => {
      const currentVal = inputVals?.[hour_key];

      const isDotContain = currentVal?.includes(".");
      if (!isDotContain) {
        const splittedHours = currentVal?.replace(":", "");
        const allZero = !Number(splittedHours);
        if (allZero) {
          return null;
        }
      }

      const replacedHours = isDotContain
        ? currentVal?.replace(".", ":")
        : currentVal;

      const splittedHours = replacedHours?.replace(":", "");
      const allZero = !Number(splittedHours);
      if (allZero) {
        return null;
      }

      return replacedHours;
    },
    [inputVals]
  );

  const handleHoursError = (
    message: string,
    field: string,
    originalValue: string
  ) => {
    updateDataInStore({ [field]: originalValue });
    onBlurUpdateFieldStatus({
      field,
    });
    notification.error({ description: message });
  };

  const handleHoursBlur = (value: string, hour_key: string) => {
    // Utility function to compute new hours to store
    const formatNewHours = (hours: string, mins: string) =>
      hour_key === "e_begin_hours" && isAllZero(`${hours}:${mins}`) // For enter 0 then take as in format
        ? "00000:00"
        : Number(hours) || Number(mins)
        ? `${hours}:${mins}`
        : "";
    // Extract the parsed time values
    const { hours, mins } = parseTimeValue(value);
    const totalCurrentMins = getTotalMinutes(value);
    const totalOldMins = getTotalMinutes(
      (details[hour_key as keyof TEquipLogDetail] as string) ?? ""
    );

    // Get related hour values
    const totalBeginningMins = getTotalMinutes(getHoursValue("e_begin_hours"));
    const totalEndingMins = getTotalMinutes(getHoursValue("e_end_hours"));
    const totalHoursUsedMins = getTotalMinutes(getHoursValue("e_hours_used"));

    // Prepare API payload
    let data_to_send_in_api: Partial<TEquipLogDetail> = {
      [hour_key]: formatNewHours(hours, mins),
    };

    // If values haven't changed, update store and return early
    if (hour_key === "e_begin_hours" && isAllZero(value)) {
      if (
        !value === !details.e_begin_hours &&
        value === details.e_begin_hours
      ) {
        updateDataInStore({
          [hour_key as string]:
            details[hour_key as keyof TEquipLogDetail] ?? "",
        });
        onBlurUpdateFieldStatus({ field: hour_key });
        return;
      }
    } else if (totalCurrentMins === totalOldMins) {
      updateDataInStore({
        [hour_key]: details[hour_key as keyof TEquipLogDetail] ?? "",
      });
      onBlurUpdateFieldStatus({ field: hour_key });
      return;
    }

    // Handle different hour key cases
    if (hour_key === "e_begin_hours") {
      if (!value && !!inputVals.e_end_hours) {
        handleHoursError(
          "A beginning hours or total hours is required",
          "e_end_hours",
          details.e_end_hours || ""
        );
        return;
      }
      if (
        totalCurrentMins &&
        totalEndingMins &&
        totalCurrentMins >= totalEndingMins
      ) {
        handleHoursError(
          "Beginning hours must be less than Ending hours",
          "e_begin_hours",
          details.e_begin_hours || ""
        );
        return;
      }

      if (!value && details.e_hours_used) {
        data_to_send_in_api.e_hours_used = "";
      } else if (totalEndingMins) {
        data_to_send_in_api.e_hours_used = calculated_new_hours(
          totalCurrentMins,
          totalEndingMins,
          "e_hours_used"
        );
      } else if (totalHoursUsedMins) {
        data_to_send_in_api.e_end_hours = calculated_new_hours(
          totalCurrentMins,
          totalHoursUsedMins,
          "e_end_hours"
        );
      }
    } else if (hour_key === "e_end_hours") {
      if (totalCurrentMins && !inputVals.e_begin_hours) {
        handleHoursError(
          "A beginning hours or total hours is required",
          "e_end_hours",
          details.e_end_hours || ""
        );
        return;
      }
      if (
        totalCurrentMins &&
        totalBeginningMins &&
        totalBeginningMins >= totalCurrentMins
      ) {
        handleHoursError(
          "Ending hours must be greater than Beginning hours",
          "e_end_hours",
          details.e_end_hours || ""
        );
        return;
      }
      if (
        !details.e_begin_hours &&
        totalHoursUsedMins &&
        totalHoursUsedMins > totalCurrentMins
      ) {
        handleHoursError(
          "Ending hours must be greater than Total hours",
          "e_end_hours",
          details.e_end_hours || ""
        );
        return;
      }

      if (!value && details.e_hours_used) {
        data_to_send_in_api.e_hours_used = "";
      } else if (details.e_begin_hours) {
        data_to_send_in_api.e_hours_used = calculated_new_hours(
          totalBeginningMins,
          totalCurrentMins,
          "e_hours_used"
        );
      } else if (totalHoursUsedMins) {
        data_to_send_in_api.e_begin_hours = calculated_new_hours(
          totalHoursUsedMins,
          totalCurrentMins,
          "e_begin_hours"
        );
      }
    } else if (hour_key === "e_hours_used") {
      if (details.e_begin_hours && details.e_end_hours) {
        data_to_send_in_api.e_begin_hours = "";
        data_to_send_in_api.e_end_hours = "";
      } else if (details.e_begin_hours) {
        data_to_send_in_api.e_end_hours = calculated_new_hours(
          totalBeginningMins,
          totalCurrentMins,
          "e_end_hours"
        );
      } else if (totalEndingMins) {
        data_to_send_in_api.e_begin_hours = calculated_new_hours(
          totalCurrentMins,
          totalEndingMins,
          "e_begin_hours"
        );
      }
    }

    if ("e_begin_hours" in data_to_send_in_api) {
      data_to_send_in_api.begin_hours =
        data_to_send_in_api.e_begin_hours as string;
    }
    if ("e_end_hours" in data_to_send_in_api) {
      data_to_send_in_api.end_hours = data_to_send_in_api.e_end_hours as string;
    }

    handleUpdateField({ data_to_send_in_api });

    if (totalHoursConfirmationOpen.open) {
      setTotalHoursConfirmationOpen({ open: false, value: null });
    }
  };

  const onCancelTotalHourConfirmation = () => {
    setTotalHoursConfirmationOpen({
      open: false,
      value: null,
    });
    updateDataInStore({ e_hours_used: details.e_hours_used });
  };

  const handleUpdateTotalHours = (value: string) => {
    const newTotalMins = getTotalMinutes(value);
    const oldTotalMins = getTotalMinutes(details?.e_hours_used);

    const endTotalMins = getTotalMinutes(details?.e_end_hours);

    if (newTotalMins !== oldTotalMins) {
      if (details.e_begin_hours && details.e_end_hours) {
        setTotalHoursConfirmationOpen({
          open: true,
          value: value,
        });
      } else {
        if (!!endTotalMins && newTotalMins > endTotalMins) {
          handleHoursError(
            "Total hours must be less than Ending hours",
            "e_hours_used",
            details.e_hours_used || ""
          );
        } else {
          handleHoursBlur(value, "e_hours_used");
        }
      }
    } else {
      updateDataInStore({ e_hours_used: details.e_hours_used });
      onBlurUpdateFieldStatus({
        field: "e_hours_used",
      });
    }
  };

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Details")}
        iconProps={{
          icon: "fa-solid fa-file-lines",
          containerClassName:
            "bg-[linear-gradient(180deg,#FF868A1a_0%,#FD33331a_100%)]",
          id: "details_icon",
          colors: ["#FF868A", "#FD3333"],
        }}
        children={
          <div className="pt-2">
            <ul className="w-full flex flex-col gap-1 mt-[3px]">
              <li>
                <InputPhoneNumber
                  ref={beignHrRef}
                  label={_t("Beginning Hours")}
                  name="e_begin_hours"
                  mask={HOURS_MASK}
                  placeholder={_t("Beginning Hours")}
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  readOnlyClassName="sm:w-[120px] w-[120px]"
                  fixStatus={getStatusForField(loadingStatus, "e_begin_hours")}
                  value={inputVals.e_begin_hours ?? ""}
                  onMouseEnter={() => {
                    onMouseEnterUpdateFieldStatus({ field: "e_begin_hours" });
                  }}
                  onMouseLeaveDiv={() => {
                    onMouseLeaveUpdateFieldStatus({ field: "e_begin_hours" });
                  }}
                  onClickStsIcon={() => {
                    beignHrRef.current?.focus();
                  }}
                  onFocus={() => {
                    onFocusUpdateFieldStatus({ field: "e_begin_hours" });
                  }}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    onChangeInputField({
                      field: "e_begin_hours",
                      value: e.target.value,
                    });
                  }}
                  onBlur={({
                    target: { value },
                  }: React.FocusEvent<HTMLInputElement>) => {
                    if (
                      details.e_begin_hours === null &&
                      Boolean(value) === Boolean(details.e_begin_hours)
                    ) {
                      onBlurUpdateFieldStatus({
                        field: "e_begin_hours",
                      });
                      return;
                    }
                    handleHoursBlur(value, "e_begin_hours");
                  }}
                  disableUnderline={true}
                />
              </li>
              <li>
                <InputPhoneNumber
                  ref={endHrRef}
                  label={_t("Ending Hours")}
                  name="e_end_hours"
                  mask={HOURS_MASK}
                  placeholder={_t("Ending Hours")}
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  readOnlyClassName="sm:w-[110px] w-[110px]"
                  fixStatus={getStatusForField(loadingStatus, "e_end_hours")}
                  value={
                    !isAllZero(inputVals.e_end_hours)
                      ? inputVals.e_end_hours
                      : ""
                  }
                  onMouseEnter={() => {
                    onMouseEnterUpdateFieldStatus({ field: "e_end_hours" });
                  }}
                  onMouseLeaveDiv={() => {
                    onMouseLeaveUpdateFieldStatus({ field: "e_end_hours" });
                  }}
                  onClickStsIcon={() => {
                    endHrRef.current?.focus();
                  }}
                  onFocus={() => {
                    onFocusUpdateFieldStatus({ field: "e_end_hours" });
                  }}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    onChangeInputField({
                      field: "e_end_hours",
                      value: e.target.value,
                    });
                  }}
                  onBlur={({
                    target: { value },
                  }: React.FocusEvent<HTMLInputElement>) => {
                    if (
                      details.e_end_hours === null &&
                      Boolean(value) === Boolean(details.e_end_hours)
                    ) {
                      onBlurUpdateFieldStatus({
                        field: "e_end_hours",
                      });
                      return;
                    }
                    handleHoursBlur(value, "e_end_hours");
                  }}
                  disableUnderline={true}
                />
              </li>
              <li>
                <InputPhoneNumber
                  ref={totalHrRef}
                  label={_t("Total Hours")}
                  name="e_hours_used"
                  mask={HOURS_MASK}
                  placeholder={_t("Total Hours")}
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  readOnlyClassName="sm:w-[110px] w-[110px]"
                  fixStatus={getStatusForField(loadingStatus, "e_hours_used")}
                  value={
                    !isAllZero(inputVals.e_hours_used)
                      ? inputVals.e_hours_used
                      : ""
                  }
                  onMouseEnter={() => {
                    onMouseEnterUpdateFieldStatus({ field: "e_hours_used" });
                  }}
                  onMouseLeaveDiv={() => {
                    onMouseLeaveUpdateFieldStatus({ field: "e_hours_used" });
                  }}
                  onClickStsIcon={() => {
                    totalHrRef.current?.focus();
                  }}
                  onFocus={() => {
                    onFocusUpdateFieldStatus({ field: "e_hours_used" });
                  }}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    onChangeInputField({
                      field: "e_hours_used",
                      value: e.target.value,
                    });
                  }}
                  onBlur={({
                    target: { value },
                  }: React.FocusEvent<HTMLInputElement>) =>
                    handleUpdateTotalHours(value)
                  }
                  disableUnderline={true}
                />
              </li>
              <li>
                <SelectField
                  label={_t("Cost Code")}
                  placeholder={_t("Select Cost Code")}
                  name="cost_code_id"
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  disabled={isReadOnly}
                  readOnly={isReadOnly}
                  showSearch
                  filterOption={(input, option) =>
                    filterOptionBySubstring(input, option?.label as string)
                  }
                  allowClear={!!Number(details?.cost_code_id ?? "")}
                  options={costCodeOptions}
                  value={
                    details.cost_code_id
                      ? costCodeOptions.filter((item) => {
                          return (
                            details?.cost_code_id?.toString() ===
                            item?.value?.toString()
                          );
                        })
                      : []
                  }
                  onSelect={(value) => {
                    updateInputFieldOnBlur({
                      field: "cost_code_id",
                      value: Number(value) || "",
                    });
                  }}
                  onClear={() => {
                    updateInputFieldOnBlur({
                      field: "cost_code_id",
                      value: "",
                    });
                  }}
                  fixStatus={getStatusForField(loadingStatus, "cost_code_id")}
                />
              </li>
            </ul>
          </div>
        }
      />

      {totalHoursConfirmationOpen.open && (
        <ConfirmModal
          isOpen={totalHoursConfirmationOpen.open as boolean}
          modaltitle={_t("Confirmation")}
          description={_t(
            "If you manually want to enter Total Hours then your selected Beginning and Ending Hours will be cleared."
          )}
          isLoading={false}
          yesButtonLabel={_t("Ok")}
          noButtonLabel={_t("Cancel")}
          modalIcon="fa-regular fa-file-check"
          onAccept={() => {
            handleHoursBlur(
              totalHoursConfirmationOpen.value as string,
              "e_hours_used"
            );
          }}
          onDecline={onCancelTotalHourConfirmation}
          onCloseModal={onCancelTotalHourConfirmation}
        />
      )}
    </>
  );
};

export default EquipmentLogsDetails;
