import dayjs, { Dayjs } from "dayjs";
import { useFormik } from "formik";
import { useEffect, useMemo, useState } from "react";
import { useNavigate, useSearchParams } from "@remix-run/react";
import type { DatePickerProps } from "antd";
import * as Yup from "yup";
import isEmpty from "lodash/isEmpty";
// Atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { TimePickerField } from "~/shared/components/molecules/timePickerField";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
//hooks
import { useIframe, useTranslation } from "~/hook";
import { formatCustomFieldForRequest } from "~/shared/utils/helper/customFieldSidebarFormat";
import { addComplianceNotice } from "../../../redux/action/dashboardAction";
import { getGConfig, getGProject, getGSettings } from "~/zustand";
import getCustomFieldAccess from "~/shared/utils/helper/getCustomFieldAccess";
import useDirectoryKeyValue from "~/shared/hooks/useCustomField/useDirectoryKeyValue";
import useSideBarCustomField from "~/shared/hooks/useCustomField/useSidebarCustomField";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import { ContactDetails } from "~/shared/components/molecules/contactDetails";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { SelectProject } from "~/shared/components/organisms/selectProject";
import { defaultConfig } from "~/data";
import OnlyRequiredCustomFields from "~/shared/components/organisms/OnlyRequiredCustomFields/OnlyRequiredCustomFields";
import {
  backendDateFormat,
  backendTimeFormat,
  displayDateFormat,
  displayTimeFormat,
} from "~/shared/utils/helper/defaultDateFormat";
import { sanitizeString } from "~/helpers/helper";
import { routes } from "~/route-services/routes";
import DirSendEmail from "~/modules/people/directory/components/DirSendEmail";
import { RFIDetailsField } from "../../../utils/constasnts";
import { AvatarIconPopover } from "~/shared/components/organisms/avatarIconPopover";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { useAppRFIDispatch } from "../../../redux/store";
import { getCustomData } from "~/redux/action/customDataAction";
import { getModuleAutoNumber } from "~/redux/action/commonAction";
import { MultiSelectButtonField } from "~/shared/components/molecules/multiSelectButtonField";
import { OpportunityFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/opportunityFieldRedirectionIcon";
import { getProjectDetails } from "~/redux/action/getProjectDetailsAction";
import { sendMessageKeys } from "~/components/page/$url/data";

const AddRequestInformation = ({
  addRequestInformationOpen,
  setAddRequestInformationOpen,
}: TAddRequestInformationProps) => {
  const navigate = useNavigate();
  const { _t } = useTranslation();
  const [searchParams] = useSearchParams();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { timezone_utc_tz_id } = user || {};
  const { project_id, project_name }: GProject = getGProject();
  const { module_id, module_key }: GConfig = getGConfig();
  const { isNoAccessCustomField }: ICustomFieldAccess = getCustomFieldAccess();
  const { date_format, is_custom_rfi_id }: GSettings = getGSettings();
  const { directoryKeyValue, directory }: IDirectoryFormCustomField =
    useDirectoryKeyValue();
  const { componentList, loadingCustomField, setLoadingCustomFields } =
    useSideBarCustomField(
      { directory, directoryKeyValue } as IDirectoryFormCustomField,
      {
        moduleId: module_id,
      } as IRequestCustomFieldForSidebar
    );

  // state
  const [isSubmit, setIsSubmit] = useState<boolean>(false);
  const [submittingFrm, setSubmittingFrm] = useState<boolean>(false);
  const [selectedData, setSelectedData] = useState<Partial<IDirectoryData>>({});
  const [selectedProject, setSelectedProject] = useState<IProject[]>([]);
  const [isIframe, setIsIframe] = useState<boolean>(true);
  const { parentPostMessage } = useIframe();
  const [isSelectProjectOpen, setIsSelectProjectOpen] =
    useState<boolean>(false);
  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);
  const [isOpenSelectAssignedTo, setIsOpenSelectAssignedTo] =
    useState<boolean>(false);
  const [selectedContactId, setSelectedContactId] = useState<string>("");
  const [selectedAdditionalContactId, setSelectedAdditionalContactId] =
    useState<string>("");
  const [isOpenContactDetails, setIsOpenContactDetails] =
    useState<boolean>(false);
  const [projectAutoNumber, setProjectAutoNumber] = useState<string>("");
  const [projectDefId, setProjectDefId] = useState<string>("");
  const dispatch = useAppRFIDispatch();
  const [inputValues, setInputValues] =
    useState<Partial<IRFIDetails>>(RFIDetailsField);
  const currentDateTime = useMemo(() => {
    try {
      const testDate = dayjs().tz(timezone_utc_tz_id);
      return testDate.format("z") !== "Invalid Date"
        ? dayjs().tz(timezone_utc_tz_id)
        : dayjs();
    } catch (e) {
      return dayjs();
    }
  }, [timezone_utc_tz_id]);

  useEffect(() => {
    if (window && window?.location?.href) {
      const currentUrl = window.location.href;
      setIsIframe(currentUrl.includes("iframecall=1"));
    }
  }, []);

  const initValues = useMemo(
    (): TAddRfiNotice => ({
      project_id: null,
      title: "",
      custom_correspondence_id: projectAutoNumber,
      correspondence_date: currentDateTime.format(date_format),
      correspondence_time: currentDateTime.format("HH:mm A"),
      user_to: "",
      info_request: "",
      custom_fields: {},
    }),
    [currentDateTime, date_format, projectAutoNumber]
  );

  useEffect(() => {
    const action = searchParams.get("action")?.trim();
    const projectParam = searchParams?.get("project") ?? "";

    const fetchProjectDetails = async (project: string) => {
      try {
        const proResApi = (await getProjectDetails({
          start: 0,
          limit: 1,
          projects: project,
          need_all_projects: 0,
          global_call: true,
          is_completed: true,
          filter: { status: "0" },
        })) as IProjectDetailsRes;

        const queryPro = proResApi?.data?.projects[0];
        if (queryPro) {
          setFieldValue("project_id", queryPro.id);
          setProjectDefId(queryPro.id as string);
          setSelectedProject([
            {
              id: Number(queryPro.id),
              project_name: queryPro.project_name,
              prj_record_type:
                queryPro.prj_record_type === "opportunity"
                  ? "opportunity"
                  : "project",
            },
          ]);
        }
      } catch (error) {}
    };

    if (action === "new" && !!projectParam && projectParam !== "0") {
      setFieldValue("project_id", project_id);
      fetchProjectDetails(projectParam);
    } else if (
      project_id &&
      project_id !== "0" &&
      (projectParam == "" || projectParam != "0")
    ) {
      setFieldValue("project_id", project_id);
      setProjectDefId(project_id);
      setSelectedProject([
        {
          id: Number(project_id),
          project_name,
          prj_record_type: "project",
        },
      ]);
    }
  }, [
    project_id,
    searchParams,
    project_name,
    componentList,
    isNoAccessCustomField,
  ]);

  const dynamicValidationSchema = componentList.reduce((acc, fieldName) => {
    if (fieldName.multiple || fieldName.type === "checkbox-group") {
      acc[fieldName.name] = Yup.array()
        .of(Yup.string().required("This field is required."))
        .min(1, "This field is required.")
        .required("This field is required.");
    } else {
      acc[fieldName.name] = Yup.string().required("This field is required.");
    }
    return acc;
  }, {} as Record<string, Yup.StringSchema | Yup.AnySchema>);

  const baseValidationSchema = {
    project_id: Yup.number().required("This field is required."),
    custom_correspondence_id:
      is_custom_rfi_id === 0
        ? Yup.string()
        : Yup.string().required("This field is required."),
    title: Yup.string().required("This field is required."),
    correspondence_date: Yup.string().required("This field is required."),
    correspondence_time: Yup.string().required("This field is required."),
    user_to: Yup.string().required("This field is required."),
    info_request: Yup.string().required("This field is required."),
  };

  const validationSchema =
    componentList.length && !isNoAccessCustomField
      ? Yup.object().shape({
          ...baseValidationSchema,
          custom_fields: Yup.object().shape(dynamicValidationSchema),
        })
      : Yup.object().shape({
          ...baseValidationSchema,
        });

  const initialFormValues =
    componentList.length && !isNoAccessCustomField
      ? {
          ...initValues,
          custom_fields: componentList.reduce((acc, item) => {
            acc[item.name] = item?.value ?? "";
            return acc;
          }, {} as ICustomFieldInitValue),
        }
      : initValues;

  const formik = useFormik({
    initialValues: initialFormValues,
    validationSchema: validationSchema,
    enableReinitialize: true,
    onSubmit: async (values, { setSubmitting }) => {
      let isCustomFieldValid = true;
      if (componentList.length && !isNoAccessCustomField) {
        for (let index = 0; index < componentList.length; index++) {
          const value = values?.custom_fields?.[componentList[index].name];
          const multiple = componentList[index].multiple;
          const typeComponent = componentList[index].type;
          if (multiple || typeComponent === "checkbox-group") {
            if (!value?.length) {
              isCustomFieldValid = false;
              break;
            }
          } else if (!value) {
            isCustomFieldValid = false;
            break;
          }
        }
      }

      if (!formik.isValid || !isCustomFieldValid) return;
      const backendDateVal = values?.correspondence_date
        ? backendDateFormat(values.correspondence_date.toString(), date_format)
        : "";
      const backendTimeVal = backendTimeFormat(values?.correspondence_time);
      const formData = {
        type: 124,
        project_id: values.project_id || projectDefId,
        title: HTMLEntities.encode(values.title ?? "").trim(),
        custom_correspondence_id: HTMLEntities.encode(
          values.custom_correspondence_id ?? ""
        ).trim(),
        user_to: values.user_to,
        info_request: values.info_request.trim(),
        correspondence_date: backendDateVal,
        correspondence_time: backendTimeVal,
        custom_fields:
          formik?.values?.custom_fields && !isNoAccessCustomField
            ? formatCustomFieldForRequest(
                formik.values?.custom_fields,
                componentList,
                date_format
              ).custom_fields
            : undefined,
        access_to_custom_fields:
          componentList.length && !isNoAccessCustomField ? 1 : 0,
      };
      setSubmitting(true);
      setSubmittingFrm(true);
      try {
        const resData = (await addComplianceNotice(
          getValuableObj(formData)
        )) as Partial<IResponse<IaddRfiApiRes>>;

        if (resData?.success) {
          if (window && window.ENV && window.ENV.PAGE_IS_IFRAME) {
            parentPostMessage(sendMessageKeys?.modal_change, { open: false });
            setSubmittingFrm(false);
          } else {
            navigate(
              `${routes?.MANAGE_RFI_NOTICES.url}/${resData?.data?.correspondence_id}`
            );
          }
        } else {
          setSubmittingFrm(false);
          notification.error({
            description: resData?.message || "",
          });
        }
      } catch (error) {
        setSubmittingFrm(false);
        notification.error({
          description: (error as Error).message || "",
        });
      }
    },
  });

  const getAutoNumber = async () => {
    const autoNumberRes = (await getModuleAutoNumber({
      module_id: module_id,
      module_key: "rfi",
    })) as GetModuleAutoNumberApiResponse;
    if (autoNumberRes.success) {
      const newId =
        autoNumberRes?.data?.last_primary_id?.toString()?.trim() !== ""
          ? (autoNumberRes?.data?.last_primary_id != null ||
              autoNumberRes?.data?.last_primary_id != undefined ||
              autoNumberRes.data?.last_primary_id === 0) &&
            autoNumberRes.data?.need_to_increment != null &&
            autoNumberRes.data?.need_to_increment != undefined
            ? (
                Number(autoNumberRes.data.last_primary_id) +
                Number(autoNumberRes.data?.need_to_increment)
              ).toString()
            : ""
          : "";
      setProjectAutoNumber(String(newId));
    }
    if (!autoNumberRes?.success) {
      notification.error({
        description: autoNumberRes.message || "Something went wrong",
      });
    }
  };

  useEffect(() => {
    dispatch(
      getCustomData({
        types: [188, 226],
        moduleId: module_id,
      })
    );
    getAutoNumber();
  }, []);

  const {
    setFieldValue,
    values,
    errors,
    setFieldError,
    isSubmitting,
    touched,
  } = formik;

  useEffect(() => {
    if (Number(is_custom_rfi_id) === 2 && projectAutoNumber) {
      formik.setValues({
        ...formik.values,
        custom_correspondence_id: projectAutoNumber,
      });
    } else if (Number(is_custom_rfi_id) === 0) {
      formik.setValues({
        ...formik.values,
        custom_correspondence_id: "",
      });
    } else {
      formik.setValues({
        ...formik.values,
        custom_correspondence_id: "",
      });
    }
  }, [is_custom_rfi_id, projectAutoNumber, addRequestInformationOpen]);

  const selectedCustomerList = useMemo((): TselectedContactSendMail[] => {
    return Array.isArray(inputValues.user_details)
      ? (inputValues.user_details as TselectedContactSendMail[])
      : [];
  }, [inputValues.user_details]);

  const handleContact = (data: TselectedContactSendMail[]) => {
    const userIdsStringTo = data
      ?.map((user) => {
        if (user?.contact_id == 0) {
          return user?.user_id?.toString();
        } else {
          return `${user?.user_id}|${user?.contact_id}`;
        }
      })
      ?.join(",");
    setFieldValue("user_to", userIdsStringTo);
    setInputValues({
      ...inputValues,
      user_details: data.map((record: any) => ({
        user_id: record.user_id,
        display_name: record.display_name,
        contact_id: record.contact_id,
        type_name: record.type_name,
        image: record.image,
        type_key: record.type_key,
        parent_type_key: record.parent_type_key,
      })),
    });
  };

  const onChangeProject = (projects: IProject[]) => {
    setSelectedProject(projects);
    if (projects.length) {
      setFieldValue("project_id", projects[0].id);
    } else {
      setFieldValue("project_id", null);
    }
  };

  const disableAfterToday: DatePickerProps["disabledDate"] = (
    current: Dayjs
  ) => {
    return current && current > dayjs().endOf("day");
  };

  const handleClose = () => {
    setAddRequestInformationOpen(false);
    formik.resetForm();
    setIsSubmit(false);
  };

  return (
    <>
      <Drawer
        open={addRequestInformationOpen}
        rootClassName="drawer-open"
        width={718}
        push={false}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-memo-circle-info"
              />
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {_t("Add Request for Information Form")}
            </Header>
          </div>
        }
        closeIcon={
          window.ENV.PAGE_IS_IFRAME
            ? null
            : !isIframe && (
                <CloseButton
                  isLoading={formik?.isSubmitting}
                  onClick={() => handleClose()}
                />
              )
        }
      >
        <form
          noValidate
          method="post"
          className="py-4"
          onSubmit={formik.handleSubmit}
          onKeyDown={(e) => {
            if (
              e.key === "Enter" &&
              (e.target as HTMLElement).tagName !== "TEXTAREA"
            ) {
              e.preventDefault();
            }
          }}
        >
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <div className="grid gap-4">
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <ButtonField
                    label={_t("Project")}
                    name="project"
                    id="project_id"
                    labelPlacement="top"
                    required={true}
                    onClick={() => setIsSelectProjectOpen(true)}
                    value={
                      selectedProject.length
                        ? HTMLEntities.decode(
                            sanitizeString(selectedProject[0]?.project_name)
                          )
                        : ""
                    }
                    errorMessage={touched.project_id ? errors.project_id : ""}
                    addonBefore={
                      !isEmpty(selectedProject?.[0]?.project_name) &&
                      selectedProject?.[0]?.prj_record_type ===
                        "opportunity" ? (
                        <OpportunityFieldRedirectionIcon
                          projectId={selectedProject?.[0]?.id?.toString() || ""}
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                        />
                      ) : (
                        !isEmpty(selectedProject?.[0]?.project_name) && (
                          <ProjectFieldRedirectionIcon
                            projectId={
                              selectedProject?.[0]?.id?.toString() || ""
                            }
                            onClick={(e) => {
                              e.stopPropagation();
                            }}
                          />
                        )
                      )
                    }
                  />
                </div>
                <div className="w-full">
                  <InputField
                    label={_t("Title")}
                    name="title"
                    id="title"
                    labelPlacement="top"
                    isRequired={true}
                    value={values?.title || ""}
                    errorMessage={touched.title ? errors?.title : ""}
                    autoComplete="off"
                    onChange={(e) => {
                      const value = e?.target?.value;
                      setFieldValue("title", value);
                    }}
                    onBlur={(e) =>
                      setFieldValue("title", e?.currentTarget?.value?.trim())
                    }
                  />
                </div>
                <div className="grid md:grid-cols-2 md:gap-4 gap-7">
                  <div className="w-full">
                    <DatePickerField
                      label={_t("Date")}
                      labelPlacement="top"
                      name="correspondence_date"
                      isRequired={true}
                      inputReadOnly={true}
                      placeholder=""
                      format={date_format}
                      value={displayDateFormat(
                        values?.correspondence_date?.trim(),
                        date_format
                      )}
                      disabledDate={disableAfterToday}
                      onChange={(_, dateString) =>
                        setFieldValue(
                          "correspondence_date",
                          dateString.toString()
                        )
                      }
                      errorMessage={
                        touched?.correspondence_date
                          ? errors?.correspondence_date
                          : ""
                      }
                    />
                  </div>
                  <div className="w-full">
                    <TimePickerField
                      label={_t("Time")}
                      labelPlacement="top"
                      isRequired={true}
                      placeholder=""
                      format="hh:mm A"
                      value={displayTimeFormat(
                        values?.correspondence_time?.trim()
                      )}
                      onChange={(_, val) => {
                        setFieldValue("correspondence_time", val);
                      }}
                      errorMessage={
                        touched?.correspondence_time
                          ? errors?.correspondence_time
                          : ""
                      }
                    />
                  </div>
                </div>
                <div className="w-full">
                  <MultiSelectButtonField
                    label={_t("To")}
                    name="user_to"
                    id="user_to"
                    labelPlacement="top"
                    required={true}
                    value={inputValues.user_details?.map((detail) => ({
                      ...detail,
                      label: detail.display_name,
                      avatarProps: {
                        user: {
                          name: detail.display_name,
                          image: detail.image,
                        },
                      },
                      value: detail.user_id,
                      onRemoveClick: () => {
                        if (detail.type_key !== "contact") {
                          const updatedList = inputValues?.user_details?.filter(
                            (item) =>
                              !(
                                item.user_id === detail.user_id &&
                                item.contact_id === 0
                              )
                          );

                          setInputValues((prev) => ({
                            ...prev,
                            user_details: updatedList,
                          }));
                          const userId = updatedList?.map(
                            (user) => user.user_id
                          );
                          formik.setFieldValue("user_to", userId?.join(","));
                        } else {
                          const updatedList = inputValues?.user_details?.filter(
                            (item) => item.contact_id !== detail.contact_id
                          );
                          setInputValues((prev) => ({
                            ...prev,
                            user_details: updatedList,
                          }));
                          const userId = updatedList?.map(
                            (user) => user.user_id
                          );
                          formik.setFieldValue("user_to", userId?.join(","));
                        }
                      },
                    }))}
                    errorMessage={
                      touched?.user_to && isSubmit ? errors?.user_to : ""
                    }
                    onClick={() => {
                      setIsOpenSelectAssignedTo(true);
                    }}
                    addonBefore={
                      <div className="flex items-center gap-1">
                        {inputValues?.user_details?.length === 1 && (
                          <>
                            <ContactDetailsButton
                              onClick={(event) => {
                                event.stopPropagation();
                                setSelectedContactId(
                                  inputValues?.user_details
                                    ? inputValues?.user_details?.[0]?.user_id?.toString()
                                    : ""
                                );
                                setSelectedAdditionalContactId(
                                  inputValues?.user_details
                                    ? inputValues?.user_details?.[0]?.contact_id?.toString() ||
                                        ""
                                    : ""
                                );
                                setIsOpenContactDetails(true);
                              }}
                            />
                            <DirectoryFieldRedirectionIcon
                              className="!w-5 !h-5"
                              directoryId={
                                inputValues?.user_details[0]?.user_id?.toString() ||
                                ""
                              }
                              directoryTypeKey={
                                inputValues?.user_details[0]?.dir_type == 1
                                  ? "employee"
                                  : inputValues?.user_details[0]?.type_key || ""
                              }
                            />
                          </>
                        )}
                        {inputValues?.user_details &&
                          inputValues?.user_details?.length > 1 && (
                            <AvatarIconPopover
                              placement="bottom"
                              assignedTo={
                                inputValues?.user_details as IAssignedToUsers[]
                              }
                              setSelectedUserId={(data) => {
                                setSelectedContactId(
                                  data?.id?.toString() || ""
                                );
                                setSelectedAdditionalContactId(
                                  data?.contactId?.toString() || ""
                                );
                              }}
                              setIsOpenContactDetails={setIsOpenContactDetails}
                              redirectionIcon
                            />
                          )}
                      </div>
                    }
                  />
                </div>
                <div className="w-full">
                  <InputField
                    label={_t("RFI") + " #"}
                    disabled={is_custom_rfi_id == 0}
                    name="custom_correspondence_id"
                    id="custom_correspondence_id"
                    labelPlacement="top"
                    isRequired={true}
                    value={
                      is_custom_rfi_id == 0
                        ? "Save To View"
                        : values?.custom_correspondence_id ?? ""
                    }
                    maxLength={20}
                    errorMessage={
                      isSubmit && touched?.custom_correspondence_id
                        ? errors?.custom_correspondence_id
                        : undefined
                    }
                    autoComplete="off"
                    onChange={(e) => {
                      const value = e?.target?.value;
                      setFieldValue("custom_correspondence_id", value);
                      if (value.trim().length === 0) {
                        setFieldError(
                          "custom_correspondence_id",
                          "This field is required."
                        );
                      } else {
                        setFieldError("custom_correspondence_id", "");
                      }
                    }}
                    onBlur={(e) =>
                      setFieldValue(
                        "custom_correspondence_id",
                        e?.currentTarget?.value?.trim()
                      )
                    }
                  />
                </div>
                <div className="w-full">
                  <TextAreaField
                    label={_t("Information Requested")}
                    labelPlacement="top"
                    name="info_request"
                    id="info_request"
                    required={true}
                    value={values?.info_request ?? ""}
                    errorMessage={
                      isSubmit && touched?.info_request
                        ? errors?.info_request
                        : ""
                    }
                    onChange={(e) => {
                      const value = e?.target?.value;
                      setFieldValue("info_request", value);
                      if (value.trim().length === 0) {
                        setFieldError(
                          "info_request",
                          "This field is required."
                        );
                      } else {
                        setFieldError("info_request", "");
                      }
                    }}
                    onBlur={(e) =>
                      setFieldValue(
                        "info_request",
                        e?.currentTarget?.value?.trim()
                      )
                    }
                  />
                </div>
              </SidebarCardBorder>

              <OnlyRequiredCustomFields
                componentList={componentList}
                formik={formik}
                isSubmit={isSubmit}
                loadingCustomField={loadingCustomField}
              />
            </div>
          </div>
          <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
            <PrimaryButton
              htmlType="submit"
              buttonText={_t("Create Request for Information")}
              onClick={() => {
                setIsSubmit(true);
              }}
              disabled={submittingFrm}
              isLoading={submittingFrm}
            />
          </div>
        </form>
      </Drawer>

      {isSelectProjectOpen && (
        <SelectProject
          isSingleSelect={true}
          open={isSelectProjectOpen}
          setOpen={setIsSelectProjectOpen}
          selectedProjects={selectedProject}
          onProjectSelected={(data) => {
            onChangeProject(data);
          }}
          isRequired={false}
          module_key={module_key}
          genericProjects="project,opportunity"
          isShowProjectType={true}
        />
      )}
      {isOpenSelectAssignedTo && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectAssignedTo}
          closeDrawer={() => {
            setIsOpenSelectAssignedTo(false);
          }}
          singleSelecte={false}
          setCustomer={(data) => {
            handleContact(
              data.length ? (data as TselectedContactSendMail[]) : []
            );
          }}
          options={[
            defaultConfig.employee_key,
            "my_crew",
            defaultConfig.customer_key,
            defaultConfig.misc_contact_key,
            defaultConfig.contractor_key,
            defaultConfig.vendor_key,
            "by_service",
            "my_project",
          ]}
          selectedCustomer={selectedCustomerList}
          groupCheckBox={true}
          projectId={values?.project_id as number}
        />
      )}
      {isOpenContactDetails && !!selectedContactId && (
        <ContactDetails
          isOpenContact={isOpenContactDetails}
          contactId={selectedContactId}
          onCloseModal={() => {
            setSelectedContactId("");
            setSelectedAdditionalContactId("");
            setIsOpenContactDetails(false);
          }}
          onEmailClick={(data) => {
            setSelectedData(data);
            setIsSendEmailSidebarOpen(true);
          }}
          additional_contact_id={selectedAdditionalContactId}
        />
      )}
      <DirSendEmail
        isOpen={isSendEmailSidebarOpen}
        options={[
          defaultConfig.employee_key,
          "my_crew",
          defaultConfig.customer_key,
          defaultConfig.lead_key,
          defaultConfig.contractor_key,
          defaultConfig.vendor_key,
          defaultConfig.misc_contact_key,
          "by_service",
        ]}
        onSendResponse={() => {
          setSelectedData({});
        }}
        onClose={() => {
          setIsSendEmailSidebarOpen(false);
          setSelectedData({});
        }}
        groupCheckBox={true}
        selectedCustomer={
          selectedData?.user_id
            ? ([selectedData] as TselectedContactSendMail[])
            : []
        }
        app_access={false}
      />
    </>
  );
};

export default AddRequestInformation;
