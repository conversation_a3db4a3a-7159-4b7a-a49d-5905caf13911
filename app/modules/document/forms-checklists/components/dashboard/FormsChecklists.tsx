// helper, hook, utils, redux,
import { getDefaultStatuscolor } from "~/helpers/helper";
import { useTranslation } from "~/hook";
// Atoms
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { DynamicTable } from "~/shared/components/molecules/dynamicTable";
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
// Other
import { GridReadyEvent } from "ag-grid-community";
import { MENU_OPTION } from "../../utils/constants";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";

const FormsChecklists = ({ formType }: { formType: string }) => {
  const { _t } = useTranslation();
  const currentModule = getCurrentMenuModule();
  const onGridReady = (gridParams: GridReadyEvent) => {};
  const columnDefs = [
    {
      headerName: "",
      field: "",
      minWidth: 125,
      maxWidth: 125,
      hide: formType === "used_form",
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      cellRenderer: () => {
        return (
          <div className="flex items-center m-auto gap-1.5 bg-blue-100 text-primary-900 py-1 px-[9px] rounded dark:bg-dark-800 dark:text-white/90 whitespace-nowrap w-fit">
            <Typography className="text-13 font-medium">
              {_t("Use This Form")}
            </Typography>
          </div>
        );
      },
    },
    {
      headerName: _t("Form Name"),
      field: "form_name",
      minWidth: 130,
      flex: 1,
      resizable: true,
      sortable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: () => {
        const formName = "Construction Project Initiation & Approval";
        return (
          <Tooltip title={formName}>
            <Typography className="table-tooltip-text">
              {formName || "-"}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Project"),
      field: "project_name",
      minWidth: 130,
      flex: 1,
      hide: formType === "company_form",
      sortable: true,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: () => {
        const projectName =
          "The Grand Haven Residential Development Initiative";
        return (
          <Tooltip title={projectName}>
            <Typography className="table-tooltip-text">
              {projectName || "-"}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Assigned To"),
      field: "assigned_to",
      minWidth: 110,
      maxWidth: 110,
      hide: formType === "company_form",
      cellClass: "ag-cell-center",
      headerClass: "ag-header-center",
      sortable: true,
      suppressMenu: true,
      cellRenderer: () => {
        const fullName = "PR";
        // const profileImage = data?.image;
        return (
          <Tooltip title={fullName}>
            <div className="w-fit mx-auto">
              <AvatarProfile
                user={{
                  name: fullName,
                  // image: profileImage,
                }}
                className="m-auto"
                iconClassName="text-[11px] font-semibold"
              />
            </div>
          </Tooltip>
        );
      },
    },
    {
      headerName: _t("Date"),
      field: "date",
      minWidth: 135,
      maxWidth: 135,
      sortable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: () => {
        const data = "20/Apr/2025";
        return data ? <DateTimeCard format="date" date={data} /> : <>-</>;
      },
    },
    {
      headerName: _t("Status"),
      field: "status",
      minWidth: 100,
      maxWidth: 100,
      hide: formType === "company_form",
      cellClass: "ag-cell-center",
      headerClass: "ag-header-center",
      sortable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: () => {
        const { color, textColor } = getDefaultStatuscolor("");
        const status = "Completed";

        return (
          <Tooltip title={status}>
            <div className="text-center overflow-hidden">
              <Tag
                color={color}
                style={{
                  color: `${textColor || ""}`,
                }}
                className={`${
                  textColor === "" && "!text-primary-900"
                } mx-auto text-13 type-badge common-tag max-w-24`}
              >
                {status}
              </Tag>
            </div>
          </Tooltip>
        );
      },
    },
    {
      headerName: "",
      field: "",
      maxWidth: formType === "used_form" ? 50 : 130,
      minWidth: formType === "used_form" ? 50 : 130,
      suppressMenu: true,
      cellStyle: { textAlign: "center" },
      headerClass: "ag-header-center",
      cellClass: "!cursor-auto",
      cellRenderer: () => {
        const is_favorite = false;
        return (
          <div className="flex items-center gap-0.5 justify-end">
            {formType === "company_form" && (
              <>
                <div className="w-6 h-6 flex items-center justify-center">
                  <Tooltip title={_t("Edit")}>
                    <FontAwesomeIcon
                      className="text-base w-4 h-4 text-primary-900"
                      icon="fa-regular fa-pencil"
                    />
                  </Tooltip>
                </div>
                <div className="w-6 h-6 flex items-center justify-center">
                  <Tooltip title={_t("Stock Form")}>
                    {/* Copied Form */}
                    <FontAwesomeIcon
                      className="text-base w-4 h-4 text-primary-900"
                      icon="fa-regular fa-memo-circle-check"
                      // fa-regular fa-file-lines
                    />
                  </Tooltip>
                </div>
                <div className="w-6 h-6 flex items-center justify-center">
                  <Tooltip
                    title={
                      is_favorite ? "Remove from Favorites" : "Add to Favorites"
                    }
                  >
                    <FontAwesomeIcon
                      className="text-base w-4 h-4 text-deep-orange-500 cursor-pointer"
                      icon={
                        is_favorite ? "fa-solid fa-star" : "fa-regular fa-star"
                      }
                    />
                  </Tooltip>
                </div>
              </>
            )}
            <DropdownMenu
              options={MENU_OPTION}
              icon="fa-regular fa-ellipsis-vertical"
              buttonClass="m-0 hover:!bg-[#0000000f]"
              footerText={_t(
                "Some actions might be unavailable depending on your privilege."
              )}
            />
          </div>
        );
      },
    },
  ];
  const { module_access = "no-access" } = currentModule || {};
  return (
    <>
      <div
        className={`list-view-table ag-grid-cell-pointer ag-theme-alpine ${
          module_access === "read_only"
            ? "h-[calc(100dvh-294px)]"
            : "h-[calc(100dvh-270px)]"
        }`}
      >
        <DynamicTable
          columnDefs={columnDefs}
          onGridReady={onGridReady}
          noRowsOverlayComponent={() => (
            <NoRecords
              rootClassName="w-full max-w-[280px]"
              image={`${window.ENV.CDN_URL}assets/images/create-record-directory.svg`}
              imageWSize="280"
              imageHSize="227"
              text={
                module_access === "full_access" ||
                module_access === "own_data_access" ? (
                  <div>
                    <Typography
                      onClick={() => {}}
                      className="sm:text-base text-xs underline underline-offset-1 text-black font-bold cursor-pointer"
                    >
                      {_t("Click here")}
                    </Typography>
                    <Typography className="sm:text-base text-xs text-black font-semibold">
                      {_t(" to Create a New Record")}
                    </Typography>
                  </div>
                ) : (
                  <Typography className="sm:text-base text-xs text-black font-semibold">
                    {_t("No Record Found")}
                  </Typography>
                )
              }
            />
          )}
          enableOpenInNewTab={window.ENV.ENABLE_ALL_CLICK}
        />
      </div>
    </>
  );
};

export default FormsChecklists;
