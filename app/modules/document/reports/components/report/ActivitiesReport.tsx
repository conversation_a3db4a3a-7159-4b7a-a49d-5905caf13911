// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
// Other
import { useTranslation } from "~/hook";
import { ICellRendererParams } from "ag-grid-community";
import { useState } from "react";
import { ActivitiesOpen } from "../sidebar/activitiesOpen";

const ActivitiesReport = () => {
  const { _t } = useTranslation();
  const [reportValue, setReportValue] = useState<any>();
  const rowData = [
    {
      report_name: _t("Employee Tasks"),
      key: "employee_activities",
    },
    {
      report_name: _t("Lead/Customer Tasks"),
      key: "lead_customer_activities",
    },
  ];
  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Tasks")}
        iconProps={{
          icon: "fa-solid fa-ballot-check",
          containerClassName:
            "bg-[linear-gradient(180deg,#FF81561a_0%,#EF531D1a_100%)]",
          id: "activities_report_icon",
          colors: ["#FF8156", "#EF531D"],
        }}
        children={
          <div className="pt-2">
            <div className="ag-theme-alpine">
              <StaticTable
                className="static-table"
                columnDefs={[
                  {
                    headerName: _t("Report Name"),
                    field: "report_name",
                    minWidth: 150,
                    flex: 1,
                    suppressMenu: true,
                    headerClass: "ag-header-left",
                    cellClass: "ag-cell-left",
                    cellRenderer: ({
                      data,
                    }: ICellRendererParams<(typeof rowData)[0]>) => {
                      const reportName = data?.report_name;
                      return (
                        <Tooltip title={reportName}>
                          <Typography className="table-tooltip-text">
                            {reportName || "-"}
                          </Typography>
                        </Tooltip>
                      );
                    },
                  },
                  {
                    headerName: "",
                    field: "",
                    maxWidth: 50,
                    minWidth: 50,
                    suppressMenu: true,
                    cellStyle: { textAlign: "center" },
                    headerClass: "ag-header-center",
                    cellClass: "!cursor-auto",
                    cellRenderer: ({
                      data,
                    }: ICellRendererParams<(typeof rowData)[0]>) => {
                      return (
                        <div className="flex items-center gap-2 justify-center">
                          <ButtonWithTooltip
                            tooltipTitle={_t("View")}
                            tooltipPlacement="top"
                            icon="fa-solid fa-eye"
                            onClick={() => setReportValue(data?.key)}
                          />
                        </div>
                      );
                    },
                  },
                ]}
                rowData={rowData}
              />
            </div>
          </div>
        }
      />
      <ActivitiesOpen
        reportValue={reportValue}
        setReportValue={setReportValue}
      />
    </>
  );
};

export default ActivitiesReport;
