// atoms
import { Head<PERSON> } from "~/shared/components/atoms/header";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Switch } from "~/shared/components/atoms/switch";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// molecules
import { FieldLabel } from "~/shared/components/molecules/fieldLabel";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { SelectField } from "~/shared/components/molecules/selectField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { RangePickerField } from "~/shared/components/molecules/rangePickerField";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
// Other
import { Form } from "@remix-run/react";
import { useTranslation } from "~/hook";
import { useState } from "react";

const ActivitiesOpen = ({
  setReportValue,
  reportValue,
}: TActivitiesOpenProps) => {
  const { _t } = useTranslation();
  const [first, setfirst] = useState<any>();
  return (
    <Drawer
      open={reportValue}
      rootClassName="drawer-open"
      width={718}
      push={false}
      maskClosable={false}
      classNames={{
        body: "!p-0 !overflow-hidden",
      }}
      title={
        <div className="flex items-center">
          <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
            <FontAwesomeIcon
              className="w-4 h-4"
              icon="fa-regular fa-ballot-check"
            />
          </div>
          <Header
            level={5}
            className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
          >
            {reportValue === "employee_activities"
              ? _t("Employee Tasks")
              : _t("Lead/Customer Tasks")}
          </Header>
        </div>
      }
      closeIcon={<CloseButton onClick={() => setReportValue(false)} />}
    >
      <Form method="post" noValidate className="py-4">
        <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
          <div className="grid gap-4">
            <SidebarCardBorder addGap={true}>
              <div className="w-full">
                <SelectField
                  label={_t("Type")}
                  labelPlacement="top"
                  options={[
                    {
                      label: "g",
                      value: "gy",
                    },
                  ]}
                />
              </div>
              {reportValue === "employee_activities" && (
                <div className="w-full">
                  <SelectField
                    label={_t("Status")}
                    labelPlacement="top"
                    options={[
                      {
                        label: "g",
                        value: "gy",
                      },
                    ]}
                  />
                </div>
              )}
              <div className="w-full">
                <div className="flex gap-2.5 items-center">
                  <FieldLabel
                    labelClass="!max-w-[80px]"
                    children={_t("Date Range")}
                  />
                  <Typography className="w-[calc(100%-133px)] h-px bg-[linear-gradient(90deg,#a5a5a53d_24%,#fafafa_100%)]"></Typography>
                  <Switch className="cf-switch success" />
                </div>
                <div className="w-full">
                  <RangePickerField
                    labelPlacement="top"
                    value={first}
                    allowClear={true}
                    onChange={(value) => {
                      setfirst(value);
                    }}
                  />
                </div>
              </div>
              <div className="w-full">
                <ButtonField
                  label={
                    reportValue === "employee_activities"
                      ? _t("Employees")
                      : _t("Contact")
                  }
                  labelPlacement="top"
                />
              </div>
            </SidebarCardBorder>
          </div>
        </div>
        <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
          <PrimaryButton htmlType="submit" buttonText={_t("Generate Report")} />
        </div>
      </Form>
    </Drawer>
  );
};
export default ActivitiesOpen;
