import { useEffect, useMemo, useRef, useState } from "react";
import parse from "html-react-parser";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import customParseFormat from "dayjs/plugin/customParseFormat";
// ag-grid
import {
  type ExcelCell,
  type ExcelExportParams,
  type ExcelRow,
  type ColDef,
  type ColumnApi,
  type GridApi,
  type GridOptions,
  type GridReadyEvent,
  type ICellRendererParams,
  type ColumnValueChangedEvent,
} from "ag-grid-community";

// Hook + redux
import { isExpiredAuthorization } from "~/zustand/global/config/slice";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { setAuthorizationExpired } from "~/zustand/global/config/action";
import { sanitizeString } from "~/helpers/helper";
import {
  cellExcel,
  EXCEL_STYLES,
  getAggFunc,
  getValueGetter,
  htmlRender,
  nameComparator,
  rowGroupCallback,
  stripHtmlTags,
  timeWorkedAggFunc,
  totalComparator,
} from "~/modules/document/reports/utils/constants";
import { getGlobalCompanySettings } from "~/zustand/global/settings/slice";
import { useTranslation } from "~/hook";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { getGConfig, getGSettings } from "~/zustand";
import {
  useAppReportDispatch,
  useAppReportSelector,
} from "~/modules/document/reports/redux/store";
import {
  clearStcFilterValue,
  setSelectedStcReport,
} from "~/modules/document/reports/redux/slices/staticRepTableSlice";
import { fetchStcReportColumnsData } from "~/modules/document/reports/redux/action/staticRepTableAction";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";

// Components
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { Spin } from "~/shared/components/atoms/spin";
import { AgGridTable } from "~/shared/components/atoms/table";
import { Progress } from "~/shared/components/atoms/progress";
import { Popover } from "~/shared/components/atoms/popover";
import { Typography } from "~/shared/components/atoms/typography";
import { faFilter as solidfaFilter } from "@fortawesome/pro-solid-svg-icons/faFilter";
import { convertImageUrlToBase64 } from "~/modules/people/safetymeetings/utils/helper";

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(customParseFormat);
const StaticReportTable = () => {
  const { _t } = useTranslation();
  const { date_format }: GSettings = getGSettings();

  const companySettings: IInitialGlobalData["settings"]["company_settings"] =
    getGlobalCompanySettings();
  const { header_logo, company_name = "" } = companySettings || {};

  const dispatch = useAppReportDispatch();
  const { formatter } = useCurrencyFormatter();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { authorization }: GConfig = getGConfig();
  const { timezone_utc_tz_id } = user || {};
  const gridApiRef = useRef<GridApi | null>(null);
  const eventSourceRef = useRef<EventSource | null>(null);
  const lastIndexRef = useRef(0);

  const {
    reportColumns,
    selectedStcRep,
    stcFilterValue,
  }: IStaticRepTableIntlState = useAppReportSelector(
    (state) => state.staticRepTable
  );
  const [columnApi, setColumnApi] = useState<ColumnApi | null>(null);
  const [isProgressStart, setIsProgressStart] = useState<boolean>(true);
  const [updateCount, setUpdateCount] = useState<number>(0);
  const [totalCount, setTotalCount] = useState<number>(0);

  const currentDateTime = () => {
    try {
      const testDate = dayjs().tz(timezone_utc_tz_id);
      return testDate.format("z") !== "Invalid Date"
        ? dayjs().tz(timezone_utc_tz_id)
        : dayjs();
    } catch (e) {
      return dayjs();
    }
  };

  const progressPerc: string = useMemo(() => {
    if (totalCount && totalCount > 0) {
      const percentage = Math.min((updateCount / totalCount) * 100, 100);

      return percentage.toFixed(0);
    }
    return "0";
  }, [updateCount, totalCount]);

  const date = dayjs();
  const buildURL = async (page: number) => {
    let tempAuthorization = authorization;
    const isExpired = isExpiredAuthorization();
    if (isExpired) {
      const response = (await webWorkerApi({
        url: "/api/auth/token",
      })) as IGetTokenFromNode;
      if (response?.success) {
        tempAuthorization = response.data.accessToken;
        setAuthorizationExpired(response.data.accessTokenExpired);
      }
    }
    const apiEndpoint = selectedStcRep?.report?.api_endpoint;
    let url = `${window.ENV.NODE_API3_URL}report/${apiEndpoint}?page=${page}&token=${tempAuthorization}&length=200`;

    url += `&timezone=${date.format("ZZ") || ""}&tzid=${
      Intl.DateTimeFormat().resolvedOptions().timeZone || ""
    }`;

    const filter = selectedStcRep?.filter;
    // Append filter keys manually
    for (const key in filter) {
      if (filter[key as keyof typeof filter]) {
        url += `&${encodeURIComponent(key)}=${encodeURIComponent(
          filter[key as keyof typeof filter]!
        )}`;
      }
    }

    if (selectedStcRep?.report?.filter_by) {
      url += `&filter_by=${selectedStcRep?.report?.filter_by}`;
    }

    if (apiEndpoint === "timecard-employees-summary-report") {
      url += "&show_project=1&show_weekend=1&show_cost_code=1";
    }
    return url;
  };

  const closeProgress = () => {
    setTimeout(() => {
      setIsProgressStart(false);
      setUpdateCount(0);
      setTotalCount(0);
    }, 1000);
  };

  const connectEventSource = async (startIndex = 0) => {
    setIsProgressStart(true);
    const url = await buildURL(startIndex);

    if (gridApiRef.current || startIndex == 0) {
      setTimeout(() => {
        gridApiRef.current!.showLoadingOverlay();
        gridApiRef.current!.setRowData([]);
      }, 10);
    }
    const eventSource = new EventSource(url);
    eventSourceRef.current = eventSource;

    eventSource.onmessage = (event: MessageEvent) => {
      const newData: any[] = JSON.parse(event.data);

      setUpdateCount((prev) => {
        const updated = prev + newData.length;
        return updated;
      });

      if (gridApiRef.current) {
        setTimeout(() => {
          gridApiRef?.current?.hideOverlay();
          gridApiRef?.current?.applyTransaction({ add: newData });
        }, 50);
      }

      lastIndexRef.current++;
    };

    eventSource.addEventListener("end", () => {
      closeProgress();
      eventSource.close();
    });

    eventSource.addEventListener("totalCount", (event: MessageEvent) => {
      setTotalCount(parseInt(event.data, 10));
    });

    eventSource.onerror = (e: Event) => {
      console.error("EventSource failed. Reconnecting...");
      eventSource.close();

      const errorData = (e as any)?.data;
      if (errorData === "Gateway timeout has occurred") {
        connectEventSource(lastIndexRef.current);
      } else {
        closeProgress();
      }
    };
  };

  // Get report records API
  useEffect(() => {
    if (gridApiRef.current && !reportColumns?.isLoading) {
      connectEventSource();
    }
    return () => {
      eventSourceRef.current?.close();
    };
  }, [gridApiRef.current, reportColumns?.isLoading]);

  // Get report columns API
  // TODO: Fix below date date key week_start_date
  useEffect(() => {
    if (selectedStcRep?.report?.stock_key) {
      const timeCardFilter: IFetchStcReportColumnsData = {
        stock_key: selectedStcRep.report.stock_key,
      };
      if (
        selectedStcRep.report.stock_key === "timecard_employees_weeklysheet"
      ) {
        timeCardFilter.start_date = "2025-06-01";
        timeCardFilter.end_date = "2025-06-07";
      }
      dispatch(
        fetchStcReportColumnsData({
          ...timeCardFilter,
        })
      );
    }
  }, [selectedStcRep?.report?.stock_key]);

  // Set group by
  useEffect(() => {
    if (!columnApi || !selectedStcRep?.filter?.group_by) return;

    // === Row Grouping ===
    if (selectedStcRep?.filter?.group_by) {
      columnApi.setRowGroupColumns([selectedStcRep?.filter?.group_by]);
    }
  }, [columnApi, selectedStcRep?.filter]);

  function dateComparator(date1: string, date2: string): number {
    const dateA = dayjs(date1, date_format, true);
    const dateB = dayjs(date2, date_format, true);

    if (!dateA.isValid()) return -1;
    if (!dateB.isValid()) return 1;

    return dateA.toDate().getTime() - dateB.toDate().getTime();
  }

  function AGformatNumber(data: ICellRendererParams) {
    if (data?.colDef?.field == "time_worked") {
      return data.value; // time values are returned as is
    } else {
      const val = data?.value;
      const nValue = formatter(Number(val).toFixed(2)).value;
      // const nValue = formatter(
      //   Number(val) == 0 ? Number(val).toFixed(0) : Number(val).toFixed(2)
      // ).value;
      return nValue;
    }
  }

  // Set report columns
  const columnDefs: ColDef[] = useMemo(() => {
    if (!reportColumns?.items || reportColumns.items.length === 0) {
      return [];
    }
    const generateColDefs = (): ColDef[] => {
      return reportColumns?.items.map((item) => {
        const field = item.column_key;
        const headerName = HTMLEntities.decode(
          sanitizeString(item.column_name)
        );
        const isRightAligned = item.classname === "text-end";

        const cellStyle = { textAlign: isRightAligned ? "right" : "left" };
        const headerClass = isRightAligned
          ? "ag-right-aligned-header"
          : "ag-left-aligned-header";
        const enableValue = isRightAligned;
        const allowedAggFuncs = isRightAligned
          ? ["sum", "avg", "min", "max"]
          : [];

        const isHyperlink =
          field === "file_1" || field === "file_2" || field === "file_3";

        const getFilterType = () => {
          if (item.column_type === "datetime") return "agDateColumnFilter";
          if (isRightAligned) return "agNumberColumnFilter";
          if (
            item.column_type !== "string" ||
            item.column_key === "est_project_manager"
          )
            return "agSetColumnFilter";
          return "agTextColumnFilter";
        };
        const cellRendererSelector = (p: ICellRendererParams) => {
          if (!p.node.group && item.column_key === "first_name") {
            return {
              component: "agGroupCellRenderer",
              params: {
                innerRenderer: (params: ICellRendererParams) =>
                  item.column_key == "first_name"
                    ? htmlRender(params)
                    : params.value,
              },
            };
          }

          return undefined;
        };

        // For hours calculation
        const hourFields = ["hours_used", "total_hours"];
        const isHours =
          field.includes("work_mins") || hourFields.includes(field);

        return {
          field,
          headerName,
          cellStyle,
          enableRowGroup: field == selectedStcRep?.filter?.group_by,
          headerClass,
          enableValue: isHours ? true : enableValue,
          aggFunc: isHours
            ? timeWorkedAggFunc
            : isRightAligned
            ? getAggFunc(item as IReportColumns)
            : "",
          allowedAggFuncs: isHours ? ["sum"] : allowedAggFuncs,
          filter: getFilterType(),
          comparator:
            item.column_type == "datetime"
              ? dateComparator
              : item.classname == "text-end"
              ? totalComparator
              : nameComparator,
          cellRenderer: !isRightAligned && field != "rating" ? htmlRender : "",
          cellRendererSelector,
          cellClass: isRightAligned
            ? "numberCommaSeparated"
            : isHyperlink
            ? "hyperlinks"
            : undefined,
          valueGetter: isRightAligned
            ? getValueGetter(item as IReportColumns)
            : "",
          valueFormatter: isRightAligned ? AGformatNumber : "",
        } as ColDef;
      });
    };

    return generateColDefs();
  }, [reportColumns?.items, selectedStcRep?.filter]);

  const defaultColDef: ColDef = {
    filter: false,
    enablePivot: false,
    sortable: false,
    filterParams: { debounceMs: 1000 },
    suppressMovable: true,
    resizable: false,
    minWidth: 100,
    menuTabs: [],
  };

  const handleColumnValueChanged = (event: ColumnValueChangedEvent) => {
    const hasValueColumns = event.columnApi.getValueColumns().length > 0;
    const includeFooter = hasValueColumns ? true : false;
    event.api.setGroupIncludeTotalFooter(includeFooter);
  };

  const gridOptions: GridOptions = useMemo(() => {
    const isGroup =
      !!selectedStcRep?.filter?.group_by &&
      selectedStcRep?.filter?.group_by !== "none";

    return {
      suppressContextMenu: true,
      rowGroupPanelShow: isGroup ? "always" : "never",
      rowSelection: "multiple",
      rowDragManaged: false,
      animateRows: true,
      suppressRowClickSelection: true,
      enableRangeSelection: true,
      // groupIncludeFooter: true,
      groupIncludeTotalFooter: true, // Shows totals below group
      onColumnValueChanged: handleColumnValueChanged,
      onColumnRowGroupChanged: handleColumnValueChanged,
      // autoGroupColumnDef: {
      //   cellClass: getIndentClass,
      // },
      suppressAggFuncInHeader: true,
      rowHeight: 30,
      headerHeight: 34,
      detailCellRenderer: "myDetailCellRenderer",
      masterDetail: true,
      detailRowHeight: 180,
      excelStyles: EXCEL_STYLES,
    };
  }, [selectedStcRep?.filter]);

  const htmlRendererSub = (data: string) => {
    return parse(
      `<span className="truncate block">${HTMLEntities.decode(
        sanitizeString(data || "")
      )}</span>`
    );
  };
  const htmlRendererSubExc = (data: string) => {
    return HTMLEntities.decode(sanitizeString(data || ""));
  };
  //  Multi tabelsa
  const detailCellRenderer = (props: any) => {
    const commonColumns = [
      {
        field: "first_name",
        headerName: "First Name",
        cellRenderer: ({ data }: ICellRendererParams) => {
          return htmlRendererSub(data.first_name);
        },
      },
      {
        field: "last_name",
        headerName: "Last Name",
        cellRenderer: ({ data }: ICellRendererParams) => {
          return htmlRendererSub(data.last_name);
        },
      },
      { field: "phone", headerName: "Phone" },
      { field: "cell", headerName: "Cell" },
      { field: "email", headerName: "Email" },
      {
        field: "title",
        headerName: "Title",
        cellRenderer: ({ data }: ICellRendererParams) => {
          return htmlRendererSub(data.title);
        },
      },
      {
        field: "notes",
        headerName: "Notes",
        cellRenderer: ({ data }: ICellRendererParams) => {
          return htmlRendererSub(data.notes);
        },
      },
    ];
    return (
      <div style={{ display: "flex", flexDirection: "column", gap: 16 }}>
        {props.data?.additional_contact?.length > 0 && (
          <div className="ag-theme-alpine">
            <AgGridTable
              rowData={props.data.additional_contact}
              columnDefs={commonColumns}
              defaultColDef={{ flex: 1 }}
              headerHeight={34}
              rowHeight={30}
            />
          </div>
        )}
      </div>
    );
  };

  const handleGridReady = (params: GridReadyEvent) => {
    gridApiRef.current = params.api;
    setColumnApi(params.columnApi);
  };

  // Download Excel

  const handleExcelExport = async () => {
    let logoUrl =
      header_logo || "https://cdn.contractorforeman.net/assets/images/logo.svg";

    let logoWidth = 0;
    let logoHeight = 0;
    if (logoUrl) {
      const { base64, width, height } = await convertImageUrlToBase64(logoUrl);
      logoWidth = logoUrl?.includes("assets/images/logo.svg") ? 320 : width;
      logoHeight = logoUrl?.includes("assets/images/logo.svg") ? 72 : height;

      logoUrl = base64 || company_name;
    }

    if (!gridApiRef.current || !columnApi) return;

    const visibleCols = columnApi.getAllDisplayedColumns();
    const columnFields: string[] = [];
    const autoGroupCol = visibleCols.find(
      (col) => col.getColDef().showRowGroup != null
    );

    visibleCols.forEach((col) => {
      const { field } = col.getColDef();
      if (!field) return;
      columnFields.push(field);
    });

    const autoGroupColId = autoGroupCol?.getColId();
    const exportColumnKeys = autoGroupColId
      ? [
          autoGroupColId,
          ...columnFields.filter((key) => key !== autoGroupColId),
        ]
      : columnFields;

    const reportTitle = selectedStcRep?.report?.report_name?.replaceAll(
      " Export",
      ""
    );

    const getEmptyCells = (count: number): ExcelCell[] =>
      Array.from({ length: count }, () => ({
        data: { value: "", type: "String" },
      }));
    const filterInfo: string[] = [];
    const filterVal = stcFilterValue?.length > 0 ? stcFilterValue : [];
    // let dateRangeFlt = "";
    filterVal
      .filter((i) => !!i.value)
      ?.sort((a, b) =>
        a.label == "Date Range" ? -1 : b.label == "Date Range" ? 1 : 0
      )
      ?.forEach((col) => {
        filterInfo.push(`${col?.label}: ${col?.value}`);

        // if (col?.value && col.label != "Date Range") {
        //   filterInfo.push(`${col?.label}: ${col?.value}`);
        // } else if (col.label === "Date Range") {
        //   dateRangeFlt = `${col?.label}: ${col?.value}`;
        // }
      });

    const prependContent: ExcelRow[] = [
      // { cells: [] },
      {
        cells: [
          {
            data: {
              type: "String",
              value: logoUrl, // see imageUtils
            },
            mergeAcross: 1,
            styleId: "title",
          },
          ...getEmptyCells(3),
          {
            data: { value: `${reportTitle} Report`, type: "String" },
            mergeAcross: 2,
            styleId: "title",
          },
        ],
      },
      {
        cells: [
          ...getEmptyCells(4),
          {
            data: {
              value: `Created On: ${currentDateTime().format(
                `${date_format}, hh:mm:ss A`
              )}`,
              type: "String",
            },
            mergeAcross: 2,
            styleId: "created_on",
          },
        ],
      },
      { cells: [] },
      ...filterInfo.map(
        (info): ExcelRow => ({
          cells: [
            {
              data: { value: info, type: "String" },
              mergeAcross: 4,
              styleId: "filter_style",
            },
          ],
        })
      ),
      { cells: [] },
    ];

    // const url = await convertImageUrlToBase64("")
    const exportParams: ExcelExportParams = {
      fileName: `${reportTitle} Report.xlsx`,
      columnKeys: exportColumnKeys,
      onlySelected: false,
      skipColumnHeaders: false,
      columnWidth: 120,
      prependContent,
      // Below code for Logo
      // rowHeight: (params) => (params.rowIndex === 1 ? 82 : 20),
      addImageToCell: (rowIndex, col, value) => {
        if (rowIndex === 1 && value?.includes("data:")) {
          return {
            image: {
              id: "logo",
              base64: value,
              imageType: "jpg",
              width: logoWidth < 320 ? logoWidth : 320,
              height: logoHeight < 100 ? logoHeight : 100,
              position: {
                rowSpan: 3,
                colSpan: 2,
              },
            },
          };
        }
        return;
      },
      autoConvertFormulas: true,
      processCellCallback: stripHtmlTags,
      processRowGroupCallback: rowGroupCallback,
      getCustomContentBelowRow: (params): ExcelRow[] | undefined => {
        const { node } = params;
        const callRecords = node.data?.additional_contact;
        const hasData = node.master && callRecords?.length > 0;
        if (!hasData) return;

        return [
          {
            outlineLevel: 1,
            cells: [
              cellExcel(""),
              cellExcel("First Name", "header"),
              cellExcel("Last Name", "header"),
              cellExcel("Phone", "header"),
              cellExcel("Cell", "header"),
              cellExcel("Email", "header"),
              cellExcel("Title", "header"),
              cellExcel("Notes", "header"),
            ],
          },
          ...callRecords.map((record: any) => ({
            outlineLevel: 1,
            cells: [
              cellExcel(""),
              cellExcel(htmlRendererSubExc(record.first_name), "body"),
              cellExcel(htmlRendererSubExc(record.last_name), "body"),
              cellExcel(record.phone, "body"),
              cellExcel(record.cell, "body"),
              cellExcel(record.email, "body"),
              cellExcel(htmlRendererSubExc(record.title), "body"),
              cellExcel(htmlRendererSubExc(record.notes), "body"),
            ],
          })),
        ];
      },
      sheetName: "Sheet",
    };

    gridApiRef.current.exportDataAsExcel(exportParams);
  };

  // Remove export option
  function getCustomContextMenuItems(params: any) {
    // Get the default context menu items
    return params.defaultItems
      .slice()
      .filter((item: string) => item !== "export");
  }

  return (
    <>
      <div className="grid gap-4 p-4">
        {isProgressStart && <Progress percent={Number(progressPerc || "")} />}

        <div className="py-3 px-[15px] common-card h-fit">
          <CrudCommonCard
            headerTitle={_t(selectedStcRep?.report?.report_name || "")}
            iconProps={{
              icon: "fa-solid fa-table-list",
              containerClassName:
                "bg-[linear-gradient(180deg,#B974FF1a_0%,#8308FF1a_100%)]",
              id: "contractor_details_icon",
              colors: ["#B974FF", "#8308FF"],
            }}
            headerRightButton={
              <div className="flex gap-2">
                {stcFilterValue?.length > 0 && (
                  <Popover
                    placement="bottomRight"
                    content={
                      <div className="p-2 dark:bg-dark-900 min-w-[155px]">
                        <Typography className="sm:text-base text-xs text-black font-semibold">
                          {_t("Applied Filters")}
                        </Typography>

                        <div className="mt-2 max-w-[400px] grid gap-1">
                          {stcFilterValue
                            .filter((i) => !!i.value)
                            .sort((a, b) =>
                              a.label == "Date Range"
                                ? -1
                                : b.label == "Date Range"
                                ? 1
                                : 0
                            )
                            .map((i, idx) => (
                              <Typography
                                key={`ap_${idx}_${i.label}`}
                                title="small"
                                className="text-13 text-black"
                              >
                                <Typography
                                  title="small"
                                  className="font-semibold text-black inline-block mr-1"
                                >
                                  {`${i.label}: `}
                                </Typography>
                                {i.value}
                              </Typography>
                            ))}
                        </div>
                      </div>
                    }
                    trigger="hover"
                  >
                    <div className="flex relative items-center justify-center pr-1.5">
                      <ButtonWithTooltip
                        icon={solidfaFilter}
                        tooltipTitle={""}
                        tooltipPlacement="top"
                        iconClassName="h-5 w-5"
                        className="!w-7 !h-7 disabled:bg-transparent"
                        onClick={() => {}}
                      />
                    </div>
                  </Popover>
                )}

                <ButtonWithTooltip
                  icon="fa-solid fa-file-excel"
                  tooltipTitle={_t("Download Excel")}
                  tooltipPlacement="top"
                  iconClassName="h-5 w-5"
                  className="!w-7 !h-7 disabled:bg-transparent mr-2"
                  onClick={() => {
                    handleExcelExport();
                  }}
                  disabled={isProgressStart}
                />

                <ButtonWithTooltip
                  tooltipTitle={_t("Close")}
                  tooltipPlacement="top"
                  icon="fa-regular fa-xmark"
                  iconClassName="h-5 w-5 !text-primary-900 group-hover/buttonHover:!text-deep-orange-500"
                  className={`!w-6 !h-6 !shadow-[0px_1px_3px] !shadow-primary-200`}
                  onClick={() => {
                    dispatch(clearStcFilterValue());
                    dispatch(setSelectedStcReport({}));
                  }}
                />
              </div>
            }
            children={
              <div className="pt-2 reports-module-table">
                {reportColumns?.isLoading && (
                  <Spin className="w-full h-[150px] flex items-center justify-center" />
                )}
                {!reportColumns?.isLoading && (
                  // <div className="ag-theme-alpine h-[585px]">
                  <div
                    className={`ag-theme-alpine h-[585px] relative ${
                      isProgressStart
                        ? "overflow-hidden pointer-events-none"
                        : ""
                    }`}
                  >
                    {/* 
                    // TODO: If want to add loding screen
                    {isProgressStart && (
                      <div className="absolute inset-0 bg-white/50 z-10" />
                    )} */}

                    <AgGridTable
                      className="static-table"
                      gridOptions={{ ...gridOptions }}
                      defaultColDef={defaultColDef}
                      columnDefs={columnDefs}
                      getContextMenuItems={getCustomContextMenuItems}
                      onGridReady={handleGridReady}
                      detailCellRenderer={detailCellRenderer}
                      isRowMaster={(data) =>
                        data?.additional_contact?.length > 0
                      }
                      noRowsOverlayComponentParams={{
                        message: "No Rows To Show",
                      }}
                    />
                  </div>
                )}
              </div>
            }
          />
        </div>
      </div>
    </>
  );
};

export default StaticReportTable;
