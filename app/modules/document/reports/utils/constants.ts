import {
  type ProcessRowGroupForExportParams,
  type ProcessCellForExportParams,
  type ExcelStyle,
  ExcelRow,
} from "ag-grid-community";
import { ColDef } from "ag-grid-community";
import parse from "html-react-parser";
import { sanitizeString } from "~/helpers/helper";

export const REPORTS_SIDEBAR_MENU_LIST: IReportSidebarMenuItem[] = [
  {
    name: "Saved (Custom) Reports",
    key: "custom_report",
    icon: "fa-regular fa-rectangle-list",
    secIcon: {
      icon: "fa-solid fa-file-signature",
      containerClassName:
        "bg-[linear-gradient(180deg,#F275941a_0%,#85489E1a_100%)]",
      id: "custom_report_icon",
      colors: ["#F27594", "#85489E"],
    },
  },
  {
    name: "Tasks",
    key: "activities_report",
    icon: "fa-regular fa-ballot-check",
    secIcon: {
      icon: "fa-solid fa-ballot-check",
      containerClassName:
        "bg-[linear-gradient(180deg,#FF81561a_0%,#EF531D1a_100%)]",
      id: "activities_report_icon",
      colors: ["#FF8156", "#EF531D"],
    },
    rowData: [
      {
        report_name: "Employee Tasks",
        parent_key: "activities_report",
        key: "employee_activities",
        stock_key: "employee_activities",
        api_endpoint: "directory-activities-report",
        filter_by: "employee",
      },
      {
        report_name: "Lead/Customer Tasks",
        parent_key: "activities_report",
        key: "lead_customer_activities",
        stock_key: "lead_customer_tasks",
        api_endpoint: "directory-activities-report",
        filter_by: "sales",
      },
    ],
  },
  {
    name: "Change Orders Report",
    key: "change_orders_report",
    icon: "fa-regular fa-cart-circle-xmark",
    secIcon: {
      icon: "fa-solid fa-cart-circle-xmark",
      containerClassName:
        "bg-[linear-gradient(180deg,#7FA3FF1a_0%,#3387FD1a_100%)]",
      id: "change_orders_report_icon",
      colors: ["#7FA3FF", "#3387FD"],
    },
    rowData: [
      {
        report_name: "Change Order By Project",
        parent_key: "change_orders_report",
        key: "change_order_project",
        stock_key: "change_order_project",
        api_endpoint: "change-order",
        filter_by: "project_id",
      },
      {
        report_name: "Change Order Request By Project",
        parent_key: "change_orders_report",
        key: "change_order_request_project",
        stock_key: "change_order_request_project",
        api_endpoint: "change-order-request",
        filter_by: "project_id",
      },
    ],
  },
  {
    name: "Contacts & Renewals",
    key: "contacts_renewals_report",
    icon: "fa-regular fa-address-book",
    secIcon: {
      icon: "fa-solid fa-address-book",
      containerClassName:
        "bg-[linear-gradient(180deg,#50EBFD1a_0%,#5996E91a_100%)]",
      id: "change_orders_report_icon",
      colors: ["#50EBFD", "#5996E9"],
    },
    rowData: [
      {
        report_name: "Directory Licenses",
        parent_key: "contacts_renewals_report",
        key: "directory_licenses",
        stock_key: "directory_license_contact",
        api_endpoint: "directory-licenses",
        filter_by: "contact",
      },
      {
        report_name: "Contractors Insurance",
        parent_key: "contacts_renewals_report",
        key: "contractors_insurance",
        stock_key: "contractor_insurance",
        api_endpoint: "directory-licenses",
        filter_by: "contractor_insurance",
      },
      {
        report_name: "Vendors Insurance",
        parent_key: "contacts_renewals_report",
        key: "vendors_insurance",
        stock_key: "vendor_insurance",
        api_endpoint: "directory-licenses",
        filter_by: "vendor_insurance",
      },
      {
        report_name: "Contractors Licenses",
        parent_key: "contacts_renewals_report",
        key: "contractors_licenses",
        stock_key: "contractor_license",
        api_endpoint: "directory-licenses",
        filter_by: "contractor_license",
      },
      {
        report_name: "Employee Training",
        parent_key: "contacts_renewals_report",
        key: "employee_training",
        stock_key: "employee_training",
        api_endpoint: "directory-licenses",
        filter_by: "employee_training",
      },
    ],
  },
  {
    name: "Daily Reports",
    key: "daily_report",
    icon: "fa-regular fa-calendar-check",
    secIcon: {
      icon: "fa-solid fa-calendar-check",
      containerClassName:
        "bg-[linear-gradient(180deg,#B974FF1a_0%,#8308FF1a_100%)]",
      id: "daily_report_icon",
      colors: ["#B974FF", "#8308FF"],
    },
    rowData: [
      {
        report_name: "Employees On Site",
        parent_key: "daily_report",
        key: "employees_on_site",
        stock_key: "employees_on_site",
        api_endpoint: "daily-log-summary-report",
        filter_by: "employees_on_site",
      },
      {
        report_name: "Equipment Delivered",
        parent_key: "daily_report",
        key: "equipment_delivered",
        stock_key: "equipment_delivered",
        api_endpoint: "daily-log-summary-report",
        filter_by: "equipment_delivered",
      },
      {
        report_name: "Equipment Used",
        parent_key: "daily_report",
        key: "equipment_used",
        stock_key: "equipment_used",
        api_endpoint: "daily-log-summary-report",
        filter_by: "equipment_used",
      },
      {
        report_name: "Material Delivered",
        parent_key: "daily_report",
        key: "material_delivered",
        stock_key: "material_delivered",
        api_endpoint: "daily-log-summary-report",
        filter_by: "material_delivered",
      },
      {
        report_name: "Material Used",
        parent_key: "daily_report",
        key: "material_used",
        stock_key: "material_used",
        api_endpoint: "daily-log-summary-report",
        filter_by: "material_used",
      },
      {
        report_name: "Subs On Site",
        parent_key: "daily_report",
        key: "subs_on_site",
        stock_key: "subs_on_site",
        api_endpoint: "daily-log-summary-report",
        filter_by: "subs_on_site",
      },
      {
        report_name: "Work Summary",
        parent_key: "daily_report",
        key: "work_summary",
        stock_key: "work_summery",
        api_endpoint: "daily-log-summary-report",
        filter_by: "work_summery",
      },
    ],
  },
  {
    name: "Equipment and Vehicles Report",
    key: "equipment_vehicles_report",
    icon: "fa-regular fa-car-side",
    secIcon: {
      icon: "fa-solid fa-car-side",
      containerClassName:
        "bg-[linear-gradient(180deg,#1CB5E01a_0%,#0086AB1a_100%)]",
      id: "equipment_vehicles_report_icon",
      colors: ["#1CB5E0", "#0086AB"],
    },
    rowData: [
      {
        report_name: "Equipment Log",
        parent_key: "equipment_vehicles_report",
        key: "equipment_log",
        stock_key: "equipment_log",
        api_endpoint: "equipment-log-summary-report",
        filter_by: "",
      },
      {
        report_name: "Equipment Maintenance Log",
        parent_key: "equipment_vehicles_report",
        key: "equipment_maintenance_log",
        stock_key: "equipment_maintenance",
        api_endpoint: "equipment-maintenance-summary-report",
        filter_by: "equipment_maintenance",
      },
      {
        report_name: "Insurance, Registration & Inspection Equipment",
        parent_key: "equipment_vehicles_report",
        key: "insurance_inspection_equipment",
        stock_key: "equipment_inspection",
        api_endpoint: "equipment-renewals-summary-report",
        filter_by: "equipment_insurance",
      },
      {
        report_name: "Vehicle Insurance",
        parent_key: "equipment_vehicles_report",
        key: "vehicle_insurance",
        stock_key: "vehicle_insurance",
        api_endpoint: "vehicles-renewals-summary-report",
        filter_by: "vehicle_registration",
      },
      {
        report_name: "Vehicle Log",
        parent_key: "equipment_vehicles_report",
        key: "vehicle_log",
        stock_key: "vehicle_log",
        api_endpoint: "vehicles-summary-report",
        filter_by: "date_added",
      },
      {
        report_name: "Vehicle Maintenance Log",
        parent_key: "equipment_vehicles_report",
        key: "vehicle_maintenance_log",
        stock_key: "vehicle_maintenance",
        api_endpoint: "vehicles-summary-report",
        filter_by: "maintenance",
      },
    ],
  },
  {
    name: "Estimate  Report",
    key: "estimate_report",
    icon: "fa-regular fa-calculator",
    secIcon: {
      icon: "fa-solid fa-calculator",
      containerClassName:
        "bg-[linear-gradient(180deg,#F275941a_0%,#85489E1a_100%)]",
      id: "estimate_report_icon",
      colors: ["#F27594", "#85489E"],
    },
    configKey: "estimate_module",
    rowData: [
      {
        report_name: "Estimates",
        parent_key: "estimate_report",
        key: "estimates",
        stock_key: "estimate",
        api_endpoint: "report-estimate",
        filter_by: "customer_id",
      },
    ],
  },
  {
    name: "Finances Report",
    key: "finances_report",
    icon: "fa-regular fa-file-invoice-dollar",
    secIcon: {
      icon: "fa-solid fa-file-invoice-dollar",
      containerClassName:
        "bg-[linear-gradient(180deg,#42DD9B1a_0%,#3CB9B31a_100%)]",
      id: "finances_report_icon",
      colors: ["#42DD9B", "#3CB9B3"],
    },
    rowData: [
      {
        report_name: "Accounts Payable",
        parent_key: "finances_report",
        key: "accounts_payable",
        stock_key: "accounts_payable",
        api_endpoint: "accounts-payable",
        filter_by: "",
      },
      {
        report_name: "Accounts Receivable",
        parent_key: "finances_report",
        key: "accounts_receivable",
        stock_key: "accounts_receivable",
        api_endpoint: "account-receivable",
        filter_by: "",
      },
      {
        report_name: "Expenses",
        parent_key: "finances_report",
        key: "expenses",
        stock_key: "expense",
        api_endpoint: "report-expense",
        filter_by: "project_id",
      },
      {
        report_name: "Invoices",
        parent_key: "finances_report",
        key: "invoices",
        stock_key: "invoice",
        api_endpoint: "invoice-summary-report",
        filter_by: "",
      },
      {
        report_name: "Payments",
        parent_key: "finances_report",
        key: "payments",
        stock_key: "payment",
        api_endpoint: "payment-summary-report",
        filter_by: "customer_id",
      },
    ],
  },
  {
    name: "Incidents & Employee Write-Ups",
    key: "incidents_employee_report",
    icon: "fa-regular fa-calendar-lines-pen",
    secIcon: {
      icon: "fa-solid fa-calendar-lines-pen",
      containerClassName:
        "bg-[linear-gradient(180deg,#81BBFC1a_0%,#33A9FF1a_100%)]",
      id: "incidents_employee_report_icon",
      colors: ["#81BBFC", "#33A9FF"],
    },
    rowData: [
      {
        report_name: "Employee Write-Ups By Employee",
        parent_key: "incidents_employee_report",
        key: "employee_write_ups",
        stock_key: "employee_writeup",
        api_endpoint: "employee-writeup",
        filter_by: "",
      },
      {
        report_name: "Incident By Date",
        parent_key: "incidents_employee_report",
        key: "incident_by_date",
        stock_key: "incident",
        api_endpoint: "report-incident",
        filter_by: "incident_date",
      },
      {
        report_name: "OSHA 300 Log By Date",
        parent_key: "incidents_employee_report",
        key: "osha_date",
        stock_key: "osha_detail",
        api_endpoint: "report-incident-osha-detail",
        filter_by: "osha_detail",
      },
    ],
  },
  {
    name: "Job Costs",
    key: "job_costs_report",
    icon: "fa-regular fa-file-magnifying-glass",
    secIcon: {
      icon: "fa-solid fa-file-magnifying-glass",
      containerClassName:
        "bg-[linear-gradient(180deg,#FAC4501a_0%,#F8A9001a_100%)]",
      id: "job_costs_report_icon",
      colors: ["#FAC450", "#F8A900"],
    },
    rowData: [
      {
        report_name: "Invoiced Vs Actual Report",
        description:
          "Sorted by Project Report shows Contract Value, Invoiced and Actuals with Gross Profit Calculation and %",
        parent_key: "job_costs_report",
        key: "invoiced_actual_report",
        stock_key: "",
        api_endpoint: "",
        filter_by: "job_cost_billing_minus_actual",
      },
      {
        report_name: "Budget Vs Actual Labor Report",
        description:
          "Report to show how estimated amount of labor is Measured against Actual labor pulled from Time Cards",
        parent_key: "job_costs_report",
        key: "budget_actual_labor_report",
        stock_key: "",
        api_endpoint: "",
        filter_by: "labor_budget",
      },
      {
        report_name: "Committed Vs Actual",
        description:
          "Clean Simple Report showing you Totals by cost Code of Committed and Actual Cost with Variances",
        parent_key: "job_costs_report",
        key: "committed_actual",
        stock_key: "",
        api_endpoint: "",
        filter_by: "labor_budget_new",
      },
      {
        report_name: "Gross Profit Summary",
        description:
          "Calculates the Contract Value against Billings and totals Contract minus cost and Billings minus cost",
        parent_key: "job_costs_report",
        key: "gross_profit_summary",
        stock_key: "",
        api_endpoint: "",
        filter_by: "gross_actual_profitability",
      },
      {
        report_name: "Job Cost Actual Report (Detailed)",
        description:
          "Shows Contract Value and Cost Budget against Time Cards, Bills and Expenses with 3 Different totals",
        parent_key: "job_costs_report",
        key: "job_cost_actual_report",
        stock_key: "",
        api_endpoint: "",
        filter_by: "",
      },
      {
        report_name: "Job Cost Committed Per Project",
        description:
          "Simple Report showing Breakdown by Cost Codes of Estimated VS Committed w/ Variance",
        parent_key: "job_costs_report",
        key: "job_cost_committed_project",
        stock_key: "",
        api_endpoint: "",
        filter_by: "",
      },
      {
        report_name: "Job Cost Committed Report (Detailed)",
        description:
          "Detailed by Cost Code showing Estimated VS Committed with Variances w/ 4 Different Totals Columns",
        parent_key: "job_costs_report",
        key: "job_cost_committed_report",
        stock_key: "",
        api_endpoint: "",
        filter_by: "",
      },
      {
        report_name: "Work In Progress (WIP) Report",
        description:
          "Provides a detailed report of all Job Cost Actual transactions",
        parent_key: "job_costs_report",
        key: "work_in_progress_report",
        stock_key: "",
        api_endpoint: "",
        filter_by: "",
      },
      {
        report_name: "Job Cost Transactions Report",
        description:
          "Clean Simple Report showing you Totals by cost Code of Committed and Actual Cost with Variances",
        parent_key: "job_costs_report",
        key: "job_cost_transactions",
        stock_key: "",
        api_endpoint: "job-cost-transactions",
        filter_by: "",
      },
      {
        report_name: "Project Management Summary",
        description:
          "Shows the overall summary of projects including Actual Cost, Contract Amount, Estimated Profit, etc.",
        parent_key: "job_costs_report",
        key: "project_management_summary",
        stock_key: "",
        api_endpoint: "",
        filter_by: "",
      },
    ],
  },
  {
    name: "Misc. Reports",
    key: "misc_report",
    icon: "fa-regular fa-file-chart-pie",
    secIcon: {
      icon: "fa-solid fa-file-chart-pie",
      containerClassName:
        "bg-[linear-gradient(180deg,#FF8AE51a_0%,#FD3ED31a_100%)]",
      id: "misc_report_icon",
      colors: ["#FF8AE5", "#FD3ED3"],
    },
    rowData: [
      {
        report_name: "Form Results",
        parent_key: "misc_report",
        key: "form_results",
        stock_key: "",
        api_endpoint: "checklist-form",
        filter_by: "",
      },
      {
        report_name: "Service Tickets",
        parent_key: "misc_report",
        key: "service_tickets",
        stock_key: "service_ticket",
        api_endpoint: "service-ticket",
        filter_by: "project_id",
      },
    ],
  },
  {
    name: "Punchlists & Submittal",
    key: "punchlists_submittal_report",
    icon: "fa-regular fa-square-list",
    secIcon: {
      icon: "fa-solid fa-square-list",
      containerClassName:
        "bg-[linear-gradient(180deg,#FF868A1a_0%,#FD33331a_100%)]",
      id: "punchlists_submittal_report_icon",
      colors: ["#FF868A", "#FD3333"],
    },
    configKey: "submittal_module",
    rowData: [
      {
        report_name: "Punchlist By Assigned To",
        parent_key: "punchlists_submittal_report",
        key: "punchlist_assigned_to",
        stock_key: "punchlist_by_assigned_to",
        api_endpoint: "punchlist",
        filter_by: "assigned_to",
      },
      {
        report_name: "Punchlist By Project",
        parent_key: "punchlists_submittal_report",
        key: "punchlist_project",
        stock_key: "punchlist_by_project",
        api_endpoint: "punchlist",
        filter_by: "project_id",
      },
      {
        report_name: "Submittals",
        parent_key: "punchlists_submittal_report",
        key: "submittals",
        stock_key: "submittal",
        api_endpoint: "report-submittal",
        filter_by: "submittal_id",
      },
    ],
  },
  {
    name: "Purchase Order Report",
    key: "purchase_order_report",
    icon: "fa-regular fa-money-check-pen",
    secIcon: {
      icon: "fa-solid fa-money-check-pen",
      containerClassName:
        "bg-[linear-gradient(180deg,#50EBFD1a_0%,#5996E91a_100%)]",
      id: "purchase_order_report_icon",
      colors: ["#50EBFD", "#5996E9"],
    },
    configKey: "purchase_order_module",
    rowData: [
      {
        report_name: "Purchase Order Status",
        parent_key: "purchase_order_report",
        key: "purchase_order_status",
        stock_key: "purchase_order",
        api_endpoint: "purchase-order",
        filter_by: "status",
      },
    ],
  },
  {
    name: `Safety Meeting  Report`,
    key: "safety_meeting_report",
    icon: "fa-regular fa-user-shield",
    secIcon: {
      icon: "fa-solid fa-user-shield",
      containerClassName:
        "bg-[linear-gradient(180deg,#B08CAB1a_0%,#734B6D1a_100%)]",
      id: "safety_meeting_report_icon",
      colors: ["#B08CAB", "#734B6D"],
    },
    configKey: "safety_meeting_module",
    rowData: [
      {
        report_name: "Safety Meeting By Date",
        parent_key: "safety_meeting_report",
        key: "safety_meeting_date",
        stock_key: "safety_meeting_by_date",
        api_endpoint: "safety-meeting",
        filter_by: "meeting_date",
      },
      {
        report_name: "Safety Meeting By Employee",
        parent_key: "safety_meeting_report",
        key: "safety_meeting_employee",
        stock_key: "safety_meeting_by_employee",
        api_endpoint: "safety-meeting",
        filter_by: "user_id",
      },
      {
        report_name: "Safety Meeting By Topic",
        parent_key: "safety_meeting_report",
        key: "safety_meeting_topic",
        stock_key: "safety_meeting_by_topic",
        api_endpoint: "safety-meeting",
        filter_by: "topic_id",
      },
    ],
  },
  {
    name: "Sub Contract Report",
    key: "sub_contract_report",
    icon: "fa-regular fa-file-signature",
    secIcon: {
      icon: "fa-solid fa-file-signature",
      containerClassName:
        "bg-[linear-gradient(180deg,#ADD1001a_0%,#7B920A1a_100%)]",
      id: "sub_contract_report_icon",
      colors: ["#ADD100", "#7B920A"],
    },
    configKey: "sub_contracts_module",
    rowData: [
      {
        report_name: "Sub-Contract Status",
        parent_key: "sub_contract_report",
        key: "sub_contract_status",
        stock_key: "sub_contracts_status",
        api_endpoint: "sub-contracts",
        filter_by: "contract_amount",
      },
      {
        report_name: "Sub-Contract Summary",
        parent_key: "sub_contract_report",
        key: "sub_contract_summary",
        stock_key: "sub_contracts_summary",
        api_endpoint: "sub-contract-summary",
        filter_by: "contract_amount",
      },
    ],
  },
  {
    name: "Time Card Report",
    key: "time_card_report",
    icon: "fa-regular fa-alarm-clock",
    secIcon: {
      icon: "fa-solid fa-alarm-clock",
      containerClassName:
        "bg-[linear-gradient(180deg,#4776E61a_0%,#8E54E91a_100%)]",
      id: "time_card_report_icon",
      colors: ["#4776E6", "#8E54E9"],
    },
    configKey: "time_card_module",
    rowData: [
      {
        report_name: "Gusto Integration",
        description:
          "Easily export data from Contractor Foreman that is ready to be imported directly into Gusto. Gusto is an all-in-one platform for managing payroll, people and HR. See more at www.Gusto.com.",
        parent_key: "time_card_report",
        key: "gusto_integration",
        stock_key: "gusto_integration",
        api_endpoint: "timecard-gusto-summary-report",
        filter_by: "",
      },
      {
        report_name: "ADP Export",
        description:
          "Easily export data from Contractor Foreman that is ready to be imported directly into ADP. ADP is an all-in-one platform for managing payroll, people, and HR. See more at www.adp.com.",
        parent_key: "time_card_report",
        key: "adp_export",
        stock_key: "timecard_adp",
        api_endpoint: "timecard-adp-summary-report",
        filter_by: "",
      },
      {
        report_name: "Time Activity (Most Popular)",
        description:
          "This is the most common report as it allows you to group items and hide/show the columns that are exported.",
        parent_key: "time_card_report",
        key: "time_activity",
        stock_key: "time_activity",
        api_endpoint: "timecard-spreadsheet",
        filter_by: "user_id",
      },
      {
        report_name: "Time Card By Employee",
        description:
          "A detailed daily report of an employees time card activity. Fields Shown; Employee, Date, Project, Clock-In, Clock-Out, Total Breaks, Work Hours, Total Hours, Modified Clock-In, Modified Clock-Out, Modified Total Breaks, Modified Work Hours, Modified Total Hours",
        parent_key: "time_card_report",
        key: "time_card_employee",
        stock_key: "employee_timecard",
        api_endpoint: "employee-timecard",
        filter_by: "user_id",
      },
      {
        report_name: "Weekly Sheet",
        description:
          "This generates the same data seen the Weekly Time Card view.",
        parent_key: "time_card_report",
        key: "weekly_sheet",
        stock_key: "timecard_employees_weeklysheet",
        api_endpoint: "timecard-employees-summary-report",
        filter_by: "",
      },
    ],
  },
];

export const EMPTY_REPORT: IReportValue = {
  report_name: "",
  parent_key: "",
  key: "",
};

export const DATE_SORTING_COL = {
  bills: "order_date",
  contractors: "date_added",
  customers: "date_added",
  daily_logs: "arrival_date",
  employee_writeups: "writeup_date",
  employees: "date_added",
  equipments: "date_added",
  equipment_logs: "used_date",
  expenses: "expense_date",
  incidents: "incident_date",
  inspections: "inspection_date",
  leads: "date_added",
  misc_contacts: "date_added",
  notes: "date_added",
  opportunities: "date_added",
  invoice_merge: "invoice_date",
  work_orders: "order_date",
  payments: "payment_date",
  project_permits: "permit_date",
  change_orders: "order_date",
  estimates: "estimate_date",
  projects: "start_date",
  punchlists: "order_date",
  purchase_orders: "order_date",
  correspondences: "correspondence_date",
  submittals: "submittal_date",
  sales_activity: "date",
  gantt_chart: "start_date",
  project_budget_tab: "date_added",
  service_tickets: "service_date",
  sub_contracts: "order_date",
  time_cards: "clock_in",
  todos: "todo_date",
  vehicle_logs: "log_date",
  vehicles: "inspection_expire_on",
  vendors: "date_added",
  forms_checklist: "date_added",
};

export const REPORTS_DATE_FORMATS = [
  "MM/DD/YYYY",
  "DD/MM/YYYY",
  "DD.MM.YYYY",
  "YYYY-MM-DD",
  "DD/MMM/YYYY",
  "MM/DD/YYYY hh:mm A",
  "DD/MM/YYYY hh:mm A",
  "DD.MM.YYYY hh:mm A",
  "YYYY-MM-DD hh:mm A",
  "DD/MMM/YYYY hh:mm A",
  "hh:mm A",
];

export const REPORT_URL_ARRAY = {
  bills: "bill",
  contractors: "contractor",
  customers: "customer",
  daily_logs: "daily-log",
  employee_writeups: "employee-writeup",
  employees: "employee",
  equipments: "equipments",
  equipment_logs: "equipment-log",
  expenses: "expense",
  incidents: "incident",
  inspections: "inspection",
  leads: "lead",
  misc_contacts: "misc-contact",
  notes: "notes",
  opportunities: "opportunity",
  invoice_merge: "invoice",
  work_orders: "work-order",
  payments: "payment",
  project_permits: "project-permit",
  change_orders: "change-order",
  estimates: "estimate",
  projects: "project",
  punchlists: "punchlist",
  purchase_orders: "purchase-order",
  correspondences: "correspond",
  submittals: "submittal",
  sales_activity: "sales-activity",
  gantt_chart: "gantt-chart",
  project_budget_tab: "schedule",
  service_tickets: "service-ticket",
  sub_contracts: "sub-contract",
  time_cards: "timecard",
  todos: "todo",
  vehicle_logs: "vehicle-log",
  vehicles: "vehicle",
  vendors: "vendor",
  forms_checklist: "form-checklist",
};

export const REPORT_MODULE_EXTRA = {
  change_orders_request: "cor",
  sales_activity_request: "contractor",
  customers: "customer",
  daily_logs: "daily-log",
  employee_writeups: "employee-writeup",
  employees: "employee",
  equipments: "equipments",
  equipment_logs: "equipment-log",
  expenses: "expense",
  incidents: "incident",
  inspections: "inspection",
  leads: "lead",
  misc_contacts: "misc-contact",
  notes: "notes",
  opportunities: "opportunity",
  invoice_merge: "invoice",
  work_orders: "work-order",
  payments: "payment",
  project_permits: "project-permit",
  change_orders: "change-order",
  estimates: "estimate",
  projects: "project",
  punchlists: "punchlist",
  purchase_orders: "purchase-order",
  correspondences: "correspond",
  submittals: "submittal",
  sales_activity: "sales-activity",
  gantt_chart: "gantt-chart",
  project_budget_tab: "schedule",
  service_tickets: "service-ticket",
  sub_contracts: "sub-contract",
  time_cards: "timecard",
  todos: "todo",
  vehicle_logs: "vehicle-log",
  vehicles: "vehicle",
  vendors: "vendor",
  forms_checklist: "form-checklist",
};

export const DATE_FILTER_LABLE = {
  bills: "Bill Date",
  contractors: "Date Added",
  customers: "Date Added",
  daily_logs: "Arrival Date",
  employee_writeups: "Write Up Date",
  employees: "Date Added",
  equipments: "Date Added",
  equipment_logs: "Log Date",
  expenses: "Expense Date",
  incidents: "Date",
  inspections: "Inspection Date",
  leads: "Date Added",
  misc_contacts: "Date Added",
  notes: "Date Added",
  opportunities: "Date Added",
  invoice_merge: "Invoice Date",
  work_orders: "Service Date",
  payments: "Payment Date",
  project_permits: "Pulled",
  change_orders: "Order Date",
  estimates: "Estimate Date",
  projects: "Start Date/End Date",
  punchlists: "Date Added",
  purchase_orders: "Date",
  correspondences: "Date",
  submittals: "Date Submitted",
  sales_activity: "Date",
  gantt_chart: "Start Date/End Date",
  project_budget_tab: "Date Added",
  service_tickets: "Service Date",
  sub_contracts: "Date",
  time_cards: "Clock In",
  todos: "To-Do Date",
  vehicle_logs: "Log Date",
  vehicles: "Insurance Expiration",
  vendors: "Date Added",
  forms_checklist: "Date Added",
};

export const FINANCES_GROUP_FIELD: Record<string, string> = {
  project: "Project",
  vendor: "Vendor",
  aging: "Aging",
  customer: "Customer",
  employee: "Employee",
  cost_code: "Cost Code",
  balance: "Balance",
  status: "Status",
  invoice: "Invoice",
  none: "None",
};

export const FINANCES_FIELD: Record<
  string,
  { fields: string[]; groupByKeys?: { key: string; value: string }[] }
> = {
  accounts_payable: {
    fields: ["vendor", "projects", "date_range", "group_by"],
    groupByKeys: [
      { key: "project", value: "prj_name" },
      { key: "vendor", value: "supplier_name" },
      { key: "aging", value: "aging" },
      { key: "none", value: "none" },
    ],
  },
  accounts_receivable: {
    fields: [
      "customer",
      "projects",
      "project_manager",
      "date_range",
      "group_by",
    ],
    groupByKeys: [
      { key: "project", value: "prj_name" },
      { key: "customer", value: "customer_name" },
      { key: "aging", value: "aging" },
      { key: "none", value: "none" },
    ],
  },
  expenses: {
    fields: [
      "date_range",
      "employees",
      "created_by",
      "projects",
      "cost_code",
      "status",
      "group_by",
      "report_format",
    ],
    groupByKeys: [
      { key: "project", value: "project_id" },
      { key: "customer", value: "customer_id" },
      { key: "employee", value: "expense_by" },
      { key: "vendor", value: "directory_id" },
      { key: "cost_code", value: "cost_code_id" },
      { key: "none", value: "none" },
    ],
  },
  invoices: {
    fields: [
      "approval_type",
      "date_range",
      "customers",
      "projects",
      "status",
      "optional_columns",
      "group_by",
      "due_date_range",
    ],
    groupByKeys: [
      { key: "project", value: "project_id" },
      { key: "customer", value: "customer_id" },
      { key: "balance", value: "balance" },
      { key: "aging", value: "aging" },
      { key: "status", value: "approval_type" },
      { key: "none", value: "none" },
    ],
  },
  payments: {
    fields: [
      "customer",
      "projects",
      "invoice",
      "date_range",
      "status",
      "group_by",
    ],
    groupByKeys: [
      { key: "project", value: "project_id" },
      { key: "customer", value: "customer_id" },
      { key: "invoice", value: "invoice_id" },
      { key: "none", value: "none" },
    ],
  },
};

export const JOBCOST_FIELD_KEYS: Record<string, string[]> = {
  invoiced_actual_report: [
    "date_range",
    "project_manager",
    "project_type",
    "project",
    "project_status",
    "optional_columns_1",
    "generate_from",
    "gross_profit_calculation",
    "sales_tax_or_markup",
    "pull_labor_from",
  ],
  budget_actual_labor_report: [
    "date_range",
    "project_type",
    "project",
    "generate_from",
    "pull_labor_from",
  ],
  committed_actual: [
    "date_range",
    "project_type",
    "project",
    "generate_from",
    "pull_labor_from",
  ],
  gross_profit_summary: [
    "projects",
    "project_type",
    "project_manager",
    "project_status",
    "calculation_method",
  ],
  job_cost_actual_report: [
    "date_range",
    "project_type",
    "project",
    "generate_from",
    "pull_labor_from",
  ],
  job_cost_committed_project: [
    "date_range",
    "project_type",
    "project",
    "generate_from",
    "pull_labor_from",
  ],
  job_cost_committed_report: [
    "date_range",
    "project_type",
    "project",
    "generate_from",
    "markup",
    "pull_labor_from",
    "sales_tax_or_markup",
  ],
  work_in_progress_report: [
    "date_range",
    "project_type",
    "project",
    "generate_from",
    "include_retention",
    "pull_labor_from",
    "include_retention_held",
  ],
  job_cost_transactions: [
    "date_range",
    "generate_from",
    "customer",
    "project_manager",
    "project_type",
    "project_status",
    "projects",
    "cost_code",
    "item_type",
    "pull_labor_from",
    "optional_columns_2",
    "group_by",
  ],
  project_management_summary: [
    "projects",
    "project_manager",
    "project_type",
    "project_status",
    "generate_from",
    "gross_profit_calculation",
    "sales_tax_or_markup",
    "pull_labor_from",
  ],
};

export const ACTION_MENU_OPT_LIST: IDropdownMenuOption[] = [
  // {
  //   label: "View/Edit",
  //   key: "view",
  //   icon: "fa-solid fa-eye",
  //   content: "View/Edit",
  // },
  {
    label: "Copy Report",
    key: "copy",
    icon: "fa-regular fa-copy",
    content: "Copy Report",
  },
  {
    label: "Delete",
    key: "delete",
    icon: "fa-regular fa-trash-can",
    content: "Delete",
  },
];

export const DATE_RANGE_KEY = {
  today: "Today",
  yesterday: "Yesterday",
  this_week: "This Week",
  last_7_days: "Last 7 Days",
  this_month: "This Month",
  last_month: "Last Month",
  last_30_days: "Last 30 Days",
  last_60_days: "Last 60 Days",
  last_90_days: "Last 90 Days",
  this_year: "This Year",
  last_year: "Last Year",
  lifetime: "Lifetime",
};
export const EMP_ACTIVITIES_STATUS = [
  {
    label: "Pending",
    value: "pending_activity",
  },
  {
    label: "Approved",
    value: "approved_activity",
  },
  {
    label: "Declined",
    value: "declined_activity",
  },
  {
    label: "Canceled",
    value: "canceled_activity",
  },
];

export const REPORTS_RECORD_STATUS: IRepStatusOption[] = [
  { label: "Active", value: "0" },
  { label: "Archived", value: "1" },
  { label: "All", value: "" },
];

export const REPORT_CONTACT_TYPE = [
  {
    label: "Contractors",
    value: "4",
  },
  {
    label: "Customers",
    value: "3",
  },
  {
    label: "Employees",
    value: "2",
  },
  {
    label: "Leads",
    value: "204",
  },
  {
    label: "Misc. Contacts",
    value: "23",
  },
  {
    label: "Vendors",
    value: "22",
  },
];

export const SHOW_ADDI_CON_ARR = [
  CFConfig.vendor_module,
  CFConfig.contractor_module,
  CFConfig.customer_module,
  CFConfig.misc_contact_module,
];

export const SHOW_SUB_TABLE_ARR = [
  CFConfig.vendor_module,
  CFConfig.contractor_module,
  CFConfig.employee_module,
  CFConfig.daily_log_module,
];

export const EXCEL_STYLES: ExcelStyle[] = [
  {
    id: "header",
    font: {
      bold: true,
      color: "#FFFFFF",
    },
    interior: {
      color: "#000000",
      pattern: "Solid",
      patternColor: undefined,
    },
  },
  {
    id: "title",
    font: { bold: true, size: 18, color: "#000000" }, // Adjust size as needed
    alignment: { horizontal: "Right", vertical: "Center" },
  },
  {
    id: "created_on",
    font: { bold: false, size: 12, color: "#000000" }, // Adjust size as needed
    alignment: { horizontal: "Right", vertical: "Center" },
  },
  {
    id: "filter_style",
    font: { bold: false, size: 12, color: "#000000" }, // Adjust size as needed
    alignment: { horizontal: "Left", vertical: "Center" },
  },
  {
    id: "numberCommaSeparated",
    numberFormat: {
      format: "#,##0.00",
    },
  },
  {
    id: "hyperlinks",
    font: {
      underline: "Single",
      color: "#358ccb",
    },
  },
  {
    id: "groupRowStyle",
    interior: {
      color: "#D9E1F2",
      pattern: "Solid",
    },
    font: {
      bold: true,
    },
  },
  {
    id: "date_range",
    font: { bold: true, size: 12, color: "#000000" }, // Adjust size as needed
    alignment: { horizontal: "Left", vertical: "Center" },
  },
];

export const PDF_FORMAT_TYPE = [
  {
    label: "A4",
    value: "A4",
  },
  {
    label: "A3",
    value: "A3",
  },
  {
    label: "A5",
    value: "A5",
  },
  {
    label: "A2",
    value: "A2",
  },
  {
    label: "Letter",
    value: "letter",
  },
  {
    label: "Custom",
    value: "custom",
  },
];

export const REPORT_OCCURRENCE_OPTIONS = [
  {
    label: "First",
    value: "55",
  },
  {
    label: "Repeated",
    value: "56",
  },
];

export const COMMON_REPORT_TYPE = {
  disciplineTypeId: 223,
};

export const REPORT_PROJECT_STATUS_OPTIONS = [
  {
    label: "Started",
    value: "started",
  },
  {
    label: "Pending",
    value: "pending",
  },
  {
    label: "Unscheduled",
    value: "unscheduled",
  },
  {
    label: "Submittal",
    value: "project_submittal",
  },

  {
    label: "Completed",
    value: "completed",
  },
  {
    label: "Bidding123",
    value: "bidding",
  },
];

export const REPORT_ITEM_TYPE_OPTIONS = [
  {
    label: "Material",
    value: "161",
  },
  {
    label: "Equipment",
    value: "162",
  },
  {
    label: "Labor",
    value: "163",
  },
  {
    label: "Subcontractor",
    value: "164",
  },

  {
    label: "Other",
    value: "165",
  },
  {
    label: "Unassigned",
    value: "0",
  },
];

export const REPORT_GROUP_LIST: IRepStatusOption[] = [
  { label: "Location", value: "project_name" },
  { label: "Equipment", value: "equipment_name" },
  { label: "Operator", value: "operator_name" },
  { label: "Date", value: "used_date" },
  { label: "None", value: "none" },
];

export const REPORT_EQI_TYPE: IRepStatusOption[] = [
  {
    label: "Equipment Insurance",
    value: "equipment_insurance",
  },
  {
    label: "Equipment Registration",
    value: "equipment_registration",
  },
  {
    label: "Equipment Inspection",
    value: "equipment_inspection",
  },
];

export const REPORT_VEHICLE_TYPE: IRepStatusOption[] = [
  {
    label: "Vehicle Insurance",
    value: "vehicle_insurance",
  },
  {
    label: "Vehicle Registration",
    value: "vehicle_registration",
  },
  {
    label: "Vehicle Inspection",
    value: "vehicle_inspection",
  },
];

export const JOB_GENERATE_FROM_OPTIONS: IRepStatusOption[] = [
  { label: "SOV", value: "sov" },
  { label: "Approved Estimate", value: "estimate" },
];

export const GROSS_PROFIT_CALCULATION_OPTIONS: IRepStatusOption[] = [
  { label: "Costs Markup", value: "costs_markup" },
  { label: "Sales Margin", value: "sales_margin" },
];

export const SALES_TAX_OR_MARKUP_OPTIONS: IRepStatusOption[] = [
  { label: "Excluded", value: "excluded" },
  { label: "Included", value: "included" },
];

export const INCLUDE_RETENTION_HELD_OPTIONS: IRepStatusOption[] = [
  { label: "Yes", value: "1" },
  { label: "No", value: "0" },
];

export const PULL_LABOR_FROM_OPTIONS: IRepStatusOption[] = [
  { label: "Wage Rate", value: "emp_wage" },
  { label: "Billing Rate", value: "billing_rate" },
  { label: "Burden Rate", value: "burden_rate" },
  { label: "Approved Estimate", value: "approved_estimate" },
];

export const CALCULATION_METHOD_OPTIONS: IRepStatusOption[] = [
  { label: "Actual", value: "actual" },
  { label: "Committed", value: "committed" },
];

export const OPTIONAL_JOB_COST_COLUMNS: IRepStatusOption[] = [
  { label: "Parent Code", value: "parent_cost_code" },
  { label: "Parent Code Description", value: "parent_cost_code_name" },
  { label: "Cost Code", value: "cost_code" },
  { label: "Cost Code Description", value: "cost_code_name" },
  { label: "Item Type (MLESOU)", value: "item_type_name" },
  { label: "Transaction Type", value: "transaction_type" },
  { label: "Date", value: "transaction_date" },
  { label: "Ref # / Source", value: "ref_source" },
  { label: "Vendor/Sub/Employee", value: "assignee_name" },
  { label: "Item Description", value: "subject" },
  { label: "Qty", value: "quantity" },
  { label: "Unit Cost / Unit Price", value: "unit_cost" },
  { label: "Unit", value: "unit" },
  { label: "Total Cost", value: "item_total" },
];

export const GROUP_BY_OPTIONS = [
  { label: "Cost Code", value: "cost_code_name" },
  { label: "Item Type (MLESOU)", value: "item_type" },
  { label: "Transaction Type", value: "transaction_type" },
  { label: "Date", value: "record_date" },
];
export const FORM_EXPORT_OPTIONS: IRepStatusOption[] = [
  {
    label: "Submitted Forms Only",
    value: "submitted_form",
  },
  {
    label: "All Forms (Submitted & Unsubmitted)",
    value: "all_form",
  },
];

export const TIMECART_COLUMNS_OPTIONS: IRepStatusOption[] = [
  {
    label: "Employee #",
    value: "employee_id",
  },
  {
    label: "Tags (Emp)",
    value: "user_tag_names",
  },
  {
    label: "Project #",
    value: "modified_project_id",
  },
  {
    label: "Parent #",
    value: "parent_csi_code",
  },
  {
    label: "Cost Code #",
    value: "modified_csi_code",
  },
  {
    label: "Time Card Tags",
    value: "tag_names",
  },
  {
    label: "Overtime Code",
    value: "overtime_code",
  },
  {
    label: "Time Card Note",
    value: "tc_notes",
  },
  {
    label: "Any Injury",
    value: "any_injury",
  },
];

export const LABOR_RATE_OPTIONS: IRepStatusOption[] = [
  {
    label: "Wage Rate",
    value: "emp_wage",
  },
  {
    label: "Billing Rate",
    value: "billing_rate",
  },
  {
    label: "Burden Rate",
    value: "burden_rate",
  },
];

export const HOURS_FORMAT_OPTIONS: IRepStatusOption[] = [
  {
    label: "hh:mm",
    value: "hours",
  },
  {
    label: "hh.mm",
    value: "decimal",
  },
];
export const TIMECARD_GROUP_BY_OPTIONS: IRepStatusOption[] = [
  {
    label: "Employee",
    value: "employee",
  },
  {
    label: "Project",
    value: "project",
  },
  {
    label: "Cost Code",
    value: "cost_code",
  },
  {
    label: "Date",
    value: "clock_in_date",
  },
  {
    label: "None",
    value: "none",
  },
];

export const TIMECARD_FIELD_MAP: Record<string, string[]> = {
  gusto_integration: ["date_range"],
  adp_export: ["adp_co_code", "date_range"],
  time_activity: [
    "date_range",
    "employees",
    "projects",
    "optional_columns",
    "labor_rate",
    "hours_format",
    "group_by",
    "tags",
    "cost_code",
    "project_type",
    "project_status",
    "report_format",
  ],
  time_card_employee: [
    "date_range",
    "tags",
    "cost_code",
    "employees",
    "projects",
    "project_status",
    "labor_rate",
    "hours_format",
    "report_format",
  ],
  weekly_sheet: ["week_picker"],
};

export const htmlRender = (params: { value: string | number }) => {
  if (!params.value) return "";
  return typeof params.value != "number"
    ? parse(
        `<span className="truncate block">${HTMLEntities.decode(
          params.value.replaceAll(/\\/g, "")
        )}</span>`
      )
    : params.value;
};

/* Time aggregation function */
export const timeWorkedAggFunc = (params: any) => {
  let totalMinutes = 0;
  params?.values?.forEach((value: any) => {
    if (value) {
      const parts = value.split(":");
      const hours = parseInt(parts[0], 10);
      const minutes = parseInt(parts[1], 10);
      totalMinutes += hours * 60 + minutes;
    }
  });
  const totalHours = Math.floor(totalMinutes / 60);
  const remainingMinutes = totalMinutes % 60;
  return `${totalHours}:${remainingMinutes.toString().padStart(2, "0")}`;
};

export const getAggFunc = (item: IReportColumns) => {
  if (item.column_key == "time_worked") {
    return timeWorkedAggFunc;
  } else {
    return "sum";
  }
};

function numberValueGetter(params: any) {
  let value = "";
  if (!params.data || params.colDef.field === undefined) {
    return ""; // Return blank if column is removed
  }
  if (params.data) {
    value = params.data[params.colDef.field];
  }
  const parsedValue = parseFloat(value);
  if (isNaN(parsedValue) || value == "0") {
    return "0.00";
  }
  return isNaN(parsedValue) ? "" : parsedValue; // Default to 0 if not a valid number
}

export const getValueGetter = (item: IReportColumns) => {
  if (item.column_key == "time_worked") {
    return null; // No valueGetter for time fields
  } else {
    return numberValueGetter; // Use numberValueGetter for numeric fields
  }
};

export const totalComparator = (
  a: number | null | undefined,
  b: number | null | undefined
): number => {
  if (a == null && b == null) return 0;
  if (a == null) return -1;
  if (b == null) return 1;
  return a - b;
};

export const nameComparator = (a: any, b: any): number => {
  const strA = a == null ? "" : String(a).toLowerCase();
  const strB = b == null ? "" : String(b).toLowerCase();
  return strA.localeCompare(strB);
};

// Custome Table Header
export const reorderAndHideColumns = (
  colDefs: ColDef[],
  orderedColIds: string[],
  hiddenColIds: string[],
  columnSizing: { colId: string; width: number }[] = []
): ColDef[] => {
  const colDefMap = new Map(colDefs.map((col) => [col.field, col]));
  const sizingMap = new Map(columnSizing.map((s) => [s.colId, s.width]));

  const getWidth = (field: string) => sizingMap.get(field) || 200;

  const validOrderedColIds = orderedColIds.filter((field) =>
    colDefMap.has(field)
  );

  const orderedVisibleCols: ColDef[] = validOrderedColIds
    .filter((field) => !hiddenColIds.includes(field))
    .map((field) => {
      const col = colDefMap.get(field)!;
      return { ...col, hide: false, width: getWidth(field) };
    });

  const orderedHiddenCols: ColDef[] = validOrderedColIds
    .filter((field) => hiddenColIds.includes(field))
    .map((field) => {
      const col = colDefMap.get(field)!;
      return { ...col, hide: true, width: getWidth(field) };
    });

  const remainingCols: ColDef[] = colDefs
    .filter((col) => !validOrderedColIds.includes(col.field || ""))
    .map((col) => ({
      ...col,
      hide: hiddenColIds.includes(col.field || ""),
      width: getWidth(col.field || ""),
    }))
    .sort((a, b) => (a.headerName || "").localeCompare(b.headerName || ""));

  return [...orderedVisibleCols, ...remainingCols, ...orderedHiddenCols];
};

export const getAssigneeValue = (data: IReportContactInformation[]) => {
  if (data.length === 0) {
    return "";
  } else if (data.length <= 1) {
    return `${data?.map((item) => item?.display_name).join(", ")}`;
  } else {
    return `${data.length} Selected`;
  }
};

export const stripHtmlTags = (data: ProcessCellForExportParams) => {
  let str = HTMLEntities.decode(
    sanitizeString(data.value?.toString() || "" || "")
  );

  if (
    (data.column?.getColId() == "file_1" ||
      data.column?.getColId() == "file_2" ||
      data.column?.getColId() == "file_3") &&
    data.value
  ) {
    const url = data.value.match(/href=['"](.*?)['"]/)[1];
    return `=HYPERLINK("${url}", "${str}")`;
  } else if (typeof str === "string") {
    return str.replace(/<[^>]*>/g, "");
  }
  return str;
};

export const rowGroupCallback = (
  params: ProcessRowGroupForExportParams
): string => {
  const { node } = params;

  if (!node) return "";

  if (!node.footer) {
    const headerName = node.rowGroupColumn?.getColDef()?.headerName ?? "Group";
    return `${headerName}: ${node.key ?? ""}`;
  }

  const isRootLevel = node.level === -1;
  if (isRootLevel) {
    return "Grand Total";
  }

  return node.key ?? "";
};

export const getReportName = (
  moduleName: string,
  extra: string,
  moduleKey: string
) => {
  if (extra == "cor") {
    return moduleName + " Request";
  } else if (extra == "etor") {
    return "Employee Time Off Requests";
  } else if (extra == "vi") {
    return moduleName + " Insurance";
  } else if (extra == "es") {
    return "Equipment Summary";
  } else if (extra == "eu") {
    return "Equipment Usage";
  } else if (extra == "md") {
    return "Material Delivered";
  } else if (moduleKey == "sales_activity") {
    return "Sales Tasks";
  } else {
    return moduleName;
  }
};

export const getIndentClass = (params: ProcessRowGroupForExportParams) => {
  var indent = 0;
  var node = params.node;

  while (node && node.parent) {
    indent++;
    node = node.parent;
  }
  return "indent-" + indent;
};

export const cellExcel = (value: string | number, styleId?: string) => ({
  data: { value },
  ...(styleId ? { styleId } : {}),
});

// TODO: Combine last 3 functions into one if possible
export const getMasterViewRowsCnt = (
  params: ProcessRowGroupForExportParams,
  moduleKey: string
): ExcelRow[] | undefined => {
  const { node } = params;

  if (!node.master || !node.data) return undefined;

  const policyRecords = node.data.callRecords || [];
  const licenseRecords = node.data.licenses || [];

  // Policy Detail Section
  const policyDetailRows: ExcelRow[] = [
    {
      outlineLevel: 0,
      cells: [
        cellExcel(""),
        cellExcel("Policy Type", "header"),
        cellExcel("Policy", "header"),
        cellExcel("Expire Date", "header"),
        cellExcel("Limit", "header"),
      ],
    },
    ...policyRecords.map((record: any) => ({
      outlineLevel: 0,
      cells: [
        cellExcel(""),
        cellExcel(
          HTMLEntities.decode(record?.type?.replaceAll(/\\/g, "")),
          "body"
        ),
        cellExcel(
          HTMLEntities.decode(record?.name?.replaceAll(/\\/g, "")),
          "body"
        ),
        cellExcel(
          record.does_not_expire == 1 ? record.does_expire : record.expire_date,
          "body"
        ),
        cellExcel(record.limit, "body"),
      ],
    })),
  ];

  if (moduleKey == CFConfig.vendor_module) return policyDetailRows;

  // License Detail Section
  const licenseDetailRows: ExcelRow[] = [
    {
      outlineLevel: 1,
      cells: [
        cellExcel(""),
        cellExcel("License Type", "header"),
        cellExcel("License", "header"),
        cellExcel("Expire Date", "header"),
      ],
    },
    ...licenseRecords.map((record: any) => ({
      outlineLevel: 1,
      cells: [
        cellExcel(""),
        cellExcel(
          HTMLEntities.decode(record?.type?.replaceAll(/\\/g, "")),
          "body"
        ),
        cellExcel(
          HTMLEntities.decode(record?.name?.replaceAll(/\\/g, "")),
          "body"
        ),
        cellExcel(
          record.does_not_expire == 1 ? record.does_expire : record.expire_date,
          "body"
        ),
      ],
    })),
  ];

  return [...policyDetailRows, ...licenseDetailRows];
};

export const getMasterViewRowsEmp = (
  params: ProcessRowGroupForExportParams
): ExcelRow[] | undefined => {
  const { node } = params;

  // If not a master row or no callRecords, return nothing
  if (!node.master || !node.data?.callRecords) return undefined;

  const callRecords = node.data.callRecords;

  // License Detail Section
  const detailRows = [
    {
      outlineLevel: 1,
      cells: [
        cellExcel(""),
        cellExcel("Subject", "header"),
        cellExcel("Date Received", "header"),
        cellExcel("Expire Date", "header"),
        cellExcel("Certificate #", "header"),
      ],
    },
    ...callRecords.map((record: any) => ({
      outlineLevel: 1,
      cells: [
        cellExcel(""),
        cellExcel(
          HTMLEntities.decode(record?.subject?.replaceAll(/\\/g, "")),
          "body"
        ),
        cellExcel(record.receive_date, "body"),
        cellExcel(
          record.does_not_expire == 1 ? record.does_expire : record.expire_date,
          "body"
        ),
        cellExcel(record.certificate_number, "body"),
      ],
    })),
  ];

  // Combine and return both sections
  return detailRows;
};

export const getMasterViewRowsDL = (
  params: ProcessRowGroupForExportParams
): ExcelRow[] | undefined => {
  const { node } = params;

  // If not a master row or no callRecords, return nothing
  if (!node.master || !node.data?.callRecords) return undefined;

  const callRecords = node.data.callRecords;

  // License Detail Section
  const detailRows = [
    {
      outlineLevel: 1,
      cells: [
        cellExcel(""),
        cellExcel("Item", "header"),
        cellExcel("Delivered By", "header"),
        cellExcel("Time", "header"),
        cellExcel("Returned?", "header"),
        cellExcel("Notes", "header"),
      ],
    },
    ...callRecords.map((record: any) => ({
      outlineLevel: 1,
      cells: [
        cellExcel(""),
        cellExcel(
          HTMLEntities.decode(record?.item_name?.replaceAll(/\\/g, "")),
          "body"
        ),
        cellExcel(
          HTMLEntities.decode(record?.delivered_by?.replaceAll(/\\/g, "")),
          "body"
        ),
        cellExcel(record.delivery_time, "body"),
        cellExcel(record.returned_yes_no, "body"),
        cellExcel(
          HTMLEntities.decode(record?.notes?.replaceAll(/\\/g, "")),
          "body"
        ),
      ],
    })),
  ];

  return detailRows;
};

export const getRepFilterLabel = <T>(list: T[], key: keyof T): string => {
  return list
    .map((item) => HTMLEntities.decode(sanitizeString(item[key] as string)))
    .join(", ");
};

export const generateWriteupDisciplineOptions = (
  writeup_discipline: IWriteupDisciplineStatus[],
  customDataList: ICustomData[]
) => {
  const coreOptions =
    writeup_discipline?.map((item) => ({
      label: item.display_name || item.name || "",
      value: item.type_id.toString(),
      isCustom: false,
    })) || [];

  const customOptions =
    customDataList
      ?.filter((item) => item.item_type === "223")
      .map((item) => ({
        label: HTMLEntities.decode(item.name || ""),
        value: item.item_id.toString(),
        isCustom: true,
      })) || [];

  return [...coreOptions, ...customOptions];
};

export const generateCustomOptions = (
  baseOptions: { label: string; value: string }[],
  customDataList: ICustomData[] = [],
  itemTypeId?: string | number
) => {
  const customOptions =
    customDataList
      ?.filter((item) => item.item_type.toString() === itemTypeId?.toString())
      .sort((a, b) => Number(a.sort_order ?? 0) - Number(b.sort_order ?? 0))
      .map((item) => ({
        label: HTMLEntities.decode(item.name || ""),
        value: item.item_id.toString(),
        isCustom: true,
      })) || [];

  return [...baseOptions, ...customOptions];
};

export function transformSafetyTopics(items?: ISMTopicItems[]) {
  const seen = new Set<string>();
  if (!items?.length) return [];

  return items.flatMap((item) => {
    const id = item?.topic_id?.toString();
    if (!id || seen.has(id)) return [];
    seen.add(id);
    return [
      {
        label: HTMLEntities.decode(sanitizeString(item?.title)),
        value: id,
      },
    ];
  });
}
