// To-Do Dashboard
interface IPoDashParamas {
  refresh_type?: string;
  isMonthWise?: boolean;
}
interface IPODashState {
  [key: string]:
    | string
    | boolean
    | IPastDeliveries[]
    | IPoStats
    | IRecentPricingRequest
    | IRecentlyDeliveredItems[]
    | ITotalbilledData
    | IUpcomingDeliveries[];
  pastDeliveries: IPastDeliveries[];
  po_stats: IPoStats;
  recent_pricing_request: IRecentPricingRequest;
  recentlyDeliveredItems: IRecentlyDeliveredItems[];
  totalbilledData: ITotalbilledData;
  upcomingDeliveries: IUpcomingDeliveries[];
  isDashLoading?: boolean;

  totalbilledLastRefresTime: string;
  poStatsLastRefresTime: string;
  recentPricingRequestLastRefresTime: string;
  recentlyDeliveredItemsLastRefresTime: string;
  upcomingDeliveriesLastRefresTime: string;
  pastDeliveriesLastRefresTime: string;
  searchValue: string;
}

interface IPastDeliveries {
  prefix_company_purchase_order_id?: string;
  date_added?: string;
  supp_name?: string;
  supplier_company_name?: string;
  purchase_order_id?: string;
}

interface IPoStats {
  current_month?: ICurrentMonth;
  last_month?: ILastMonth;
  current_year?: ICurrentYear;
  last_year?: ILastYear;
}

interface ICurrentMonth {
  current_month?: string;
  count_row?: string;
}

interface ILastMonth {
  last_month?: string;
  count_row?: string;
}

interface ICurrentYear {
  current_year?: string;
  count_row?: string;
}

interface ILastYear {
  last_year?: string;
  count_row?: string;
}

interface IRecentPricingRequest {
  recently_submitted?: IRecentPricingList[];
  awaiting_response?: IRecentPricingList[];
}

interface IRecentPricingList {
  pricing_supplier_id?: string;
  prefix_company_purchase_order_id?: string;
  date_added?: string;
  vender_name?: string;
  purchase_order_id?: string;
}

interface IRecentlyDeliveredItems {
  project_id_string?: string;
  date_added?: string;
  subject?: string;
  purchase_order_id?: string;
  supplier_details?: IPoMultipleSupplier[];
}

interface ITotalbilledData {
  total?: string;
  decimal_total?: string;
}

interface IUpcomingDeliveries {
  prefix_company_purchase_order_id?: string;
  date_added?: string;
  supp_name?: string;
  supplier_company_name?: string;
  purchase_order_id?: string;
}

interface IPODashApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IPODashState;
  refresh_type?: string;
}

interface IPODetailApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IPODetailData;
}

interface IAddPurchaseOrderItemResData {
  purchase_order_item_id: number;
}

interface IAddPurchaseOrderItemRes extends Omit<IDefaultAPIRes, "data"> {
  data: IAddPurchaseOrderItemResData;
}

interface IPOItemsApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    items: IPOItemData[] & IPOItemSectionData;
    detail?: IPODetailData;
  };
  po_status_message?: string;
}

interface IPOItemSectionData {
  lumpsum?: IPOItemData[];
  work_order: WorkOrder[];
  change_order: ChangeOrder[];
  estimate: Estimate[];
  others?: IPOItemData[];
}
interface WorkOrder {
  work_order_id: number;
  subject: string;
  items: IPOItemData[];
}

interface ChangeOrder {
  change_order_id: number;
  subject: string;
  items: IPOItemData[];
}
interface Estimate {
  estimate_id: number;
  subject: string;
  sections: Section[];
}
interface Section {
  section_id: string;
  section_name: string;
  custom_section_id: number;
  items: IPOItemData[];
}

interface IPOPricingItemsApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IPOSubmitedPricingDetail[];
}

interface IPOBillHistoryApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IPOBillHistory[];
}

interface IAddPOItemParams {
  purchase_order_id: number;
  items: (Partial<IPOAddItem> & Pick<IPOAddItem, "subject" | "item_type">)[];
}

interface IUpdatePOItemParams {
  purchase_order_id: number | string;
  items: Partial<IPOItemData>[];
}

interface IUpdatePOItemOrderParams {
  purchase_order_id: number | string;
  items: Partial<IPOItemData>[];
}

interface IPODashPastDeliveriesTableCellRenderer {
  data: Partial<IPastDeliveries>;
}

interface IPODashUpcomingDeliveriesTableCellRenderer {
  data: Partial<IUpcomingDeliveries>;
}

interface IPODashRecentlyDeliveredTableCellRenderer {
  data: Partial<IRecentlyDeliveredItems>;
}

interface IPODashRecentPricingTableCellRenderer {
  data: Partial<IRecentPricingList>;
}

// PurchaseOrder List

interface IPurchaseOrdersListProps {
  filterParams: {}; // Notes: This will change when filter developer
  search: string;
  setAddPOOpen: (value: boolean) => void;
}

interface IPoListParmas {
  limit: number;
  start: number;
  directory?: number[] | string[];
  order_by_dir?: string;
  order_by_name?: string;
  search?: string;
  filter?: IDirTempFil;
}

interface IPOSort {
  dir?: string;
  column?: string;
}

interface IPoListParams {
  limit?: number;
  start?: number;
  todo?: number[] | string[];

  search?: string;
  filter?: Partial<PurchaseOrdersFilter>;
  has_template?: number;
  order?: IPOSort;
  ignore_filter?: number;
  order_by_dir?: string;
  order_by_name?: string;
}

interface IPurchaseOrderData {
  purchase_order_id: string;
  project_id: string;
  supplier_company_name: string;
  subject: string;
  project_name: string;
  order_date: string;
  approved_by: string;
  approved_username: string;
  billing_status: string;
  total: number; // Chnage this type by developer
  status_color: string;
  billing_status_name: string;
  company_id: string;
  prefix_company_purchase_order_id: string;
  custom_field_id: string;
  multiple_supplier_names: string;
  default_status_color: string;
  multiple_supplier_details: IPoMultipleSupplier[];
  supplier_details: IPoMultipleSupplier[];
  is_multiple_suppliers?: string | number;
}

interface IPoMultipleSupplier {
  contact_id?: string;
  user_id?: string | number;
  first_name?: string;
  last_name?: string;
  unique_name?: string;
  company_name?: string;
  display_name?: string;
  email?: string;
  type_name?: string;
  dir_type?: string | number;
  dir_type_name?: string;
  directory_id?: string;
  supplier_name?: string;
  supplier_dir_type?: string | number;
  image?: string;
}

interface IPurchaseOrderApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IPurchaseOrderData[];
}

interface IPOFilePhotoAddRes extends Omit<IDefaultAPIRes, "data"> {
  data: { aws_files: IFile[] };
}

interface IPoTableCellRenderer {
  data: Partial<IPurchaseOrderData>;
}

interface IPoBillHistoryTableCellRenderer {
  data: Partial<IPOBillHistory>;
}

interface IPODetailParams {
  purchase_order_id?: number | string;
  module_id?: number;
  need_section?: number;
  project_id?: number;
  isHideLoading?: boolean;
  need_section?: boolean;
}

interface IPODetailState {
  isPODetailLoading?: boolean;
  purchaseOrderDetail?: IPODetailData;
  items?: IPOItemData[];
}
// interface IPOScheduleValueItemApiRes extends Omit<IDefaultAPIRes, "data"> {
//   data: {
//     items: ISCSchIPOItemDataeduleItems[];
//   };
// }

interface IPOScheduleValueItemApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    budget_data: IPOItemData[];
    allow_over_billing: string;
    billing_option: string;
    estimate_section: EstimateSection[];
    // change_order_data: ChangeOrderDaum[]
    change_order_sections: ChangeOrderSection[];
    work_order_sections: WorkOrderSection[];
    // work_order_data: IPOItemData[]
    work_order_items: IPOItemData[];
  };
}
interface IPOItemState {
  isPOItemsLoading?: boolean;
  purchaseOrderItems?: IPOItemData[];
  purchaseOrderSectionItems?: IPOItemSectionData;
  isBillGenerated?: boolean;

  poScheduleValueItems: IPOSceduleValue;
  isPOScheduleValueLoading: boolean;
  isPOScheduleValueFetched: boolean;

  poEstimateItems: IPOItemData[];
  isPOEstitemsLoading: boolean;
  isPOEstitemsFetched: boolean;
  filter: string[];

  selectedItems: {
    changeOrders: { [key: string]: IPOItemData[] };
    // workOrders: { [key: string]: IPOItemData[] };
    estimates: { [key: string]: IPOItemData[] };
    workOrderItems: IPOItemData[];
    otherItems: IPOItemData[];
  };
  defaultCheckedItems: {
    changeOrders: { [key: string]: IPOItemData[] };
    // workOrders: { [key: string]: IPOItemData[] };
    workOrderItems: IPOItemData[];
    otherItems: IPOItemData[];
    estimates: { [key: string]: IPOItemData[] };
  };
}

interface IPOSceduleValue {
  // sov_section: {
  //   items: IPOItemData[];
  //   defaultCheckedItems: IPOItemData[];
  // };
  // change_order_section: {
  //   items: IPOItemData[];
  //   defaultCheckedItems: IPOItemData[];
  // };
  // work_order_section: {
  //   items: IPOItemData[];
  //   defaultCheckedItems: IPOItemData[];
  // };
  allow_over_billing: string;
  billing_option: string;
  // change_order_data: ChangeOrderDaum[]
  // work_order_data: IPOItemData[]
  budget_data: IPOItemData[];
  work_order_items: IPOItemData[];
  change_order_sections: ChangeOrderSection[];
  work_order_sections: WorkOrderSection[];
  estimate_section: EstimateSection[];
}
interface EstimateSection {
  section_id: string;
  section_name: string;
  items: IPOItemData[];
}
interface ChangeOrderSection {
  section_id: number;
  section_name: string;
  section_subject_name: string;
  change_order_section_subject: string;
  company_order_id: string;
  items: IPOItemData[];
}
interface WorkOrderSection {
  section_id: number;
  section_name: string;
  section_subject_name: string;
  work_order_section_subject: string;
  company_order_id: string;
  items: IPOItemData[];
}
interface IPOPricingDetailState {
  poPricingDetail?: IPOSubmitedPricingDetail[];
  isPOPricingLoading?: boolean;
}
interface IPOCommonState {
  filter: string[];
}

interface IPOBillHistoryState {
  isPOBillHistoryLoading?: boolean;
  poBillHistory?: IPOBillHistory[];
  totalPayment?: number;
}

interface IPODetailData {
  purchase_order_id?: number | string;
  total?: string;
  supplier_contact_id?: number;
  billing_status?: string | number;
  supplier_name?: string;
  subject?: string;
  ship_via?: string;
  fob_point?: string;
  term_name?: string;
  term_key?: string;
  term_id?: string;
  reference_id?: string;
  notes?: string;
  supplier_status?: string;
  po_suppliers?: string;
  supplier_company_name?: string;
  supplier_image?: string;
  ip_address?: string;
  signature?: string;
  company_purchase_order_id?: string;
  custom_purchase_order_id?: string;
  custom_purchase_orders_id?: string;
  tax_id?: string;
  multiple_ref_bill_ids?: string;
  project_name?: string;
  pro_id?: number;
  project_id_string?: string;
  project_type_name?: string;
  project_type?: string;
  ship_to_name?: string;
  emp_username?: string;
  po_created_email?: string;
  order_from_username?: string;
  order_from_image?: string;
  approved_username?: string;
  billing_status_name?: string;
  ship_to_company_name?: string;
  from_company_name?: string;
  from_address1?: string;
  from_address2?: string;
  from_city?: string;
  from_state?: string;
  from_zip?: string;
  from_phone?: string;
  original_order_date?: string;
  order_date?: string;
  po_order_date?: string;
  delivery_date?: string;
  projectPrefix?: string;
  need_prefix_project?: string;
  date_added?: string;
  time_added?: string;
  date_supplier_status?: string;
  time_supplier_status?: string;
  origin_date_modified?: string;
  billing_status_key?: string;
  supplier_id?: number | string;
  supplier_details?: IPoMultipleSupplier[];
  ship_to?: number | string;
  order_from?: number | string;
  pro_id?: number | string;
  project_id?: number;
  order_from_dir_type?: number;
  is_deleted?: number;
  is_multiple_suppliers?: number;
  qb_date_added?: string;
  qb_time_added?: string;
  quickbook_purchaseorder_id?: string;
  order_from_dir_type?: string | number;
  is_reversible_tax?: string;
  tax_name?: string;
  tax_rate?: string;
  is_group_tax?: string;
  temperature_scale?: string;
  customer_id?: string;
  address_from?: string;
  po_address1?: string;
  po_address2?: string;
  po_city?: string;
  po_state?: string;
  po_zip?: string;
  user_id?: number | string;
  longitude?: number | string;
  latitude?: number | string;
  ship_to_contact_name?: string;
  ship_to_contact?: number | string;
  supplier_dir_type?: number | string;
  ship_to_contact_additional_contact_id?: number | string;
  is_billed?: number | string;
  supp_name?: string;
  image?: string;
  ship_to_contact_image?: string;
  email_subject?: string;
  supplier_email?: string;
}

interface IPOSubmitedPricingDetail {
  contact_id?: string;
  user_id?: string;
  first_name?: string;
  last_name?: string;
  unique_name?: string;
  company_name?: string;
  display_name?: string;
  submitted_datetime?: string;
  pricing_ved_items?: IPOPricingDetail[];
  ip_address?: string;
  notes?: string;
}

interface IPOPricingDetail {
  item_id?: number | string;
  purchase_order_id?: number | string;
  subject?: string;
  quantity?: number | string;
  unit?: string;
  unit_cost?: number | string;
  markup?: number | string;
  tax_id?: string;
  total_tax?: string;
  cost_code_id?: number | string;
  total?: number | string;
  description?: string;
  purchase_order_item_no?: number | string;
  item_type?: number | string;
  reference_item_id?: number | string;
  assigned_to?: number | string;
  company_id?: number | string;
  directory_id?: number | string;
  is_deleted?: number | string;
  date_added?: string;
  date_modified?: string;
  parent_item_id?: number | string;
  demo_data?: number | string;
  quickbook_poitem_id?: number | string;
  quickbook_item_id?: number | string;
  qbc_id?: number | string;
  estimate_id?: string;
  change_order_id?: number | string;
  work_order_id?: number | string;
  reference_module_item_id?: number | string;
  project_budget_item_id?: number | string;
  user_id?: number | string;
  apply_global_tax?: number | string;
  unit_cost_back?: string;
  is_markup_percentage?: number | string;
  markup_amount?: number | string;
  item_on_database?: number | string;
  is_discount_item?: number | string;
  is_tax_item?: number | string;
  is_freight_charge_item?: number | string;
  sku?: string;
  assigned_to_contact_id?: number | string;
  variation_id?: number | string;
  billed_quantity?: string;
  delivered_quantity?: number | string;
  internal_notes?: string;
  is_project_template?: number | string;
  project_template_id?: string;
  origin?: number | string;
  item_type_display_name?: string;
  item_type_name?: string;
  item_type_key?: string;
  tax_rate?: string;
  cost_code_name?: string;
  cost_code?: string;
  code_id?: number | string;
  pricing_item_id?: number | string;
  note?: string;
  submitted_datetime?: string;
  ip_address?: string;
}

interface IPOStatusList {
  label?: string;
  value?: IStatus | undefined;
  default_color?: string;
  bgcolor?: string;
  icon?: IFontAwesomeIconProps["icon"];
  key?: string | undefined;
  bgcolor?: string;
  sort_order?: number;
  status_color?: string;
  show_in_progress_bar?: number;
  does_sync_qb?: boolean | number;
  item_id?: number | string;
}

interface IPOItemTableCellRenderer {
  data: Partial<IPOItemData>;
}
interface IPODetailsApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IPODetailData;
}
interface IPOItemData {
  item_id: number;
  purchase_order_id: number;
  subject: string;
  quantity: number;
  unit: string;
  // unit_cost: number;
  markup: string;
  tax_id: number | string;
  total_tax: number | string;
  cost_code_id: number | string;
  total: string | undefined;
  description: string;
  purchase_order_item_no: number;
  item_type: number | string;
  reference_item_id: number;
  assigned_to: number;
  company_id: number;
  directory_id: number | string;
  is_deleted: number;
  date_added: string;
  date_modified: string;
  parent_item_id: number;
  demo_data: number;
  quickbook_poitem_id: number;
  quickbook_item_id: number;
  qbc_id: string;
  estimate_id: number;
  change_order_id: number;
  work_order_id: number;
  reference_module_item_id: number;
  project_budget_item_id: number;
  user_id: number;
  apply_global_tax: number;
  unit_cost_back: number | string;
  is_markup_percentage: number;
  markup_amount: number | string;
  item_on_database: number;
  is_discount_item: number;
  is_tax_item: number;
  is_freight_charge_item: number;
  sku: string;
  assigned_to_contact_id: number;
  variation_id: number;
  billed_quantity: number;
  delivered_quantity: number;
  internal_notes: string;
  is_project_template: number;
  project_template_id: number | string;
  origin: number;
  item_type_display_name: string;
  item_type_name: string;
  item_type_key: string;
  assignee_type: number | string;
  assignee_name: number | string;
  assigned_to_name_only: string;
  assigned_to_company_name: string;
  company_order_id: string;
  order_item_no: string;
  company_estimate_id: string;
  estimate_item_no: number;
  work_order: number | string;
  budget_item_no: number | string;
  budget_item_id: number | string;
  tax_rate: number | string;
  cost_code_name: string;
  cost_code: string;
  code_id: number | string;
  quickbook_costcode_id: number | string;
  bill_item_id: string;
  origin_date_modified: string;
  variation_name: number | string;
  amount: number | string;
  one_build_id?: number | string | undefined;
  updated_unit_cost: string;
  code_is_deleted: number;
  csi_code: string;
  reference_item_id: string | number | undefined;
  type_name: string;
  total?: string | number;
  unit_cost?: string | number;
  modified_unit_cost?: string | number;
  itemData?: IItemData;
  change_order_id?: number | string;
  source_name?: string;
  item_total?: string | number;
  reference_item_type_id?: number | string;
  items?: IPOItemData[];
  section_id?: string | number;
  section_name?: string;
  reference_module_id?: string | number;
  user_image?: string;
  remaining_quantity?: string | number;
  sectionID?: number;
  sectionKey?: string;
}

interface IPOOriginalScope {
  item_id?: number;
  sub_contract_id?: number;
  subject?: string;
  item_type?: number;
  reference_item_id?: number | string;
  quantity?: string;
  unit?: string;
  unit_cost?: string;
  markup?: string;
  total?: string;
  cost_code_id?: number;
  sub_contract_item_no?: number;
  reference_module_item_id?: number;
  estimate_id?: number;
  estimate_item_id?: string;
  user_id?: number;
  company_id?: number;
  tax_id?: string;
  is_markup_percentage?: number;
  description?: string;
  internal_notes?: string;
  item_on_database?: number;
  assigned_to?: number;
  assigned_to_contact_id?: string;
  bill_item_id?: string;
  item_type_display_name?: string;
  item_type_name?: string;
  item_type_key?: string;
  assignee_type?: number;
  assignee_name?: string;
  assigned_to_name_only?: string;
  assigned_to_company_name?: string;
  cost_code_name?: string;
  cost_code?: string;
  source?: string;
  no_mu_total?: string;
  budget_item_id?: number;
  total_sc_paid_bill_amt?: string;
  total_sc_paid_bill_percentage?: number;
  data_id?: number;
  change_order_id?: string;
  code_is_deleted?: number;
  reference_item_type_id?: string;
  modified_unit_cost?: string;
  item_total?: string;
}

interface IPOAddrInfo {
  user_id?: string | number;
  address1?: string;
  address2?: string;
  street1?: string;
  street2?: string;
  city?: string;
  state?: string;
  zip?: string;
  type?: string | number;
  latitude?: number;
  longitude?: number;
  address_from?: string;
}
interface IPOAddressKey {
  project?: string[];
  customer?: string[];
  other?: string[];
  directory?: string[];
}
interface IPOAddItem extends Partial<IPOItemData> {
  add_item_to_database?: number;
}

interface IPOFilePhotoFormData {
  primary_id: number;
  module_id: number;
  module_key: string;
  directory_type?: number;
  attach_image?: string;
  files?: IFile[];
  project_id?: number | string;
  folder_id?: number;
}

interface IPOBillHistory {
  bill_id: number;
  total: string;
  cost: string;
  company_bill_id: string;
  payment_amount: number;
  billing_status: string;
  payment_date: string;
  payment_id: number;
  due_date: string;
}

interface IEStatusProcess {
  [key: string]: string | number | boolean | undefined;
  estimate_id?: string | number;
  generate_invoice?: number | boolean;
  generate_project?: number | boolean;
  copy_to_project_sov?: number | boolean;
  create_schedule_tasks?: number | boolean;
  enable_procurement_tab?: number | boolean;
  complete_project?: number | boolean;
  project_color?: "FFFFFF";
}

interface IPODeleteItems {
  item_id: string | number | undefined;
  purchase_order_id: string | number | undefined;
}
interface IPOAddItemsApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    purchase_order_item_id?: number;
    items: IPOItemData[];
  };
}

interface IPOSelectSupllierApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    detail?: {
      data: IPODetailData;
    };
    items: {
      data: IPOItemData[];
    };
  };
}

interface IPOFilePhotoInitialState {
  filePhotos: IFile[];
  isFilePhotoLoading: boolean;
  isFileDataFetched: boolean;
}

// interface IPOImportItemsInitialState {
//   // estimateItems: ISCEstimateItem[];
//   // isEstitemsLoading: boolean;
//   // isEstitemsFetched: boolean;

//   poScheduleValueItems: ISCChangeOrder[];
//   isPOScheduleValueLoading: boolean;
//   isPOScheduleValueFetched: boolean;

//   scheduleValueItems: ISCScheduleValueItemData;
//   isScheduleValueLoading: boolean;
//   isScheduleValueFetched: boolean;
// }
