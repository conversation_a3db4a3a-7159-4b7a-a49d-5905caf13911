interface IPOKanbanProps {
  kanbanSetting: IKanbanSetting | undefined;
  setKanbanSetting: React.Dispatch<
    React.SetStateAction<IKanbanSetting | undefined>
  >;
  kanbanSelected: string[];
  setKanbanSelected: React.Dispatch<string[]>;
  search: string;
  isReadOnly: boolean;
}

interface POMatrix {
  subtotalTaxeble: number;
  subtotal: number;
  tax: number;
  grandTotal: number;
  allGrandTotal: number;
  EstimateMatrix: MatrixAnalytics;
  WorkOrderMatrix: MatrixAnalytics;
  ChangeOrderMatrix: MatrixAnalytics;
  LumpsumMatrix: MatrixAnalytics;
  OthersMatrix: MatrixAnalytics;
}
interface MatrixAnalytics {
  subtotalTaxeble: number;
  subtotal: number;
  tax: number;
  grandTotal: number;
}

interface HandleViewItem {
  ({
    data,
    allDetails,
    sectionName,
    ViewItemsKey,
  }: {
    data: IPOItemData;
    allDetails: IPOItemData[];
    sectionName: string;
    ViewItemsKey: ViewItemsKey;
  }): void;
}
interface IPOKanbanRowDataList {
  purchase_order_id: number;
  cost: string;
  project_id: number;
  subject: string;
  order_date: string;
  total: string;
  supplier_id: number;
  billing_status: number;
  is_deleted: number;
  supplier_contact_id: number;
  project_name: string;
  pro_id: number;
  project_id_string: string;
  supplier_email: number | string;
  supplier_name: number | string;
  email_subject: string;
  billing_status_name: string;
  supp_name: number | string;
  company_purchase_order_id: string;
  purchase_order: string;
  order_date_po_maill: string;
  po_order_date: number | string;
  projectPrefix: string;
  prefix_company_purchase_order_id: string;
  date_added: string;
  kanban_date_modified: string;
  number_of_item: number | string;
}
interface IPOKanbanData {
  sort_order: string;
  status_color: string;
  item_id: number;
  key: string;
  is_custom_item: string;
  name: string;
  item_type: string;
  qb_full_name: string;
  qb_account_type: string;
  qb_account_sub_type: string;
  quickbook_category_id: string;
  status_name: string;
  is_deleted: string;
  does_sync_qb: number;
  orig_name: string;
  is_status: number;
  sorting_id: number;
  type: string;
  show_in_progress_bar: number;
  is_collapse_card: number;
  total_count: string;
  kanban_data: IPOKanbanRowDataList[];
  itemId: number;
  total_count: string;
}

interface IPOKanbanApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IPOKanbanData[];
  kanban_estimate_type_selected: number[];
  kanban_setting: IKanbanSetting;
}

interface IPODetailsTopBarProps {
  onReloadPODetails: () => void;
  sidebarCollapse: boolean | undefined;
  handleStepClick?: (value: string) => void;
  activeStep: number | string;
  setActiveStep: React.Dispatch<React.SetStateAction<number | string>>;
  purchaseStatList: IPOStatusList[];
  purchaseStatusVal?: IPOStatusList;
  isLoading?: boolean;
  handleChangeFieldStatus?: ({ field, status, action }: IPOFieldStatus) => void;
  handleUpdateField?: (data: IPODetailData, extraData?: IPODetailData) => void;
  isLoadingStatus?: IPOStatusHeaderLoaderList | undefined;
  selectStatusKey?: IPOStatusHeaderKeyList;
  selectedStatusKey?: string | undefined;
  //   isConfirmCompletedStatus?: IPOStatusCompletedState;
  //   isConfirmCompletedProjectStatus?: IPOStatusCompletedProjectState;
  //   statusCompletedModalHandler?: ((value: number) => void) | undefined;
  //   onCloseCompletedModal?: () => void;
  //   isCompletedLoading?: boolean;
  //   isCompletedProjectLoading?: boolean;
  //   isConfirmApprovedStatus?: IPOStatusApprovedState | undefined;
  //   isLoadLock?: boolean;
  loadingStatus?: Array<IPOFieldStatus>;
  isReadOnly?: boolean;
  isStatusLost?: boolean;
  selectedStatusInd?: number;
}

interface IPOStatusHeaderLoaderList {
  loadingStatus?: Array<IPOFieldStatus>;
  setLoadingStatus?: React.Dispatch<
    React.SetStateAction<Array<IPOFieldStatus>>
  >;
}
interface IPOFieldStatus {
  field?: string;
  status: IStatus;
  action?: string;
}
interface IPOStatusHeaderKeyList {
  selectedStatusKey?: string | undefined;
  setSelectedStatusKey?: React.Dispatch<React.SetStateAction<string>>;
}

interface IPOStatusCompletedObj {
  action?: boolean;
  value?: string | number;
}

interface IPOStatusbarProps {
  inputValues?: IPODetailData;
  isStatusLost?: boolean;
  setInputValues?: React.Dispatch<React.SetStateAction<IPODetailData>>;
  setWorkOrderStatus?: React.Dispatch<
    React.SetStateAction<string | number | undefined>
  >;
  handleUpdateField: (data: IPODetailData, extraData?: IPODetailData) => void;
  handleChangeFieldStatus: (data: IFieldStatus) => void;
  purchaseStatList: IPOStatusList[];
  isReadOnly: boolean;
  activeStep?: number | string;
  setActiveStep: React.Dispatch<React.SetStateAction<number | string>>;
  selectedStatusInd?: number;
}

interface IPOSendEmailForm extends ISendEmailFormDataWithApiDefaultNew {
  t_id?: string;
  purchase_order_id?: number | string;
  action: string;
}

interface IDownloadPurchaseOrderPdf {
  purchase_order_id: number | undefined;
  action?: string;
  t_id?: string;
}

interface IDownloadPurchaseOrderRes extends Omit<IDefaultAPIRes, "data"> {
  data: ObjType;
  base64_encode_pdf: string | undefined;
  base64_encode_pdfUrl: string | undefined;
}

interface IPOItemFilter {
  title: string;
  item_type: string;
}

interface IPOSupplierTable {
  totalAmount: Array[number | string];
  listData: Array<TableData>;
}

interface TableData {
  subject: string | undefined;
  quantity: string | number | undefined;
  purchase_order_id: string | number | undefined;
  arrTotal: (string | number)[];
}

interface UpdatePOItemPayload {
  itemId: string | number;
  items?: Partial<IPOItemData>[];
  updatedItem: Partial<IPOItemData>;
  sectionKey: string;
  sectionID?: string | number;
  EstimateSectionId?: string | number;
}
interface RemovePOItemPayload {
  itemIds: number[];
  // itemIds: string[] | number[];
  updatedItem?: Partial<IPOItemData>;
  sectionKey: SectionKey;
  sectionID: string | number;
  EstimateSectionId?: string | number;
}

interface ItemListProps {
  filteredCodeCostData: Array<{ label: string; value: string }>;
  isReadOnly: boolean;
  handleDeleteItemOpen: (data: IPOItemProps) => void;
  // handleDeleteItemClose: () => void;
  handleViewItem: HandleViewItem;
  setItemsData: (data: Partial<IPOItemData>) => void;
  POMatrix: POMatrix;
  isTaxEnabled: boolean;
  isParentReadOnly: boolean;
}

interface IPOItemProps {
  itemData: IPOItemData;
  sectionID?: number;
  sectionName?: string;
  sectionKey?: string;
  EstimateSectionId?: string | number;
}

interface IPurchaseOrderItemDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  itemsData: Partial<IPOItemData>;
  isReadOnly: boolean;
  setItemOpen: Dispatch<SetStateAction<boolean>>;
  setPOToView: (data: Partial<IPOItemData>) => void;
  isItemAdd: boolean;
  isViewOnly: boolean;
  // ParentIsReadOnly: boolean;
  purchaseOrderItems: Array<IPOItemData>;
  selectedSectionName: string | null;
  ViewItemsKey: ViewItemsKey;
  isTaxEnabled: boolean;
}

interface IPOCommonColumns {
  _t?: (key: string) => string;
  isReadOnly: boolean;
  isParentReadOnly: boolean;
  updateItemField: (params: {
    itemId: number;
    updatedItem: Partial<IPOItemData>;
    itemData?: IPOItemData;
  }) => Promise<void>;
  filteredCodeCostData: Array<{
    label: string;
    value: string;
    csi_code?: string;
    cost_code_name?: string;
    code_id?: string | number;
  }>;
  // DeliveredHeader: () => React.ReactNode;
  setItemsData?: (data: Partial<IPOItemData>) => void;
  // setPurchaseOrderItem: (value: boolean) => void;
  // suppressKeyboardEvent?: (params: ColDef<IPOItemData>) => boolean;
  formatter?: (value: string) => {
    value_with_symbol: string;
    value: string;
  };
  inputFormatter?: (value: string) => {
    value: string;
  };
  isLoadingCheckBox?: Record<number, boolean>;
  handleDeleteItemOpen?: (itemId: number) => void;
  handleDeleteItem?: (data: IPOItemData) => void;
  handleDeleteItemClose?: () => void;
  handleViewItem?: HandleViewItem;
  handleAllClick?: () => void;
  sectionName?: string;
  rowData?: IPOItemData[];
  ViewItemsKey: {
    sectionKey: keyof IPOItemSectionData;
    sectionId?: string | number;
  };
  isTaxEnabled?: boolean;
}

// Directory Addressinfo

interface IPOAddrInfo {
  user_id?: string | number;
  address1?: string;
  address2?: string;
  po_address1?: string;
  po_address2?: string;
  city?: string;
  state?: string;
  po_city?: string;
  po_state?: string;
  zip?: string;
  po_zip?: string;
  type?: string | number;
  latitude?: number;
  longitude?: number;
}
