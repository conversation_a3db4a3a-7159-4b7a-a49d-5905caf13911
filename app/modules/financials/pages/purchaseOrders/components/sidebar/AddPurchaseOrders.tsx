// Hook
import { useIframe, useTranslation } from "~/hook";
// Atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { SelectField } from "~/shared/components/molecules/selectField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { ErrorMessage } from "~/shared/components/molecules/errorMessage";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { RadioGroupList } from "~/shared/components/molecules/radioGroupList";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { CheckboxGroupList } from "~/shared/components/molecules/checkboxGroupList";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { CustomFieldSkeleton } from "~/shared/components/molecules/customFieldSkeleton";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";
// organisms
import { SelectProject } from "~/shared/components/organisms/selectProject";
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";
// Other
import { getGConfig, getGProject, getGSettings } from "~/zustand";
import { replaceDOMParams, sanitizeString } from "~/helpers/helper";
import { useEffect, useMemo, useRef, useState } from "react";
import { getDirectaryKeyById } from "~/components/sidebars/multi-select/customer/zustand/action";
import { useFormik } from "formik";
import * as Yup from "yup";
import {
  getContactDetailsApi,
  getModuleAutoNumber,
} from "~/redux/action/commonAction";
import { useAppPOSelector } from "../../redux/store";
import { toBoolean } from "../../../estimates/utils/common";
import {
  BillingStatuskeytoItemId,
  PO_STATUS_ICON,
  OrderTypeList,
} from "../../utils/constants";
import useDirectoryKeyValue from "~/shared/hooks/useCustomField/useDirectoryKeyValue";
import useSideBarCustomField from "~/shared/hooks/useCustomField/useSidebarCustomField";
import { Form, useNavigate, useSearchParams } from "@remix-run/react";
import getCustomFieldAccess from "~/shared/utils/helper/getCustomFieldAccess";
import { formatCustomFieldForRequest } from "~/shared/utils/helper/customFieldSidebarFormat";
import { addPOApi } from "../../redux/action/dashboardAction";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import {
  filterOptionBySubstring,
  onKeyDownNumber,
} from "~/shared/utils/helper/common";
import { displayDateFormat } from "~/shared/utils/helper/defaultDateFormat";
import { sendMessageKeys } from "~/components/page/$url/data";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { getProjectDetails } from "~/redux/action/getProjectDetailsAction";
import isEmpty from "lodash/isEmpty";
import { Popover } from "~/shared/components/atoms/popover";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { IconButton } from "~/shared/components/molecules/iconButton";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { HtmlDecode } from "../../utils/function";
import { getModuleAutoIncrementPrimaryId } from "~/zustand/module-auto-increment-primary-id/store";
import { AvatarIconPopover } from "~/shared/components/organisms/avatarIconPopover";
import OnlyRequiredCustomFields from "~/shared/components/organisms/OnlyRequiredCustomFields/OnlyRequiredCustomFields";

const AddPurchaseOrders = ({
  addPurchaseOrders,
  setAddPurchaseOrders,
  onClose = () => {},
}: IAddPurchaseOrdersProps) => {
  const { _t } = useTranslation();
  const { parentPostMessage } = useIframe();
  const navigate = useNavigate();
  const { module_singular_name, module_id, module_key }: GConfig = getGConfig();
  const { isNoAccessCustomField }: ICustomFieldAccess = getCustomFieldAccess();
  const {
    is_custom_purchase_order_id,
    quickbook_desktop_sync,
    quickbook_sync,
    date_format,
  }: GSettings = getGSettings();
  const gConfig: GConfig = getGConfig();
  const { project_id, project_name }: GProject = getGProject();
  const [searchParams, setSearchParams] = useSearchParams();
  const isCalledProApiRef = useRef<string | null>(null);
  const { isSLDataFetched, customStatusList }: ICustomStatusListInitialState =
    useAppPOSelector((state) => state.customStatusListData);
  const { purchaseOrderDetail, isPODetailLoading } = useAppPOSelector(
    (state) => state?.purchaseOrderDetail
  );
  const [isMultipleSupplier, setIsMultipleSupplier] = useState<number>(-1);
  const [isSelectEmployeeOpen, setIsSelectEmployeeOpen] =
    useState<boolean>(false);
  const [selectedEmployee, setSelectedEmployee] = useState<
    TselectedContactSendMail[]
  >([]);
  const [isOpenSelectSupplierTo, setIsOpenSelectSupplierTo] =
    useState<boolean>(false);
  const [selectedSupplier, setSelectedSupplier] = useState<
    (TselectedContactSendMail & { supplier_dir_type?: string })[]
  >([]);
  const [submittingFrm, setSubmittingFrm] = useState<boolean>(false);
  const [additionalContact, setAdditionContact] = useState<number>(0);
  const [supplierAdditionContact, setSupplierAdditionContact] =
    useState<number>(0);
  const [isOpenContactDetailsForEmployee, setIsOpenContactDetailsForEmployee] =
    useState<boolean>(false);
  const [isOpenContactDetailsForSupplier, setIsOpenContactDetailsForSupplier] =
    useState<boolean>(false);
  const [contactId, setcontactId] = useState<number>();
  const [suppliercontactId, setSuppliercontactId] = useState<number>();
  const [isSubmit, setIsSubmit] = useState<boolean>(false);
  const [selectedProject, setSelectedProject] = useState<IProject[]>([]);
  const [isSelectProjectOpen, setIsSelectProjectOpen] =
    useState<boolean>(false);
  const { directoryKeyValue, directory }: IDirectoryFormCustomField =
    useDirectoryKeyValue();
  const { componentList, loadingCustomField } = useSideBarCustomField(
    { directory, directoryKeyValue } as IDirectoryFormCustomField,
    {
      moduleId: module_id,
    } as IRequestCustomFieldForSidebar
  );
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const {
    need_to_increment,
    last_primary_id,
  }: Partial<IResponseGetModuleAutoNumber> =
    getModuleAutoIncrementPrimaryId() || {};
  const {
    user_id = 0,
    type = 0,
    first_name = "",
    last_name = "",
    email,
    company_name = "",
    display_name = "",
    image = "",
  } = user || {};
  const initValues: IaddPurchaseOrderData = {
    project_id: null,
    is_multiple_suppliers: 0,
    subject: null,
    order_from: user_id,
    custom_purchase_orders_id: null,
    billing_status: BillingStatuskeytoItemId["po_on_hold"],
    supplier_id: null,
  };

  const defaultUser = useMemo(
    () => ({
      user_id: user_id,
      type: type,
      orig_type: type,
      type_key: getDirectaryKeyById(type === 1 ? 2 : Number(type), undefined),
      first_name: first_name,
      last_name: last_name,
      email: email ?? "",
      company_name: company_name,
      display_name: display_name,
      image: image,
    }),
    [
      user_id,
      type,
      first_name,
      last_name,
      email,
      company_name,
      display_name,
      image,
    ]
  );

  // Static validation schema
  // Your existing logic to create dynamicValidationSchema
  const dynamicValidationSchema = componentList.reduce((acc, fieldName) => {
    if (fieldName.multiple || fieldName.type === "checkbox-group") {
      acc[fieldName.name] = Yup.array()
        .of(Yup.string().required("This field is required."))
        .min(1, "This field is required.")
        .required("This field is required.");
    } else {
      acc[fieldName.name] = Yup.string().required("This field is required.");
    }
    return acc;
  }, {} as Record<string, Yup.StringSchema | Yup.AnySchema>);
  // Static validation schema
  const baseValidationSchema = {
    // project_id: Yup.string().required("This field is required."),
    is_multiple_suppliers: Yup.string().required("This field is required."),
    order_from: Yup.number().required("This field is required."),
    subject: Yup.string().required("This field is required."),
    billing_status: Yup.number().required("This field is required."),
    custom_purchase_orders_id:
      is_custom_purchase_order_id === 0
        ? Yup.string()
        : Yup.string().required("This field is required."),
    // supplier_id: Yup.number().required("This field is required."),
  };
  const validationSchema =
    componentList?.length && !isNoAccessCustomField
      ? Yup.object().shape({
          ...baseValidationSchema,
          custom_fields: Yup.object().shape(dynamicValidationSchema),
        })
      : Yup.object().shape({
          ...baseValidationSchema,
        });

  const initialFormValues =
    componentList?.length && !isNoAccessCustomField
      ? {
          ...initValues,
          custom_fields: componentList.reduce((acc, item) => {
            acc[item.name] = item?.value ?? "";
            return acc;
          }, {} as ICustomFieldInitValue),
        }
      : initValues;

  const onSubmitForm = async (values: IaddPurchaseOrderData) => {
    // if (values?.billing_status === 189 && values?.is_multiple_suppliers === 1) {
    //   notification.error({
    //     description:
    //       "You can not select pricing request while Billing Status is Approved!",
    //   });
    //   return;
    // }
    const formData = getValuableObj({
      ...values,
      custom_fields:
        values.custom_fields && !isNoAccessCustomField
          ? formatCustomFieldForRequest(
              values.custom_fields,
              componentList,
              date_format
            ).custom_fields
          : undefined,
      access_to_custom_fields:
        componentList?.length && !isNoAccessCustomField ? 1 : 0,
      custom_purchase_orders_id:
        HTMLEntities.encode(
          sanitizeString(values?.custom_purchase_orders_id?.toString())
        ).trim() || "",
    });

    try {
      const responseApi = (await addPOApi({
        ...formData,
        custom_purchase_orders_id:
          HTMLEntities.encode(
            sanitizeString(values?.custom_purchase_orders_id?.toString())
          ).trim() || "",
      })) as IPODetailApiRes;

      if (responseApi?.success) {
        if (window && window.ENV && window.ENV.PAGE_IS_IFRAME) {
          parentPostMessage(sendMessageKeys?.modal_change, {
            open: false,
          });
          setAddPurchaseOrders(false);
          setSubmittingFrm(false);

          onClose();
        } else {
          navigate(`${(responseApi?.data as IPOItemData)?.purchase_order_id}`);
          // dispatch(resetDash());
          return;
        }
      } else {
        // setAddPurchaseOrders(false);
        // onClose();
        notification.error({
          description: responseApi?.message || "",
        });
        setSubmittingFrm(false);
      }
    } catch (error) {
      // setAddPurchaseOrders(false);
      // onClose();
      notification.error({
        description: (error as Error).message || "",
      });
      setSubmittingFrm(false);
    }
  };

  // const initialFormValues = initValues;
  const formik = useFormik({
    initialValues: initialFormValues,
    validationSchema: validationSchema,
    validateOnChange: false,
    onSubmit: async (values, { setSubmitting }) => {
      setIsSubmit(true);
      // Manually validate the form first
      const errors = await validateForm();
      // If there are errors, stop further execution
      if (Object?.keys(errors)?.length > 0) {
        setTouched(
          Object?.keys(errors)?.reduce((acc: Record<string, boolean>, key) => {
            acc[key] = true; // Mark fields with errors as touched
            return acc;
          }, {})
        );
        return;
      }
      const billing_status = status?.statusList?.find(
        (item) => item.value == values?.billing_status
      )?.key;
      if (
        ["po_approved", "po_submitted", "po_received", "po_paid"]?.includes(
          billing_status || ""
        )
      ) {
        if (
          !values?.po_suppliers ||
          !values?.supplier_id ||
          values?.supplier_id == 0
        ) {
          return notification.error({
            description: _t("Missing Supplier."),
          });
        }
      }
      setSubmittingFrm(true);
      await onSubmitForm(values);
      setSubmitting(false);
    },
  });

  const {
    handleSubmit,
    setFieldValue,
    values,
    errors,
    setFieldError,
    setValues,
    handleBlur,
    handleChange,
    resetForm,
    setTouched,
    validateForm,
    isSubmitting,
  } = formik;
  useEffect(() => {
    if (
      searchParams.get("action")?.trim() === "new" &&
      searchParams.get("project")
    ) {
      if (isCalledProApiRef.current !== searchParams.get("project")) {
        isCalledProApiRef.current = searchParams.get("project");

        (async () => {
          try {
            const proResApi = (await getProjectDetails({
              start: 0,
              limit: 1,
              projects: searchParams.get("project") || "",
              need_all_projects: 0,
              global_call: true,
              is_completed: true,
              filter: { status: "0" },
            })) as IProjectDetailsRes;

            const queryPro = proResApi?.data?.projects[0];
            setFieldValue("project_id", queryPro?.id);
            setSelectedProject([
              {
                id: Number(queryPro?.id),
                project_name: queryPro?.project_name,
              },
            ]);
          } catch (e) {}
        })();
      }
    } else {
      if (
        project_id &&
        project_id !== "0"
        // &&        isEmpty(searchParams.get("project"))
      ) {
        (async () => {
          setSelectedProject([
            {
              id: Number(project_id),
              project_name: project_name,
            },
          ]);
          await setFieldValue("project_id", project_id);
        })();
      }
      // else {
      //   setFieldValue("project_id", null);
      //   setSelectedProject([]);
      // }
    }
  }, [
    // addPurchaseOrders,
    project_id,
    project_name,
    isCalledProApiRef,
    searchParams.get("action"),
    searchParams.get("project"),
    componentList,
    isNoAccessCustomField,
  ]);

  const fetchCustDetails = async (id: string, contactId?: string) => {
    try {
      const customerDetail = (await getContactDetailsApi({
        directoryId: id || "",
      })) as IGenerateShareLinkApiRes;

      if (customerDetail?.success) {
        const custData = customerDetail?.data;
        const displayName =
          !!custData?.emp_name && !!custData?.company_name
            ? `${custData?.emp_name} (${custData?.company_name})`
            : !!custData?.emp_name
            ? custData?.emp_name
            : custData?.company_name;
        const assigneeObj = [
          {
            user_id: custData?.user_id,
            display_name: displayName,
            company_name: displayName,
            image: custData?.image || "",
            type_name: custData?.type_name || "",
            type: custData?.type,
          },
        ] as Partial<TselectedContactSendMail[]>;

        const suppliers =
          custData?.contact_id == 0
            ? custData?.user_id?.toString()
            : `${custData?.user_id}|${custData?.contact_id}`;
        setFieldValue("po_suppliers", suppliers);
        setFieldValue("supplier_id", Number(custData?.user_id || 0));
        setFieldValue(
          "supplier_contact_id",
          Number(custData?.contact_id != 0 ? custData?.contact_id : 0)
        );
        setSelectedSupplier(assigneeObj as TselectedContactSendMail[]);
      }
    } catch (e) {}
  };
  useEffect(() => {
    const dirId =
      searchParams.get("contractor_id") || searchParams.get("vendor_id");
    if (!dirId || dirId == "0") {
      return;
    }
    fetchCustDetails(dirId);
  }, [searchParams.get("contractor_id"), searchParams.get("vendor_id")]);

  // Reset form on drawer close
  const handleCloseDrawer = () => {
    searchParams.delete("project");
    searchParams.delete("contractor_id");
    searchParams.delete("vendor_id");
    searchParams.delete("action");
    setSearchParams(searchParams);
    setAddPurchaseOrders(false);
    setAdditionContact(0);
    setSelectedEmployee([]);
    setSelectedSupplier([]);
    setSelectedProject([]);
    resetForm();
  };
  const getAutoNumber = async () => {
    // setNumberLoading(true);
    const autoNumberRes = (await getModuleAutoNumber({
      module_id: module_id,
      module_key: module_key,
    })) as GetModuleAutoNumberApiResponse;
    if (autoNumberRes?.success) {
      const newId = autoNumberRes?.data?.last_primary_id
        ? Number(autoNumberRes?.data?.last_primary_id) +
          autoNumberRes?.data?.need_to_increment
        : "";
      setValues({
        ...values,
        custom_purchase_orders_id: String(newId),
      });
    }
    if (!autoNumberRes?.success) {
      notification.error({
        description: autoNumberRes?.message || "Something went wrong",
      });
    }
  };

  useEffect(() => {
    if (addPurchaseOrders) getAutoNumber();
  }, [addPurchaseOrders]);

  useEffect(() => {
    if (user_id !== 0) {
      if (user) {
        setSelectedEmployee([
          defaultUser as unknown as TselectedContactSendMail,
        ]);
      }
      formik.setFieldValue("order_from", user_id);
    }
  }, [addPurchaseOrders]);

  const purchaseStatList: EStatusList[] = useMemo(
    () =>
      customStatusList?.map((item: ICustomStatusSL) => ({
        label: HTMLEntities.decode(sanitizeString(item?.name)),
        value: item?.key?.toString(),
        item_id: item?.item_id,
        default_color: item?.status_color,
        sort_order: Number(item?.sort_order),
        icon: PO_STATUS_ICON[item?.key as keyof typeof PO_STATUS_ICON],
        key: item?.key ?? "",
        does_sync_qb: item?.does_sync_qb,
        show_in_progress_bar: item?.show_in_progress_bar,
      })) as EStatusList[],
    [customStatusList, purchaseOrderDetail]
  );

  const status = useMemo(() => {
    const statusList = purchaseStatList?.map((item: EStatusList) => {
      const poLabel = `${item?.label}${
        item?.does_sync_qb &&
        (toBoolean(quickbook_sync) || toBoolean(quickbook_desktop_sync))
          ? " (Post To QuickBooks)"
          : ""
      }`;
      return {
        label: HtmlDecode(poLabel) || "",
        value: item?.item_id?.toString() ?? "",
        key: item?.value?.toString() ?? "",
        show_in_progress_bar: item?.show_in_progress_bar,
        sort_order: item?.sort_order,
        does_sync_qb: item?.does_sync_qb,
        // icon: (
        //   <FontAwesomeIcon
        //     // icon="fa-solid fa-square"
        //     icon={faSquare}
        //     className="h-3.5 w-3.5"
        //     style={{
        //       color: item?.default_color,
        //     }}
        //   />
        // ),
      };
    });
    return { statusList };
  }, [purchaseStatList]);
  const handleSupplierTo = (data: TselectedContactSendMail[]) => {
    // if (data?.length !== 0) {
    // const assignedToArray = data?.map((user) => {
    //   const supplierKey: string = getDirectaryKeyById(
    //     Number(user?.type),
    //     gConfig
    //   );
    //   return {
    //     ...data,
    //     supplier_dir_type: supplierKey,
    //     type_name: user?.type_name,
    //     type: user?.type,
    //     contact_id: user?.contact_id?.toString(),
    //     // directory_id: user.directory_id?.toString(),
    //     directory_id:
    //       user?.contact_id?.toString() == "0"
    //         ? user?.user_id?.toString()
    //         : user?.contact_id?.toString(),
    //     company_name: user?.company_name?.trim(),
    //     user_id: user?.user_id?.toString(),
    //   };
    // });
    const suppliers = data
      ?.map((user) => {
        if (user?.contact_id == 0) {
          return user?.user_id?.toString();
        } else {
          return `${user?.user_id}|${user?.contact_id}`;
        }
      })
      ?.join(",");
    setSelectedSupplier(data);
    setFieldValue("po_suppliers", suppliers);
    setFieldValue("supplier_id", Number(data?.[0]?.user_id || 0));
    // setFieldValue("is_multiple_suppliers", suppliers);
    setFieldValue(
      "supplier_contact_id",
      Number(
        data?.filter((user) => user?.contact_id != 0)?.[0]?.contact_id || 0
      )
    );
  };

  //Add new project logic==============================
  //  let { getExistingProjectsWithApi }: UseExistingProjectsResponse =
  //    useExistingProjects("project");
  // const fetchDefaultProject = async (id: string) => {
  //   const projects = await getExistingProjectsWithApi(id);
  //   const projectName =
  //     projects && projects?.[0] && projects?.[0]?.project_name;
  //   const projectId = Number(id) || projects[0]?.key;

  //   if (projectId && projectName) {
  //     setSelectedProject(projects as IProject[]);
  //   }
  //   setIsSelectProjectOpen(false);
  // };
  //================================
  useEffect(() => {
    if (
      Number(is_custom_purchase_order_id) === 2 &&
      need_to_increment &&
      last_primary_id
    ) {
      formik.setValues({
        ...formik.values,
        custom_purchase_orders_id: (
          Number(need_to_increment) + Number(last_primary_id)
        ).toString(),
      });
    } else if (Number(is_custom_purchase_order_id) === 0) {
      formik.setValues({
        ...formik.values,
        custom_purchase_orders_id: "", // Brijesh Kevadiya told to send "" in case if the gSetting.is_custom_purchase_order_id is 0 -> "Set at number"
        // custom_purchase_orders_id: (gSetting?.custom_work_orders_id).toString(),
      });
    } else {
      formik.setValues({
        ...formik.values,
        custom_purchase_orders_id: "",
      });
    }
  }, [
    is_custom_purchase_order_id,
    need_to_increment,
    last_primary_id,
    addPurchaseOrders,
  ]);
  return (
    <Drawer
      open={addPurchaseOrders}
      push={false}
      rootClassName="drawer-open"
      width={718}
      classNames={{
        body: "!p-0 !overflow-hidden",
      }}
      title={
        <div className="flex items-center">
          <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
            <FontAwesomeIcon
              className="w-4 h-4"
              icon="fa-regular fa-file-invoice"
            />
          </div>
          <Header
            level={5}
            className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
          >
            {_t(`Add ${module_singular_name}`)}
          </Header>
        </div>
      }
      closeIcon={
        !window.ENV.PAGE_IS_IFRAME ? (
          <CloseButton
            onClick={() => {
              onClose();
              handleCloseDrawer();
            }}
          />
        ) : null
      }
    >
      <Form method="post" onSubmit={handleSubmit} noValidate>
        <div className="py-4">
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <div className="grid gap-4">
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <ButtonField
                    label={_t("Project")}
                    name="project_id"
                    id="project_id"
                    key="project_id"
                    required={false}
                    labelPlacement="top"
                    onClick={() => setIsSelectProjectOpen(true)}
                    value={
                      selectedProject?.length
                        ? HtmlDecode(selectedProject[0]?.project_name)
                        : ""
                    }
                    errorMessage={
                      formik.touched?.project_id && !formik.values.project_id
                        ? formik.errors.project_id
                        : ""
                    }
                    addonBefore={
                      selectedProject?.length ? (
                        <ProjectFieldRedirectionIcon
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                          projectId={`${selectedProject[0]?.id}`}
                        />
                      ) : null
                    }
                  />
                </div>
                <div className="w-full">
                  <SelectField
                    label={_t("Type")}
                    labelPlacement="top"
                    name="is_multiple_suppliers"
                    id="is_multiple_suppliers"
                    options={OrderTypeList}
                    isRequired={true}
                    value={values?.is_multiple_suppliers?.toString()}
                    onChange={(val) => {
                      setSelectedSupplier([]);
                      setFieldValue("supplier_id", null);
                      setIsMultipleSupplier(Number(val));
                      if (val) {
                        setFieldValue("is_multiple_suppliers", Number(val));
                      } else {
                        setFieldValue("is_multiple_suppliers", "");
                      }
                    }}
                    errorMessage={
                      formik?.touched.is_multiple_suppliers
                        ? errors.is_multiple_suppliers
                        : ""
                    }
                  />
                </div>
                <div className="w-full">
                  <InputField
                    name="subject"
                    id="subject"
                    label={_t("Subject")}
                    labelPlacement="top"
                    isRequired={true}
                    value={HtmlDecode(values?.subject || "")}
                    errorMessage={errors?.subject}
                    autoComplete="off"
                    onChange={(e) => {
                      setFieldValue("subject", e?.target?.value?.trimStart());
                      if (e?.target?.value?.trimStart()) {
                        setFieldError("subject", "");
                      } else {
                        setFieldError("subject", "This field is required.");
                      }
                    }}
                  />
                </div>
                <div className="w-full overflow-hidden">
                  <ButtonField
                    label={_t("From")}
                    labelPlacement="top"
                    name="order_from"
                    id="order_from"
                    key="order_from"
                    required={true}
                    onClick={() => {
                      setIsSelectEmployeeOpen(true);
                    }}
                    value={
                      selectedEmployee?.length &&
                      HtmlDecode(selectedEmployee[0]?.display_name)
                    }
                    errorMessage={
                      formik?.touched?.order_from && !values?.order_from
                        ? errors?.order_from
                        : ""
                    }
                    avatarProps={
                      selectedEmployee?.length == 1
                        ? {
                            user: {
                              name: HtmlDecode(
                                selectedEmployee?.[0]?.display_name
                              ),
                              image: selectedEmployee?.[0]?.image,
                            },
                          }
                        : undefined
                    }
                    addonBefore={
                      selectedEmployee?.length ? (
                        <div className="flex items-center gap-1">
                          <ContactDetailsButton
                            onClick={async (e) => {
                              e.stopPropagation();
                              await setAdditionContact(
                                Number(selectedEmployee?.[0]?.contact_id || 0)
                              );
                              await setcontactId(
                                selectedEmployee?.[0]?.user_id as number
                              );
                              setIsOpenContactDetailsForEmployee(true);
                            }}
                          />
                          <DirectoryFieldRedirectionIcon
                            directoryId={
                              selectedEmployee?.[0]?.user_id?.toString() || ""
                            }
                            directoryTypeKey={getDirectaryKeyById(
                              selectedEmployee?.[0]?.type?.toString() === "1"
                                ? 2
                                : Number(selectedEmployee?.[0]?.type),
                              gConfig
                            )}
                          />
                        </div>
                      ) : null
                    }
                  />
                </div>
                <div className="w-full overflow-hidden">
                  <ButtonField
                    label={_t("Supplier")}
                    labelPlacement="top"
                    name="supplier_id"
                    id="supplier_id"
                    key="supplier_id"
                    required={false}
                    onClick={() => {
                      setIsOpenSelectSupplierTo(true);
                    }}
                    value={
                      selectedSupplier?.length &&
                      selectedSupplier?.length !== 0 &&
                      selectedSupplier.length > 2
                        ? `${selectedSupplier?.length} Selected`
                        : HtmlDecode(
                            selectedSupplier
                              ?.map((supplier: TselectedContactSendMail) => {
                                return supplier?.display_name;
                              })
                              .join(", ")
                          )
                    }
                    avatarProps={
                      selectedSupplier?.length == 1
                        ? {
                            user: {
                              name: HtmlDecode(
                                selectedSupplier?.[0]?.display_name || ""
                              ),
                              image: selectedSupplier?.[0]?.image,
                            },
                          }
                        : undefined
                    }
                    errorMessage={
                      formik?.touched?.supplier_id && !values?.supplier_id
                        ? errors?.supplier_id
                        : ""
                    }
                    addonBefore={
                      <>
                        {selectedSupplier && selectedSupplier?.length == 1 && (
                          <div className="flex items-center gap-1">
                            <ContactDetailsButton
                              onClick={async (e) => {
                                e?.stopPropagation();
                                await setSupplierAdditionContact(
                                  Number(selectedSupplier?.[0]?.contact_id || 0)
                                );
                                await setSuppliercontactId(
                                  selectedSupplier?.[0]?.user_id as number
                                );
                                setIsOpenContactDetailsForSupplier(true);
                                // setIsContactDetails(true);
                              }}
                            />
                            <DirectoryFieldRedirectionIcon
                              directoryId={
                                selectedSupplier?.[0]?.user_id?.toString() as string
                              }
                              directoryTypeKey={getDirectaryKeyById(
                                selectedSupplier?.[0]?.type?.toString() === "1"
                                  ? 2
                                  : Number(selectedSupplier?.[0]?.type),
                                gConfig
                              )}
                            />
                          </div>
                        )}
                        {selectedSupplier && selectedSupplier?.length > 1 && (
                          <AvatarIconPopover
                            assignedTo={selectedSupplier as IAssignedToUsers[]}
                            setSelectedUserId={(data) => {
                              setcontactId(data.id);
                            }}
                            setIsOpenContactDetails={
                              setIsOpenContactDetailsForEmployee
                            }
                            placement="bottom"
                            redirectionIcon={true}
                          />
                        )}
                      </>
                    }
                  />
                </div>
                <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                  <div className="w-full">
                    <InputField
                      name="custom_purchase_orders_id"
                      id="custom_purchase_orders_id"
                      label={_t("PO") + " #"}
                      labelPlacement="top"
                      disabled={is_custom_purchase_order_id == 0}
                      isRequired={is_custom_purchase_order_id != 0}
                      value={
                        is_custom_purchase_order_id == 0
                          ? "Save To View"
                          : HtmlDecode(values?.custom_purchase_orders_id || "")
                      }
                      errorMessage={errors?.custom_purchase_orders_id}
                      autoComplete="off"
                      onChange={(e) => {
                        setFieldValue(
                          "custom_purchase_orders_id",
                          e?.target?.value?.trimStart()
                        );
                        if (e?.target?.value?.trimStart()) {
                          setFieldError("custom_purchase_orders_id", "");
                        } else {
                          setFieldError(
                            "custom_purchase_orders_id",
                            "This field is required."
                          );
                        }
                      }}
                    />
                  </div>
                  <div className="w-full">
                    <SelectField
                      name="billing_status"
                      id="billing_status"
                      label={_t("Billing Status")}
                      labelPlacement="top"
                      value={
                        status?.statusList?.length > 0
                          ? values?.billing_status?.toString()
                          : undefined
                      }
                      // disabled={gSetting?.unit_progressive_billing == 0}
                      options={status?.statusList}
                      onChange={(val) => {
                        if (val) {
                          setFieldValue("billing_status", Number(val));
                        } else {
                          setFieldValue("billing_status", "");
                        }
                      }}
                      isRequired={true}
                      errorMessage={
                        formik?.touched.billing_status
                          ? errors.billing_status
                          : ""
                      }
                    />
                  </div>
                </div>
              </SidebarCardBorder>
              {/* ----- custom component start ----- */}

              {!isNoAccessCustomField && loadingCustomField && (
                <CustomFieldSkeleton />
              )}
              <OnlyRequiredCustomFields
                componentList={componentList}
                formik={formik}
                isSubmit={isSubmit}
                loadingCustomField={loadingCustomField}
              />
            </div>
          </div>
          <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
            <PrimaryButton
              htmlType="submit"
              buttonText={_t(
                `Create ${HtmlDecode(module_singular_name ?? "Purchase Order")}`
              )}
              onClick={async () => {
                // validateForm(values);
                // setIsSubmit(true);
                // // Manually validate the form first
                // const errors = await validateForm();
                // console.log(values, errors);
                // // If there are errors, stop further execution
                // if (Object?.keys(errors)?.length > 0) {
                //   setTouched(
                //     Object?.keys(errors)?.reduce(
                //       (acc: Record<string, boolean>, key) => {
                //         acc[key] = true; // Mark fields with errors as touched
                //         return acc;
                //       },
                //       {}
                //     )
                //   );
                //   return;
                // }
                // const billing_status = status?.statusList?.find(
                //   (item) => item.value === values?.billing_status
                // )?.key;
                // if (
                //   [
                //     "po_approved",
                //     "po_submitted",
                //     "po_received",
                //     "po_paid",
                //   ]?.includes(billing_status || "")
                // ) {
                //   if (!values?.po_suppliers || values?.supplier_id == 0) {
                //     return notification.error({
                //       description: _t("Missing Supplier."),
                //     });
                //   }
                // }
                // if (
                //   billing_status === "po_approved" &&
                //   values?.is_multiple_suppliers === 1
                // ) {
                //   return;
                // }
                // handleSubmit();
              }}
              isLoading={isSubmitting || submittingFrm}
              disabled={isSubmitting || loadingCustomField || submittingFrm}
            />
          </div>
        </div>
      </Form>

      {isSelectProjectOpen && (
        <SelectProject
          isSingleSelect={true}
          open={isSelectProjectOpen}
          setOpen={setIsSelectProjectOpen}
          selectedProjects={selectedProject}
          onProjectSelected={(data) => {
            // onChangeProject(data);
            setSelectedProject(data);
            if (data?.length) {
              setFieldValue("project_id", data?.[0]?.id);
            } else {
              setFieldValue("project_id", null);
            }
          }}
          // customer_id={selectedCustomer?.[0]?.user_id?.toString()}
          isRequired={false}
          module_key={module_key}
          // module_key={
          //   Number(gProjectModules?.[module_key as keyof IProjectModules]) !== 1
          // }
          genericProjects="project"
          // isShowProjectType={true}
          // addNewProjectProps={{
          //   onNewProjectAdd: (insertId) => {
          //     fetchDefaultProject(insertId);
          //   },
          // }}
        />
      )}

      {isOpenContactDetailsForEmployee && (
        <ContactDetailsModal
          isOpenContact={isOpenContactDetailsForEmployee}
          contactId={contactId}
          onCloseModal={() => {
            setIsOpenContactDetailsForEmployee(false);
          }}
          additional_contact_id={additionalContact}
        />
      )}
      {isOpenContactDetailsForSupplier && (
        <ContactDetailsModal
          isOpenContact={isOpenContactDetailsForSupplier}
          contactId={suppliercontactId}
          onCloseModal={() => {
            setIsOpenContactDetailsForSupplier(false);
          }}
          additional_contact_id={supplierAdditionContact}
        />
      )}
      {isSelectEmployeeOpen && (
        <SelectCustomerDrawer
          closeDrawer={() => {
            setIsSelectEmployeeOpen(false);
          }}
          singleSelecte={true}
          openSelectCustomerSidebar={isSelectEmployeeOpen}
          options={[
            // "my_project",
            CFConfig?.employee_key,
            // CFConfig?.contractor_key,
            ...(values?.project_id && values?.project_id != 0
              ? ["my_project" as CustomerEmailTab]
              : []),
          ]}
          setCustomer={(data) => {
            setSelectedEmployee(data);
            if (data?.length) {
              setFieldValue("order_from", data?.[0]?.user_id);
            } else {
              setFieldValue("order_from", null);
            }
          }}
          selectedCustomer={selectedEmployee}
          groupCheckBox={false}
          projectId={Number(selectedProject?.[0]?.id || 0)}
          additionalContactDetails={0}
          // app_access={true}
          // canWrite={false}
        />
      )}
      {isOpenSelectSupplierTo && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectSupplierTo}
          closeDrawer={() => {
            setIsOpenSelectSupplierTo(false);
            setIsMultipleSupplier(-1);
          }}
          // singleSelecte={purchaseOrderDetail?.is_multiple_suppliers == 0}
          singleSelecte={
            !!(isMultipleSupplier == -1
              ? values?.is_multiple_suppliers == 0
              : isMultipleSupplier == 0)
          }
          setCustomer={(data) => {
            if (data?.length != 0) {
              // setSelectedSupplier(data);
              handleSupplierTo(data);
              // setFieldValue("supplier_id", data?.[0]?.user_id || 0);
              // setFieldValue("supplier_contact_id", data?.[0]?.contact_id || 0);
              //// setFieldValue("is_multiple_suppliers", 0);
            } else {
              setSelectedSupplier([]);
              setFieldValue("supplier_id", null);
              setFieldValue("po_suppliers", null);
              // setFieldValue("is_multiple_suppliers", null);
              setFieldValue("supplier_contact_id", null);
            }
          }}
          activeTab={CFConfig?.vendor_key}
          options={[
            CFConfig?.vendor_key,
            CFConfig?.contractor_key,
            CFConfig?.misc_contact_key,
            "by_service",
            ...(values?.project_id && values?.project_id != 0
              ? ["my_project" as CustomerEmailTab]
              : []),
          ]}
          selectedCustomer={
            isMultipleSupplier == -1 &&
            selectedSupplier &&
            selectedSupplier?.length !== 0
              ? selectedSupplier
              : []
          }
          groupCheckBox={true}
          projectId={Number(selectedProject?.[0]?.id || 0)}
          // additionalContactDetails={0}
        />
      )}
    </Drawer>
  );
};

export default AddPurchaseOrders;
