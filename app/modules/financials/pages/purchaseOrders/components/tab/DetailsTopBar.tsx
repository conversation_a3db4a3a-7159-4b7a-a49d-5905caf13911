// Hooks
import { useTranslation } from "~/hook";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Dropdown } from "~/shared/components/atoms/dropDown";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { IconButton } from "~/shared/components/molecules/iconButton";
import { InputField } from "~/shared/components/molecules/inputField";
import { FieldStatus } from "~/shared/components/molecules/fieldStatus";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { TopBarSkeleton } from "~/shared/components/molecules/topBarSkeleton";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";
// organisms
import { SelectProject } from "~/shared/components/organisms/selectProject";
// Other
import { PODetailsFields } from "../../utils/constants";
import { useEffect, useMemo, useState } from "react";
import {
  getGConfig,
  getGSettings,
  setCommonSidebarCollapse,
  useGModules,
} from "~/zustand"; // In future this code move in redux, developer change this code
import POStatusbar from "../POStatusbar";
import { toBoolean } from "../../../estimates/utils/common";
import { getStatusForField } from "~/shared/utils/helper/common";
import { sanitizeString } from "~/helpers/helper";
import { MenuProps } from "antd";
import isEmpty from "lodash/isEmpty";
import { useAppPODispatch, useAppPOSelector } from "../../redux/store";
import { useParams } from "@remix-run/react";
import POListAction from "../dashboard/POListAction";
import { resetPOItem } from "../../redux/slices/poItemsSlice";
import dayjs from "dayjs";
import { HtmlDecode } from "../../utils/function";

const DetailsTopBar = ({
  sidebarCollapse,
  handleStepClick,
  activeStep,
  setActiveStep,
  purchaseStatList,
  purchaseStatusVal,
  onReloadPODetails,
  isLoading,
  handleChangeFieldStatus = () => {},
  handleUpdateField = (
    data: IPODetailData = {},
    extraData?: IPODetailData
  ) => {},
  // isLoadingStatus,
  loadingStatus,
  // selectStatusKey,
  selectedStatusKey,
  isReadOnly,
  isStatusLost,
  selectedStatusInd,
}: // isStatusLost,
IPODetailsTopBarProps) => {
  const { _t } = useTranslation();
  const gConfig: GConfig = getGConfig();
  const { module_key }: GConfig = getGConfig();
  const { checkModuleAccessByKey } = useGModules();
  const dispatch = useAppPODispatch();
  // const params: RouteParams = useParams();
  const { id: purchase_order_id } = useParams();
  const { purchaseOrderDetail, isPODetailLoading } = useAppPOSelector(
    (state) => state?.purchaseOrderDetail
  );
  const {
    is_custom_purchase_order_id,
    quickbook_sync,
    quickbook_desktop_sync,
    date_format,
  }: GSettings = getGSettings();

  const [isFocused, setIsFocused] = useState(false);
  const [isSelectProjectOpen, setIsSelectProjectOpen] =
    useState<boolean>(false);
  const [selectedProject, setSelectedProject] = useState<IProject[]>([]);
  const [inputValues, setInputValues] =
    useState<IPODetailData>(PODetailsFields);
  // const [poValues, setPOValues] = useState<string | number>(
  //   purchaseOrderDetail?.custom_purchase_order_id?.toString() ||
  //     // inputValues?.company_estimate_id?.toString() ||
  //     ""
  // );

  // const [clicked, setClicked] = useState<boolean>(false);
  // const { loadingStatus, handleChangeFieldStatus, setLoadingStatus } =
  //   useFieldStatus(POfieldStatus);
  useEffect(() => {
    if (purchaseOrderDetail) {
      setInputValues(purchaseOrderDetail);
      // setPOValues(
      //   HTMLEntities.decode(
      //     sanitizeString(
      //       isFocused
      //         ? purchaseOrderDetail?.custom_purchase_order_id?.toString()
      //         : `${purchaseOrderDetail?.custom_purchase_order_id?.toString()}`
      //     )
      //   )
      // );
    }
  }, [purchaseOrderDetail]);
  const status = useMemo(() => {
    const statusList = purchaseStatList?.map((item: EStatusList) => {
      const poLabel = `${item?.label}${
        item?.does_sync_qb &&
        (toBoolean(quickbook_sync) || toBoolean(quickbook_desktop_sync))
          ? " (Post To QuickBooks)"
          : ""
      }`;
      return {
        label: HtmlDecode(poLabel) || "",
        key: item?.value?.toString() ?? "",
        show_in_progress_bar: item?.show_in_progress_bar,
        sort_order: item?.value,
        does_sync_qb: item?.does_sync_qb,
        icon: (
          <FontAwesomeIcon
            icon="fa-solid fa-square"
            className="h-3.5 w-3.5"
            style={{
              color: item?.default_color,
            }}
          />
        ),
      };
    });
    const getSelectStatus = purchaseStatList?.find(
      (item) => item?.value?.toString() == activeStep?.toString()
    );
    const selectStatus = (
      <Tooltip title={HtmlDecode(getSelectStatus?.label)}>
        <div
          className={`py-0.5 rounded flex items-center justify-center w-full status-dropdown-block group/status-dropdown px-2.5 ${
            isReadOnly ? "" : "hover:px-1 cursor-pointer"
          }`}
          style={{
            backgroundColor: getSelectStatus?.default_color + "1d",
          }}
        >
          <Typography
            style={{
              color: getSelectStatus?.default_color,
            }}
            className="text-xs whitespace-nowrap truncate"
          >
            {HtmlDecode(getSelectStatus?.label || "Select Status")}
          </Typography>
          {!isReadOnly && (
            <FontAwesomeIcon
              className="w-2.5 h-2.5 pl-0.5 group-hover/status-dropdown:flex hidden ease-in-out duration-300"
              style={{
                color: getSelectStatus?.default_color,
              }}
              icon="fa-regular fa-chevron-down"
            />
          )}
        </div>
      </Tooltip>
    );
    return { statusList, selectStatus, getSelectStatus };
  }, [purchaseStatList, activeStep, purchaseOrderDetail?.billing_status_key]);
  const POID = useMemo(
    () =>
      !inputValues?.custom_purchase_order_id?.toString() ||
      inputValues?.custom_purchase_order_id?.length < 1
        ? purchaseOrderDetail?.company_purchase_order_id?.toString()
        : inputValues?.custom_purchase_order_id?.toString() || "",
    [
      inputValues?.custom_purchase_order_id,
      purchaseOrderDetail?.company_purchase_order_id,
    ]
  );
  const poValue = (() => {
    if (purchaseOrderDetail?.need_prefix_project) {
      const prefixedId = purchaseOrderDetail?.projectPrefix;
      if (prefixedId) {
        return `${prefixedId}${POID}`;
      }
      return POID;
    } else {
      return POID;
    }
  })();

  const handleStatus: MenuProps["onClick"] = (e) => {
    // 09-July-2025: PO > New > Do not make the Supplier a required field unless the user is trying to change the status to Approved > Submitted > Received > Closed.  This change is necessary for another change that will allow the user submit pricing request from multiple suppliers.
    // https://app.clickup.com/t/86czjvb53 > Remove Required Project and Supplier fields in PO's
    if (
      ["po_approved", "po_submitted", "po_received", "po_paid"]?.includes(
        e?.key
      ) &&
      (purchaseOrderDetail?.supplier_details?.length <= 0 ||
        !purchaseOrderDetail?.supplier_id ||
        purchaseOrderDetail?.supplier_id == "" ||
        purchaseOrderDetail?.supplier_id == 0)
    ) {
      return notification.error({
        description: _t("Missing Supplier."),
      });
    }
    const sort_Value = purchaseStatList?.find((item) => item?.key === e?.key);
    // setSelectedStatusKey(sort_Value?.key);
    // setActiveStep(Number(sort_Value?.sort_order));
    // if (sort_Value?.label && !clicked) {
    //   setClicked(true);
    //   return handleStatusChange(sort_Value?.label);
    // }
    const extraPaylaod: { delivery_date?: string; po_order_date?: string } = {};
    if (e?.key == "po_received" && !!!purchaseOrderDetail?.delivery_date) {
      extraPaylaod["delivery_date"] = dayjs()?.format(date_format);
    }
    if (e?.key == "po_submitted" && !!!purchaseOrderDetail?.po_order_date) {
      extraPaylaod["po_order_date"] = dayjs()?.format(date_format);
    }
    if (inputValues?.billing_status_key !== e?.key)
      handleUpdateField(
        {
          // approval_type: e?.key,
          // approval_type_name: sort_Value?.label,
          billing_status: Number(sort_Value?.item_id),
          billing_status_key: e?.key,
          billing_status_name: sort_Value?.label
            ? sort_Value?.label
            : "Select Status",
          is_multiple_suppliers: purchaseOrderDetail?.is_multiple_suppliers,
          supplier_id: purchaseOrderDetail?.supplier_id || 0,
          supplier_contact_id: purchaseOrderDetail?.supplier_contact_id || 0,
          po_suppliers: purchaseOrderDetail?.po_suppliers,

          // customer_id: inputValues?.customer_id,
        },
        extraPaylaod
      );
  };
  // const isStatusNotShow = useMemo(() => {
  //   if (status?.statusList)
  //     return status?.statusList?.find((el) => el.key === selectedStatusKey)
  //       ?.show_in_progress_bar;
  // }, [selectedStatusKey, status?.statusList]);

  const onChangeProject = async (projects: IProject[]) => {
    const projectDetail = projects?.[0] ?? {};
    handleChangeFieldStatus({
      field: "project_id",
      status: "loading",
      action: "API",
    });

    handleUpdateField({
      project_id: Number(projectDetail?.id ?? 0),
      pro_id: Number(projectDetail?.id ?? 0),
    });
    if (
      projectDetail &&
      projectDetail?.id &&
      projectDetail?.id != 0 &&
      projectDetail?.project_name &&
      projectDetail?.project_name != ""
    ) {
      setSelectedProject([
        {
          id: Number(projectDetail?.id),
          project_name: projectDetail?.project_name,
          prj_record_type: projectDetail?.prj_record_type,
        },
      ]);
      setInputValues({
        ...inputValues,
        pro_id: Number(projectDetail?.id) ?? 0,
        project_name: projectDetail?.project_name ?? "",
        project_type: projectDetail?.prj_record_type ?? "",
      });
      dispatch(resetPOItem());
    } else {
      setSelectedProject([]);
      setIsSelectProjectOpen(false);
      setInputValues({
        ...inputValues,
        pro_id: 0,
        project_name: "",
      });
    }
  };
  useEffect(() => {
    if (
      !!inputValues?.pro_id &&
      inputValues?.pro_id != 0 &&
      !!inputValues?.project_name &&
      inputValues?.project_name != ""
    ) {
      setSelectedProject([
        {
          id: Number(inputValues?.pro_id),
          project_name: inputValues?.project_name,
          prj_record_type: inputValues?.project_type,
        },
      ]);
    } else {
      setSelectedProject([]);
    }
  }, [purchaseOrderDetail, inputValues]);

  const handleInpOnChange = ({
    target: { value, name },
  }: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setInputValues({ ...inputValues, [name]: value });
  };

  return (
    <>
      <div className="sticky top-0 z-[99] bg-[#f8f8f8] p-[15px] pb-0 mb-[15px]">
        <div className="flex items-center bg-white dark:bg-dark-800 py-[5px] px-3.5 shadow-[0_4px_24px_0] shadow-[#22292f1a] rounded-md">
          <div className="w-full flex md:flex-row flex-col-reverse md:items-center justify-between sm:gap-2 gap-1.5">
            {isPODetailLoading ? (
              <TopBarSkeleton statusList={true} num={5} />
            ) : (
              <>
                <div className="flex items-center xl:flex-[1_0_0%] xl:w-[calc(25%-75px)] md:max-w-[calc(100%-125px)] w-full">
                  <div
                    className={`w-11 h-11 flex items-center justify-center rounded-full relative before:absolute before:w-[39px] before:h-[39px] before:top-1/2 before:left-1/2 before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full before:border-2 before:border-white`}
                    style={{
                      backgroundColor: status?.getSelectStatus?.default_color,
                    }}
                  >
                    <FontAwesomeIcon
                      className={`w-[18px] h-[18px] text-white`}
                      // icon="fa-light fa-clipboard-check"
                      icon={status?.getSelectStatus?.icon || ""}
                    />
                  </div>
                  <div
                    className={`flex flex-col gap-0.5 w-[calc(100%-44px)] 2xl:pr-3.5 ${
                      isReadOnly ? "pl-2" : "pl-2.5"
                    }`}
                  >
                    <Tooltip
                      title={`Subject: ${HtmlDecode(inputValues?.subject)}`}
                      placement="topLeft"
                    >
                      <div
                        className={`max-w-full ${
                          gConfig?.module_read_only
                            ? "w-fit"
                            : "xl:w-full sm:w-1/2 w-full"
                        }`}
                      >
                        <InputField
                          placeholder={_t("Subject")}
                          labelPlacement="left"
                          formInputClassName="ellipsis-input-field"
                          className="h-6 !text-base font-medium py-0"
                          readOnlyClassName="h-6 !text-base font-medium whitespace-nowrap truncate sm:block flex"
                          inputStatusClassName="!w-[15px] !h-[15px]"
                          editInline={true}
                          iconView={true}
                          readOnly={isReadOnly}
                          disabled={
                            getStatusForField(
                              loadingStatus ?? [],
                              "subject"
                            ) === "loading" || isReadOnly
                          }
                          fixStatus={getStatusForField(
                            loadingStatus ?? [],
                            "subject"
                          )}
                          name="subject"
                          value={HtmlDecode(inputValues?.subject || "")}
                          maxLength={200}
                          onChange={handleInpOnChange}
                          onMouseEnter={() => {
                            handleChangeFieldStatus({
                              field: "subject",
                              status: "edit",
                              action: "ME",
                            });
                          }}
                          onMouseLeaveDiv={() => {
                            handleChangeFieldStatus({
                              field: "subject",
                              status: "button",
                              action: "ML",
                            });
                          }}
                          onFocus={() =>
                            handleChangeFieldStatus({
                              field: "subject",
                              status: "save",
                              action: "FOCUS",
                            })
                          }
                          onBlur={(e) => {
                            setIsFocused(false);
                            const value = e?.target?.value
                              ?.replace(/\s+/g, " ")
                              ?.trim();
                            if (value === "") {
                              handleChangeFieldStatus({
                                field: "subject",
                                status: "error",
                                action: "BLUR",
                              });
                              notification.error({
                                description: _t("Subject field is required."),
                              });
                            }
                            if (!isEmpty(value)) {
                              if (value !== purchaseOrderDetail?.subject) {
                                handleUpdateField({
                                  subject: `${HTMLEntities.encode(value)}`,
                                  // customer_id: inputValues?.customer_id,
                                });
                              } else {
                                handleChangeFieldStatus({
                                  field: "subject",
                                  status: "button",
                                  action: "BLUR",
                                });
                                setInputValues({
                                  ...inputValues,
                                  subject: inputValues?.subject?.trim(),
                                });
                              }
                            } else {
                              handleChangeFieldStatus({
                                field: "subject",
                                status: "button",
                                action: "BLUR",
                              });
                              setInputValues({
                                ...inputValues,
                                subject: purchaseOrderDetail?.subject
                                  ?.replace(/\s+/g, " ")
                                  ?.trim(),
                              });
                            }
                          }}
                        />
                      </div>
                    </Tooltip>
                    {/* Project Fields */}
                    <ButtonField
                      label=""
                      labelProps={{
                        labelClass: "!hidden",
                      }}
                      name="project_id"
                      labelPlacement="left"
                      editInline={true}
                      iconView={true}
                      placeholder={_t("Select Project")}
                      required={false}
                      readOnly={isReadOnly}
                      mainReadOnlyClassName="sm:w-fit max-w-full"
                      className="h-[22px] py-0 w-full gap-0"
                      readOnlyClassName="text-base h-[22px] whitespace-nowrap truncate sm:block flex"
                      inputClassName="w-fit"
                      fieldClassName="w-auto"
                      spanWidthClass="w-fit"
                      statusProps={{
                        status: getStatusForField(
                          loadingStatus ?? [],
                          "project_id"
                        ),
                        className: "right-6",
                        iconProps: {
                          className: "!w-4 !h-4",
                        },
                      }}
                      value={
                        !!inputValues?.project_name && !!inputValues?.pro_id
                          ? HtmlDecode(inputValues?.project_name || "")
                          : ""
                      }
                      headerTooltip={`Project: ${
                        !!inputValues?.project_name && !!inputValues?.pro_id
                          ? HTMLEntities.decode(
                              sanitizeString(inputValues?.project_name || "")
                            )
                          : ""
                      }`}
                      onClick={() => setIsSelectProjectOpen(true)}
                      disabled={
                        getStatusForField(loadingStatus ?? [], "project_id") ===
                          "loading" || isReadOnly
                      }
                      // readOnly={isReadOnly}
                      onBlur={() => {
                        handleChangeFieldStatus({
                          field: "project_id",
                          status: "button",
                          action: "BLUR",
                        });
                      }}
                      rightIcon={
                        inputValues?.pro_id && inputValues?.pro_id != 0 ? (
                          <ProjectFieldRedirectionIcon
                            projectId={inputValues?.pro_id?.toString() || ""}
                            onClick={(e) => {
                              e?.stopPropagation();
                            }}
                          />
                        ) : (
                          <></>
                        )
                      }
                    />
                    <div
                      className={`flex items-center gap-2 ${
                        isReadOnly ? "pl-1.5" : ""
                      }`}
                    >
                      <div className="flex gap-2 items-center">
                        <Dropdown
                          menu={{
                            items: status?.statusList,
                            selectedKeys: [selectedStatusKey ?? ""],
                            selectable: true,
                            defaultSelectedKeys: [activeStep as string],
                            onClick: handleStatus, // change this in future ( temporary resolve type issue ),
                          }}
                          disabled={
                            isReadOnly ||
                            getStatusForField(
                              loadingStatus ?? [],
                              "billing_status"
                            ) == "loading"
                          }
                          trigger={["click"]}
                          overlayClassName="dropdown-color-option-block !min-w-40"
                        >
                          {status?.selectStatus}
                        </Dropdown>
                        {["loading", "success", "error"].includes(
                          getStatusForField(
                            loadingStatus ?? [],
                            "billing_status"
                          )
                        ) && (
                          <FieldStatus
                            className="flex items-center"
                            iconProps={{
                              className: "!w-[15px] !h-[15px]",
                            }}
                            status={getStatusForField(
                              loadingStatus ?? [],
                              "billing_status"
                            )}
                          />
                        )}
                      </div>
                      <Tooltip
                        title={
                          isFocused
                            ? HtmlDecode(
                                inputValues?.custom_purchase_order_id?.toString()
                              ) || ""
                            : HtmlDecode(`${_t("PO #")}${poValue}`)
                        }
                        placement="topLeft"
                      >
                        <div
                          className={`overflow-hidden ${
                            isReadOnly ? "w-fit" : "w-full max-w-[230px]"
                          }`}
                        >
                          <InputField
                            placeholder={_t("PO") + " #"}
                            labelPlacement="left"
                            formInputClassName="ellipsis-input-field"
                            className="h-[22px] !text-sm font-medium py-0"
                            readOnlyClassName="text-sm h-[22px] font-medium whitespace-nowrap truncate sm:block flex"
                            inputStatusClassName="!w-[15px] !h-[15px]"
                            name="custom_purchase_order_id"
                            id="custom_purchase_order_id"
                            maxLength={21}
                            readOnly={isReadOnly}
                            disabled={
                              isReadOnly
                                ? true
                                : is_custom_purchase_order_id !== 0
                                ? false
                                : true
                            }
                            iconClassName="!w-3 !h-3"
                            value={
                              isFocused
                                ? HtmlDecode(
                                    inputValues?.custom_purchase_order_id?.toString()
                                  ) || ""
                                : HtmlDecode(`${_t("PO #")}${poValue}`)
                            }
                            editInline={is_custom_purchase_order_id != 0}
                            iconView={true}
                            fixStatus={getStatusForField(
                              loadingStatus ?? [],
                              "custom_purchase_orders_id"
                            )}
                            onChange={handleInpOnChange}
                            onMouseEnter={() => {
                              handleChangeFieldStatus({
                                field: "custom_purchase_orders_id",
                                status: "edit",
                                action: "ME",
                              });
                            }}
                            onMouseLeaveDiv={() => {
                              handleChangeFieldStatus({
                                field: "custom_purchase_orders_id",
                                status: "button",
                                action: "ML",
                              });
                            }}
                            onFocus={() => {
                              setIsFocused(true);
                              handleChangeFieldStatus({
                                field: "custom_purchase_orders_id",
                                status: "save",
                                action: "FOCUS",
                              });
                              // setPOValues(
                              //   HTMLEntities.decode(
                              //     sanitizeString(
                              //       purchaseOrderDetail?.custom_purchase_order_id?.toString()
                              //     )
                              //   )
                              // );
                              // if (inputValues?.custom_purchase_order_id) {
                              //   setInputValues((prev) => {
                              //     return {
                              //       ...prev,
                              //       custom_purchase_order_id:
                              //         prev?.custom_purchase_order_id?.toString(),
                              //     };
                              //   });
                              // }
                            }}
                            onBlur={(e) => {
                              setIsFocused(false);
                              const value = e?.target?.value.trim();
                              // let newValue = "";
                              // if (value?.includes("PO. #")) {
                              //   newValue = value?.replace("PO. #", "");
                              // }
                              // if (
                              //   value?.includes(
                              //     purchaseOrderDetail?.need_prefix_project || ""
                              //   )
                              // ) {
                              //   newValue = value?.replace(
                              //     purchaseOrderDetail?.need_prefix_project ||
                              //       "",
                              //     ""
                              //   );
                              // }
                              // const newValue = value?.includes("PO. #")
                              //   ? value?.replace("PO. #", "")
                              //   : value;

                              // Handle saving the updated value
                              if (value === "") {
                                notification.error({
                                  description: _t("PO # field is required."),
                                });
                                // handleUpdateField({
                                //   custom_purchase_order_id: estValue,
                                //   customer_id: purchaseOrderDetail?.customer_id,
                                // });
                              }
                              if (
                                value !==
                                  purchaseOrderDetail?.custom_purchase_order_id &&
                                value !== ""
                              ) {
                                handleUpdateField({
                                  custom_purchase_orders_id: value,
                                  custom_purchase_order_id: value,
                                  // customer_id: purchaseOrderDetail?.customer_id,
                                });
                                setInputValues({
                                  ...inputValues,
                                  custom_purchase_order_id: value,
                                });
                              } else {
                                handleChangeFieldStatus({
                                  field: "custom_purchase_orders_id",
                                  status: "button",
                                  action: "BLUR",
                                });
                                setInputValues({
                                  ...inputValues,
                                  custom_purchase_order_id:
                                    purchaseOrderDetail?.custom_purchase_order_id,
                                });
                                // setPOValues(
                                //   HTMLEntities.decode(
                                //     sanitizeString(
                                //       !isFocused
                                //         ? purchaseOrderDetail?.custom_purchase_order_id?.toString()
                                //         : `${purchaseOrderDetail?.custom_purchase_order_id?.toString()}`
                                //     )
                                //   )
                                // );
                              }
                            }}
                          />
                        </div>
                      </Tooltip>
                    </div>
                  </div>
                </div>
                {/* {["po_canceled","po_declined"]?.includes(purchaseStatusVal?.toString()) ? */}
                <div className="w-auto flex-[0_0_auto]">
                  <ul className="items-center justify-center 2xl:-ml-3.5 w-[calc(100%-0px)] xl:flex hidden">
                    {!isPODetailLoading &&
                      !["po_declined", "po_canceled"]?.includes(
                        purchaseStatusVal?.key ?? ""
                      ) && (
                        <POStatusbar
                          handleUpdateField={handleUpdateField}
                          handleChangeFieldStatus={handleChangeFieldStatus}
                          purchaseStatList={purchaseStatList}
                          isReadOnly={isReadOnly ?? false}
                          activeStep={activeStep}
                          setActiveStep={setActiveStep}
                          selectedStatusInd={selectedStatusInd}
                        />
                      )}
                    {/* {PO_STATUS_LIST?.map((item: IStatusList, index: number) => {
                  const isActive = Number(activeStep) >= Number(item.value);
                  return (
                  <li
                    key={index}
                    className={`relative 2xl:min-w-[125px] xl:min-w-24 lg:w-24 w-20 grid justify-end first:before:hidden before:absolute before:h-[2px] before:!w-[calc(100%-54px)]  ${
                      isActive
                        ? "before:bg-primary-900"
                        : "before:bg-[#ACAEAF]"
                    } before:top-[30%] lg:before:left-[-22px] before:left-[-13px]`}
                  >
                    <ProgressBarHeader option={item} />
                  </li>);
                })} */}
                  </ul>
                </div>
                {/* : <></>} */}
                <div className="xl:flex-[1_0_0%] xl:w-[calc(25%-75px)] md:w-fit w-full">
                  <div className="flex justify-between">
                    <div className="flex gap-2.5">
                      {!window.ENV.PAGE_IS_IFRAME && (
                        <div
                          className="flex items-center cursor-pointer md:!hidden"
                          onClick={() => {}}
                        >
                          <IconButton
                            htmlType="button"
                            variant="default"
                            className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                            iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                            icon="fa-regular fa-chevron-left"
                          />
                        </div>
                      )}
                      <div>
                        <IconButton
                          htmlType="button"
                          variant="default"
                          className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                          iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                          icon="fa-regular fa-bars"
                          onClick={() =>
                            setCommonSidebarCollapse(!sidebarCollapse)
                          }
                        />
                      </div>
                    </div>
                    <ul className="flex items-center justify-end gap-2.5">
                      <li>
                        <ButtonWithTooltip
                          tooltipTitle={_t("Refresh")}
                          tooltipPlacement="top"
                          icon="fa-regular fa-arrow-rotate-right"
                          // iconClassName="text-primary-900 group-hover/buttonHover:text-deep-orange-500"
                          iconClassName={`!text-primary-900 ${
                            isPODetailLoading && "fa-spin"
                          } ${
                            isPODetailLoading
                              ? "group-hover/buttonHover:!text-primary-900"
                              : "group-hover/buttonHover:!text-deep-orange-500"
                          }`}
                          className={`!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !shadow-primary-200 ${
                            isPODetailLoading
                              ? "hover:bg-transparent"
                              : "hover:!bg-deep-orange-500/5"
                          }`}
                          // className="!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                          disabled={isPODetailLoading}
                          onClick={() => {
                            onReloadPODetails();
                          }}
                        />
                      </li>
                      {/* <li> */}
                      {!isReadOnly && (
                        <li>
                          <POListAction
                            tooltipcontent={_t("More")}
                            paramsData={purchaseOrderDetail}
                            pId={purchase_order_id}
                            buttonClass={`!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !shadow-primary-200 ${"hover:bg-transparent hover:!bg-deep-orange-500/5"}`}
                            refreshAgGrid={onReloadPODetails}
                            iconClassName={`text-primary-900 ${"group-hover/buttonHover:text-deep-orange-500"}`}
                            // pId={purchaseOrderDetail?.purchase_order_id}
                            isBilled={purchaseOrderDetail?.is_billed}
                            POID={poValue}
                            isdetail={true}
                          />
                        </li>
                      )}
                      {/* <DropdownMenu
                          options={PO_OPTIONS}
                          buttonClass="!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !rounded !shadow-primary-200 hover:!bg-deep-orange-500/5"
                          iconClassName="text-primary-900 group-hover/buttonHover:text-deep-orange-500"
                          icon="fa-regular fa-ellipsis-vertical"
                          tooltipcontent={_t("More")}
                          footerText={_t(
                            "Some actions might be unavailable depending on your privilege."
                          )}
                        /> */}
                      {/* </li> */}
                    </ul>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {isSelectProjectOpen && (
        <SelectProject
          isSingleSelect={true}
          open={isSelectProjectOpen}
          setOpen={setIsSelectProjectOpen}
          selectedProjects={selectedProject}
          onProjectSelected={(data: IProject[]) => {
            // if (data?.length) {
            onChangeProject(data);
            // } else {
            //   notification.error({
            //     description: "Project field is required.",
            //   });
            // }
          }}
          // customer_id={purchaseOrderDetail?.supplier_id?.toString()}
          isRequired={false}
          module_key={gConfig?.module_key}
          // genericProjects="project"
          // isShowProjectType={true}
          // addNewProjectProps={{
          //   onNewProjectAdd: (insertId) => {
          //     fetchDefaultProject(insertId);
          //   },
          // }}
        />
      )}
    </>
  );
};

export default DetailsTopBar;
