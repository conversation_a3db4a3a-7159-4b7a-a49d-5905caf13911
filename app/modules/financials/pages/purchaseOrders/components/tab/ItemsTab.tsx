// molecules
import { CidbItemDrawer } from "~/shared/components/molecules/cidbItemDrawer";
// Other
import { useMemo, useState } from "react";
import CalcDetailsCard from "./items/CalcDetailsCard";
// import SupplierProvidedPricing from "./items/SupplierProvidedPricing";
import { getGConfig, getGSettings, useGModules } from "~/zustand";
import { useAppPODispatch, useAppPOSelector } from "../../redux/store";
import {
  addPOItems,
  deletePOItems,
  getPOItems,
} from "../../redux/action/POItemAction";
// import { addItems, removeItems } from "../../redux/slices/poItemsSlice";
import PurchaseOrderAddItem from "./items/PurchaseOrderAddItem";
import PurchaseOrderItemList from "./items/PurchaseOrderItemList";
// import { PurchaseOrderItem } from "./sidebar";

const ItemsTab = ({
  isReadOnly: ParentIsReadOnly,
  POMatrix,
  isTaxEnabled = false,
}: POReadOnlyComponentwithPOMatrix) => {
  const gSettings: GSettings = getGSettings();
  const {
    default_equipment_markup_percent,
    default_labor_markup_percent,
    default_material_markup_percent,
    default_other_item_markup_percent,
    default_sub_contractor_markup_percent,
    has_project_based_cost_codes,
  } = gSettings;

  const dispatch = useAppPODispatch();

  const {
    purchaseOrderSectionItems,
    isPOItemsLoading,
    purchaseOrderItems = [],
  } = useAppPOSelector((state) => state?.purchaseOrderItems);
  const { purchaseOrderDetail } = useAppPOSelector(
    (state) => state?.purchaseOrderDetail
  );
  const itemTypes = useAppPOSelector(
    (state) => state?.purchaseOrderItemTypes.itemTypes
  );
  // const { poPricingDetail = [], isPOPricingLoading } = useAppPOSelector(
  //   (state) => state.purchaseOrderPricingDetail
  // );

  const [isCostDbItemOpen, setIsCostDbItemOpen] = useState<boolean>(false);
  const [purchaseOrderItem, setPurchaseOrderItem] = useState<boolean>(false);
  const [isItemAdd, setIsItemAdd] = useState<boolean>(false);
  const [itemsData, setItemsData] = useState<Partial<IPOItemData>>({});
  const { checkModuleAccessByKey } = useGModules();
  const gConfig: GConfig = getGConfig();
  const isReadOnly = useMemo(
    () =>
      Boolean(
        checkModuleAccessByKey(gConfig?.module_key) === "read_only" ||
          ReadOnlyModuleStatsuKey?.[
            `${purchaseOrderDetail?.billing_status_key}`
          ] ||
          ParentIsReadOnly ||
          !purchaseOrderDetail
      ),
    [
      gConfig?.module_key,
      purchaseOrderDetail?.billing_status_key,
      ParentIsReadOnly,
    ]
  );
  // const isTaxEnabled = useMemo(
  //   () =>
  //     nonUnitedStateCountries.includes(qb_country?.toUpperCase()) ||
  //     quickbook_sync == "0",
  //   [qb_country, quickbook_sync]
  // );

  const handleItemFromCostItemDatabase = async (data: CIDBItemSideData[]) => {
    const removedItemIds = (purchaseOrderSectionItems?.lumpsum || [])
      .filter((purchaseOrderItem) => {
        if (
          purchaseOrderItem.reference_item_id &&
          (purchaseOrderItem?.reference_item_id as number) > 0
        ) {
          return !data.some((item) => {
            if (item?.type_name === "Material") {
              return item.material_id === purchaseOrderItem.reference_item_id;
            } else if (item?.type_name === "Labor") {
              return (
                Number(purchaseOrderItem.reference_item_id) ===
                Number(item.labor_id)
              );
            } else if (item?.type_name === "Subcontractor") {
              return (
                Number(purchaseOrderItem.reference_item_id) ===
                Number(item.contractor_id)
              );
            } else if (item?.type_name === "Equipment") {
              return (
                Number(purchaseOrderItem.reference_item_id) ===
                Number(item.equipment_id)
              );
            } else if (item?.type_name === "Other Items") {
              return (
                Number(purchaseOrderItem.reference_item_id) ===
                Number(item.other_item_id)
              );
            } else if (item?.type_name === "Groups") {
              return (
                Number(purchaseOrderItem.reference_item_id) ===
                Number(item.item_id)
              );
            }
          });
        }
      })
      .map((item) => Number(item.item_id));

    const itemsToBeAdd = data
      .filter(
        (jobItem) =>
          !(purchaseOrderSectionItems?.lumpsum || []).some((item) => {
            if (jobItem?.type_name === "Material") {
              return (
                Number(item.reference_item_id) === Number(jobItem.material_id)
              );
            } else if (jobItem?.type_name === "Labor") {
              return (
                Number(item.reference_item_id) === Number(jobItem.labor_id)
              );
            } else if (jobItem?.type_name === "Subcontractor") {
              return (
                Number(item.reference_item_id) === Number(jobItem.contractor_id)
              );
            } else if (jobItem?.type_name === "Equipment") {
              return (
                Number(item.reference_item_id) === Number(jobItem.equipment_id)
              );
            } else if (jobItem?.type_name === "Other Items") {
              return (
                Number(item.reference_item_id) === Number(jobItem.other_item_id)
              );
            } else if (jobItem?.type_name === "Group") {
              return Number(item.reference_item_id) === Number(jobItem.item_id);
            }
          })
      )
      .map((item) => {
        // Set markup, Prioriy (Project, Default settings, Item markup)
        let newMarkup = item.markup;
        if (item?.type_name === "Material" && default_material_markup_percent) {
          newMarkup = default_material_markup_percent;
        } else if (
          item?.type_name === "Labor" &&
          default_labor_markup_percent
        ) {
          newMarkup = default_labor_markup_percent;
        } else if (
          item?.type_name === "Subcontractor" &&
          default_sub_contractor_markup_percent
        ) {
          newMarkup = default_sub_contractor_markup_percent;
        } else if (
          item?.type_name === "Equipment" &&
          default_equipment_markup_percent
        ) {
          newMarkup = default_equipment_markup_percent;
        } else if (
          item?.type_name === "Other Items" &&
          default_other_item_markup_percent
        ) {
          newMarkup = default_other_item_markup_percent;
        }
        // else if (default_undefined_markup_percent) {
        //   newMarkup = default_undefined_markup_percent;
        // }

        let applyGlobalTax: number;
        if (Number(item.item_type) === 161) {
          applyGlobalTax = gSettings.is_taxable_material_items;
        } else if (Number(item.item_type) === 162) {
          applyGlobalTax = gSettings.is_taxable_equipment_items;
        } else if (Number(item.item_type) === 163) {
          applyGlobalTax = gSettings.is_taxable_labor_items;
        } else if (Number(item.item_type) === 164) {
          applyGlobalTax = gSettings.is_taxable_subcontractor_items;
        } else if (Number(item.item_type) === 165) {
          applyGlobalTax = gSettings.is_taxable_other_items;
        } else {
          applyGlobalTax = 0;
        }

        if (purchaseOrderDetail?.pro_id) {
          const matchedItemType = itemTypes.find(
            (type) => type?.type_id?.toString() == item?.item_type
          );
          if (matchedItemType) {
            newMarkup = matchedItemType.mark_up || newMarkup;
          }
        }

        let temData: IPOAddItem = {
          // add_item_to_database: 1,
          apply_global_tax: applyGlobalTax,
          cost_code_id: Number(item.cost_code_id) || Number(item?.costCodeId),
          description: item?.notes || item?.description || "",
          internal_notes: item?.internal_notes || item?.internalNotes,
          markup: newMarkup,
          quantity: Number(item.quantity) || 0,
          subject: item.name,
          item_type: item.item_type,
          unit: item.unit,
          unit_cost: Number(item.unit_cost),
          total: `${
            (isNaN(Number(item.quantity)) ? 0 : Number(item.quantity)) *
            (isNaN(Number(item.unit_cost)) ? 0 : Number(item.unit_cost))
          }`,
          type_name: item.type_name,
          sku: item.sku,
          is_markup_percentage: 1,
          assigned_to_contact_id: 0,
          // markup_amount: item.markup_amount,
          // variation_id: item.variation_id,
        };

        switch (item?.type_name) {
          case "Material":
            temData.reference_item_id =
              item.material_id !== undefined
                ? Number(item.material_id)
                : undefined;
            break;
          case "Labor":
            temData.reference_item_id =
              item.labor_id !== undefined ? Number(item.labor_id) : undefined;
            break;
          case "Subcontractor":
            temData.reference_item_id =
              item.contractor_id !== undefined
                ? Number(item.contractor_id)
                : undefined;
            break;
          case "Equipment":
            temData.reference_item_id =
              item.equipment_id !== undefined
                ? Number(item.equipment_id)
                : undefined;
            break;
          case "Other Items":
            temData.reference_item_id =
              item.other_item_id !== undefined
                ? Number(item.other_item_id)
                : undefined;
            break;
          case "Group": {
            const refId =
              item.equipment_id ??
              item.material_id ??
              item.labor_id ??
              item.contractor_id ??
              item.other_item_id ??
              item.item_id;
            temData.reference_item_id =
              refId !== undefined ? Number(refId) : undefined;
            break;
          }
          default:
            break;
        }
        return temData;
      });

    if (itemsToBeAdd?.length) {
      const addResponse = await dispatch(
        addPOItems({
          items: itemsToBeAdd as [],
          purchase_order_id: purchaseOrderDetail?.purchase_order_id as number,
        })
      );
      const addAPiRes = addResponse?.payload as IPOAddItemsApiRes;
      if (addAPiRes?.success && addAPiRes?.data?.items?.length) {
        if (removedItemIds.length <= 0) {
          dispatch(
            getPOItems({
              module_id: CFConfig.purchase_order_module_id,
              purchase_order_id:
                purchaseOrderDetail?.purchase_order_id as number,
              need_section: 1,
              project_id: Number(purchaseOrderDetail?.pro_id),
              isHideLoading: true,
            })
          );
        }
        // dispatch(addItems({ itemData: addAPiRes?.data?.items }));
      } else {
        notification.error({
          description: addAPiRes?.message || "Something went wrong!",
        });
        return;
      }
    }

    if (removedItemIds?.length) {
      const deleteRes = await dispatch(
        deletePOItems({
          purchase_order_id: purchaseOrderDetail?.purchase_order_id,
          item_id: removedItemIds.join(","),
        })
      );
      const delAPiRes = deleteRes?.payload as IPOItemsApiRes;
      if (delAPiRes?.success) {
        dispatch(
          getPOItems({
            module_id: CFConfig.purchase_order_module_id,
            purchase_order_id: purchaseOrderDetail?.purchase_order_id as number,
            need_section: 1,
            project_id: Number(purchaseOrderDetail?.pro_id),
            isHideLoading: true,
          })
        );
        // dispatch(removeItems({ itemIds: removedItemIds }));
      } else {
        notification.error({
          description: delAPiRes?.message || "Something went wrong!",
        });
        return;
      }
    }

    setIsCostDbItemOpen(false);
  };

  const costItemsFromDatabase = useMemo(() => {
    const data = purchaseOrderSectionItems?.lumpsum
      ?.filter((item: IPOItemData) => Number(item?.reference_item_id) > 0)
      ?.map((item: IPOItemData) => {
        let temData: Partial<CIDBItemSideData> = {
          name: item.subject || "",
          reference_item_id: item?.reference_item_id,
          item_id: item?.item_id as unknown as string,
        };
        if (item?.item_type_key === "item_material") {
          temData.material_id = item?.reference_item_id;
          temData.item_type = "161";
          temData.type_name = "Material";
        } else if (item?.item_type_key === "item_labour") {
          temData.labor_id = item?.reference_item_id;
          temData.item_type = "163";
          temData.type_name = "Labor";
        } else if (item?.item_type_key === "item_sub_contractor") {
          temData.contractor_id = item?.reference_item_id;
          temData.item_type = "164";
          temData.type_name = "Subcontractor";
        } else if (item?.item_type_key === "item_equipment") {
          temData.equipment_id = item?.reference_item_id;
          temData.item_type = "162";
          temData.type_name = "Equipment";
        } else if (item?.item_type_key === "item_other") {
          temData.other_item_id = item?.reference_item_id;
          temData.item_type = "165";
          temData.type_name = "Other Items";
        }
        return temData as Partial<CIDBItemSideData>;
      });
    return data;
  }, [JSON.stringify(purchaseOrderSectionItems?.lumpsum)]);

  return (
    <div className="grid gap-2.5">
      <div className="py-3 px-[15px] common-card">
        <PurchaseOrderAddItem
          isReadOnly={isReadOnly}
          setIsCostDbItemOpen={setIsCostDbItemOpen}
          POMatrix={POMatrix}
          setPurchaseOrderItem={setPurchaseOrderItem}
          setItemsData={setItemsData}
          setIsItemAdd={setIsItemAdd}
          purchaseOrderItem={purchaseOrderItem}
          itemsData={itemsData}
          isItemAdd={isItemAdd}
          isTaxEnabled={isTaxEnabled}
        />
      </div>
      <PurchaseOrderItemList
        isReadOnly={isReadOnly}
        POMatrix={POMatrix}
        isTaxEnabled={isTaxEnabled}
      />
      {!isPOItemsLoading && isTaxEnabled && purchaseOrderItems?.length > 0 ? (
        <div
          className={`py-3 px-[15px] common-card ${
            isTaxEnabled ? "min-h-[150px]" : "min-h-[50px]"
          } md:w-1/2 w-full ml-auto`}
        >
          <CalcDetailsCard
            isReadOnly={isReadOnly}
            POMatrix={POMatrix}
            isTaxEnabled={isTaxEnabled}
          />
        </div>
      ) : (
        ""
      )}
      {/* {poPricingDetail?.length > 0 &&
      poPricingDetail?.some((item) => item?.pricing_ved_items?.length) ? (
        <div className="py-3 px-[15px] common-card min-h-[150px] overflow-hidden">
          <SupplierProvidedPricing isReadOnly={isReadOnly} />
        </div>
      ) : (
        <></>
      )} */}

      {isCostDbItemOpen && (
        <CidbItemDrawer
          closeDrawer={() => {
            setIsCostDbItemOpen(false);
          }}
          options={[
            "material",
            "labor",
            "equipment",
            "subcontractor",
            "other_items",
            "groups",
          ]}
          singleSelecte={false}
          addItem={(data) => {
            handleItemFromCostItemDatabase(data as CIDBItemSideData[]);
          }}
          openSendEmailSidebar={isCostDbItemOpen}
          data={costItemsFromDatabase as Partial<CIDBItemSideData>[]}
          cidbModuleVIseIdAndValue={{
            [CFConfig.material_key]: {
              id: CFConfig.material_teb_id,
              value: CFConfig.material_key,
            },
            [CFConfig.equipment_key]: {
              id: CFConfig.equipment_teb_id,
              value: CFConfig.equipment_key,
            },
            [CFConfig.labor_key]: {
              id: CFConfig.labor_teb_id,
              value: CFConfig.labor_key,
            },
            [CFConfig.subcontractor_key]: {
              id: CFConfig.subcontractor_teb_id,
              value: CFConfig.subcontractor_key,
            },
            [CFConfig.other_items_key]: {
              id: CFConfig.other_items_teb_id,
              value: CFConfig.other_items_key,
            },
          }}
          //PO & SC
          projectId={
            has_project_based_cost_codes?.toString() === "1"
              ? purchaseOrderDetail?.pro_id
              : undefined
          }
          isHiddenMarkupApply={false}
        />
      )}
    </div>
  );
};

export default ItemsTab;

export const ReadOnlyModuleStatsuKey: Record<string, boolean> = {
  po_approved: true,
  po_paid: true,
};
