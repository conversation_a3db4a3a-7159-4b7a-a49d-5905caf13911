import { useNavi<PERSON>, usePara<PERSON>, useSearchParams } from "@remix-run/react";
import { useEffect, useMemo, useState } from "react";
import { getApiData } from "~/helpers/axios-api-helper";
import { getApiDefaultParams, sanitizeString } from "~/helpers/helper";
import { useIframe, useTranslation } from "~/hook";
import DirSendEmail from "~/modules/people/directory/components/DirSendEmail";
import { sendCommonEmailApi } from "~/redux/action/sendEmailAction";
import { apiRoutes, routes } from "~/route-services/routes";
// molecules
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { PDFFilePreview } from "~/shared/components/molecules/pdfFilePreview";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { ShareInternalLinkModal } from "~/shared/components/molecules/shareInternalLinkModal";
// Other
import { removeFirstSlash } from "~/shared/utils/helper/common";
import { getGConfig } from "~/zustand";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { downloadPurchaseOrderPDF } from "../../redux/action/dashboardAction";
import { copyPODetailApi } from "../../redux/action/PODetailAction";
import { useAppPODispatch, useAppPOSelector } from "../../redux/store";
import { BillingStatuskeytoItemId, PO_OPTIONS } from "../../utils/constants";
import { GenerateBill } from "../tab/sidebar";
import { getDirectaryKeyById } from "~/components/sidebars/multi-select/customer/zustand/action";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { HtmlDecode } from "../../utils/function";
import CustomContextMenu from "~/shared/components/molecules/CustomContextMenu";
import { faArrowUpRightFromSquare } from "@fortawesome/pro-regular-svg-icons";
import { updatePODetail } from "../../redux/slices/poDetailSlice";
import { sendMessageKeys } from "~/components/page/$url/data";
import { toBoolean } from "../../../estimates/utils/common";

const POListAction = ({
  paramsData,
  buttonClass,
  iconClassName,
  tooltipcontent,
  icon = "fa-regular fa-ellipsis-vertical",
  isDashboard = false,
  pId = 0,
  isBilled = 0,
  // disabledIcon = false,
  // fetchEstimateList = () => {},
  refreshAgGrid = () => {},
  POID = "",
  isdetail = false,
}: {
  paramsData?: IPODetailData;
  buttonClass?: string;
  iconClassName?: string;
  tooltipcontent?: string;
  isDashboard?: boolean;
  pId?: string | number;
  // disabledIcon?: boolean;
  icon?: IFontAwesomeIconProps["icon"];
  isBilled?: number | string;
  // fetchEstimateList?: () => void;
  refreshAgGrid?: () => void;
  POID?: string;
  isdetail?: boolean;
}) => {
  const { _t } = useTranslation();
  const { id: purchase_order_id } = useParams();
  const gConfig: GConfig = getGConfig();
  const navigate = useNavigate();
  const dispatch = useAppPODispatch();
  const { parentPostMessage } = useIframe();
  const { getGlobalModuleByKey } = useGlobalModule();
  const moduleBill = getGlobalModuleByKey(CFConfig?.bill_module);
  const billName = HtmlDecode(moduleBill?.module_name) || "Bill";
  const poName = HtmlDecode(gConfig?.module_singular_name) || "Purchase Order";
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const [generateBill, setGenerateBill] = useState<boolean>(false);
  const [isConfirmOpen, setIsConfirmOpen] = useState<boolean>(false);
  //   share PO states
  const [isShareOpen, setIsShareOpen] = useState<boolean>(false);
  const [selectedData, setSelectedData] = useState<Partial<IPODetailData>>();
  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);
  const [shareLink, setShareLink] = useState<string>("");
  //delete PO states
  const [delArchConfirmOpen, setDelArchConfirmOpen] = useState<string>("");
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [isPOPdfViewOpen, setIsPOPdfViewOpen] = useState<boolean>(false);
  const [showPricingRequestButton, setShowPricingRequestButton] =
    useState<boolean>(false);
  const [pdfTempId, setPdfTempId] = useState<string>("");
  const [isSumbitForApprovalLoading, setIsSumbitForApprovalLoading] =
    useState<boolean>(false);
  const { isPOItemsLoading, purchaseOrderItems, isBillGenerated } =
    useAppPOSelector((state) => state?.purchaseOrderItems);
  const [isSubmitForPricingRequest, setIsSubmitForPricingRequest] =
    useState<boolean>(false);
  const [isSelectCustomerDrawerOpen, setIsSelectCustomerDrawerOpen] =
    useState<boolean>(false);
  const [isCustomButtonText, setIsCustomButtonText] = useState(false);
  const [isMissingEmailConfirmOpen, setIsMissingEmailConfirmOpen] =
    useState<boolean>(false);
  const [selectedCustomerData, setSelectedCustomerData] = useState<
    TselectedContactSendMail[]
  >([]);
  const [searchParams, setSearchParams] = useSearchParams();
  const [isCopyLoading, setIsCopyLoading] = useState<boolean>(false);
  const [supplierTo, setSupplierTo] = useState<TselectedContactSendMail[]>([]);
  const [contextMenu, setContextMenu] = useState({
    x: 0,
    y: 0,
    visible: false,
    useListMenu: false,
  });
  const [isLoading, setIsLoading] = useState(false);
  const iFrame = searchParams?.get("iframecall")?.trim();
  const pageOpenForm = searchParams?.get("pageOpenfrom")?.trim();
  const authToken = searchParams?.get("authorize_token")?.trim();
  useEffect(() => {
    if (searchParams.get("openInNewtab") === "1") {
      setGenerateBill(true);
      if (!selectedData?.purchase_order_id && paramsData?.purchase_order_id) {
        setSelectedData(paramsData);
      }
      const sp = new URLSearchParams(searchParams);
      sp.delete("openInNewtab");
      setSearchParams(sp, { replace: true });
    }
  }, []);
  const { purchaseOrderDetail, isPODetailLoading } = useAppPOSelector(
    (state) => state?.purchaseOrderDetail
  );
  const supplierOptionKey: CustomerEmailTab[] = useMemo(() => {
    const keys: CustomerEmailTab[] = [
      CFConfig.vendor_key,
      CFConfig.contractor_key,
      CFConfig?.misc_contact_key,
      "by_service",
    ];
    if (purchaseOrderDetail?.pro_id != 0) {
      keys.push("my_project" as CustomerEmailTab);
    }
    return keys;
  }, [purchaseOrderDetail]);
  const getOptions = (data: IPODetailData) => {
    return PO_OPTIONS?.filter((option: { key: string; disabled?: boolean }) => {
      // const detailStatus =
      //   paramsData?.billing_status_key?.toString()?.trim() ?? "";
      if (option.key === "archive" && data?.is_deleted === 1) {
        return false;
      } else if (option.key === "active" && data?.is_deleted === 0) {
        return false;
      }
      if (option.key === "delete") {
        option.disabled =
          user?.allow_delete_module_items === "0" || gConfig?.page_is_iframe;
      }

      // if (isDashboard && ["submit_pricing_request"]?.includes(option?.key)) {
      // if (["submit_pricing_request"]?.includes(option?.key)) {
      //   return false;
      // }
      if (
        option.key === "generate_bill" &&
        (isBilled == 1 ||
          (data?.billing_status_key &&
            !["po_approved"]?.includes(data?.billing_status_key)))
      ) {
        return false;
      }
      if (option.key === "submit_pricing_request") {
        return ["po_on_hold", "po_pricing_requested"]?.includes(
          data?.billing_status_key || ""
        );
        // for dashboard show Submit Pricing Request ============
        // ||
        // ["Pricing Requested", "Draft"].includes(
        //   data?.billing_status_name || ""
        // )
      }

      return true;
    })?.map((option: { key: string; label?: string }) => {
      return {
        ...option,
        label:
          option.key === "generate_bill"
            ? _t(`Generate a ${billName}`)
            : _t(option?.label || ""),
        onClick: async (e: { domEvent: any }) => {
          setSelectedData(data);
          switch (option.key) {
            case "email-pdf":
              setIsPOPdfViewOpen(true);
              break;
            case "make_copy":
              setIsConfirmOpen(true);
              break;
            case "share":
              setIsShareOpen(true);
              break;
            case "archive":
              setDelArchConfirmOpen("archive");
              break;
            case "delete":
              setDelArchConfirmOpen("delete");
              break;
            case "active":
              setDelArchConfirmOpen("active");
              break;
            case "generate_bill":
              const event = e.domEvent;
              if (
                Number(window.ENV.ENABLE_ALL_CLICK) &&
                (event.ctrlKey || event.metaKey)
              ) {
                const url = new URL(window.location.href);
                url.searchParams.set("openInNewtab", "1");
                window.open(url.toString(), "_blank");
                return;
              }
              setGenerateBill(true);
              break;
            case "submit_pricing_request":
              setIsSubmitForPricingRequest(true);
              break;
            default:
              break;
          }
        },
        ...(Number(window.ENV.ENABLE_ALL_CLICK) && {
          onAuxClick: (e: { button: number }) => {
            if (option.key === "generate_bill" && e.button === 1) {
              const url = new URL(window.location.href);
              url.searchParams.set("openInNewtab", "1");
              window.open(url.toString(), "_blank");
            }
          },
          onContextMenu: (e: {
            preventDefault: () => void;
            clientX: any;
            clientY: any;
          }) => {
            if (option.key === "generate_bill") {
              e.preventDefault();
              setContextMenu({
                x: e.clientX,
                y: e.clientY,
                visible: true,
                useListMenu: true,
              });
            }
          },
        }),
      };
    });
  };

  useEffect(() => {
    if (selectedData?.supplier_details?.length !== 0) {
      const assigned_to = selectedData?.supplier_details?.map((supplier) => {
        const supplierKey: string = getDirectaryKeyById(
          Number(supplier?.supplier_dir_type),
          gConfig
        );
        return {
          ...supplier,
          display_name:
            HTMLEntities.decode(
              sanitizeString(supplier?.display_name?.trim())
            ) ?? "",
          type: supplier?.supplier_dir_type,
          type_key: supplierKey,
          user_id: Number(supplier.user_id),
        };
      });
      setSupplierTo(assigned_to as TselectedContactSendMail[]);
    }
  }, [selectedData?.supplier_details]);

  // ! Archive functionality
  const handleDirDelArch = async () => {
    try {
      setIsDeleting(true);
      getApiData({
        url: apiRoutes.SERVICE.url,
        method: "post",
        data: getApiDefaultParams({
          op: "update_module_item_status",
          user,
          otherParams: {
            primary_key: selectedData?.purchase_order_id,
            module_key: gConfig?.module_key,
            status: selectedData?.is_deleted?.toString() == "0" ? 1 : 0,
            // status: 0,
          },
        }),
        success: (response: { message: string }) => {
          setIsDeleting(false);
          // fetchEstimateList();
          //uncomment
          // fetchServiceTicketList();
          // dispatch(fetchDashData());
          onCloseDelModal();
          if (window.ENV.PAGE_IS_IFRAME) {
            parentPostMessage(sendMessageKeys?.modal_change, {
              open: false,
            });
          } else {
            refreshAgGrid();
            navigate(`${routes.MANAGE_PURCHASE_ORDERS.url}`);
          }
        },
        callComplete: () => {
          setIsDeleting(false);
          // onCloseDelModal();
        },
        error: (description) => {
          setIsDeleting(false);
          notification.error({
            description,
          });
        },
      });
    } catch (error) {
      notification.error({
        description: (error as Error)?.message,
      });
    }
  };

  // delete PO functinality
  const handleDirDelActive = async () => {
    try {
      setIsDeleting(true);
      getApiData({
        url: apiRoutes.SERVICE.url,
        method: "post",
        data: getApiDefaultParams({
          op: "delete_module_record",
          user,
          otherParams: {
            primary_key: selectedData?.purchase_order_id,
            module_key: gConfig?.module_key,
            remove_associated_data: 0,
          },
        }),
        success: (response: { message: string }) => {
          setIsDeleting(false);
          // fetchEstimateList();
          //   //uncomment
          // fetchServiceTicketList();
          // dispatch(fetchDashData());
          onCloseDelModal();
          if (window.ENV.PAGE_IS_IFRAME) {
            parentPostMessage(sendMessageKeys?.modal_change, {
              open: false,
            });
          } else {
            refreshAgGrid();
            navigate(`${routes.MANAGE_PURCHASE_ORDERS.url}`);
          }
        },
        error: (description) => {
          notification.error({
            description,
          });
        },
        callComplete: () => {
          setIsDeleting(false);
          // onCloseDelModal();
        },
      });
    } catch (error: unknown) {
      setIsDeleting(false);
      notification.error({
        description: (error as Error)?.message,
      });
    }
  };

  const onCloseDelModal = () => {
    setSelectedData({});
    setDelArchConfirmOpen("");
  };

  const options: CustomerEmailTab[] = [
    CFConfig.employee_key,
    "my_crew",
    CFConfig.customer_key,
    CFConfig.contractor_key,
    CFConfig.vendor_key,
    CFConfig.misc_contact_key,
    "by_service",
    "my_project",
  ];

  const emailApiCall = async (
    tempFormData: SendEmailFormDataWithApiDefault,
    closeSendMailSidebar: () => void,
    ccMailCopy: boolean
  ) => {
    const formData: IPOSendEmailForm = {
      ...tempFormData,
      send_me_copy: ccMailCopy ? 1 : 0,
      send_custom_email: 0,
      module_id: gConfig?.module_id,
      module_key: gConfig?.module_key,
      t_id: pdfTempId,
      purchase_order_id: selectedData?.purchase_order_id,
      action: "send",
      op: "pdf_purchase_order",
    };
    try {
      const res = (await sendCommonEmailApi(formData)) as ISendEmailCommonRes;
      if (res) {
        if (res.success) {
          closeSendMailSidebar();
          // dispatch(setShareLinkModalOpen(false));
          // setSendEmailOpenStatus(false);
        } else {
          notification.error({
            description: res.message,
          });
        }
      }
    } catch (error) {
      notification.error({
        description: (error as Error).message || "",
      });
    }
  };

  const requestPricingApiCall = async (
    customerData: TselectedContactSendMail[]
  ) => {
    setIsLoading(true);
    const formData = {
      purchase_order_id: selectedData?.purchase_order_id,
      po_supplier_contacts: customerData?.map((item) => item.user_id).join(","),
      po_resend_pricing_request: 0,
    };
    try {
      getApiData({
        url: apiRoutes.SERVICE.url,
        method: "post",
        data: getApiDefaultParams({
          op: "send_notification_pricing_vendor",
          user,
          otherParams: {
            ...formData,
          },
        }),
        success: (response: { success: boolean | string; message: string }) => {
          if (response?.success?.toString() === "1") {
            setIsSubmitForPricingRequest(false);
            setIsPOPdfViewOpen(false);
            if (purchaseOrderDetail?.billing_status_key == "po_on_hold") {
              dispatch(
                updatePODetail({
                  billing_status: Number(
                    BillingStatuskeytoItemId?.po_pricing_requested
                  ),
                  billing_status_key: "po_pricing_requested",
                  billing_status_name: "Pricing Requested",
                })
              );
            }
            setIsLoading(false);
            setIsSelectCustomerDrawerOpen(false);
          } else {
            notification.error({
              description: response?.message,
            });
            setIsLoading(false);
            setIsSelectCustomerDrawerOpen(false);
          }
        },
        error: (description) => {
          notification.error({
            description,
          });
        },
      });
    } catch (error) {
      notification.error({
        description: (error as Error).message || "",
      });
      setIsLoading(false);
    }
    setSelectedCustomerData([]);
    setIsMissingEmailConfirmOpen(false);
  };

  const downloadPdf = async (tId: string) => {
    const res = (await downloadPurchaseOrderPDF({
      purchase_order_id: Number(selectedData?.purchase_order_id),
      action: "download",
      t_id: tId,
    })) as IDownloadPurchaseOrderRes;

    if (res) {
      if (res.success) {
        const fileName = res?.data?.pdf_name;
        const link = document.createElement("a");
        link.href = res.base64_encode_pdf ?? "";
        link.download = fileName
          ? fileName.toString()
          : res.base64_encode_pdfUrl ?? "";

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        notification.error({
          description: res.message,
        });
      }
    }
  };

  const handleSubmitForApproval = async (
    subject: string,
    message: string,
    tId: string
  ) => {
    try {
      if (!paramsData?.supplier_email) {
        return notification.error({
          description: _t(
            "An email address does not exist in the directory record.  Add one to use this function."
          ),
        });
      }

      const purchaseOrderId = selectedData?.purchase_order_id;
      setIsSumbitForApprovalLoading(true);
      getApiData({
        url: apiRoutes.SERVICE.url,
        method: "post",
        data: getApiDefaultParams({
          op: "pdf_purchase_order",
          user,
          otherParams: {
            action: "send",
            purchase_order_id: Number(purchaseOrderId),
            // send_custom_email: 0,
            req_from: "send_to_supplier",
            custom_subject: HTMLEntities.encode(subject),
            custom_approve_message: message,
            t_id: tId,
            send_me_copy: 0,
          },
        }),
        success: (response: { message: string }) => {
          setIsSumbitForApprovalLoading(false);
          if (Number(purchaseOrderId) !== 0) {
            refreshAgGrid();
            // navigate(`${routes.MANAGE_PURCHASE_ORDERS.url}/${purchaseOrderId}`);
          }
        },
        error: (description) => {
          notification.error({
            description,
          });
        },
        callComplete: () => {
          // dispatch(setShouldWidgetsRefresh(true));
        },
      });
    } catch (error) {
      notification.error({
        description: (error as Error).message || "",
      });
    }
  };

  function checkSupplierEmail(customerData: TselectedContactSendMail[]) {
    // check if all customer have email then send email
    const isEmail = customerData?.every((item) => item.email);
    if (isEmail) {
      // send email
      requestPricingApiCall(customerData);
      return;
    }

    //if one or more customer dont have email then show error
    const isSomehasEmail = customerData?.some((item) => item.email);
    if (isSomehasEmail) {
      setIsMissingEmailConfirmOpen(true);
      return;
    }

    //if all supplier dont have email then
    const allDontHaveEmail = customerData?.every((item) => !item.email);
    if (allDontHaveEmail) {
      notification.error({
        description:
          "Supplier does not have an email associated with their account. Go back to their Directory listing to add it.",
      });
      setIsSelectCustomerDrawerOpen(false);
      setSelectedCustomerData([]);
      return;
    }
  }

  const contextMenuItems = [
    {
      label: "Open in new tab",
      icon: faArrowUpRightFromSquare,
      onClick: () => {
        const url = new URL(window.location.href);
        url.searchParams.set("openInNewtab", "1");
        window.open(url.toString(), "_blank");
      },
    },
  ];
  const contextListMenuItems = [
    {
      label: "Open in new tab",
      icon: faArrowUpRightFromSquare,
      onClick: () => {
        const url = new URL(
          routes.MANAGE_PURCHASE_ORDERS.url +
            "/" +
            paramsData?.purchase_order_id,
          window.location.origin
        );
        url.searchParams.set("openInNewtab", "1");
        window.open(url.toString(), "_blank");
      },
    },
  ];
  const handleRequestPricingForisSubmitForPricingRequest = () => {
    // if (
    //   ["po_approved", "po_submitted", "po_received", "po_paid"]?.includes(
    //     paramsData?.billing_status_key || ""
    //   ) ||
    //   (paramsData?.supplier_details &&
    //     paramsData?.supplier_details?.length <= 0 &&
    //     (!paramsData?.supplier_id ||
    //       paramsData?.supplier_id == "" ||
    //       paramsData?.supplier_id == 0))
    // ) {
    //   return notification.error({
    //     description: _t("Supplier is required"),
    //   });
    // }
    if (purchaseOrderItems?.length) {
      setIsSelectCustomerDrawerOpen(true);
      setIsCustomButtonText(true);
    } else {
      notification.error({
        description:
          "There is no item in the purchase order, You must add at least 1 item.",
      });
    }
  };

  const handleRequestPricingForisPOPdfViewOpen = () => {
    setIsSelectCustomerDrawerOpen(true);
    setIsCustomButtonText(true);
  };

  return (
    <>
      <DropdownMenu
        // options={PO_OPTIONS}
        options={paramsData ? getOptions(paramsData) : []}
        iconClassName={
          iconClassName
            ? iconClassName
            : "text-primary-900/80 group-hover/buttonHover:text-primary-900"
        }
        buttonClass={buttonClass ? buttonClass : "m-0 hover:!bg-[#0000000f]"}
        icon={icon}
        // disabledIcon={disabledIcon}
        tooltipcontent={tooltipcontent}
        {...(user?.allow_delete_module_items === "0" && {
          footerText: _t(
            "Some actions might be unavailable depending on your privilege."
          ),
        })}
      />
      {/* Generate bill */}
      {generateBill && (
        <GenerateBill
          generateBill={generateBill}
          setGenerateBill={setGenerateBill}
          pId={pId}
          isDashboard={isDashboard}
        />
      )}
      {/* generate copy */}
      <ConfirmModal
        isOpen={isConfirmOpen}
        modalIcon="fa-regular fa-clone"
        modaltitle={_t("Copy")}
        description={_t(
          `Are you sure you want to generate a copy of this ${gConfig?.module_singular_name}?`
        )}
        onCloseModal={() => setIsConfirmOpen(false)}
        isLoading={isCopyLoading}
        onAccept={async () => {
          try {
            setIsCopyLoading(true);
            if (!selectedData) {
              throw new Error("selectedData is undefined");
            }

            const response = (await copyPODetailApi({
              purchase_order_id: selectedData.purchase_order_id,
            })) as {
              success: boolean;
              data?: { purchase_order_id?: number | string };
            };
            if (response?.success) {
              if (window.ENV.PAGE_IS_IFRAME) {
                navigate(
                  `${routes.MANAGE_PURCHASE_ORDERS.url}/${response?.data?.purchase_order_id}?iframecall=${iFrame}&pageOpenfrom=${pageOpenForm}&authorize_token=${authToken}`
                );
              } else {
                navigate(
                  `${routes.MANAGE_PURCHASE_ORDERS.url}/${response?.data?.purchase_order_id}`
                );
              }
              setIsCopyLoading(false);
            }
          } catch (error) {
            notification.error({
              description: (error as Error)?.message,
            });
            setIsCopyLoading(false);
            setIsConfirmOpen(false);
          } finally {
            setIsCopyLoading(false);
            setIsConfirmOpen(false);
          }

          // dispatch({ type: 'COPY_PO_DETAIL' });
        }}
        onDecline={() => setIsConfirmOpen(false)}
      />

      {/* ! Share link */}
      {isShareOpen && (
        <ShareInternalLinkModal
          isOpen={isShareOpen}
          shareLinkParams={{
            record_id: Number(selectedData?.purchase_order_id),
            module_key: gConfig?.module_key,
            module_page: removeFirstSlash(
              routes?.MANAGE_PURCHASE_ORDERS?.url || ""
            ),
          }}
          onEmailLinkClick={(data) => {
            setIsSendEmailSidebarOpen(true);
            setShareLink(data);
            setIsShareOpen(false);
          }}
          onCloseModal={() => {
            setSelectedData({});
            setIsShareOpen(false);
            setShareLink("");
          }}
        />
      )}

      {/* ! Delete Popup */}
      {delArchConfirmOpen !== "" && (
        <ConfirmModal
          isOpen={delArchConfirmOpen !== ""}
          modaltitle={
            delArchConfirmOpen === "delete"
              ? _t("Delete")
              : delArchConfirmOpen === "active"
              ? _t("Active")
              : _t("Archive")
          }
          description={
            delArchConfirmOpen === "delete"
              ? _t("Are you sure you want to delete this Item?")
              : delArchConfirmOpen === "archive"
              ? _t(
                  "Are you sure you want to Archive this item? To view it or Activate it later, set the filter to show Archived items."
                )
              : _t("Are you sure you want to Activate this data?")
          }
          withConfirmText={delArchConfirmOpen === "delete"}
          modalIcon={
            delArchConfirmOpen === "delete"
              ? "fa-regular fa-trash-can"
              : delArchConfirmOpen === "archive"
              ? "fa-regular fa-box-archive"
              : "fa-regular fa-regular-active"
          }
          isLoading={isDeleting}
          onAccept={(data) => {
            if (delArchConfirmOpen === "delete") {
              handleDirDelActive();
            } else {
              handleDirDelArch();
            }
          }}
          onDecline={onCloseDelModal}
          onCloseModal={onCloseDelModal}
        />
      )}

      {/* ! Email send popup */}
      <DirSendEmail
        isOpen={isSendEmailSidebarOpen}
        canWrite={shareLink ? false : true}
        // recordId={selectedData?.customer_id}
        appUsers={true}
        options={
          shareLink
            ? undefined
            : [
                CFConfig.employee_key,
                "my_crew",
                CFConfig.customer_key,
                CFConfig.contractor_key,
                CFConfig.vendor_key,
                CFConfig.misc_contact_key,
                "by_service",
              ]
        }
        isViewAttachment={false}
        emailData={{
          subject: shareLink ? "Shared Link" : "",
          body: shareLink
            ? `A link to a record within Contractor Foreman has been shared with you. <a href="${shareLink}">View Details.</a>`
            : "",
        }}
        onSendResponse={() => {
          setSelectedData({});
          setShareLink("");
        }}
        onClose={() => {
          setIsSendEmailSidebarOpen(false);
          setSelectedData({});
          setShareLink("");
        }}
      />
      {isPOPdfViewOpen && (
        <PDFFilePreview
          op={"pdf_purchase_order"}
          isOpen={isPOPdfViewOpen}
          selectedCustomer={
            purchaseOrderDetail?.is_multiple_suppliers == 0 && !isdetail
              ? [
                  {
                    display_name:
                      purchaseOrderDetail?.supplier_company_name || "",
                    user_id: Number(purchaseOrderDetail?.supplier_id),
                    type: purchaseOrderDetail?.supplier_dir_type,
                    type_key: getDirectaryKeyById(
                      purchaseOrderDetail?.supplier_dir_type === 1
                        ? 2
                        : Number(purchaseOrderDetail?.supplier_dir_type),
                      gConfig
                    ),
                    contact_id: purchaseOrderDetail?.supplier_contact_id ?? 0,
                    image: purchaseOrderDetail?.supplier_image ?? "",
                    email: purchaseOrderDetail?.supplier_email ?? "",
                  },
                ]
              : Array.isArray(selectedData?.supplier_details) &&
                selectedData?.supplier_details?.length !== 0
              ? (selectedData?.supplier_details?.map((supplier) => ({
                  ...supplier,
                  display_name:
                    supplier?.display_name || supplier?.company_name,
                })) as TselectedContactSendMail[])
              : []
          }
          setSelectedTemplateItem={(item) => {
            if (item.pdf_value === "pricing_request_pdf") {
              setShowPricingRequestButton(true);
            } else {
              setShowPricingRequestButton(false);
            }
          }}
          isRequestPricing={
            showPricingRequestButton &&
            ["po_on_hold", "po_pricing_requested"]?.includes(
              selectedData?.billing_status_key || ""
            )
          }
          handleRequestPricing={() => {
            handleRequestPricingForisPOPdfViewOpen();
          }}
          onCloseModal={() => {
            setIsPOPdfViewOpen(false);
            setShowPricingRequestButton(false);
          }}
          moduleId={gConfig?.module_id}
          daynamicOp={false}
          idName="purchase_order_id"
          isLoading={false}
          isSumbitForApproval={
            ["po_on_hold", "po_pricing_requested", "190", "359"]?.includes(
              selectedData?.billing_status_key ||
                selectedData?.billing_status?.toString() ||
                ""
            ) && !toBoolean(selectedData?.is_multiple_suppliers)
          }
          id={selectedData?.purchase_order_id?.toString() || ""}
          options={options}
          emailSubject={selectedData?.email_subject || ""}
          // emailSubject={`${poName} #${POID}`}
          handleEmailApiCall={emailApiCall}
          // submitForApprovalSubject={`${poName} #${POID}`}
          submitForApprovalSubject={selectedData?.email_subject || ""}
          handleSubmitForApproval={handleSubmitForApproval}
          isSumbitForApprovalLoading={isSumbitForApprovalLoading}
          handleDownload={downloadPdf}
          isViewAttachment={false}
          moduleName={gConfig?.module_singular_name}
          setPdfTempId={setPdfTempId}
          projectId={selectedData?.pro_id}
        />
      )}

      {isSubmitForPricingRequest && (
        <PDFFilePreview
          op={"pdf_purchase_order"}
          isOpen={isSubmitForPricingRequest}
          onCloseModal={() => setIsSubmitForPricingRequest(false)}
          moduleId={gConfig?.module_id}
          daynamicOp={false}
          idName="purchase_order_id"
          isLoading={false}
          isRequestPricing={true}
          id={selectedData?.purchase_order_id?.toString() || ""}
          handleEmailApiCall={emailApiCall}
          options={options}
          handleDownload={downloadPdf}
          isViewAttachment={false}
          moduleName={gConfig?.module_singular_name}
          setPdfTempId={setPdfTempId}
          projectId={selectedData?.pro_id}
          hideTemplateDropdown={true}
          handleRequestPricing={
            handleRequestPricingForisSubmitForPricingRequest
          }
          hideEmail={true}
        />
      )}

      {isSelectCustomerDrawerOpen && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isSelectCustomerDrawerOpen}
          singleSelecte={false}
          setCustomer={(data) => {
            if (data?.length == 0) {
              notification.error({
                description: "Please select contact to send pricing request.",
              });
              return;
            }

            setSelectedCustomerData(data);
            checkSupplierEmail(data);
          }}
          closeDrawer={() => {
            setIsSelectCustomerDrawerOpen(false);
            // setSelectedCustomerData([]);
          }}
          options={supplierOptionKey}
          activeTab={CFConfig.vendor_key}
          selectedCustomer={
            selectedData?.supplier_details &&
            selectedData?.supplier_details?.length !== 0
              ? [...supplierTo, ...selectedCustomerData]
              : [...selectedCustomerData]
          }
          // selectedCustomer={[
          //   {
          //     user_id: Number(selectedData?.supplier_id),
          //     display_name: selectedData?.supplier_name,
          //   },
          //   ...selectedCustomerData,
          // ]}
          groupCheckBox={true}
          projectId={Number(selectedData?.pro_id)}
          buttontext={isCustomButtonText ? "Send" : "Save"}
          isLoading={isLoading}
          loadingOnSave={isCustomButtonText}
        />
      )}

      {isMissingEmailConfirmOpen && (
        <ConfirmModal
          isOpen={isMissingEmailConfirmOpen}
          modalIcon="fa-regular fa-clipboard-list-check"
          isLoading={isSumbitForApprovalLoading}
          modaltitle={_t("Confirmation")}
          description={_t(
            "Some suppliers are missing email addresses. Click OK to continue sending to the others."
          )}
          yesButtonLabel={_t("Ok")}
          noButtonLabel={_t("Cancel")}
          onCloseModal={() => {
            setIsMissingEmailConfirmOpen(false);
          }}
          onAccept={(e) => {
            requestPricingApiCall(selectedCustomerData);
          }}
          onDecline={() => {
            setSelectedCustomerData([]);
            setIsMissingEmailConfirmOpen(false);
          }}
        />
      )}

      <CustomContextMenu
        position={{ x: contextMenu.x, y: contextMenu.y }}
        visible={contextMenu.visible}
        items={
          contextMenu.useListMenu ? contextListMenuItems : contextMenuItems
        }
        onClose={() =>
          setContextMenu((c) => ({ ...c, visible: false, useListMenu: false }))
        }
      />
    </>
  );
};

export default POListAction;
