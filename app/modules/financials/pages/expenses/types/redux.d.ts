// Expenses Dashboard
interface IExpensesDashState {
  resRecentExpense: IExpensesByVender[];
  resExpensesByMonth: IResexpensesByMonth;
  expensesByType: IExpensesByType;
  expenseByVendor: IExpensesByVender[];
  isDashLoading?: boolean;
  isDataFetched?: boolean;
  show_client_access?: string;
  expenseByVendorLastRefreshTime: string;
  recentExpenseLastRefreshTime: string;
  expenseByTypeLastRefreshTime: string;
  expenseByMonthLastRefreshTime: string;
}
interface IExpensesByType {
  current_month: Array<string>;
  current_year: Array<string>;
  iteam_type: Array<string>;
  last_month: Array<string>;
  last_year: Array<string>;
}

interface IRecentExpense {
  amount?: string;
  decimal_amount?: string;
}

interface IResexpensesByMonth {
  this_year_expense: Array<number | string>;
  x_data: Array<string>;
}

interface IExpensesDashApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IExpensesDashState;
  refresh_type?: string;
}
interface IExpensesUpdateApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IExpensesDashState;
  refresh_type?: string;
}

// Expenses List

interface IExpensesListProps {
  filterParams: {}; // Notes: This will change when filter developer
  search: string;
}

interface IExpensesDashParmas {
  refreshType?: string;
  isMonthWise?: boolean;
}

interface IExpensesListParmas {
  limit: number;
  start: number;
  directory?: number[] | string[];
  order_by_dir?: string;
  order_by_name?: string;
  search?: string;
  filter?: IExpenseFilter;
  status?: number;
  is_expired?: number;
  ignore_filter?: number;
}

interface IExpenseFilter {
  project?: string;
  directory?: string;
  status?: string;
  vendor?: string;
  expense_by?: string;
  vendor_names?: string;
  expense_by_names?: string;
  directory_names?: string;
  end_date?: string;
  start_date?: string;
  project_names?: string;
}

interface IExpensesListObjectValue {
  expense_id: number;
  expense_name: string;
  amount: string;
  employee: string;
  item_type_name: string;
  item_type: string;
  vendor_name: string;
  expense_by_name: string;
  project_type: string;
  project_quickbook_customer_id: string;
  category_name: string;
  qb_account_type: string;
  paid_through: string;
  paid_through_name: string;
  paid_by: string;
  project_name: string;
  date_added: string;
  amount_with_tax: string;
}

interface IExpenseListObject {
  statusCode: number;
  message: string;
  success: boolean;
  responseTime: string;
  data: IExpensesListObjectValue[];
}

interface IExpenseDetailParams {
  expense_id: string | undefined;
  add_event?: true;
}

interface IExpensesData extends IExpenseDetailData {
  amount?: string;
  amount_with_tax?: string;
  category_name?: string;
  date_added?: string;
  date_added?: string;
  expense_date?: string;
  employee?: string;
  expense_by_name?: string;
  expense_id?: number;
  expense_name?: string;
  item_type?: string | number | undefined;
  item_type_name?: string;
  paid_through?: string;
  paid_through_name?: string;
  paid_by?: string;
  project_name?: string;
  project_quickbook_customer_id?: number;
  project_type?: string;
  qb_account_type?: string;
  vendor_name?: string;
  vendor_company_name?: string;
  expense_date?: string;
  is_deleted?: number;
  email_subject?: string;
  paid_through_id?: string;
}

interface IExpensesApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IExpensesData[];
}

interface IExpensesTableCellRenderer {
  data: Partial<IExpensesData>;
}

interface IExpensesListObjectValue {
  expense_id: number;
  expense_name: string;
  amount: string;
  employee: string;
  item_type_name: string;
  item_type: string;
  vendor_name: string;
  expense_by_name: string;
  project_type: string;
  project_quickbook_customer_id: string;
  category_name: string;
  qb_account_type: string;
  paid_through: string;
  paid_by: string;
  paid_through_name: string;
  project_name: string;
  date_added: string;
  amount_with_tax: string;
}

interface IExpenseListObject {
  statusCode: number;
  message: string;
  success: boolean;
  responseTime: string;
  data: IExpensesListObjectValue[];
}
// add expenses

interface IExpensesAddState {
  isAccountLoading: boolean;
  categoryData: ICustomData[];
  accountData: ICustomData[];
  expenseCreditAccountId: string;
  expenseAccountId: string;
  callListApi?: boolean;
  details?: IDirDetails;
  isDetailLoading: boolean;
}

interface IExpensesAccountParams {
  types: Array<number>;
  moduleId: number;
}

interface IExpensesAddParams {
  project_id?: string;
  amount?: number;
  directory_id?: number;
  expense_name?: string;
  category?: number;
  paid_through?: number;
  paid_by?: string;
  from?: string;
  access_to_custom_fields?: number;
  expense_by?: number;
  item_type?: number;
  cost_code_id?: number;
}

interface IExpensesCustomDataApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IIncidentCustomData[];
  expense_credit_account_id: string;
  expense_account_id: string;
}

interface IExpenseHeaderInitialValues {
  searchValue: string;
  filter: IExpenseFilter;
}

//expense Detail

interface IExpenseDetailData {
  expense_id?: number;
  amount?: number;
  paid_by?: string;
  paid_through?: string;
  category?: string;
  expense_name?: string;
  reason?: string;
  user_id?: number;
  item_type?: string;
  item_type_name?: string;
  is_shared?: number;
  is_billable?: number;
  cost_code_id?: string;
  cost_code_name?: string;
  project_name?: string;
  ref_number?: string;
  expense_by?: number;
  expense_by_name?: string;
  directory_id?: number;
  vendor_name?: string;
  paid_through_name?: string;
  expense_date?: string;
  project_id?: number;
  date_added?: string;
  date_modified?: string;
  time_added?: string;
  qb_date_added?: any;
  qb_time_added?: any;
  employee?: string;
  type_key?: string;
  quickbook_user_id?: number;
  tax_id?: string;
  show_client_access?: string;
  expense_by_image?: string;
  is_favorite_vendor?: number;
}
interface IExpenseDetailApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IExpenseDetailData;
}

interface IExpensesDetailState {
  isExpenseDetailLoading: boolean;
  expenseDetail?: partial<IExpenseDetailData>;
  taxData: IExpenseTaxData;
}

interface IExpenseTaxData {
  isLoading?: boolean;
  isError?: boolean;
  data?: ITaxAmount[];
}

// expense files
interface IExpenseFilePhotoState {
  expenseFilePhotos: IFile[];
  isExpenseFilePhotoLoading: boolean;
}

interface IExpenseFetchFilePhotoParams {
  record_id: number;
  module_key: string;
}

interface IExpenseFileApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: { aws_files: IFile[] };
}

// expense notes
interface IExpenseNotesState {
  expenseNotes: ICommonNote[];
  timeOffRequests: IDirTimeOffRequests[];
  timeOffReqUpRes: IDirLoadingRes;
  isExpenseNotesLoading: boolean;
}
interface IExpenseNotesApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: ICommonNote[];
}
interface IExpenseHeaderInitialValues {
  searchValue: string;
  filter: {};
}
interface IExpenseDropdownInitialValues {
  shareLink: string;
  shareLinkModelOpen: boolean;
  confirmDialogOpen: boolean;
  confirmArchiveDialogOpen: boolean;
  expenseListPdfViewOpen: boolean;
  cellData: IExpensesData;
  isConfirmModalOpen: boolean;
}

interface IExpensesByVender {
  vendor_name: string;
  company_name: string;
  total_amount: string;
  expense_date: string;
}
interface IBillTaxAmountData {
  totalRecords: string;
  getTaxDetails: ITaxAmount[];
}

interface BillAccountData {
  item_id: number;
  is_custom_item: string;
  name: string;
  item_type: string;
  qb_full_name: string;
  qb_account_type: string;
  qb_account_sub_type: string;
  quickbook_category_id: string;
  date_added: string;
  date_modified: string;
  status_name: string;
  is_deleted: string;
  status_color: string;
  sort_order: string;
}

interface IBillTaxAmountApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IBillTaxAmountData;
}
interface IBillAccountApiRes extends Omit<IDefaultAPIRes, "data"> {
  totalCount: number;
  bill_account_id: number | string;
  bill_payment_account_id: number;
  deposit_account_id: number;
  expense_account_id: number;
  expense_credit_account_id: number;
  qb_options: Record<string, unknown>;
  data: BillAccountData[];
}
