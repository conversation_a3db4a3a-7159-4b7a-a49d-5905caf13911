import { useEffect, useMemo, useState, useCallback } from "react";
import { useParams } from "@remix-run/react";
// Hook Redux + store
import { formatAmount, sanitizeString } from "~/helpers/helper";
import { defaultConfig } from "~/data";
import { useTranslation } from "~/hook";
import { getGConfig, getGModuleByKey } from "~/zustand";
import {
  addSubContractsItemsApi,
  deleteSCItems,
  fetchSCOriginalScope,
  fetchSCChangeOrder,
  fetchSCEstimateItem,
  fetchSCWorkOrder,
} from "~/modules/financials/pages/subcontracts/redux/action";
import {
  useAppSCDispatch,
  useAppSCSelector,
} from "~/modules/financials/pages/subcontracts/redux/store";
import { updateSCDetail } from "~/modules/financials/pages/subcontracts/redux/slices/sCDetailsSlice";
import { resetDash } from "~/modules/financials/pages/subcontracts/redux/slices";
import { setFilterAct } from "~/modules/financials/pages/subcontracts/redux/slices/commonSCSlice";
// Atoms
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { CidbItemDrawer } from "~/shared/components/molecules/cidbItemDrawer";
import { ModuleItemsFilter } from "~/shared/components/molecules/moduleItemsFilter";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import AddSubContractItem from "../sidebar/subContractItem/AddSubContractItem";
import { ImportItemsChangeOrder } from "../sidebar/importItemsChangeOrder";
import { ImportItemsEstimate } from "../sidebar/importItemsEstimate";
import { ProjectBudgetItems } from "../sidebar/projectBudgetItems";
import { PostPayment } from "../modal";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";

const TotalCalc = () => {
  const { _t } = useTranslation();
  const { id: sCId }: RouteParams = useParams();
  const dispatch = useAppSCDispatch();
  const { formatter } = useCurrencyFormatter();
  const gConfig: GConfig = getGConfig();
  const appSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();
  const moduleChangeOrder: GModule | undefined = getGModuleByKey(
    defaultConfig.change_order_module
  );
  const moduleEstimate: GModule | undefined = getGModuleByKey(
    defaultConfig.estimate_module
  );
  const subContracts =
    HTMLEntities.decode(sanitizeString(gConfig?.module_singular_name)) ||
    "Sub-Contract";
  const chnageOrderSLName =
    HTMLEntities.decode(sanitizeString(moduleChangeOrder?.module_name)) ||
    "Change Order";
  const estimateSLName =
    HTMLEntities.decode(sanitizeString(moduleEstimate?.module_name)) ||
    "Estimate";

  const isReadOnly = useMemo(
    () => gConfig?.module_access === "read_only",
    [gConfig?.module_access]
  );

  const {
    originalScope,
    changeOrder,
    estimateItem,
    workOrderItem,
  }: ISCItemsInitialState = useAppSCSelector(
    (state) => state.subContractsItems
  );
  const { details }: ISCDetailsInitialState = useAppSCSelector(
    (state) => state.subContractsDetails
  );
  const { companyItemsData }: ISCCommonInitialState = useAppSCSelector(
    (state) => state.sCCommonData
  );
  const { filter }: ISCCommonInitialState = useAppSCSelector(
    (state) => state.sCCommonData
  );

  const [open, setOpen] = useState<boolean>(false);

  const [postPaymentOpen, setPostPaymentOpen] = useState<boolean>(false);
  const [selectedValue, setSelectedValue] = useState<string>();
  const [openItem, setOpenItem] = useState<boolean>(false);
  const [isDataUpdating, setIsDataUpdating] = useState<boolean>(false);
  const [initialSubMaterial, setInitialSubMaterial] = useState<
    Partial<CIDBItemSideData>[]
  >([]);
  const SUBCONTRACTS_ITEMS_LIST_OPTIONS = [
    {
      label: `Add Item to ${subContracts}`,
      value: "add_item_to_subcontract",
      disabled: true,
    },
    {
      label: "Import from Cost Items Database",
      value: "Database",
    },
    {
      label: `Import from ${chnageOrderSLName}`,
      value: "import_from_change_order",
    },
    {
      label: `Import from ${estimateSLName}`,
      value: "estimate",
    },
    {
      label: "Import from Schedule of Values",
      value: "import_from_schedule_of_values",
    },
    {
      label: `Add Manual ${subContracts} Item`,
      value: "add_manual_subcontract_item",
    },
  ];

  const handleItemFromEstimate = (
    data: IEstimateAddItems[],
    removeData: number[]
  ) => {
    const estimateItemsAdd = data?.map((item) => {
      let temData: IEstimateAddItems = {
        subject: item.subject,
        cost_code_id: item.cost_code_id || "0",
        quantity: item.quantity,
        unit: item.unit,
        unit_cost: item.modified_unit_cost,
        tax_id: item.tax_id,
        markup: item.markup,
        is_markup_percentage: Number(item.is_markup_percentage),
        total: Number(item.no_mu_total).toString(),
        assigned_to: item.assigned_to,
        assigned_to_contact_id: 0,
        description: item.description,
        internal_notes: item.internal_notes,
        estimate_item_id: item.item_id,
        estimate_id: item.estimate_id,
        item_type: item?.item_type || "",
        reference_item_type_id: item?.reference_item_id || undefined,
      };
      return temData;
    });

    if (estimateItemsAdd.length > 0) {
      const newFormData = { estimate_items: estimateItemsAdd };
      handleSaveImportSCItems(newFormData, "estimate_item", removeData);
    }
  };
  const handleItemFromDirectSov = (
    data: ISCOriginalScope[],
    removeData: number[]
  ) => {
    const sovDirectItemsAdd = data?.map((item) => {
      let temData: ISCOriginalScope = {
        data_id: item.data_id,
        budget_item_id: item.item_id,
        subject: item.subject,
        quantity: item.quantity,
        markup: item.markup,
        is_markup_percentage: Number(item.is_markup_percentage),
        unit_cost: item.modified_unit_cost,
        estimate_id: item.estimate_id,
        change_order_id: item.change_order_id,
        estimate_item_id: item?.reference_item_id?.toString() || "",
        unit: item.unit,
        cost_code_id: item.cost_code_id || 0,
        tax_id: item.tax_id,
        item_type: Number(item.item_type),
        reference_item_id: item?.reference_item_type_id || "",
        assigned_to: item.assigned_to,
        assigned_to_contact_id: "",
        total: Number(item.no_mu_total).toString(),
        sub_contract_item_no: item.sub_contract_item_no,
        description: item.description,
        internal_notes: item.internal_notes,
      };
      return temData;
    });

    if (sovDirectItemsAdd.length > 0) {
      const newFormData = { project_items: sovDirectItemsAdd };
      handleSaveImportSCItems(newFormData, "original_scope", removeData);
    }
  };

  const handleItemFromSVEstimate = (
    data: IEstimateAddItems[],
    removeData: number[]
  ) => {
    const sovEstimateItemsAdd = data?.map((item) => {
      let temData: IEstimateAddItems = {
        data_id: item.data_id,
        budget_item_id: item.item_id,
        subject: item.subject,
        quantity: item.quantity,
        markup: item.markup,
        is_markup_percentage: Number(item.is_markup_percentage),
        unit_cost: item?.modified_unit_cost,
        estimate_id: item.estimate_id,
        change_order_id: item.change_order_id,
        estimate_item_id: item.reference_item_id,
        unit: item.unit,
        cost_code_id: item.cost_code_id || "0",
        tax_id: item.tax_id || "",
        item_type: item.item_type || "",
        reference_item_id: item.reference_item_type_id,
        assigned_to: item.assigned_to || item.item_assigned_to || 0,
        assigned_to_contact_id: 0,
        total: Number(item.no_mu_total || ""),
        sub_contract_item_no: item.sub_contract_item_no || "1",
        description: item.description,
        internal_notes: item.internal_notes,
      };
      return temData;
    });

    if (sovEstimateItemsAdd.length > 0) {
      const newFormData = { project_items: sovEstimateItemsAdd };
      handleSaveImportSCItems(newFormData, "estimate_item", removeData);
    }
  };

  const handleItemFromChangeOrder = (
    data: ISCChangeOrder[],
    import_value: string,
    removeData: number[]
  ) => {
    const changeOrderAdd = data?.map((item) =>
      import_value === "change_order" ? item.item_id : item.reference_item_id
    );
    if (changeOrderAdd.length > 0) {
      const newFormData = { co_items: changeOrderAdd };
      handleSaveImportSCItems(newFormData, "change_order", removeData);
    }
  };
  const handleItemFromWorkOrder = (
    data: ISCWorkOrder[],
    removeData: number[]
  ) => {
    const workOrderAdd = data?.map((item) => item.item_id);
    if (workOrderAdd.length > 0) {
      const newFormData = { wo_items: workOrderAdd };
      handleSaveImportSCItems(newFormData, "work_order", removeData);
    }
  };

  // Save Import Items
  const handleSaveImportSCItems = async (
    newFormData: ISCImportItemsSaveData,
    items: string,
    removeData: number[]
  ) => {
    try {
      setIsDataUpdating(true);
      const responseApi = (await addSubContractsItemsApi(
        newFormData,
        sCId || ""
      )) as IAddSubContractsRes;

      if (responseApi?.success) {
        dispatch(resetDash());
        if (items === "estimate_item" && removeData.length <= 0) {
          dispatch(
            fetchSCEstimateItem({
              id: sCId || "",
              is_separate_estimate_sections: 1,
              isHideLoading: true,
            })
          );
        }
        if (items === "change_order" && removeData.length <= 0) {
          dispatch(fetchSCChangeOrder({ id: sCId || "" }));
        }
        if (items === "original_scope" && removeData.length <= 0) {
          dispatch(fetchSCOriginalScope({ id: sCId || "" }));
        }
        if (items === "work_order" && removeData.length <= 0) {
          dispatch(fetchSCWorkOrder({ id: sCId || "" }));
        }
      } else {
        notification.error({
          description: responseApi?.message || "",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error).message || "",
      });
    } finally {
      setIsDataUpdating(false);
      setOpenItem(false);
    }
  };

  // Remove Import Items
  const handleDeleteImportSCItems = async (
    removeRecordId: number[],
    removeItem: string
  ) => {
    setIsDataUpdating(true);
    const deleteRes = (await deleteSCItems({
      id: Number(sCId),
      item_ids: removeRecordId || 0,
    })) as ISCItemsDeleteApiRes;
    if (deleteRes.success) {
      dispatch(resetDash());
      if (removeItem === "remove_estimate_item") {
        dispatch(
          fetchSCEstimateItem({
            id: sCId || "",
            is_separate_estimate_sections: 1,
            isHideLoading: true,
          })
        );
      }
      if (removeItem === "remove_direct_item") {
        dispatch(fetchSCOriginalScope({ id: sCId || "" }));
      }
      if (removeItem === "remove_change_order") {
        dispatch(fetchSCChangeOrder({ id: sCId || "" }));
      }
      if (removeItem === "remove_work_order") {
        dispatch(fetchSCWorkOrder({ id: sCId || "" }));
      }
      setIsDataUpdating(false);
      setOpenItem(false);
    } else {
      notification.error({
        description: deleteRes?.message,
      });
    }
  };

  // Import from Estimate Item
  const estimateItemsHandler = useCallback(
    async ({
      itemsToAddEstimateItem,
      itemToBeDeleteIdEstimateItem,
    }: {
      itemsToAddEstimateItem: IEstimateAddItems[];
      itemToBeDeleteIdEstimateItem: number[] | [];
    }) => {
      if (!!itemsToAddEstimateItem.length) {
        await handleItemFromEstimate(
          itemsToAddEstimateItem,
          itemToBeDeleteIdEstimateItem
        );
      }
      if (!!itemToBeDeleteIdEstimateItem.length) {
        await handleDeleteImportSCItems(
          itemToBeDeleteIdEstimateItem,
          "remove_estimate_item"
        );
      }
    },
    [handleItemFromEstimate, handleDeleteImportSCItems]
  );

  // Import from Change Order
  const changeOrderItemsHandler = useCallback(
    async ({
      itemsToAdd,
      itemToBeDeleteId,
    }: {
      itemsToAdd: ISCChangeOrder[];
      itemToBeDeleteId: number[] | [];
    }) => {
      if (!!itemsToAdd.length) {
        await handleItemFromChangeOrder(
          itemsToAdd,
          "change_order",
          itemToBeDeleteId
        );
      }
      if (!!itemToBeDeleteId.length) {
        await handleDeleteImportSCItems(
          itemToBeDeleteId,
          "remove_change_order"
        );
      }
    },
    [handleItemFromChangeOrder, handleDeleteImportSCItems]
  );

  // Import from Schedule Value - Item Direct SOV section
  const scheduleValueSovItemsHandler = useCallback(
    async ({
      itemsToAdd,
      itemToBeDeleteId,
    }: {
      itemsToAdd: ISCOriginalScope[];
      itemToBeDeleteId: number[] | [];
    }) => {
      if (!!itemsToAdd.length) {
        await handleItemFromDirectSov(itemsToAdd, itemToBeDeleteId);
      }
      if (!!itemToBeDeleteId.length) {
        await handleDeleteImportSCItems(itemToBeDeleteId, "remove_direct_item");
      }
    },
    [handleItemFromDirectSov, handleDeleteImportSCItems]
  );
  // Import from Schedule Value - Change Order section
  const scheduleValueChanageOrderItemsHandler = useCallback(
    async ({
      itemsToAddChangeOrder,
      itemToBeDeleteIdChangeOrder,
    }: {
      itemsToAddChangeOrder: ISCChangeOrder[];
      itemToBeDeleteIdChangeOrder: number[] | [];
    }) => {
      if (!!itemsToAddChangeOrder.length) {
        await handleItemFromChangeOrder(
          itemsToAddChangeOrder,
          "schedule_value",
          itemToBeDeleteIdChangeOrder
        );
      }
      if (!!itemToBeDeleteIdChangeOrder.length) {
        await handleDeleteImportSCItems(
          itemToBeDeleteIdChangeOrder,
          "remove_change_order"
        );
      }
    },
    [handleItemFromChangeOrder, handleDeleteImportSCItems]
  );

  // Import from Schedule Value - Work Order section
  const scheduleValueWorkOrderItemsHandler = useCallback(
    async ({
      itemsToAddWorkOrder,
      itemToBeDeleteIdWorkOrder,
    }: {
      itemsToAddWorkOrder: ISCWorkOrder[];
      itemToBeDeleteIdWorkOrder: number[] | [];
    }) => {
      if (!!itemsToAddWorkOrder.length) {
        await handleItemFromWorkOrder(
          itemsToAddWorkOrder,
          itemToBeDeleteIdWorkOrder
        );
      }
      if (!!itemToBeDeleteIdWorkOrder.length) {
        await handleDeleteImportSCItems(
          itemToBeDeleteIdWorkOrder,
          "remove_work_order"
        );
      }
    },
    [handleItemFromWorkOrder, handleDeleteImportSCItems]
  );

  const scheduleValueEsHandler = useCallback(
    async ({
      itemsToAddEstimateItem,
      itemToBeDeleteIdEstimateItem,
    }: {
      itemsToAddEstimateItem: IEstimateAddItems[];
      itemToBeDeleteIdEstimateItem: number[] | [];
    }) => {
      if (!!itemsToAddEstimateItem.length) {
        await handleItemFromSVEstimate(
          itemsToAddEstimateItem,
          itemToBeDeleteIdEstimateItem
        );
      }
      if (!!itemToBeDeleteIdEstimateItem.length) {
        await handleDeleteImportSCItems(
          itemToBeDeleteIdEstimateItem,
          "remove_estimate_item"
        );
      }
    },
    [handleItemFromSVEstimate, handleDeleteImportSCItems]
  );

  const costItemsFromDatabase = useMemo(() => {
    const data = originalScope
      ?.filter((item: ISCOriginalScope) => Number(item?.reference_item_id) > 0)
      ?.map((item: ISCOriginalScope) => {
        let temData: Partial<CIDBItemSideData> = {
          name: item.subject || "",
          reference_item_id: item?.reference_item_id,
          item_id: item?.item_id as unknown as string,
        };
        if (item?.item_type_key === "item_material") {
          temData.material_id = item?.reference_item_id;
          temData.item_type = "161";
          temData.type_name = "Material";
        } else if (item?.item_type_key === "item_labour") {
          temData.labor_id = item?.reference_item_id;
          temData.item_type = "163";
          temData.type_name = "Labor";
        } else if (item?.item_type_key === "item_sub_contractor") {
          temData.contractor_id = item?.reference_item_id;
          temData.item_type = "164";
          temData.type_name = "Subcontractor";
        } else if (item?.item_type_key === "item_equipment") {
          temData.equipment_id = item?.reference_item_id;
          temData.item_type = "162";
          temData.type_name = "Equipment";
        } else if (item?.item_type_key === "item_other") {
          temData.other_item_id = item?.reference_item_id;
          temData.item_type = "165";
          temData.type_name = "Other Items";
        }
        return temData as Partial<CIDBItemSideData>;
      });
    return data;
  }, [JSON.stringify(originalScope)]);

  const handleSelectChange = (selectedItem: string) => {
    setSelectedValue(selectedItem);
    // setImportItemsChangeOrder(true);
    setOpenItem(true);
  };

  const handleItemFromCostItemDatabase = async (data: CIDBItemSideData[]) => {
    const uniqueJobItems = Array.from(
      new Map(
        originalScope
          .filter(
            (jobItem) =>
              jobItem.reference_item_id && Number(jobItem.reference_item_id) > 0
          )
          .map((jobItem) => [jobItem.reference_item_id, jobItem])
      ).values()
    );

    // Remove data collection
    const removedItemIds = uniqueJobItems
      .filter((jobItem) => {
        if (
          jobItem.reference_item_id &&
          (jobItem?.reference_item_id as number) > 0
        ) {
          return !data.some((item) => {
            if (item?.type_name === "Material") {
              return item.material_id === jobItem.reference_item_id;
            } else if (item?.type_name === "Labor") {
              return (
                Number(jobItem.reference_item_id) === Number(item.labor_id)
              );
            } else if (item?.type_name === "Subcontractor") {
              return (
                Number(jobItem.reference_item_id) === Number(item.contractor_id)
              );
            } else if (item?.type_name === "Equipment") {
              return (
                Number(jobItem.reference_item_id) === Number(item.equipment_id)
              );
            } else if (item?.type_name === "Other Items") {
              return (
                Number(jobItem.reference_item_id) === Number(item.other_item_id)
              );
            } else if (item?.type_name === "Groups") {
              return Number(jobItem.reference_item_id) === Number(item.item_id);
            }
          });
        }
      })
      .map((item) => Number(item.item_id));

    // add data collection
    const mutationPromises = [];
    const itemsToBeAdd = data
      .filter(
        (jobItem) =>
          !originalScope.some((item) => {
            if (jobItem?.type_name === "Material") {
              return (
                Number(item.reference_item_id) === Number(jobItem.material_id)
              );
            } else if (jobItem?.type_name === "Labor") {
              return (
                Number(item.reference_item_id) === Number(jobItem.labor_id)
              );
            } else if (jobItem?.type_name === "Subcontractor") {
              return (
                Number(item.reference_item_id) === Number(jobItem.contractor_id)
              );
            } else if (jobItem?.type_name === "Equipment") {
              return (
                Number(item.reference_item_id) === Number(jobItem.equipment_id)
              );
            } else if (jobItem?.type_name === "Other Items") {
              return (
                Number(item.reference_item_id) === Number(jobItem.other_item_id)
              );
            } else if (jobItem?.type_name === "Group") {
              return Number(item.reference_item_id) === Number(jobItem.item_id);
            }
          })
      )
      .map((item) => {
        let temData: IAddSubContractItem = {
          subject: item.name,
          quantity: item.quantity,
          unit: item.unit,
          hidden_markup: item.hidden_markup,
          // We have used it, because in the common comp.(AddMultiselectDirectorySide), you have added the unit cost & cost code Id as unitCost & costCodeId instead of unit_cost, please check that
          unit_cost: item.unit_cost
            ? Number(item.unit_cost).toString()
            : Number(item?.unitCost).toString() ?? "",
          cost_code_id: item.cost_code_id ?? item?.costCodeId ?? "0",
          markup: item.markup?.toString() ?? "",
          is_markup_percentage: 1,
          total: item?.total?.toString(),
          assigned_to: "",
          assigned_to_contact_id: 0,
          contractor_id: item.contractor_id?.toString() || "",
          contractor_contact_id: 0,
          is_cost_item: 1,
          description: item.notes || "",
          internal_notes: item.internal_notes || item.internalNotes,
          item_type: item?.item_type?.toString() || "",
        };
        if (item?.type_name === "Material") {
          temData.reference_item_id = item?.material_id?.toString();
        } else if (item?.type_name === "Labor") {
          temData.reference_item_id = item?.labor_id?.toString();
        } else if (item?.type_name === "Subcontractor") {
          temData.reference_item_id = item?.contractor_id?.toString();
        } else if (item?.type_name === "Equipment") {
          temData.reference_item_id = item?.equipment_id?.toString();
        } else if (item?.type_name === "Other Items") {
          temData.reference_item_id = item?.other_item_id?.toString();
        } else if (item?.type_name === "Group") {
          temData.reference_item_id =
            item?.equipment_id?.toString() ||
            item?.material_id?.toString() ||
            item?.labor_id?.toString() ||
            item?.contractor_id?.toString() ||
            item?.other_item_id?.toString() ||
            item?.item_id?.toString();
        }
        return temData;
      });

    // Add api call
    if (itemsToBeAdd.length) {
      mutationPromises.push(...itemsToBeAdd);
      const newFormData = { cost_code_items: mutationPromises };
      await handleSaveImportSCItems(
        newFormData,
        "original_scope",
        removedItemIds
      );
    }

    // Remove api call
    if (removedItemIds.length) {
      const deleteRes = (await deleteSCItems({
        id: Number(sCId),
        item_ids: removedItemIds || 0,
      })) as ISCItemsDeleteApiRes;
      if (deleteRes.success) {
        dispatch(fetchSCOriginalScope({ id: sCId || "" }));
      } else {
        notification.error({
          description: deleteRes?.message,
        });
      }
    }
  };

  useEffect(() => {
    let total = 0;

    if (originalScope?.length > 0) {
      originalScope
        .filter((item) =>
          filter.length > 0
            ? item?.item_type_key !== undefined &&
              filter.includes(item?.item_type_key)
            : true
        )
        .forEach((entry) => {
          const amount = entry.total ? parseFloat(entry.total as string) : 0;
          total += amount;
        });
    }
    if (estimateItem?.length > 0) {
      estimateItem.map((items) => {
        if (items?.items?.length > 0) {
          items.items
            .filter((item) =>
              filter.length > 0
                ? item?.item_type_key !== undefined &&
                  filter.includes(item?.item_type_key)
                : true
            )
            .forEach((entry) => {
              const amount = entry.no_mu_total
                ? parseFloat(entry.no_mu_total)
                : 0;
              total += amount;
            });
        }
      });
    }

    if (changeOrder?.length > 0) {
      changeOrder
        .filter((item) =>
          filter.length > 0
            ? item?.item_type_key !== undefined &&
              filter.includes(item?.item_type_key)
            : true
        )
        .forEach((entry) => {
          const amount = entry.total ? parseFloat(entry.total) : 0;
          total += amount;
        });
    }
    if (workOrderItem?.length > 0) {
      workOrderItem
        .filter((item) =>
          filter.length > 0
            ? item?.item_type_key !== undefined &&
              filter.includes(item?.item_type_key)
            : true
        )
        .forEach((entry) => {
          const amount = entry.total ? parseFloat(entry.total) : 0;
          total += amount;
        });
    }

    dispatch(updateSCDetail({ total: total }));
  }, [
    JSON.stringify(changeOrder),
    JSON.stringify(originalScope),
    JSON.stringify(workOrderItem),
    JSON.stringify(estimateItem),
    JSON.stringify(filter),
  ]);

  const totalAmount = useMemo(() => {
    const billedAmount = formatter(
      formatAmount(Number(details?.sc_billed_amount || "") / 100)
    ).value_with_symbol;
    const totalRetainage = formatter(
      formatAmount(Number(details?.sc_retainage_held || "") / 100)
    ).value_with_symbol;

    const remainingRetainageTemp =
      Number(details?.sc_retainage_held || "") -
      Number(details?.retainage_remaining || "");

    const remainingRetainage = formatter(
      formatAmount(remainingRetainageTemp / 100)
    ).value_with_symbol;

    return { billedAmount, totalRetainage, remainingRetainage };
  }, [
    details?.sc_billed_amount,
    details?.sc_retainage_held,
    details?.retainage_remaining,
  ]);
  useEffect(() => {
    if (originalScope?.length) {
      const jobsiteDataArr: any = [];

      originalScope.forEach((data: any) => {
        jobsiteDataArr.push({
          name: data.subject || "",
          reference_item_id: data.reference_item_id || "",
          item_id: data.item_id || "",
          material_id: data.reference_item_id || "",
          item_type: String(data.item_type || ""),
          type_name: data.item_type_name || "",
        });
      });
      setInitialSubMaterial(jobsiteDataArr);
    } else {
      setInitialSubMaterial([]);
    }
  }, [originalScope]);
  return (
    <>
      <div className="!bg-[#EEEFF0] collapse-with-table rounded-md relative">
        <div
          className={`2xl:flex-row sm:flex-col flex-col-reverse flex 2xl:items-center sm:items-start items-end justify-between px-3 py-2.5 ${
            originalScope?.length > 0 ? "sm:gap-2 gap-3" : "sm:gap-0 gap-3"
          }`}
        >
          <div className="sm:min-w-[300px]">
            <div className="flex w-fit items-center gap-1.5 font-medium bg-[#DFE1E4] text-primary-900 py-[3px] px-[9px] rounded-[4px] dark:bg-dark-800 dark:text-white/90 whitespace-nowrap">
              <FontAwesomeIcon
                className="w-[17px] h-[17px]"
                icon="fa-duotone fa-solid fa-money-check-dollar"
              />
              <Typography className="sm:text-base text-13 font-semibold text-primary-900">
                {_t(
                  `Total: ${
                    formatter(formatAmount(+details?.total / 100))
                      .value_with_symbol
                  }`
                )}
              </Typography>
            </div>
          </div>
          {(originalScope?.length > 0 ||
            changeOrder?.length > 0 ||
            estimateItem?.length > 0 ||
            workOrderItem?.length > 0) && (
            <div className="2xl:w-fit w-full flex sm:flex-row flex-col sm:items-center items-end justify-end gap-2">
              <div className="py-[3px] px-2.5 bg-white border border-solid rounded border-deep-orange-500">
                <Typography className="text-deep-orange-500 font-semibold text-13">
                  {_t("Billed Amount")}: {totalAmount?.billedAmount}
                </Typography>
              </div>

              <div className="py-[3px] px-2.5 bg-white border border-solid rounded border-[#008000]">
                <Typography className="text-[#008000] font-semibold text-13">
                  {_t("Total Retainage")}: {totalAmount?.totalRetainage}
                </Typography>
              </div>

              <div className="py-[3px] px-2.5 bg-white border border-solid rounded border-primary-900">
                <Typography className="text-primary-900 font-semibold text-13">
                  {_t("Remaining Retainage")}: {totalAmount?.remainingRetainage}
                </Typography>
              </div>
            </div>
          )}
          <div className="2xl:w-fit w-full sm:flex-row flex-col-reverse flex justify-end sm:items-center items-end gap-2 sm:min-w-[300px]">
            <div className="flex items-center gap-2 2xl:relative 2xl:top-0 2xl:right-0 sm:absolute sm:top-2.5 sm:right-2.5">
              <div className="w-full">
                {details.response !== "accept" &&
                  details.response !== "closed" &&
                  !isReadOnly && (
                    <DropdownMenu
                      options={SUBCONTRACTS_ITEMS_LIST_OPTIONS?.map((item) => ({
                        ...item,
                        onClick: () => {
                          handleSelectChange(item.value);
                        },
                      }))}
                      buttonClass="w-fit h-auto"
                      contentClassName="add-items-drop-down"
                      placement="bottomRight"
                    >
                      <div className="py-1 px-2.5 bg-[#EBF1F9] rounded flex items-center gap-[5px]">
                        <Typography className="text-primary-900 text-sm">
                          {_t(`Add Item to ${subContracts}`)}
                        </Typography>
                        <FontAwesomeIcon
                          className="w-3 h-3 text-primary-900"
                          icon="fa-regular fa-chevron-down"
                        />
                      </div>
                    </DropdownMenu>
                  )}
              </div>
              <ModuleItemsFilter
                onOpenChange={(newOpen: boolean) => setOpen(newOpen)}
                filter={filter}
                onChangeFilter={(data) => {
                  dispatch(setFilterAct(data));
                }}
                openFilter={open}
              />
            </div>
          </div>
        </div>
      </div>

      {openItem && selectedValue === "Database" && (
        <CidbItemDrawer
          closeDrawer={() => {
            setOpenItem(false);
          }}
          options={[
            "material",
            "labor",
            "equipment",
            "subcontractor",
            "other_items",
            "groups",
          ]}
          singleSelecte={false}
          addItem={(data) => {
            handleItemFromCostItemDatabase(data as CIDBItemSideData[]);
          }}
          itemTypes={companyItemsData?.items?.map((item) => {
            return {
              ...item,
              default_color: item.default_color?.toString(),
            };
          })}
          openSendEmailSidebar={openItem}
          data={costItemsFromDatabase as Partial<CIDBItemSideData>[]}
          initialSubMaterial={initialSubMaterial as CIDBItemSideData[]}
          cidbModuleVIseIdAndValue={{
            [defaultConfig.material_key]: {
              id: defaultConfig.material_teb_id,
              value: defaultConfig.material_key,
            },
            [defaultConfig.equipment_key]: {
              id: defaultConfig.equipment_teb_id,
              value: defaultConfig.equipment_key,
            },
            [defaultConfig.labor_key]: {
              id: defaultConfig.labor_teb_id,
              value: defaultConfig.labor_key,
            },
            [defaultConfig.subcontractor_key]: {
              id: defaultConfig.subcontractor_teb_id,
              value: defaultConfig.subcontractor_key,
            },
            [defaultConfig.other_items_key]: {
              id: defaultConfig.other_items_teb_id,
              value: defaultConfig.other_items_key,
            },
          }}
          isHiddenMarkupApply={false}
          //PO & SC
          // projectId={details?.project_id}
          projectId={
            appSettings?.has_project_based_cost_codes?.toString() === "1"
              ? details?.project_id
              : undefined
          }
        />
      )}

      {openItem && selectedValue === "import_from_change_order" && (
        <ImportItemsChangeOrder
          isOpen={openItem}
          onClose={() => setOpenItem(false)}
          isDataUpdating={isDataUpdating}
          changeOrderItemHandler={changeOrderItemsHandler}
        />
      )}

      {openItem && selectedValue === "estimate" && (
        <ImportItemsEstimate
          isOpen={openItem}
          onClose={() => setOpenItem(false)}
          isDataUpdating={isDataUpdating}
          estimateItemHandler={estimateItemsHandler}
        />
      )}

      {openItem && selectedValue === "import_from_schedule_of_values" && (
        <ProjectBudgetItems
          isOpen={openItem}
          onClose={() => setOpenItem(false)}
          isDataUpdating={isDataUpdating}
          scheduleValueSovItemsHandler={scheduleValueSovItemsHandler}
          scheduleValueChanageOrderItemsHandler={
            scheduleValueChanageOrderItemsHandler
          }
          scheduleValueWorkOrderItemsHandler={
            scheduleValueWorkOrderItemsHandler
          }
          scheduleValueEsHandler={scheduleValueEsHandler}
        />
      )}

      {openItem && selectedValue === "add_manual_subcontract_item" && (
        <AddSubContractItem
          moduleName={gConfig?.module_singular_name}
          isOpen={openItem}
          onClose={() => setOpenItem(false)}
        />
      )}

      <PostPayment
        isOpen={postPaymentOpen}
        onCloseModal={() => {
          setPostPaymentOpen(false);
        }}
      />
    </>
  );
};

export default TotalCalc;
