interface IBillDashState {
  countBillPaymentValue:
    | IBillDashboardApiResponseDataCountBillPaymentValue
    | {};
  unPaidBillsValue: IBillDashboardApiResponseDataUnPaidBillsValue | {};
  balanceStatusData: IBillDashboardApiResponseDataBalanceStatus | {};
  billComingDue: IBillDashboardApiResponseDataBillComingDue[];
  billPastDue: IBillDashboardApiResponseDataBillPastDue[];
  salesByMonth: IBillDashboardApiResponseDataSalesByMonth | {};
  countBillPaymentValueLastRefreshTime: string;
  unPaidBillsValueLastRefreshTime: string;
  billComingDueLastRefreshTime: string;
  billPastDueLastRefreshTime: string;
  balanceStatusDataLastRefreshTime: string;
  salesByMonthLastRefreshTime: string;
  isDashLoading?: boolean;
  isDataFetched?: boolean;
  searchValue?: string;
}
interface IDashboardCardHeaderProps {
  title: string;
  children?: React.ReactNode;
}
interface IPaymentData {
  amount: string;
}
interface IBillDashboardApiResponseDataCountBillPaymentValue {
  data: Partial<IPaymentData>;
}

interface IBillDashboardApiResponseDataUnPaidBillsValue {
  this_month: string;
  previous_month: string;
  this_year: string;
  previous_year: string;
}

interface IBillDashboardApiResponseDataBalanceStatus {
  bill_due_today: IBillDashboardApiResponseDataDueToday[];
  bill_due_1_30: IBillDashboardApiResponseDataDueToday[];
  bill_due_31_60: IBillDashboardApiResponseDataDueToday[];
  bill_due_61: IBillDashboardApiResponseDataDueToday[];
  [key: string]: IBillDashboardApiResponseDataDueToday[];
}
interface IBillDashboardApiResponseDataDueToday {
  total: string | number | undefined;
  no_of_bill: string | number;
  per: string | number | undefined;
  key: string;
  bill: string;
  due_date: string;
}

interface IBillDashboardBalanceStatusListProps {
  title: string;
  TotalBill: number;
  Amount: string;
  percent: number;
  strokeColor: string;
}

interface IBillDashboardApiResponseDataBillComingDue {
  bill_id: number;
  due_date: string;
  total: string;
  due_balance: string;
  amount: string;
  bill_total: string;
  supplier_name: string;
  supplier_name_only: string;
  bill: string;
  supplier_company_name: string;
}
interface IBillDashboardApiResponseDataBillPastDue {
  bill_id: number;
  due_date: string;
  total: string;
  due_balance: string;
  bill_total: string;
  supplier_name: string;
  supplier_name_only: string;
  bill: string;
  supplier_company_name: string;
}
interface IBillDashboardApiResponseDataSalesByMonth {
  data: string[];
  this_year_payment: string[];
  this_year_bill: string[];
  previous_year_payment: string[];
  previous_year_bill: string[];
}

interface IBillDetailResponse extends Omit<IApiCallResponse, "data"> {
  responseTime: string;
  data: IBillDetailsRes;
}

interface IAccountDetails extends Omit<IApiCallResponse, "data"> {
  responseTime: string;
  data: IBillDetailsRes;
}

interface IBillDashboardGetModuleProps {
  moduleName?: string;
}

interface IBillUpdateApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IBillDashState;
  refresh_type?: string;
}

interface IBillDetailData {
  data: {
    bill_id?: string | number | undefined;
    project_id?: number;
    subject?: string;
    order_date?: string;
    due_date?: string;
    term_id?: number;
    term_key?: string;
    notes?: string;
    tax_id?: number;
    signature?: null;
    total?: string;
    cost?: string;
    user_id?: number;
    company_id?: number;
    date_added?: string;
    date_modified?: string;
    demo_data?: number;
    parent_bill_id?: number;
    total_tax_rate?: string | number | boolean | undefined;
    company_bill_id?: string;
    supplier_id?: string | number;
    is_deleted?: number;
    supplier_contact_id?: string | number;
    qb_date?: string;
    quickbook_bill_id?: number;
    qbc_id?: string;
    is_updated?: number;
    custom_bill_id?: string;
    amount?: string;
    reference_purchase_order_id?: number;
    reference_invoice_id?: number;
    need_prefix_project?: number;
    is_notes_convert?: number;
    ref_po?: string;
    reference_module_id?: number;
    reference_primary_id?: number;
    reference_sub_contract_id?: number;
    is_shared?: boolean;
    reversible_tax_amount?: string;
    sc_multi_bill?: number;
    origin?: number;
    is_billable?: boolean;
    project_name?: string;
    pro_id?: number;
    project_id_string?: string;
    customer_id?: number;
    project_billed_to?: string;
    customer_billed_to?: string;
    quickbook_project_id?: number;
    is_reversible_tax?: string;
    quickbook_classprojecttype_id?: null;
    project_type_name?: null;
    project_type?: string;
    prj_address1?: string;
    prj_address2?: string;
    prj_city?: string;
    prj_state?: string;
    prj_zip?: string;
    term_name?: string;
    term_key_id?: string;
    emp_username?: string;
    supplier_name?: string;
    supplier_name_only?: string;
    email_subject?: string;
    supplier_company_name?: string;
    quickbook_contractor_id?: number;
    supplier_dir_type?: string;
    supplier_address1?: string;
    supplier_address2?: string;
    supplier_city?: string;
    supplier_state?: string;
    supplier_zip?: string;
    supplier_phone?: string;
    tpar?: number;
    cmp_company_name?: string;
    cmp_street?: string;
    cmp_city?: string;
    cmp_state?: string;
    cmp_zip?: string;
    cmp_phone?: string;
    due_balance?: string;
    order_date_use_qb?: string;
    due_date_use_qb?: string;
    time_added?: string;
    qb_date_added?: string;
    qb_time_added?: string;
    date_today?: string;
    reference_po_name?: string;
    reference_po_only_id?: string;
    origin_date_modified?: string;
    bill_payment?: string;
    quickbook_term_id?: string;
    is_access?: string;
    show_client_access?: string;
    allow_overbilling?: string;
    custom_form_data?: Object[];
    items?: IBillDetailsItem[] | undefined;
    supplier_details?: ISupplierDetails;
    aws_files?: IBillAwsFiles[];
    taxes?: IBillDetailsTax[];
    payments?: IBillDetailPayments[] | undefined;
    custom_field_id?: number;
    custom_field_form_json?: ICustomFieldFormJson[];
    notes_data?: IBillDetailsNotesData[];
    project_id?: number;
    quickbook_bill_id?: number | string;
  };
}

interface IBillDetailPayments {
  company_bill_id: string;
  total: string;
  bill_id: number;
  payment_notes: string;
  payment_id: number;
  quickbook_payment_id: number;
  is_updated: number;
  user_id: number;
  come_from: string;
  amount: string;
  posted_by: string;
  payment_date_use_qb: string;
  bill_credit_account_id: string;
  quickbook_bill_id: number;
  credit_account_name: string;
  qb_account_type: string;
  quickbook_credit_account_id: string;
  payment_date: string;
  date_modified: string;
  origin_date_modified: string;
}

interface IBillDetailApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IBillDetailData;
}

interface IBillDetailsNotesData {
  note_id: string;
  company_id: string;
  user_id: string;
  record_id: string;
  module_key: string;
  title: string;
  description: string;
  image: string;
  project_id: string;
  customer_id: string;
  is_deleted: string;
  is_zoho_shared: string;
  date_added: string;
  date_modified: string;
  companycam_commentable_id: string;
  project_name: string;
  added_by: string;
  customer_name: string;
  time_added: string;
  time_modified: string;
  aws_files: Array<IAttachedFile>;
}

interface IBillDetailsTax {
  tax_id: number;
  company_id: number;
  tax_name: string;
  tax_rate: string;
  is_deleted: number;
  date_added: string;
  date_modified: string;
  parent_tax_id: null;
  demo_data: number;
  quickbook_tax_id: string;
  qbc_id: string;
  is_updated: number;
  come_from: string;
  is_group_tax: number;
  group_children: null;
  is_reversible: number;
  tax_rate_backup: null;
}

interface IAttachedFile {
  image_id: string;
  image: string;
  is_image: boolean | string;
  file_path: string;
  name: string;
  notes?: string;
  file_ext?: string;
  annotation_data?: string;
  original_file_path?: string;
  icon?: { iconName?: string };
}

interface IBillDetailsItem {
  item_id: number;
  bill_id: number;
  subject: string;
  quantity: number;
  unit: string;
  unit_cost: string;
  markup: string;
  tax_id: string;
  total_tax: string;
  cost_code_id: number | string;
  total: string;
  description?: string;
  account_id: number;
  bill_item_no: number;
  item_type: number;
  reference_item_id: number;
  assigned_to: number;
  assigned_to_contact_id: number;
  directory_id: number;
  company_id: number;
  is_deleted: number;
  date_added: string;
  date_modified: string;
  parent_item_id: number;
  demo_data: number;
  quickbook_billitem_id: number;
  quickbook_item_id: number;
  qbc_id: string;
  reference_module_item_id: number;
  reference_module_id: number;
  reference_primary_id: number;
  is_feature_item: number;
  feature_type: string;
  markup_amount: string;
  apply_global_tax: string | number;
  is_markup_percentage: number;
  item_category: string;
  sc_retainage: string;
  is_retainage_item: number;
  sc_paid_bill_amt: number;
  sc_paid_bill_percentage: number;
  internal_notes?: string;
  origin: number;
  item_on_database: number;
  item_type_display_name: string;
  item_type_name: string;
  item_type_key: string;
  assignee_type: string;
  assignee_name: string;
  assigned_to_name_only: string;
  assigned_to_company_name: string;
  account_name?: string;
  qb_account_type?: string;
  quickbook_category_id?: string;
  tax_rate: string;
  cost_code_name: string;
  cost_code: string;
  code_id: number;
  quickbook_costcode_id: number;
  invoice_item_id: string;
  origin_date_modified: string;
  total_sc_paid_bill_amt: string;
  total_sc_paid_bill_percentage: number;
  isChecked?: boolean;
  reference_module_name?: string;
  is_discount_item?: number;
}

interface IBillDetailsRes {
  bill_details: IBillDetailData;
}

interface ILumSumItem {
  subject: string;
  item_type: string | number;
  total?: string;
  description?: string;
  internal_notes?: string;
  apply_global_tax?: boolean;
  account?: string;
  submitAction?: string;
  quantity?: number;
  unit_cost?: string;
}

interface IBillAwsFiles {
  image_id: number;
  project_id: string;
  type: string;
  type_id: null;
  file_ext: string;
  file_path: string;
  title: string;
  notes: string;
  is_deleted: boolean;
  date_added: string;
  date_modified: string;
  module_id: number;
  primary_id: number;
  file_tags: string;
  child_item_id: number;
  user_id: number;
  company_id: number;
  file_type: number;
  is_image: number;
  parent_image_id: null;
  demo_data: number;
  height: number;
  width: number;
  size: string;
  mode: string;
  child_item_backup: null;
  child_item_backup12: null;
  upload_from: string;
  image_res: string;
  camera_res: string;
  file_name: string;
  thumb_flag: number;
  large_flag: number;
  static_folder: number;
  folder_id: number;
  refrence_img_id: number;
  quickbook_attachable_id: string;
  is_common_notes: number;
  is_bidding_file: number;
  child_item_name: string;
  file_save_when_send_email: number;
  is_file_shared: number;
  annotation_data: string;
  original_file_path: string;
  is_google_drive_file: number;
  companycam_photo_id: number;
  companycam_creator_name: null;
  original_image_id: number;
  is_project_template: number;
  project_template_id: null;
  id: number;
  customer_id: number;
  project_name: string;
  address1: string;
  address2: string;
  city: string;
  state: string;
  zip: string;
  location_map: null;
  description: string;
  project_type: number;
  project_status: number;
  project_status_key: string;
  site_manager_id: null;
  safety_contact_id: number;
  image: null;
  start_date: string;
  end_date: null;
  prepared_by: number;
  parent_project_id: null;
  completion_date: null;
  warranty_start_date: null;
  contract_amount: null;
  budget_amount: null;
  create_file_folder: number;
  view_in_schedule: number;
  view_in_calendar: number;
  view_in_geofence: number;
  progress: string;
  assigned_to: string;
  task_user_id: string;
  permit_details: null;
  retention: null;
  project_type_key: string;
  notice_to_proceed: null;
  latitude: null;
  longitude: null;
  billed_to: number;
  qb_date: string;
  quickbook_project_id: number;
  qbc_id: string;
  is_updated: number;
  view_in_timecard: number;
  add_wo_total_to_revised_contract_amount: number;
  customer_contact_id: number;
  wbs: null;
  budget_is: string;
  original_retention: null;
  project_color: string;
  geo_image: null;
  geo_radius: string;
  referral_source: number;
  referral_source_key: null;
  stage: number;
  stage_key: null;
  score: null;
  est_sales_date: null;
  company_project_id: string;
  project_prefix: string;
  is_notes_convert: number;
  allow_overbilling: number;
  billing_option: string;
  bid_due_date: null;
  budget_row_from: string;
  show_client_access: string;
  client_access_html: null;
  project_manager_id: number;
  show_client_phone: number;
  show_client_email: number;
  site_manager_contact_id: number;
  safety_additional_contact_id: number;
  client_manager_id: number;
  billed_to_contact: number;
  client_cover_image: null;
  duration: number;
  customer_contract: null;
  client_dashboard_message: null;
  enable_client_access: number;
  associated_estimate_id: number;
  default_tax_rate_id: number;
  allow_online_payment: number;
  companycam_project_id: number;
  companycam_project_updated: number;
  companycam_label_id: number;
  companycam_updated_at: null;
  wip_progress: number;
  wip_notes: string;
  budgeted: string;
  budgeted_labor: string;
  original_contract_amount: string;
  original_budget_amount: string;
  revised_contract_amount: string;
  invoiced: string;
  gross_profit_total: string;
  revised_contract_amount_w_o_tax: string;
  commited: string;
  actual: string;
  actual_labor: string;
  tax_id: number;
  total: string;
  revenue: string;
  cost: string;
  sales_rep: null;
  estimator: number;
  no_tax_contract_amount: string;
  save_as_template: number;
  project_default_material_markup_percent: string;
  project_default_equipment_markup_percent: string;
  project_default_labor_markup_percent: string;
  project_default_sub_contractor_markup_percent: string;
  project_default_other_item_markup_percent: string;
  project_default_undefined_markup_percent: string;
  project_enable_labor_markup_wage_rate: number;
  project_enable_labor_markup_billing_rate: number;
  project_enable_labor_markup_burden_rate: number;
  is_converted: number;
  origin: number;
  markup_preference: string;
  need_adjust_invoice: number;
  is_custom_data_copy: number;
  original_url: string;
}

interface ISupplierDetails {
  user_id: number;
  first_name: string;
  last_name: string;
  address1: string;
  address2: string;
  phone: string;
  cell: string;
  email: string;
  city: string;
  state: string;
  zip: string;
  type: number;
  company_name: string;
  type_name: string;
  type_key: string;
  image: string;
}

interface ICustomFieldFormJson {
  type: string;
  required?: boolean;
  label: string;
  className: string;
  name: string;
  values: IValue[];
}

interface IValue {
  label: string;
  value: string;
  selected?: boolean;
}

interface IBillDetailState {
  loading: boolean;
  response: partial<IBillDetailData>;
}

interface IBillDetailsInitialState {
  billDetail: IBillDetailData;
  itemFilter?: IBillDetailsItem[];
  data?: IBillDetailData;
  isBillDetailLoading: boolean;
  items: IBillDetailsItem[];
  taxAmount: ITaxAmount[];
  defaultTermType: string;
  amountList: BillAccountData[];
  billAccountId: string;
}

interface IBillDetailsTopbarProps {
  setIsSelectProjectOpen: React.Dispatch<React.SetStateAction<boolean>>;
  isBillDetailLoading: boolean;
  selectedProject: IProject | null | undefined;
  loadingStatus: IFieldStatus[];
  sidebarCollapse: boolean | undefined;
  inputValues: IBillDetailData | undefined;
  setInputValues: React.Dispatch<
    React.SetStateAction<IBillDetailData | undefined>
  >;
  fetchBillDetails: () => void;
  handleUpdateField: (data: IBillDetailsFields) => Promise<void>;
  handleChangeFieldStatus: ({
    field,
    status,
    action,
  }: IBillFieldStatus) => void;
}

interface IBillCardProps extends IBillDetailsTopbarProps {
  isReadOnly?: boolean;
  bill_id?: string | undefined;
}

interface IBillDetailsFields {
  [key: string]: string | number | boolean;
}

interface IBillFieldStatus {
  field?: string;
  status: IStatus;
  action?: string;
}

interface IBillItemTableCellRenderer {
  data: Partial<IBillDetailsItem>;
}

interface IAddItem {
  label: string;
  value: string;
  module_id?: number;
  disabled?: boolean;
}

interface ISelectItemsFilterList {
  item_type: string;
  title: string;
}

interface IBillsNotesTabProps {
  projectid?: string | number;
}

interface IBillFilePhotoFormData {
  primary_id: number;
  module_id: number;
  module_key: string;
  directory_type?: number;
  attach_image?: string;
  files?: IFile[];
  project_id?: number | string;
}

interface IBillFilePhotoAddRes extends Omit<IDefaultAPIRes, "data"> {
  data: { aws_files: IFile[] };
}

interface IBillFetchFilePhotoParams {
  module_key?: string;
  record_id: number;
}

interface IbillFilePhotoState {
  billFilePhotos: IFile[];
  isBillFilePhotoLoading: boolean;
}

interface IBillUpdateItemParmas {
  bill_id?: number | undefined;
  is_single_item?: number;
  from?: string;
  items?: IBillDetailsItem[];
}

interface IBillSidebarValues {
  sidebarOpen: boolean;
  project: Partial<Project>;
}

interface IBillAddInitialValues {
  addBillSidebar: IBillSidebarValues;
  termTypes: ICustomData[];
}

interface IBillAddItesmApiRes extends Omit<IDefaultAPIRes, "data"> {
  success: boolean;
  data?: IBillDetailsItem[];
  statusCode?: number;
  updateBillTotalValue: IBillDetailData[];
  updatedItemdata: IBillDetailsItem[];
}

interface IBillListState {
  items: IBillDetailsItem[];
  listParams: Partial<ObjType>;
  isDataFetched: boolean;
  isDataLoading: boolean;
  kanbanItems: IChangeOrderKanbanItem[];
  kanbanSettings?: Partial<IChangeOrderKanbanSettings>;
  kanbanEstimateTypeSelected: string[];
}

interface IBillRefreshParamsReq {
  refresh_type?: string;
}
interface IBillDashApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IBillDashboardApiResponse;
  refresh_type?: string;
}
interface ITaxAmount {
  tax_id: string;
  company_id: string;
  tax_name: string;
  tax_rate: string;
  is_deleted: string;
  date_added: string;
  date_modified: string;
  parent_tax_id: string;
  demo_data: string;
  quickbook_tax_id: string;
  qbc_id: string;
  is_updated: string;
  come_from: string;
  is_group_tax: string;
  group_children: string;
  is_reversible: string;
  tax_rate_backup: string;
  status_name: string;
}
interface ITermTypes {
  name: string | undefined;
  term_id: string;
}

interface ICustomDataAddUpRes extends Omit<IDefaultAPIRes, "data"> {
  data: ICustomData;
  action?: string;
}
