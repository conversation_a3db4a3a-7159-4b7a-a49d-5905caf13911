import {
  escapeHtmlEntities,
  formatAmount,
  Number,
  sanitizeString,
} from "~/helpers/helper";
import { useNavigate } from "@remix-run/react";
import { useTranslation } from "~/hook";
import { memo, useEffect, useMemo, useRef, useState } from "react";

//Zustand
import {
  clearBilldetailObject,
  updateBillableApi,
} from "~/zustand/pages/manage-bills/actions";
import { getGConfig } from "~/zustand";
import { getBillFilter } from "../../zustand/filter/slice";

//Redux
import { getBillListApi } from "../../redux/action/billListAction";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
//molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { DynamicTable } from "~/shared/components/molecules/dynamicTable";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";

//other
import { STATUS_CODE } from "~/shared/constants/index";
import isEmpty from "lodash/isEmpty";
import {
  GridReadyEvent,
  IServerSideDatasource,
  IServerSideGetRowsParams,
} from "ag-grid-community";
import { SortChangedEvent } from "ag-grid-community";
import BillTableDropDownItems from "./BillTableDropDownItems";
import { BillDetailActionsTab } from "../tab/BillDetailActionsTab";
import {
  copyBillApi,
  generateBillIntoInvoice,
} from "../../redux/action/billDetailAction";
import PostPayment from "../tab/details/PostPayment";
import { useAppBillSelector } from "../../redux/store";

let timeout: NodeJS.Timeout;
const limit = 30;
const BillList = ({
  moduleAccess,
  setAddBillSidebarOpen = () => {},
  customRef,
}: BillsTableProps) => {
  const { _t } = useTranslation();

  const [isShareOpen, setIsShareOpen] = useState(false);
  const { module_singular_name }: GConfig = getGConfig();
  const navigate = useNavigate();
  const isInitialized = useRef<boolean>(false);
  const filter = getBillFilter();
  const { searchValue }: IBillDashState = useAppBillSelector(
    (state) => state.dashboardData
  );
  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);
  const [isCopyBillConfirmOpen, setIsCopyBillConfirmOpen] =
    useState<boolean>(false);
  const [isCopyBillLoading, setIsCopyBillLoading] = useState<boolean>(false);
  const [isViewEmailModalOpen, setIsViewEmailModalOpen] =
    useState<boolean>(false);
  const [billData, setBillData] = useState<IBillListApiResponse["data"][0]>();
  const [paramsData, setParamsData] =
    useState<AgGridCellRenderParams<IBillListApiResponseDataRecord>>();
  const { formatter } = useCurrencyFormatter();
  const gridRef = useRef<ExtendedAgGridReact | null>(null);
  const billTab = filter?.tab;
  const [confirmDialogOpen, setConfirmDialogOpen] = useState<boolean>(false);
  const [gridRowParams, setGridRowParams] =
    useState<IGridParamsBillList | null>(null);
  const [confirmArchiveDialogOpen, setConfirmArchiveDialogOpen] =
    useState<boolean>(false);
  const [shareLink, setShareLink] = useState<string>("");
  const [billLoadingStatus, setBillLoadingStatus] = useState<{
    [key: string]: boolean;
  }>({});
  const [itemBox, setItemBox] = useState<boolean>(false);

  const gConfig: GConfig = getGConfig();
  const noData = useMemo(
    () =>
      gridRef &&
      gridRef.current &&
      gridRef.current.api &&
      (gridRef.current.api.getDisplayedRowCount() || 0) <= 1,
    [gridRef?.current?.api?.getDisplayedRowCount()]
  );

  const handleChange = async (
    params: IBillDataResponseAPI,
    fieldName?: keyof IBillListApiResponseDataRecord
  ) => {
    const { data }: IBillDataResponseAPI = params;
    if (!data || !data.bill_id || !fieldName) return;
    const isChecked =
      String(data[fieldName]) === "1" ||
      (typeof data[fieldName] === "boolean" && data[fieldName])
        ? false
        : true;
    fieldName === "is_billable" &&
      setBillLoadingStatus({
        [data.bill_id]: true,
      });
    const updateRes = (await updateBillableApi({
      bill_id: data.bill_id,
      module_id: gConfig?.module_id,
      custom_bill_id: data.custom_bill_id || data.company_bill_id,
      supplier_id: data.supplier_id,
      ...(fieldName && { [fieldName]: isChecked }),
    })) as IBillUpdateApiRes;

    if (updateRes?.success) {
      gridRef.current?.api?.getDisplayedRowAtIndex(params.rowIndex)?.setData({
        ...data,
        ...(fieldName ? { [fieldName]: isChecked } : { is_shared: isChecked }),
      });
    } else {
      notification.error({
        description: updateRes.message,
      });
    }
    fieldName === "is_billable" && setBillLoadingStatus({});
  };

  const handleCopyBill = async (bill_id: string | number) => {
    setIsCopyBillLoading(true);
    try {
      const response = await copyBillApi({
        id: bill_id,
      });

      if (response) {
        const billId = response?.data?.billId as string;
        if (billId) {
          const url = `/manage-bills/${billId}`;
          if (window?.ENV?.PAGE_IS_IFRAME) {
            window.parent.location.href = url;
          } else {
            navigate(url);
          }
          setIsCopyBillLoading(false);
          setIsCopyBillConfirmOpen(false);
        } else {
          notification.error({
            description:
              response?.message || "An error occurred while copying the bill.",
          });
        }
      }
    } catch (error) {
      console.error("Error copying bill:", error);
      notification.error({
        description: "Failed to copy the bill. Please try again.",
      });
    }
  };

  const GenerateInvoice = async (data: IBillListApiResponseDataRecord) => {
    if (data?.project_id) {
      try {
        // Call the API and await its result
        const response = await generateBillIntoInvoice({
          bill_id: data?.bill_id,
        });
        if (response?.data?.invoice_id) {
          const url = `/manage-invoice/${response?.data.invoice_id}`;
          if (window && window.ENV && window.ENV.PAGE_IS_IFRAME) {
            if (window.parent && window.parent.location) {
              window.parent.location.href = url;
            }
          } else {
            navigate(url);
          }
        }
      } catch (error) {
        notification.error({
          description: "An error occurred while generating the invoice.",
        });
        console.error("Generate Invoice Error:", error);
      }
    } else {
      notification.error({
        description: "Please select project.",
      });
    }
  };

  const columnDefs = useMemo(() => {
    return [
      {
        headerName: _t("Bill") + " #",
        field: "company_bill_id",
        minWidth: 150,
        maxWidth: 150,
        suppressMovable: false,
        suppressMenu: true,
        sortable: true,
        cellRenderer: ({ data: { company_bill_id } }: IBillDataResponseAPI) => {
          return company_bill_id ? (
            <Tooltip title={company_bill_id}>
              <Typography className="table-tooltip-text">
                {company_bill_id}
              </Typography>
            </Tooltip>
          ) : (
            <div>-</div>
          );
        },
      },
      {
        headerName: _t("Due Date"),
        field: "due_date",
        minWidth: 135,
        maxWidth: 135,
        suppressMovable: false,
        suppressMenu: true,
        sortable: true,
        cellRenderer: ({ data: { due_date } }: IBillDataResponseAPI) => {
          return <DateTimeCard format="date" date={due_date} />;
        },
      },
      {
        headerName: _t("Vendor"),
        field: "supplier_company_name",
        minWidth: 150,
        flex: 1,
        sortable: true,
        resizable: true,
        suppressMovable: false,
        suppressMenu: true,
        cellRenderer: ({
          data: { supplier_company_name },
        }: IBillDataResponseAPI) => {
          const companyName = HTMLEntities.decode(
            sanitizeString(supplier_company_name)
          );
          return companyName ? (
            <Tooltip title={companyName}>
              <Typography className="table-tooltip-text">
                {companyName}
              </Typography>
            </Tooltip>
          ) : (
            <div>-</div>
          );
        },
      },
      {
        headerName: _t("Project"),
        field: "project_name",
        minWidth: 150,
        flex: 1,
        sortable: true,
        resizable: true,
        suppressMovable: false,
        suppressMenu: true,
        cellRenderer: ({ data: { project_name } }: IBillDataResponseAPI) => {
          const projectName = HTMLEntities.decode(sanitizeString(project_name));
          return projectName ? (
            <Tooltip title={projectName}>
              <Typography className="table-tooltip-text">
                {projectName}
              </Typography>
            </Tooltip>
          ) : (
            <div>-</div>
          );
        },
      },

      {
        headerName: _t("Description"),
        field: "notes",
        minWidth: 150,
        flex: 1,
        sortable: true,
        resizable: true,
        suppressMovable: false,
        suppressMenu: true,
        cellRenderer: ({ data: { notes } }: IBillDataResponseAPI) => {
          const notesStr = notes?.toString();
          return notesStr ? (
            <Tooltip title={notesStr}>
              <Typography className="table-tooltip-text">{notesStr}</Typography>
            </Tooltip>
          ) : (
            <div>-</div>
          );
        },
      },

      {
        headerName: _t("Billable"),
        field: "is_billable",
        maxWidth: 92,
        minWidth: 92,
        suppressMenu: true,
        sortable: true,
        suppressMovable: false,
        headerClass: "ag-header-center",
        cellRenderer: (params: IBillDataResponseAPI) => {
          const { data } = params;
          return (
            <Tooltip
              title={
                (typeof data.is_billable === "boolean"
                  ? data.is_billable
                  : data.is_billable?.toString() === "1") &&
                !(data && data.bill_id
                  ? billLoadingStatus[data.bill_id]
                  : false) &&
                "This Item is Billable to the Customer"
              }
            >
              <div className="flex items-center gap-2 justify-center">
                <CustomCheckBox
                  className="gap-0"
                  name="is_billable"
                  onChange={() => {
                    handleChange(params, "is_billable");
                  }}
                  checked={
                    typeof data.is_billable === "boolean"
                      ? data.is_billable
                      : data.is_billable?.toString() === "1"
                  }
                  disabled={
                    (data && data.bill_id && billLoadingStatus[data.bill_id]) ||
                    !(
                      moduleAccess === "full_access" ||
                      moduleAccess === "own_data_access"
                    )
                  }
                  loadingProps={{
                    isLoading:
                      data && data.bill_id
                        ? billLoadingStatus[data.bill_id]
                        : false,
                    className: "bg-white",
                  }}
                />
              </div>
            </Tooltip>
          );
        },
      },

      {
        headerName: _t("Total"),
        field: "total",
        minWidth: 150,
        maxWidth: 150,
        sortable: true,
        suppressMenu: true,
        suppressMovable: false,
        headerClass: "ag-header-right",
        cellClass: "ag-cell-right",
        cellRenderer: ({ data: { total = "0" } }: IBillDataResponseAPI) => {
          const totalValue = parseFloat(total) / 100;
          const totalFor = formatter(
            formatAmount(totalValue, { isDashboard: true })
          ).value_with_symbol;
          return totalFor ? (
            <div className="text-right">
              <Tooltip title={totalFor}>
                <Typography className="table-tooltip-text">
                  {totalFor}
                </Typography>
              </Tooltip>
            </div>
          ) : (
            <div>-</div>
          );
        },
      },
      {
        headerName: _t("Balance Due"),
        field: "due_balance",
        maxWidth: 150,
        minWidth: 150,
        sortable: true,
        suppressMenu: true,
        suppressMovable: false,
        headerClass: "ag-header-right",
        cellClass: "ag-cell-right",
        cellRenderer: ({ data: { due_balance } }: IBillDataResponseAPI) => {
          const dueBalanceFor =
            Number(due_balance) <= 0
              ? formatter((+0)?.toFixed(2)).value_with_symbol
              : formatter(((Number(due_balance) || 0) / 100).toFixed(2))
                  .value_with_symbol;
          return dueBalanceFor ? (
            <div className="text-right">
              <Tooltip title={dueBalanceFor}>
                <Typography className="table-tooltip-text">
                  {dueBalanceFor}
                </Typography>
              </Tooltip>
            </div>
          ) : (
            <div>-</div>
          );
        },
      },

      {
        headerName: "",
        field: "",
        sortable: true,
        maxWidth: 50,
        minWidth: 50,
        suppressMenu: true,
        cellClass: "!cursor-auto",
        cellRenderer: (
          params: AgGridCellRenderParams<IBillListApiResponseDataRecord>
        ) => {
          if (
            params?.data &&
            (moduleAccess === "full_access" ||
              moduleAccess === "own_data_access")
          ) {
            return (
              <BillDetailActionsTab
                iconClassName="text-primary-900/80 group-hover/buttonHover:text-primary-900"
                buttonClass="m-0 hover:!bg-[#0000000f]"
                onClick={(value) => {
                  if (value === "change_status") {
                    setBillData(params?.data);
                    setConfirmArchiveDialogOpen(true);
                  } else if (value === "delete") {
                    setBillData(params?.data);
                    setConfirmDialogOpen(true);
                  } else if (value === "share_internal_link") {
                    setBillData(params?.data);
                    setIsShareOpen(true);
                  } else if (value === "view_pdf") {
                    setBillData(params?.data);
                    setIsViewEmailModalOpen(true);
                  } else if (value === "Post Payment") {
                    setParamsData(params);
                    setBillData(params?.data);
                    setItemBox(true);
                  } else if (value === "Generate an Invoice") {
                    GenerateInvoice(params?.data);
                  } else if (value === "Copy bill") {
                    setBillData(params?.data);
                    setIsCopyBillConfirmOpen(true);
                  }
                }}
                isArchive={params?.data?.is_deleted?.toString() === "0"}
                dueBalance={params?.data?.due_balance}
              />
            );
          }
        },
      },
    ];
  }, [_t, gridRef?.current, noData]);

  const dataSource: IServerSideDatasource = useMemo(
    () => ({
      rowCount: undefined,
      getRows: async (params: IServerSideGetRowsParams) => {
        const request = params?.request;

        const startRow = request.startRow ?? 0;
        const endRow = request.endRow ?? 20;
        const currentPageNumber = Math.floor(endRow / (endRow - startRow));

        let dataParams: IBillListParmas = {
          limit: limit,
          start: (currentPageNumber - 1) * limit,
        };

        const sortModel = request?.sortModel;

        if (sortModel.length) {
          const { colId, sort } = sortModel[0];
          dataParams = {
            ...dataParams,
            order_by_name: colId,
            order_by_dir: sort.toUpperCase(),
          };
        }

        setGridRowParams({
          changeGridParams: dataParams,
          gridParams: params,
        });
      },
    }),
    []
  );

  const fetchBillList = async () => {
    let gridData: {
      rowCount?: number;
      rowData: IBillListApiResponseDataRecord[];
    } = {
      rowData: [],
    };

    if (!gridRowParams) {
      return;
    }
    const { changeGridParams, gridParams }: IGridParamsBillList = gridRowParams;

    if (isEmpty(changeGridParams) || isEmpty(gridParams)) {
      return;
    }

    const tempFil: ITempFil = {
      status: STATUS_CODE.ACTIVE,
      tab: "all",
    };

    if (filter?.start_date) {
      tempFil.start_date = filter.start_date;
    }
    if (filter?.end_date) {
      tempFil.end_date = filter.end_date;
    }

    if (filter?.directory) {
      tempFil.directory = filter.directory;
    }
    if (filter?.project) {
      tempFil.project = filter.project;
    }

    if (filter?.status) {
      tempFil.status = filter.status;
    }

    if (filter?.tab) {
      tempFil.tab = filter.tab;
    }

    if (!filter?.status) {
      delete tempFil.status;
    }

    let dataParams: IBillListParmas = {
      ...changeGridParams,
      filter: filter,
      status: String(billTab) || "",
      tab: String(billTab),
    };

    if (searchValue != "") {
      dataParams = {
        ...dataParams,
        search: escapeHtmlEntities(searchValue || ""),
      };
    }

    try {
      const resData = (await getBillListApi(dataParams)) as IBillListObject;
      const rowCount = gridParams?.api?.getDisplayedRowCount() ?? 0;
      if (resData?.data?.data?.length < length) {
        gridData = {
          ...gridData,
          rowCount: rowCount + (resData?.data?.data?.length ?? 0) - 1,
        };
      }
      gridData = { ...gridData, rowData: resData?.data?.data ?? [] };
      gridParams?.success(gridData);
      if (
        (!resData?.success || gridData.rowData.length <= 0) &&
        dataParams?.start === 0
      ) {
        gridParams?.api.showNoRowsOverlay();
      } else if (resData?.success && gridData.rowData.length > 0) {
        gridParams?.api.hideOverlay();
      }
    } catch (error) {
      gridParams?.success({ rowCount: 0, rowData: [] });
      gridParams?.api.showNoRowsOverlay();
      gridParams?.fail();
    }
  };

  useEffect(() => {
    if (gridRowParams?.changeGridParams) {
      if (timeout) {
        clearTimeout(timeout);
      }
      timeout = setTimeout(() => {
        fetchBillList();
      }, 500);
    }
  }, [gridRowParams?.changeGridParams]);

  useEffect(() => {
    if (gridRef && gridRef.current && isInitialized.current) {
      refreshAgGrid();
    } else {
      isInitialized.current = true;
    }
  }, [searchValue, filter, billTab]);

  const refreshAgGrid = () => {
    const gridParams = gridRowParams?.gridParams;
    if (gridParams) {
      gridParams.api.setServerSideDatasource({ getRows: () => {} });
      gridParams.api.setServerSideDatasource(dataSource);
    }
  };

  const onGridReady = (gridParams: GridReadyEvent) => {
    gridParams?.api?.setServerSideDatasource(dataSource);
    setTimeout(() => {
      gridParams.api.redrawRows();
    }, 50);
  };

  const onSortChanged = async (params: SortChangedEvent) => {
    params.api.setServerSideDatasource(dataSource);
  };

  useEffect(() => {
    if (customRef) {
      customRef.current = {
        refreshAgGrid,
        // Add other methods or properties here
      };
    }

    // Clean up the ref on unmount
    return () => {
      if (customRef) {
        customRef.current.refreshAgGrid = () => {};
      }
    };
  }, [refreshAgGrid, customRef]);

  return (
    <>
      <div className="h-[calc(100dvh-292px)] ag-grid-cell-pointer ag-theme-alpine list-view-table">
        <BillTableDropDownItems
          confirmArchiveDialogOpen={confirmArchiveDialogOpen}
          confirmDialogOpen={confirmDialogOpen}
          callApiAgain={() => {
            refreshAgGrid();
            setConfirmArchiveDialogOpen(false);
            setConfirmDialogOpen(false);
          }}
          isDeleted={billData?.is_deleted?.toString()}
          primary_key={billData?.bill_id?.toString() ?? ""}
          onClose={(value: string) => {
            if (value === "change_status") {
              setConfirmArchiveDialogOpen(false);
              setBillData({} as IBillListApiResponse["data"][0]);
            } else if (value === "delete") {
              setConfirmDialogOpen(false);
              setBillData({} as IBillListApiResponse["data"][0]);
            }
          }}
          isViewEmailModalOpen={isViewEmailModalOpen}
          setIsViewEmailModalOpen={setIsViewEmailModalOpen}
          data={billData}
          billId={billData?.bill_id?.toString()}
          setBillData={setBillData}
          isShareOpen={isShareOpen}
          setIsSendEmailSidebarOpen={setIsSendEmailSidebarOpen}
          shareLink={shareLink}
          setShareLink={setShareLink}
          setIsShareOpen={setIsShareOpen}
          refreshAgGrid={refreshAgGrid}
        />
        <DynamicTable
          ref={gridRef}
          columnDefs={columnDefs}
          onGridReady={onGridReady}
          onSortChanged={onSortChanged}
          enableOpenInNewTab={window.ENV.ENABLE_ALL_CLICK}
          generateOpenInNewTabUrl={(data: { bill_id?: number }) =>
            `/manage-bills/${data?.bill_id}`
          }
          clearDataOnNavigation={clearBilldetailObject}
          restrictOpenInNewTabFields={["is_billable"]}
          noRowsOverlayComponent={() => (
            <NoRecords
              rootClassName="w-full max-w-[280px]"
              image={`${window.ENV.CDN_URL}assets/images/create-record-directory.svg`}
              imageWSize="280"
              imageHSize="227"
              text={
                moduleAccess === "full_access" ||
                moduleAccess === "own_data_access" ? (
                  <div>
                    <Typography
                      onClick={() => {
                        if (setAddBillSidebarOpen) {
                          setAddBillSidebarOpen(true);
                        }
                      }}
                      className="sm:text-base text-xs underline underline-offset-1 text-black font-bold cursor-pointer"
                    >
                      {_t("Click here")}
                    </Typography>
                    <Typography className="sm:text-base text-xs text-black font-semibold">
                      {_t(" to Create a New Record")}
                    </Typography>
                  </div>
                ) : (
                  <Typography className="sm:text-base text-xs text-black font-semibold">
                    {_t("No Record Found")}
                  </Typography>
                )
              }
            />
          )}
        />
      </div>
      {itemBox && (
        <PostPayment
          billDetail={billData}
          paramsData={paramsData}
          setItemBox={setItemBox}
          itemBox={itemBox}
        />
      )}

      {isCopyBillConfirmOpen && (
        <ConfirmModal
          isOpen={isCopyBillConfirmOpen}
          modaltitle={_t("Copy")}
          description={_t(
            `Are you sure you want to generate a copy of this ${module_singular_name}?`
          )}
          modalIcon="fa-regular fa-clone"
          isLoading={isCopyBillLoading}
          onAccept={() => {
            handleCopyBill(billData?.bill_id);
          }}
          onDecline={() => {
            setIsCopyBillConfirmOpen(false);
          }}
          onCloseModal={() => {
            setIsCopyBillConfirmOpen(false);
          }}
        />
      )}
    </>
  );
};

export default memo(BillList);
