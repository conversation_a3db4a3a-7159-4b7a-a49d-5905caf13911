import { useFormik } from "formik";
import * as Yup from "yup";
// Atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { SelectField } from "~/shared/components/molecules/selectField";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Hook
import { useTranslation } from "~/hook";
import { useEffect, useState } from "react";
import { useAppBillDispatch, useAppBillSelector } from "../../../redux/store";
import { useDateFormatter } from "~/hook/date-formatter-d";
import { displayDateFormat } from "~/shared/utils/helper/defaultDateFormat";

import { getGSettings, getGConfig, getGModuleByKey } from "~/zustand";

import {
  filterOptionBySubstring,
  onKeyDownCurrency,
} from "~/shared/utils/helper/common";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";
import { Number, sanitizeString } from "~/helpers/helper";

import { getBillType } from "~/zustand/pages/manage-bills/store";
import {
  getAccountDetails,
  getBillDetailAPI,
  setPostPayment,
  updatePaymentAPI,
} from "../../../redux/action/billDetailAction";
import { getCostCodeList } from "~/redux/action/getCostCodeListAction";
import {
  updatebillDetail,
  updateBillDetailItem,
} from "../../../redux/slices/billDetailSlice";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { defaultConfig } from "~/data";

export const formAddPostPaymentSchema = () => {
  const dateFormatter = useDateFormatter();

  const initValues: Partial<IPostPayment> = {
    payment_date: dateFormatter({
      format: CFConfig.luxon_date_format,
      zone: true,
    }),
    amount: 0,
    payment_notes: "",
  };

  let validateSchema = null;

  validateSchema = Yup.object().shape({
    payment_date: Yup.string().required("This field is required."),
    amount: Yup.number().required("This field is required."),
  });
  return {
    initValues,
    validateSchema,
  };
};

const PostPayment = ({
  itemBox,
  setItemBox,
  billDetail,
  isViewEdit,
  isEdit = false,
  isView = false,
  paramsData,
}: Partial<PostPaymentProps>) => {
  const { _t } = useTranslation();
  const dateFormat = useDateFormatter();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { user_id = 0, company_id = 0 } = user || {};
  const [bill_credit_account_id, setBillCreditAccountId] = useState<string>("");
  const setting: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();
  const { amountList }: IBillDetailsInitialState = useAppBillSelector(
    (state) => state.billDetails
  );
  const dispatch = useAppBillDispatch();
  const billDeatilType: IBillDetailType[] = getBillType();
  const gConfig: GConfig = getGConfig();
  const [accountDetails, setAccountDetails] =
    useState<Array<Partial<IBillType>>>();
  const gSettings: GSettings = getGSettings();
  const { initValues, validateSchema } = formAddPostPaymentSchema();
  const [postPaymentSaveLoader, setPostpaymentSaveLoader] =
    useState<boolean>(false);
  const [billType, setBillType] = useState<IBillDetailType[]>();
  const [costCodes, setCostCode] = useState<ICostCode[]>([]);
  const { inputFormatter, unformatted } = useCurrencyFormatter();
  const [isFocusAmountVal, setIsFocusAmountVal] = useState(false);
  const [isSubmit, setIsSubmit] = useState<boolean>(false);

  const amountZeroScehma = Yup.object().shape({
    payment_date: Yup.string().required("This field is required."),
    amount: Yup.number()
      .notOneOf([0], "Amount must be greater than 0.")
      .required("This field is required."),
  });
  const formik = useFormik({
    initialValues: {
      ...initValues,
    } as Partial<IPostPayment>,
    validationSchema: isSubmit ? amountZeroScehma : validateSchema,
    enableReinitialize: true,
    onSubmit: () => addPostPayment(),
  });

  const { values, setFieldValue, errors, setValues, handleSubmit } = formik;

  const paymentModule: GModule | undefined = getGModuleByKey(
    defaultConfig.payment_module
  );

  useEffect(() => {
    if (amountList) {
      setAccountDetails(
        amountList?.filter(
          (item: IBillDetailType) =>
            item &&
            (item.item_type === gConfig?.bill_type_id?.toString() ||
              (item.item_type === getCustomDateStaticType.toString() &&
                Boolean(Number(gSettings?.quickbook_desktop_sync))) ||
              (item.item_type == 0 && item.qb_account_type))
        )
      );
    }
  }, [amountList]);

  const getCustomDateStaticType = 170;
  useEffect(() => {
    setBillType(
      billDeatilType?.filter(
        (item: IBillDetailType) =>
          item &&
          (item.item_type === gConfig?.bill_type_id?.toString() ||
            (item.item_type === getCustomDateStaticType.toString() &&
              Boolean(Number(gSettings?.quickbook_desktop_sync))) ||
            (item.item_type == 0 && item.qb_account_type))
      )
    );
  }, [billDeatilType, gSettings?.quickbook_desktop_sync]);

  useEffect(() => {
    const dueBalance =
      (Number(billDetail?.due_balance) || Number(billDetail?.amount) || 0) /
        100 || 0;
    const formattedAmount = isViewEdit
      ? Number(billDetail?.amount) / 100
      : dueBalance.toFixed(2);
    formik.setFieldValue("amount", Number(formattedAmount));
    formik.setFieldValue("payment_notes", billDetail?.payment_notes);
    formik.setFieldValue(
      "payment_date",
      billDetail?.payment_date ||
        dateFormat({
          format: CFConfig.luxon_date_format,
          zone: true,
        })
    );
    setBillCreditAccountId(billDetail?.bill_credit_account_id);
  }, []);

  const addPostPayment = async () => {
    try {
      setIsSubmit(true);
      setPostpaymentSaveLoader(true);
      const date: string = dateFormat({
        date: values.payment_date,
        format: "yyyy-MM-dd",
      });

      const isValid = await formik.validateForm();
      if (!formik.isValid || Object.keys(isValid).length > 0) {
        setPostpaymentSaveLoader(false);
        return;
      }

      if (!date) {
        console.error("Invalid payment_date");
        return;
      }
      const notes = (values.payment_notes || "").toString().trim();

      const form = {
        user_id,
        company_id,
        bill_id: billDetail?.bill_id,
        payment_date: date,
        amount: Number(values.amount) * 100,
        ...(notes ? { payment_notes: notes } : {}),
        ...(bill_credit_account_id
          ? { bill_credit_account_id: bill_credit_account_id }
          : null),
      };
      if (isEdit) {
        const form = {
          user_id,
          bill_id: billDetail?.bill_id,
          payment_id: billDetail?.payment_id,
          payment_date: date,
          amount: Number(values.amount) * 100,
          ...(notes ? { payment_notes: notes } : {}),
          ...(bill_credit_account_id
            ? { bill_credit_account_id: bill_credit_account_id }
            : null),
        };
        try {
          const response = await updatePaymentAPI(form);
          if (response?.success) {
            setPostpaymentSaveLoader(false);
            await dispatch(
              updateBillDetailItem({
                bill_id: billDetail?.bill_id,
                user_id: form?.user_id,
                updatedData: {
                  amount: Number(form?.amount),
                  payment_id: form?.payment_id,
                  payment_date: form?.payment_date,
                  payment_notes: form?.payment_notes,
                },
              })
            );
          } else {
            setPostpaymentSaveLoader(false);
          }
        } catch (error) {
          setPostpaymentSaveLoader(false);
          console.error("Error updating payment:", error);
        }
      } else {
        const response = await setPostPayment(form);
        if (response?.success) {
          paramsData?.node?.setDataValue(
            "due_balance",
            response?.getBillModuleDetailData?.due_balance
          );
          setPostpaymentSaveLoader(false);

          await dispatch(
            updateBillDetailItem({
              bill_id: billDetail?.bill_id,
              user_id: form?.user_id,
              updatedData: {
                amount: Number(form?.amount),
                payment_id: form?.payment_id,
                payment_date: form?.payment_date,
                payment_notes: form?.payment_notes,
              },
            })
          );
        } else {
          setPostpaymentSaveLoader(false);
        }
      }

      resetForm();
    } catch (error) {
      setPostpaymentSaveLoader(false);
      console.error("Error in addPostPayment:", error);
    }
  };

  const getCostCode = async () => {
    const costCodeList = (await getCostCodeList({
      daily_equipment_cost_code: 1,
    })) as ICostCodeList;
    setCostCode(costCodeList?.data);
  };

  const resetForm = async () => {
    formik.setFieldValue("payment_notes", "");

    formik.setFieldValue("amount", "");

    setBillCreditAccountId("");

    setItemBox(!itemBox);

    setTimeout(async () => {
      const updatedBillDetails = await getBillDetailAPI({
        bill_id: billDetail?.bill_id || "",
      });

      dispatch(updatebillDetail(updatedBillDetails?.data));
      getCostCode();
    }, 300);
  };

  return (
    <>
      <Drawer
        open={itemBox}
        rootClassName="drawer-open"
        width={718}
        push={false}
        classNames={{
          body: "!p-0 !overflow-hidden ",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-envelope-open-dollar"
              />
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {_t(`Post ${paymentModule?.module_name}`)}
            </Header>
          </div>
        }
        closeIcon={
          <CloseButton
            onClick={() => {
              setItemBox(!itemBox);
            }}
          />
        }
      >
        <form className="py-4" onSubmit={handleSubmit} noValidate>
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <SidebarCardBorder addGap={true}>
              <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                <div className="w-full">
                  <DatePickerField
                    label={_t("Payment Date")}
                    labelPlacement="top"
                    name="payment_date"
                    isRequired={true}
                    allowClear={true}
                    placeholder=""
                    value={displayDateFormat(
                      values.payment_date?.toString().trim(),
                      CFConfig.day_js_date_format
                    )}
                    onChange={(value) => {
                      if (value) {
                        setFieldValue(
                          "payment_date",
                          value.format(CFConfig.day_js_date_format)
                        );
                      } else {
                        setFieldValue("payment_date", undefined);
                      }
                    }}
                    format={CFConfig.day_js_date_format}
                    errorMessage={
                      errors.payment_date ? errors.payment_date : ""
                    }
                    disabled={isView || postPaymentSaveLoader}
                  />
                </div>
                <div className="w-full">
                  <InputNumberField
                    label={_t("Amount")}
                    isRequired={true}
                    name="amount"
                    readOnly={isView}
                    labelClass="dark:text-white/90"
                    placeholder={inputFormatter("0.00").value}
                    labelPlacement="top"
                    disabled={postPaymentSaveLoader}
                    value={values.amount}
                    formatter={(value, info) => {
                      const inputValue = info.input.trim();
                      const valueToFormat =
                        inputValue !== "0" && inputValue.length > 0
                          ? unformatted(inputValue)
                          : String(value);

                      return isFocusAmountVal
                        ? inputFormatter(valueToFormat).value
                        : !!value
                        ? inputFormatter(Number(value)?.toFixed(2)).value
                        : "";
                    }}
                    onChange={(e) => {
                      const cleaned =
                        e === "" || e === null || isNaN(Number(e))
                          ? undefined
                          : Number(e);
                      formik.setFieldValue("amount", cleaned);
                    }}
                    parser={(value) => {
                      const inputValue = value
                        ? unformatted(value.toString())
                        : "";
                      return inputValue;
                    }}
                    onFocus={() => {
                      setIsFocusAmountVal(true);
                    }}
                    onBlur={(event) => {
                      if (
                        event.target.value &&
                        Number(event.target.value) === 0
                      ) {
                        formik.setErrors({
                          ...formik.errors,
                          amount: "Amount must be greater than 0.",
                        });
                      } else if (formik.errors.amount) {
                        formik.setErrors({
                          ...formik.errors,
                          amount: formik.errors.amount ?? undefined,
                        });
                      } else {
                        formik.setErrors({
                          ...formik.errors,
                          amount: undefined,
                        });
                      }
                      setIsFocusAmountVal(false);
                    }}
                    onKeyDown={(event) =>
                      onKeyDownCurrency(event, {
                        integerDigits: 10,
                        decimalDigits: 2,
                        unformatted,
                        allowNegative: false,
                        decimalSeparator: inputFormatter().decimal_separator,
                      })
                    }
                    prefix={inputFormatter().currency_symbol}
                    errorMessage={errors.amount ? errors.amount : ""}
                  />
                </div>
              </div>

              {(Boolean(Number(setting?.quickbook_sync)) ||
                Boolean(Number(setting?.quickbook_desktop_sync))) && (
                <div className="w-full">
                  <SelectField
                    label={_t("Account")}
                    name="bill_credit_account_id"
                    labelPlacement={"top"}
                    showSearch
                    allowClear
                    filterOption={(input, option) =>
                      filterOptionBySubstring(input, option?.label as string)
                    }
                    value={
                      !!bill_credit_account_id && bill_credit_account_id !== "0"
                        ? bill_credit_account_id
                        : ""
                    }
                    onChange={(
                      value: string | string[],
                      options?: BaseOptionType | DefaultOptionType
                    ) => setBillCreditAccountId(value)}
                    disabled={postPaymentSaveLoader}
                    options={
                      accountDetails && accountDetails.length > 0
                        ? accountDetails.map(
                            (type: { name?: string; item_id?: string }) => ({
                              label: HTMLEntities.decode(
                                sanitizeString(type?.name || "")
                              ),
                              value: type?.item_id?.toString() || "", // Ensure value is always a string
                            })
                          )
                        : []
                    }
                  />
                </div>
              )}

              <div className="w-full">
                <TextAreaField
                  label={_t("Payment Notes")}
                  name="notes"
                  placeholder=""
                  labelClass="dark:text-white/90"
                  value={values.payment_notes}
                  disabled={postPaymentSaveLoader}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => {
                    formik.setFieldValue("payment_notes", e.target.value);
                  }}
                  readOnly={isView}
                />
              </div>
            </SidebarCardBorder>
          </div>
          {!isView && (
            <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
              <PrimaryButton
                htmlType="submit"
                buttonText={_t("Confirm")}
                isLoading={postPaymentSaveLoader}
                disabled={postPaymentSaveLoader}
                onClick={() => setIsSubmit(true)}
              />
            </div>
          )}
          {isView && (
            <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
              <PrimaryButton
                className="justify-center primary-btn w-full"
                onClick={() => {
                  setItemBox(!itemBox);
                }}
                buttonText={_t("Close")}
              />
            </div>
          )}
        </form>
      </Drawer>
    </>
  );
};

export default PostPayment;
