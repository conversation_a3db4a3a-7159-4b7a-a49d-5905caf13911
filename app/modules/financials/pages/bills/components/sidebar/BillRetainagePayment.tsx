import { useEffect, useState } from "react";
import { useTranslation } from "~/hook";
// Atoms
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { CommonModal } from "~/shared/components/molecules/commonModal";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { getGTypes } from "~/zustand";
import { useParams } from "@remix-run/react";
import { floatNumberRegex } from "../../utils/common";
import { getRetainageDetails } from "../../redux/action/billDetailAction";
import { onKeyDownCurrency } from "~/shared/utils/helper/common";

const BillRetainagePayment = ({
  addRetainagePayment,
  setaddRetainagePayment,
  onSubmit,
}: IBillRetainageItemProps) => {
  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const { formatter, inputFormatter, unformatted } = useCurrencyFormatter();

  const { singular_name } = currentModule || {};
  const { _t } = useTranslation();
  const itemType: GType[] = getGTypes();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const params = useParams();
  const billId = params && params.id ? params.id : "";
  const [retainageData, setRetainageData] = useState<IBillRetainageInfo>({});
  const [retainageValue, setRetainageValue] = useState<number | string>("");

  const discountItem = itemType?.find(
    (item: Partial<GType>) =>
      item?.type === "company_items" && item.key === "item_other"
  );

  const [formData, setFormData] = useState<IBillDetailsItem>({
    item_type: discountItem?.type_id || "",
    subject: "Retainage Payment",
    is_discount_item: 1,
    quantity: 1,
  });

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setErrorMessage(null);
    if (!formData.item_type || !formData.unit_cost) {
      setErrorMessage("This field is required.");
      return;
    }
    setIsLoading(true);
    onSubmit(
      [
        {
          ...formData,
          unit_cost: (Number(formData.unit_cost) * 100).toString(),
          total: (Number(formData.unit_cost) * 100).toString(),
          markup: "0",
          is_markup_percentage: 0,
          is_feature_item: 1,
          feature_type: "retainage_payment",
          bill_id: Number(billId),
          subject: "Retainage Payment",
          quantity: 1,
          item_category: "category",
          item_type_key: "item_other",
          item_type_display_name: "Other",
        },
      ],
      {
        addFromResponse: true,
      }
    )
      .then(() => {
        setIsLoading(false);
        setaddRetainagePayment(false);
      })
      .catch(() => {
        setIsLoading(false);
      });
  };

  const retaingeData = async () => {
    const response = await getRetainageDetails({ billId: billId || 0 });
    if (response?.success) {
      setRetainageData(response?.data);
    }
  };
  useEffect(() => {
    retaingeData();
  }, []);
  const handleRetainagePayment = (value: number | null) => {
    setErrorMessage(null);
    const originalInput = String(value);
    if (originalInput.trim() === "-" || originalInput.trim() === "") {
      setFormData((prev: IBillDetailsItem) => ({
        ...prev,
        unit_cost: "",
      }));
      return;
    }
    const [integerPart] = originalInput.split(".");
    if (integerPart.length > 5) {
      return;
    }
    if (floatNumberRegex.test(originalInput)) {
      setFormData((prev: IBillDetailsItem) => ({
        ...prev,
        unit_cost: originalInput,
      }));
    }
  };
  useEffect(() => {
    const retainage = (
      Number(retainageData?.remainaning_retainage) / 100
    ).toFixed(2);
    if (retainage && Number(retainage) > 0) {
      setRetainageValue(retainage);
    }
  }, [retainageData]);

  const retainedOriginalValue = (
    Number(retainageData?.remainaning_retainage ?? 0) / 100
  ).toFixed(2);

  const retainageColorClass =
    retainageValue === "" ||
    retainageValue === null ||
    retainageValue === undefined
      ? ""
      : retainageValue > retainedOriginalValue
      ? "!text-red-600"
      : retainageValue < retainedOriginalValue
      ? "!text-[#008000]"
      : "";
  return (
    <>
      <CommonModal
        isOpen={addRetainagePayment}
        widthSize="550px"
        onCloseModal={() => setaddRetainagePayment(false)}
        modalBodyClass="p-0"
        header={{
          title: _t(`${singular_name} Retainage Payment`),
          icon: (
            <FontAwesomeIcon
              className="w-4 h-4"
              icon="fa-regular fa-memo-circle-check"
            />
          ),
          closeIcon: true,
        }}
      >
        <form onSubmit={handleSubmit} className="py-4">
          <div className="modal-body grid gap-5 overflow-y-auto max-h-[calc(100vh-200px)] px-4">
            <InlineField
              label={_t("Retainage Payment")}
              labelPlacement="top"
              isRequired={true}
              field={
                <div className="grid md:grid-cols-2 gap-5">
                  <InputNumberField
                    name="title"
                    className={`${retainageColorClass} retainage-payment-filed`}
                    labelClass="dark:text-white/90"
                    errorMessage={errorMessage ? errorMessage : ""}
                    placeholder={inputFormatter("0.00").value}
                    labelPlacement="top"
                    value={retainageValue ? retainageValue : ""}
                    formatter={(value) => {
                      const valueToFormat = String(value);
                      return inputFormatter(valueToFormat).value;
                    }}
                    parser={(value) => {
                      if (!value) return "";
                      const inputValue = unformatted(value.toString());
                      return inputValue;
                    }}
                    onChange={(inputValue) => {
                      if (setErrorMessage) {
                        setErrorMessage("");
                      }
                      setRetainageValue(inputValue || "");
                      handleRetainagePayment(Number(inputValue) || "");
                    }}
                    onKeyDown={(
                      event: React.KeyboardEvent<HTMLInputElement>
                    ) => {
                      onKeyDownCurrency(event, {
                        integerDigits: 10,
                        decimalDigits: 2,
                        unformatted,
                        decimalSeparator: inputFormatter().decimal_separator,
                        allowNegative: false,
                      });
                    }}
                    isRequired
                    prefix={inputFormatter().currency_symbol}
                  />
                  <div className="flex items-center gap-1">
                    <Typography>of:</Typography>
                    <Typography className="bg-blue-100 py-[3px] px-[9px] rounded block font-medium dark:bg-dark-500 text-sm text-primary-900 dark:text-white/90">
                      {formatter(retainedOriginalValue).value_with_symbol}
                    </Typography>
                  </div>
                </div>
              }
            />
          </div>

          <div className="modal-footer text-center px-4 pt-4">
            <PrimaryButton
              htmlType="submit"
              className="w-fit"
              buttonText={_t(`Save & Close`)}
              disabled={isLoading}
              isLoading={isLoading}
            />
          </div>
        </form>
      </CommonModal>
    </>
  );
};

export default BillRetainagePayment;
