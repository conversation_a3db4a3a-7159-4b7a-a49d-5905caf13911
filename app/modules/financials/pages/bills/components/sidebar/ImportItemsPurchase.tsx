import { use<PERSON>arams } from "@remix-run/react";
import {
  ColDef,
  GridReadyEvent,
  IRowNode,
  SelectionChangedEvent,
} from "ag-grid-community";
import { useEffect, useRef, useState } from "react";
import { defaultConfig } from "~/data";
import { Float } from "~/helpers/helper";
import { useTranslation } from "~/hook";
// Atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Header } from "~/shared/components/atoms/header";
import { Button } from "~/shared/components/atoms/button";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { SelectField } from "~/shared/components/molecules/selectField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import ToolTipCell from "~/shared/components/molecules/table/ToolTipCell";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { getGModule, useGModules } from "~/zustand";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { apiRoutes } from "~/route-services/routes";
import { GridApi } from "ag-grid-community";
import { useAppBillDispatch, useAppBillSelector } from "../../redux/store";
import {
  addBillItems,
  deleteBillItem,
} from "../../redux/action/billDetailAction";
import { addItems, removeBillItem } from "../../redux/slices/billDetailSlice";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";

let timeout: NodeJS.Timeout;

const ImportItemsPurchase = ({
  importItemsPurchase,
  setImportItemsPurchase = () => {},
  handleBillDetails,
}: Partial<IImportItemsPurchaseProps>) => {
  const { _t } = useTranslation();
  const { getGModuleByKey } = useGModules();

  const [selectedItems, setSelectedItems] = useState<
    Array<Partial<IPurchaseOrderItems>>
  >([]);
  const { module_name = "Purchase-Orders" }: Partial<GModule> =
    getGModule(defaultConfig.purchase_order_module_id) || {};
  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const { module_id } = currentModule || {};
  const { formatter } = useCurrencyFormatter();
  const params: RouteParams = useParams();
  const [purchaseOrderList, setPurchaseOrderList] = useState<IPoList[]>([]);
  const [selectedPurchaseOrder, setSelectedPurchaseOrder] = useState<
    number | null
  >(null);
  const [purchaseOrderItems, setPurchaseOrderItems] = useState<
    IPurchaseOrderItems[]
  >([]);
  const [itemsLoading, setItemsLoading] = useState<boolean>(false);
  const dispatch = useAppBillDispatch();
  const [saveButtonLoader, setSaveButtonLoader] = useState<boolean>(false);
  const agGridRef = useRef<GridApi | null>(null);
  const { billDetail }: IBillDetailsInitialState = useAppBillSelector(
    (state) => state.billDetails
  );
  const onGridReady = (params: GridReadyEvent) => {
    agGridRef.current = params.api;
  };

  useEffect(() => {
    if (purchaseOrderItems) {
      setSelectedItems(
        purchaseOrderItems?.map((value: Partial<IPurchaseOrderItems>) => ({
          ...value,
          isChecked: Boolean(
            billDetail?.data?.items?.find(
              (itemData: Partial<IBillDetailsItem>) =>
                itemData?.reference_module_item_id?.toString() ===
                  value?.item_id?.toString() ||
                itemData?.reference_item_id?.toString() ===
                  value?.item_id?.toString()
            )
          ),
        }))
      );
    }
  }, [purchaseOrderItems, billDetail.data.items]);

  const fetchData = async (page: number, limit: number) => {
    // setLoading(true);
    const response = (await webWorkerApi<IPurchaseOrderApiRes>({
      url: apiRoutes.GET_PURCHASE_ORDER_LIST.url,
      method: "post",
      data: {
        filter: {
          project: billDetail?.data?.project_id?.toString(),
          status: "0",
        },
        module_id: "0",
        limited_fields: 1,
        ignore_filter: 1,
      },
    })) as IPurchaseOrderApiRes;

    if (response.success) {
      setPurchaseOrderList((prev) => [...prev, ...response?.data]);
    }
    // setLoading(false);
  };
  useEffect(() => {
    if (importItemsPurchase) fetchData(0, 20);
  }, [importItemsPurchase]);

  const fetchItems = async (purchase_order_id: number) => {
    setItemsLoading(true);

    const response = (await webWorkerApi<IPurchaseOrderItems>({
      url: apiRoutes.GET_PURCHASE_ORDER_ITEMS_LIST.url,
      method: "post",
      data: {
        global_project: "",
        purchaseOrderId: purchase_order_id,
        moduleId: module_id,
      },
    })) as IPurchaseOrderItemApiRes;

    if (response && response.success && response?.data) {
      setPurchaseOrderItems(response?.data);
    } else {
      setPurchaseOrderItems([]);

      notification.error({
        description: response.message,
      });
    }
    setItemsLoading(false);
  };

  useEffect(() => {
    if (selectedPurchaseOrder) {
      fetchItems(selectedPurchaseOrder);
    }
  }, [selectedPurchaseOrder]);

  useEffect(() => {
    if (
      billDetail.data &&
      billDetail?.data?.items &&
      billDetail?.data?.items?.length > 0 &&
      purchaseOrderItems?.length > 0
    ) {
      const ids = billDetail.data?.items?.map((item) => {
        return item.reference_module_item_id || item.reference_item_id;
      });
      const selectedNodes: IRowNode[] = [];
      purchaseOrderItems.forEach((row: IPurchaseOrderItems) => {
        if (ids.includes(Number(row.item_id))) {
          const index = purchaseOrderItems?.indexOf(row);
          const node =
            index !== -1
              ? agGridRef?.current?.getRowNode(index.toString())
              : null;
          if (node) {
            node.setSelected(true);

            selectedNodes.push(node);
          }
        }
      });
      agGridRef?.current?.ensureNodeVisible(selectedNodes[0]);
    }
  }, [purchaseOrderItems, billDetail?.data.items]);

  const addPurchseOrderItem = async () => {
    try {
      if (!selectedItems || selectedItems?.length === 0) {
        notification.error({
          description: "Please select atleast one item?.",
        });
      } else {
        setSaveButtonLoader(true);
        const billId = params && params.id ? Number(params.id) : "";
        const items: Partial<IBillDetailsItem>[] = [];
        selectedItems?.map(async (item: Partial<IPurchaseOrderItems>) => {
          if (item.isChecked === true) {
            const tempObj: Partial<IBillDetailsItem> = {
              unit_cost: Float(item?.unit_cost).toString(),
              item_id: item.item_id,
              cost_code_id:
                item && item.cost_code_id ? item.cost_code_id : undefined,
              item_type_key: item.item_type_key,
              item_type: Number(item?.item_type), // change this in future ( temporary resolve type issue )
              bill_id: billId || 0,
              isChecked: true,
              subject: item?.subject,
              quantity: Number(item?.quantity) ?? "",
              account_id: item?.account_id ? Number(item?.account_id) : 0,
              apply_global_tax: item?.apply_global_tax?.toString() || "",
              item_category: "item",
              total: Float(item?.unit_cost).toString(),
              internal_notes: (item as { notes: string })?.notes,
              reference_module_name: "purchase-order",
              reference_module_id: 66,
              reference_module_item_id: Number(item?.item_id) ?? "",
              reference_item_id: Number(item?.item_id) ?? "",
              reference_primary_id: Number(item?.purchase_order_id) ?? "",
              ...(item?.description && {
                description: item?.description || "",
              }),
              ...(item?.unit && {
                unit: item?.unit || "",
              }),
            };
            items.push(tempObj);
          }
        });
        let deleteItems = selectedItems?.filter(
          (value: Partial<IPurchaseOrderItems>) =>
            value.hasOwnProperty("isChecked") && value.isChecked === false
        );
        const currentData = billDetail?.data?.items?.map(
          (value: Partial<IBillDetailsItem>) =>
            value.reference_module_item_id || value.reference_item_id
        );
        const uniqueNewData = items.reduce(
          (
            acc: Array<Partial<IBillDetailsItem>>,
            item: Partial<IBillDetailsItem>
          ) => {
            const existingItem = acc?.find(
              (i: Partial<IBillDetailsItem>) =>
                i.reference_module_item_id === item.reference_module_item_id ||
                i.reference_item_id === item.reference_item_id
            );
            if (!existingItem) {
              acc.push(item);
            }
            return acc;
          },
          []
        );
        const filteredData = uniqueNewData?.filter(
          (item: Partial<IBillDetailsItem>) =>
            !currentData?.includes(item.reference_module_item_id) ||
            !currentData?.includes(item.reference_item_id)
        );
        const response = await addBillItems({
          id: Number(billDetail?.data?.bill_id),
          items: filteredData as IBillDetailsItem[],
          is_single_item: 0,
          module_id: module_id || 0,
        });
        if (response.success) {
          setSaveButtonLoader(false);
          setImportItemsPurchase(false);
          dispatch(addItems(filteredData));
          if (handleBillDetails) {
            handleBillDetails(response);
          }
        }
        if (deleteItems.length) {
          for (let index = 0; index < deleteItems.length; index++) {
            const selectItems = deleteItems[index];
            const selectItem = billDetail?.data?.items?.find(
              (itemData: Partial<IBillDetailsItem>) => {
                const itemId = selectItems?.item_id?.toString() ?? "0";
                const referenceModuleItemId =
                  itemData?.reference_module_item_id?.toString() ?? "0";
                const referenceItemId =
                  itemData?.reference_item_id?.toString() ?? "0";

                return [referenceModuleItemId, referenceItemId].includes(
                  itemId
                );
              }
            );
            if (!!selectItem?.item_id && !!selectItem?.bill_id) {
              const response = await deleteBillItem({
                id: selectItem?.item_id?.toString(),
                bill_id: Number(billDetail?.data?.bill_id),
              });
              if (response) {
                setSaveButtonLoader(false);

                setImportItemsPurchase(false);
                dispatch(removeBillItem(selectItem));
              }
            }
          }
        }
      }
    } catch (error) {
      notification.error({
        description: "something went wrong!",
      });
    }
  };

  const handleSelectionChanged = (event: SelectionChangedEvent): void => {
    const selectedNodes: IPurchaseOrderItems[] = [];
    event.api.forEachNode((node: IRowNode<IPurchaseOrderItems>, index) => {
      if (node.isSelected() && node.data) {
        selectedNodes.push(node.data);
      }
    });
    const selectedIds = selectedNodes.map((value) => value?.item_id);

    setSelectedItems((prevSelectedData: Partial<ISubcontract>[]) => {
      return prevSelectedData?.map((data) => ({
        ...data,
        isChecked: selectedIds.includes((data as { item_id: number }).item_id),
      }));
    });
  };

  const tableHeaders: ColDef[] = [
    {
      headerName: "",
      resizable: false,
      field: "source_name",
      minWidth: 36,
      maxWidth: 36,
      checkboxSelection: true,
      suppressMenu: true,
      headerCheckboxSelection: true,
    },
    {
      headerName: _t("Item Name"),
      resizable: false,
      field: "subject",
      minWidth: 150,
      flex: 2,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellRenderer: ToolTipCell,
    },
    {
      headerName: _t("QTY"),
      resizable: false,
      field: "quantity",
      maxWidth: 70,
      minWidth: 70,
      suppressMenu: true,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",

      valueGetter: ({ data }) => Number(data.quantity).toString() || "0.00",
      cellRenderer: ToolTipCell,
    },
    {
      headerName: _t("Cost/Unit"),
      resizable: false,
      field: "unit_cost",
      minWidth: 120,
      maxWidth: 120,
      suppressMenu: true,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      valueGetter: ({ data }) =>
        `${
          formatter(
            (Number(data.unit_cost) / 100).toFixed(2).toString() || "0.00"
          ).value_with_symbol
        }${data?.unit ? `/${data.unit}` : ""}`,
      cellRenderer: ToolTipCell,
    },
    {
      headerName: _t("Total"),
      resizable: false,
      field: "total",
      minWidth: 120,
      maxWidth: 120,
      suppressMenu: true,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      valueGetter: ({ data }) =>
        formatter((Number(data.total) / 100).toFixed(2).toString() || "0.00")
          .value_with_symbol,
      cellRenderer: ToolTipCell,
    },
  ];

  return (
    <>
      <Drawer
        open={importItemsPurchase}
        rootClassName="drawer-open"
        width={750}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-box-circle-check"
              />
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {_t(
                `Import ${
                  getGModuleByKey(defaultConfig.purchase_order_module)
                    ?.module_name
                } Items`
              )}
            </Header>
          </div>
        }
        closeIcon={
          <CloseButton onClick={() => setImportItemsPurchase(false)} />
        }
      >
        <form className="py-4">
          <div className="sidebar-body h-[calc(100dvh-132px)]">
            <div className="grid gap-4">
              <div className="px-4 h-[calc(100dvh-172px)] overflow-y-auto">
                <SidebarCardBorder addGap={true}>
                  <div className="grid gap-3.5">
                    <div className="w-full">
                      <SelectField
                        isRequired
                        label={module_name}
                        labelPlacement="top"
                        // onPopupScroll={onScroll}
                        options={
                          purchaseOrderList
                            ? purchaseOrderList?.map(
                                (item: {
                                  prefix_company_purchase_order_id: string;
                                  subject: string;
                                  purchase_order_id: number;
                                }) => ({
                                  label: `PO #${item?.prefix_company_purchase_order_id}: ${item?.subject}`,
                                  value: item?.purchase_order_id,
                                })
                              )
                            : []
                        }
                        onChange={(value) => {
                          setSelectedPurchaseOrder(
                            purchaseOrderList.find(
                              (item) => item.purchase_order_id == Number(value)
                            )?.purchase_order_id || null
                          );
                        }}
                      />
                    </div>
                    <div className="p-2 common-card">
                      <div className={`ag-theme-alpine`}>
                        {itemsLoading && (
                          <Spin className="w-full h-[208px] flex items-center justify-center" />
                        )}
                        <StaticTable
                          columnDefs={tableHeaders}
                          rowMultiSelectWithClick={true}
                          suppressRowClickSelection={true}
                          className={
                            itemsLoading
                              ? "static-table checkbox-ml-none hidden"
                              : "static-table"
                          }
                          rowSelection={"multiple"}
                          rowData={purchaseOrderItems}
                          onSelectionChanged={handleSelectionChanged}
                          onGridReady={onGridReady}
                          noRowsOverlayComponent={() => (
                            <NoRecords
                              image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                            />
                          )}
                        />
                      </div>
                    </div>
                  </div>
                </SidebarCardBorder>
              </div>
            </div>
          </div>
          <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
            <Button
              type="primary"
              className="w-full justify-center primary-btn"
              htmlType="button"
              loading={saveButtonLoader}
              onClick={addPurchseOrderItem}
              disabled={
                !selectedPurchaseOrder ||
                selectedItems?.every((element) => {
                  return !(element as { isChecked: boolean }).isChecked;
                })
              }
            >
              {_t("Save & Close")}
            </Button>
          </div>
        </form>
      </Drawer>
    </>
  );
};

export default ImportItemsPurchase;
