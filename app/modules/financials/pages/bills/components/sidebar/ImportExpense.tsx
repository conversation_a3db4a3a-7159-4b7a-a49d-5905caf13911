import { useEffect, useRef, useState } from "react";
import { defaultConfig } from "~/data";
// Atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { getGModuleByKey } from "~/zustand";
import { useTranslation } from "~/hook";
import { useAppBillDispatch, useAppBillSelector } from "../../redux/store";
import { webWorkerApi } from "~/shared/services/apiService/web-worker";
import { apiRoutes } from "~/route-services/routes";
import { GridApi, GridReadyEvent } from "ag-grid-community";
import { Float } from "~/helpers/helper";
import {
  addBillItems,
  deleteBillItem,
} from "../../redux/action/billDetailAction";
import { IRowNode } from "ag-grid-community";
import { addItems, removeBillItem } from "../../redux/slices/billDetailSlice";
import { SelectionChangedEvent } from "ag-grid-community";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";

const ImportExpense = ({
  expenseSingularItem,
  setExpenseSingularItem,
  handleBillDetails,
}: IImportItemsExpenseProps) => {
  const { _t } = useTranslation();
  const { formatter } = useCurrencyFormatter();

  const { billDetail }: IBillDetailsInitialState = useAppBillSelector(
    (state) => state.billDetails
  );
  const dispatch = useAppBillDispatch();
  const gridApiRef = useRef<GridApi | null>(null);
  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const { module_id } = currentModule || {};

  const [selectedItems, setSelectedItems] = useState<
    Array<Partial<IExpensesData>>
  >([]);
  const [itemsLoading, setItemsLoading] = useState<boolean>(false);
  const [saveButtonLoader, setSaveButtonLoader] = useState<boolean>(false);
  const [expenseLogListData, setExpenseLogList] = useState<IExpensesData[]>([]);

  useEffect(() => {
    if (expenseLogListData) {
      setSelectedItems(
        expenseLogListData?.map((value: Partial<IExpensesData>) => ({
          ...value,
          isChecked: Boolean(
            billDetail?.data?.items?.find(
              (itemData: Partial<IBillDetailsItem>) =>
                itemData?.reference_item_id?.toString() ===
                value?.expense_id?.toString()
            )
          ),
        }))
      );
    }
  }, [expenseLogListData, billDetail.data.items]);

  const fetchData = async () => {
    setItemsLoading(true);
    const response = (await webWorkerApi<IExpensesApiRes>({
      url: apiRoutes.GET_EXPENSE_LOGS_LIST.url,
      method: "post",
      data: {
        filter: {
          project: billDetail?.data?.project_id,
          status: "0",
          tab: "open",
        },
        limitedFields: 1,
        ignore_filter: 1,
      },
    })) as IExpensesApiRes;

    if (response.success) {
      setExpenseLogList(response?.data);
    }
    setItemsLoading(false);
  };

  const onGridReady = (params: GridReadyEvent) => {
    gridApiRef.current = params.api;
  };
  const handleSelectionChanged = (event: SelectionChangedEvent<any>): void => {
    const selectedNodes: IExpensesData[] = [];
    event.api.forEachNode((node: IRowNode<IExpensesData>, index) => {
      if (node.isSelected() && node.data) {
        selectedNodes.push(node.data);
      }
    });
    const selectedIds = selectedNodes.map((value) => value?.expense_id);

    setSelectedItems((prevSelectedData: Partial<IExpensesData>[]) => {
      return prevSelectedData?.map((data) => ({
        ...data,
        isChecked: selectedIds.includes(
          (data as { expense_id: number }).expense_id
        ),
      }));
    });
  };
  useEffect(() => {
    if (expenseSingularItem) fetchData();
  }, [expenseSingularItem]);

  const addExpense = async () => {
    try {
      if (!selectedItems || selectedItems?.length === 0) {
        notification.error({
          description: "Please select at least one item.",
        });
      } else {
        setSaveButtonLoader(true);
        const billId =
          billDetail && billDetail.data.bill_id ? billDetail.data.bill_id : "";
        const items: Partial<IBillDetailsItem>[] = [];
        await Promise.all(
          selectedItems?.map(async (item: Partial<IExpensesData>) => {
            if ((item as { isChecked: boolean }).isChecked === true) {
              const tempObj: Partial<IBillDetailsItem> = {
                unit_cost: Float(item?.amount).toString(),
                cost_code_id:
                  item && item.cost_code_id ? item.cost_code_id : undefined,
                cost_code_name:
                  item && item.cost_code_name ? item.cost_code_name : undefined,
                reference_module_name: "expense",
                isChecked: true,
                reference_module_id: 12,
                reference_item_id: item?.expense_id,
                item_type: Number(item?.item_type),
                bill_id: Number(billId),
                subject: item?.expense_name,
                item_category: "category",
                total: Float(item?.amount).toString(),
                quantity: 1,
                internal_notes: (item as { notes: string })?.notes,
                account_id: (item as { account_id: number })?.account_id,
                apply_global_tax:
                  (item as { apply_global_tax: string })?.apply_global_tax ??
                  "",
                is_feature_item: 0,
                description: (item as { description: string })?.description,
              };
              items?.push(tempObj);
            }
          })
        );
        let deleteItems = selectedItems?.filter(
          (expense: Partial<IExpensesData>) =>
            expense.hasOwnProperty("isChecked") &&
            (expense as { isChecked: boolean }).isChecked === false
        );
        const currentData = billDetail?.data?.items?.map(
          (value: Partial<IBillDetailsItem>) => value.reference_item_id
        );
        const uniqueNewData = items.reduce(
          (
            acc: Array<Partial<IBillDetailsItem>>,
            item: Partial<IBillDetailsItem>
          ) => {
            const existingItem = acc?.find(
              (i: Partial<IBillDetailsItem>) =>
                i.reference_item_id === item.reference_item_id
            );
            if (!existingItem) {
              acc.push(item);
            }
            return acc;
          },
          []
        );

        const filteredData = uniqueNewData?.filter(
          (item: Partial<IBillDetailsItem>) =>
            !currentData?.includes(item.reference_item_id)
        );
        const response = await addBillItems({
          id: Number(billDetail?.data?.bill_id),
          items: filteredData as IBillDetailsItem[],
          is_single_item: 0,
          module_id: module_id || 0,
        });
        if (response.success) {
          setSaveButtonLoader(false);
          setExpenseSingularItem(false);
          if (handleBillDetails) {
            handleBillDetails(response);
          }
          if (response?.data?.length) {
            dispatch(addItems(response.data));
          }
        }
        if (deleteItems.length) {
          for (let index = 0; index < deleteItems.length; index++) {
            const selectItems = deleteItems[index];
            const selectItem = billDetail?.data?.items?.find(
              (itemData: Partial<IBillDetailsItem>) => {
                const expId = selectItems?.expense_id?.toString() ?? "0";
                const referenceItemId =
                  itemData?.reference_item_id?.toString() ?? "0";

                return [referenceItemId].includes(expId);
              }
            );
            if (!!selectItem?.item_id && !!selectItem?.bill_id) {
              const response = await deleteBillItem({
                id: selectItem?.item_id?.toString(),
                bill_id: Number(billDetail?.data?.bill_id),
              });
              if (response) {
                setSaveButtonLoader(false);
                setExpenseSingularItem(false);
                dispatch(removeBillItem(selectItem));
              }
            }
          }
        }
      }
    } catch (error) {
      notification.error({
        description: "Something went wrong!",
      });
    }
  };

  useEffect(() => {
    if (
      billDetail.data &&
      billDetail?.data?.items &&
      billDetail?.data?.items?.length > 0 &&
      expenseLogListData?.length > 0
    ) {
      const ids = billDetail.data?.items?.map((item) => {
        return item.reference_item_id;
      });
      const selectedNodes: IRowNode[] = [];

      expenseLogListData.forEach((row: IExpensesData) => {
        if (ids.includes(Number(row.expense_id))) {
          const index = expenseLogListData?.indexOf(row);
          const node =
            index !== -1
              ? gridApiRef?.current?.getRowNode(index.toString())
              : null;
          if (node) {
            node.setSelected(true);
            selectedNodes.push(node);
          }
        }
      });
      gridApiRef?.current?.ensureNodeVisible(selectedNodes[0]);
    }
  }, [expenseLogListData, billDetail?.data.items]);
  return (
    <Drawer
      open={expenseSingularItem}
      rootClassName="drawer-open"
      width={750}
      classNames={{
        body: "!p-0 !overflow-hidden",
      }}
      title={
        <div className="flex items-center">
          <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
            <FontAwesomeIcon
              className="w-4 h-4"
              icon="fa-regular fa-money-bill"
            />
          </div>
          <Header
            level={5}
            className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
          >
            {_t(
              `Import ${
                getGModuleByKey(defaultConfig.expense_module)?.module_name
              }`
            )}
          </Header>
        </div>
      }
      closeIcon={<CloseButton onClick={() => setExpenseSingularItem(false)} />}
    >
      <form className="py-4">
        <div className="sidebar-body h-[calc(100dvh-132px)]">
          <div className="grid gap-4">
            <div className="px-4 h-[calc(100dvh-172px)] overflow-y-auto">
              <SidebarCardBorder addGap={true}>
                <div className="grid gap-3.5">
                  <div className="p-2 common-card">
                    <div className={`ag-theme-alpine`}>
                      {itemsLoading && (
                        <Spin className="w-full h-[208px] flex items-center justify-center" />
                      )}
                      <StaticTable
                        className={
                          itemsLoading
                            ? "static-table checkbox-ml-none hidden"
                            : "static-table checkbox-ml-none"
                        }
                        onSelectionChanged={handleSelectionChanged}
                        rowMultiSelectWithClick={true}
                        suppressRowClickSelection={true}
                        rowData={expenseLogListData}
                        rowSelection="multiple"
                        onGridReady={onGridReady}
                        columnDefs={[
                          {
                            headerName: "",
                            minWidth: 36,
                            maxWidth: 36,
                            field: "",
                            suppressMenu: true,
                            checkboxSelection: true,
                            headerCheckboxSelection: true,
                            headerClass: "ag-header-center",
                            cellClass: "ag-cell-center",
                          },
                          {
                            headerName: _t("Expense Name"),
                            minWidth: 240,
                            resizable: false,
                            field: "expense_name",
                            suppressMenu: true,
                            flex: 2,
                            cellRenderer: "agGroupCellRenderer",
                          },
                          {
                            headerName: _t("Amount"),
                            maxWidth: 120,
                            minWidth: 120,
                            field: "amount",
                            resizable: false,
                            suppressMenu: true,
                            headerClass: "ag-header-right",
                            cellClass: "ag-right-aligned-cell",
                            valueFormatter: (params) =>
                              formatter(
                                Number(params?.data?.amount || 0) / 100 == 0
                                  ? (
                                      Number(params?.data?.amount || 0) / 100
                                    )?.toFixed(0)
                                  : (
                                      Number(params?.data?.amount || 0) / 100
                                    )?.toFixed(2)
                              ).value_with_symbol,
                          },
                        ]}
                        noRowsOverlayComponent={() => (
                          <NoRecords
                            image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
                          />
                        )}
                      />
                    </div>
                  </div>
                </div>
              </SidebarCardBorder>
            </div>
          </div>
        </div>
        <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
          <PrimaryButton
            htmlType="button"
            buttonText={_t("Save & Close")}
            onClick={addExpense}
            loading={saveButtonLoader}
            disabled={
              saveButtonLoader ||
              selectedItems?.every((element) => {
                return !(element as { isChecked: boolean }).isChecked;
              })
            }
          />
        </div>
      </form>
    </Drawer>
  );
};

export default ImportExpense;
