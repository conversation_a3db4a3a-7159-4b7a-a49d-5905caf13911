import { useRef, useState, useMemo, useEffect, FormEvent } from "react";
import * as Yup from "yup";
import { useFormik } from "formik";
// Hook Redux
import { useTranslation } from "~/hook";
import { sanitizeString } from "~/helpers/helper";
import {
  getItemTypeIcon,
  onKeyDownCurrency,
} from "~/shared/utils/helper/common";
// Atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { InputField } from "~/shared/components/molecules/inputField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { Text<PERSON>reaField } from "~/shared/components/molecules/textAreaField";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";
import { apiRoutes } from "~/route-services/routes";
import { addBillItems } from "../../redux/action/billDetailAction";
import { useAppBillDispatch, useAppBillSelector } from "../../redux/store";
import { addItems } from "../../redux/slices/billDetailSlice";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";

type FormikSubmitEvent = FormEvent<HTMLFormElement> & {
  nativeEvent: { submitter?: HTMLButtonElement };
};

const AddLumSum = ({
  isOpen,
  onClose,
  isViewOnly = false,
  handleBillDetails,
}: ILumSumProps) => {
  const { _t } = useTranslation();
  const appSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();
  const { quickbook_sync = "", quickbook_desktop_sync = "" } =
    appSettings || {};
  const { inputFormatter, unformatted } = useCurrencyFormatter();
  const [itemTypes, setItemTypes] = useState<IItemTypes[]>([]);
  const [submittingFrm, setSubmittingFrm] = useState<string>("");
  const [formEvent, setFormEvent] = useState<FormikSubmitEvent | null>(null);
  const [isFocusAmountVal, setIsFocusAmountVal] = useState(false);
  const skipClose = useRef<boolean>(false);
  const { billDetail, amountList, billAccountId }: IBillDetailsInitialState =
    useAppBillSelector((state) => state.billDetails);
  const dispatch = useAppBillDispatch();

  // Define the condition to check Quickbook sync
  const isQuickbookSyncEnabled =
    Boolean(Number(quickbook_sync)) || Boolean(Number(quickbook_desktop_sync));

  const initialValues: IAddLumSumItem = useMemo(() => {
    return {
      subject: "",
      item_type: "",
      total: "",
      quantity: 1,
      unit_cost: "",
      account: billAccountId,
      description: "",
      internal_notes: "",
      apply_global_tax: false,
    };
  }, []);

  const validationSchema = Yup.object().shape({
    subject: Yup.string().trim().required("This field is required."),
    item_type: Yup.string().trim().required("This field is required."),
    account: Yup.string()
      .trim()
      .when([], {
        is: () => isQuickbookSyncEnabled,
        then: (schema) => schema.required("This field is required."),
        otherwise: (schema) => schema.notRequired(),
      }),
    description: Yup.string().trim().required("This field is required."),
  });

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values, { resetForm }) => {
      const isSkipClose =
        formEvent?.nativeEvent.submitter?.getAttribute("data-skip-close") ===
          "true" || skipClose.current;

      const lumpSumValues = Object.fromEntries(
        Object.entries(values).filter(
          ([, v]) => v !== null && v !== undefined && String(v).trim() !== ""
        )
      );
      const formData = {
        ...lumpSumValues,
        submitAction: undefined,
      };

      if (formData?.total) {
        formData.total = (Number(formData?.total) * 100).toString();
        formData.unit_cost = formData?.total;
      }

      if (isSkipClose) {
        setSubmittingFrm("save1");
      } else {
        setSubmittingFrm(
          values.submitAction == "saveAndAddAnother"
            ? "saveAndAddAnother"
            : "save"
        );
      }

      try {
        const response = await addBillItems({
          id: Number(billDetail?.data?.bill_id),
          items: [formData],
          is_single_item: true,
        });

        if (response?.success) {
          dispatch(addItems(response.data));
          if (handleBillDetails) {
            handleBillDetails(response);
          }
          if (!isSkipClose) {
            if (values.submitAction == "saveAndAddAnother") {
              setFormEvent(null);
              resetForm();
            } else {
              onClose();
            }
          }
        } else {
          notification.error({
            description: response?.message || "",
          });
        }
        setSubmittingFrm("");
      } catch (error) {
        setSubmittingFrm("");
        notification.error({
          description: (error as Error).message || "",
        });
      } finally {
        setSubmittingFrm("");
      }
    },
  });

  useEffect(() => {
    formik.resetForm({ values: initialValues });
  }, [initialValues]);

  const {
    handleSubmit,
    submitForm,
    handleChange,
    setFieldValue,
    setFieldTouched,
    values,
    errors,
    touched,
  } = formik;

  const handleEnterKeyPress = (
    event: React.KeyboardEvent<HTMLInputElement>
  ) => {
    event.preventDefault();
  };

  useEffect(() => {
    const fetchCompanyItems = async () => {
      if (billDetail?.data?.project_id) {
        try {
          const data = await getWebWorkerApiParams({
            otherParams: billDetail?.data?.project_id,
          });

          const response = (await webWorkerApi({
            url: apiRoutes.COMMON.get_company_items,
            method: "post",
            data: data,
          })) as IStatusRes;

          setItemTypes(response?.data as unknown as IItemTypes[]);
        } catch (error) {
          console.error(error);
        }
      }
    };

    fetchCompanyItems();
  }, [billDetail?.data?.project_id]);

  const itemsTypeList = useMemo(() => {
    return itemTypes
      ?.filter((item) => item?.type_id)
      .map((item) => ({
        label: (
          <div className="flex items-center gap-1.5">
            <FontAwesomeIcon
              icon={getItemTypeIcon({ type: item?.type_id?.toString() })}
            />
            {HTMLEntities.decode(sanitizeString(item?.name))}
          </div>
        ),
        value: item?.type_id?.toString(),
        ...item,
      }));
  }, [itemTypes]);

  return (
    <>
      <Drawer
        open={isOpen}
        rootClassName="drawer-open"
        width={718}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-hand-holding-dollar"
              />
            </div>
            <div className="flex justify-between w-full">
              <Header
                level={5}
                className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
              >
                {_t("Lump Sum Total")}
              </Header>
            </div>
          </div>
        }
        closeIcon={<CloseButton onClick={() => onClose()} />}
      >
        <form method="post" className="py-4" onSubmit={handleSubmit} noValidate>
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <div className="grid gap-4">
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <SelectField
                    label={_t("Item Type")}
                    labelPlacement="top"
                    isRequired={true}
                    name="item_type"
                    value={
                      values.item_type && values.item_type != "0"
                        ? values.item_type?.toString()
                        : undefined
                    }
                    onChange={(value) => {
                      setFieldValue("item_type", value || "");
                      setFieldTouched("item_type", value ? false : true);
                    }}
                    options={itemsTypeList || []}
                    errorMessage={touched?.item_type ? errors?.item_type : ""}
                  />
                </div>
                <div className="w-full">
                  <InputField
                    label={_t("Item Name")}
                    labelPlacement="top"
                    isRequired={true}
                    name="subject"
                    value={HTMLEntities.decode(sanitizeString(values?.subject))}
                    onChange={handleChange}
                    errorMessage={touched?.subject ? errors?.subject : ""}
                    disabled={isViewOnly}
                    onPressEnter={handleEnterKeyPress}
                  />
                </div>
                <div
                  className={`grid md:gap-5 gap-5 ${
                    !Boolean(Number(quickbook_sync)) ||
                    (!Boolean(Number(quickbook_desktop_sync)) &&
                      "md:grid-cols-2")
                  }`}
                >
                  {isQuickbookSyncEnabled && (
                    <div className="w-full">
                      <SelectField
                        isRequired={isQuickbookSyncEnabled ? true : false}
                        name="account"
                        label="Account"
                        value={values?.account}
                        options={
                          amountList && amountList.length > 0
                            ? amountList.map((type: BillAccountData) => ({
                                label: HTMLEntities.decode(
                                  sanitizeString(type?.name || "")
                                ),
                                value: type?.item_id?.toString(),
                              }))
                            : []
                        }
                        className="text-sm"
                        onChange={(value) =>
                          setFieldValue("account", value || "")
                        }
                        errorMessage={touched?.account ? errors?.account : ""}
                      />
                    </div>
                  )}
                  <div className="w-full">
                    <InputNumberField
                      label="Total"
                      value={
                        Number(values.total) != 0
                          ? values.total?.toString()
                          : ""
                      }
                      size="middle"
                      name="total"
                      placeholder="0.00"
                      onChange={(value) => {
                        setFieldValue("total", value);
                      }}
                      formatter={(value, info) => {
                        const inputValue = info.input.trim();
                        const valueToFormat =
                          inputValue !== "0" && inputValue.length > 0
                            ? unformatted(inputValue)
                            : String(value);

                        return isFocusAmountVal
                          ? inputFormatter(valueToFormat).value
                          : !!value
                          ? inputFormatter(Number(value)?.toFixed(2)).value
                          : "";
                      }}
                      parser={(value) => {
                        if (!value) return "";
                        const inputValue = unformatted(value.toString());
                        return inputValue;
                      }}
                      onFocus={() => {
                        setIsFocusAmountVal(true);
                      }}
                      onBlur={() => setIsFocusAmountVal(false)}
                      onKeyDown={(
                        event: React.KeyboardEvent<HTMLInputElement>
                      ) => {
                        onKeyDownCurrency(event, {
                          integerDigits: 10,
                          decimalDigits: 2,
                          unformatted,
                          decimalSeparator: inputFormatter().decimal_separator,
                          allowNegative: false,
                        });
                      }}
                      prefix={inputFormatter().currency_symbol}
                    />
                  </div>
                </div>
                <div className="w-full">
                  <TextAreaField
                    label={_t("Description")}
                    name="description"
                    labelPlacement="top"
                    value={HTMLEntities.decode(
                      sanitizeString(values?.description)
                    )}
                    disabled={isViewOnly}
                    onChange={handleChange}
                    errorMessage={
                      touched?.description ? errors.description : ""
                    }
                    required={true}
                  />
                </div>
                <div className="w-full">
                  <TextAreaField
                    label={_t("Internal Notes")}
                    name="internal_notes"
                    labelPlacement="top"
                    value={HTMLEntities.decode(
                      sanitizeString(values?.internal_notes)
                    )}
                    disabled={isViewOnly}
                    onChange={handleChange}
                  />
                </div>
                <div className="w-full">
                  <CheckBox
                    className="gap-1.5 w-fit text-primary-900"
                    name="apply_global_tax"
                    checked={values.apply_global_tax}
                    onChange={(event) => {
                      const valueToSet: number = event.target.checked ? 1 : 0;
                      setFieldValue("apply_global_tax", valueToSet);
                    }}
                  >
                    {_t("Collect Tax on this Item?")}
                  </CheckBox>
                </div>
              </SidebarCardBorder>
            </div>
          </div>
          <div className="sidebar-footer flex items-center gap-4 justify-center w-full px-4 pt-4">
            <PrimaryButton
              htmlType="submit"
              onClick={() => setFieldValue("submitAction", "saveAndClose")}
              isLoading={submittingFrm == "save"}
              disabled={!!submittingFrm || isViewOnly}
              buttonText={_t("Save & Close")}
            />
            <PrimaryButton
              htmlType="submit"
              onClick={() => setFieldValue("submitAction", "saveAndAddAnother")}
              isLoading={submittingFrm == "saveAndAddAnother"}
              disabled={!!submittingFrm || isViewOnly}
              buttonText={_t("Save & Add Another Item")}
            />
          </div>
        </form>
      </Drawer>
    </>
  );
};

export default AddLumSum;
