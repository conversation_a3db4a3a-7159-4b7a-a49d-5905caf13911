import { createSlice } from "@reduxjs/toolkit";
import { billDetailsField } from "../../utils/constants";
import { getAccountDetails, getTaxAmount } from "../action/billDetailAction";

const initialState: IBillDetailsInitialState = {
  billDetail: billDetailsField,
  isBillDetailLoading: false,
  items: [],
  taxAmount: [],
  defaultTermType: "",
  itemFilter: [],
  amountList: [],
  billAccountId: "",
};

export const billDetailSlice = createSlice({
  name: "billDetails",
  initialState,
  reducers: {
    setBillDetailLoading: (state, { payload }) => {
      state.isBillDetailLoading = payload;
    },
    updatebillDetail: (state, { payload }) => {
      state.billDetail.data = { ...state.billDetail.data, ...payload };
    },

    updateBillDetailItems: (state, { payload }) => {
      state.billDetail.data.items = (state?.billDetail?.data?.items || []).map(
        (item) => {
          if (item.item_id === payload.item_id) {
            return { ...item, ...payload };
          }
          return item;
        }
      );
    },
    updateBillDragDropItems: (state, { payload }) => {
      state.billDetail.data.items = payload;
    },
    getSinglebillDetail: (state, { payload }) => {
      state.billDetail = payload;
    },
    setItemFilterData: (state, { payload }) => {
      state.itemFilter = payload;
    },
    deleteBillDetail: (state, { payload }) => {
      const { item_id } = payload;
      state.items = state.items.filter((item) => item.item_id !== item_id);
      if (state.items.length === 0) {
        state.items = [];
      } else {
        state.items = {
          ...state.items,
        };
      }
    },

    reloadItems: (state, { payload }) => {
      state.billDetail.data.items = payload?.data?.items;
    },

    addItems: (state, { payload }) => {
      state.billDetail.data.items = [
        ...(state.billDetail.data.items || []),
        ...payload,
      ];
    },

    removeBillItem: (state, { payload }) => {
      state.billDetail.data.items =
        state.billDetail?.data?.items?.filter(
          (item) => item.item_id !== payload.item_id
        ) || [];
    },

    updateBillDetailItem: (state, { payload }) => {
      const { item_id, updatedData } = payload;
      const itemIndex = state.items.findIndex(
        (item) => item.item_id === item_id
      );
      if (itemIndex !== -1) {
        state.items[itemIndex] = {
          ...state.items[itemIndex],
          ...updatedData,
        };
      }
    },
  },

  extraReducers: (builder) => {
    builder.addCase(getTaxAmount.fulfilled, (state, { payload }) => {
      let newPayload = payload as IBillTaxAmountApiRes;
      if (newPayload && newPayload.success && newPayload.data) {
        const { data } = newPayload;
        state.taxAmount = data.getTaxDetails;
      }
    });
    builder.addCase(getAccountDetails.fulfilled, (state, { payload }) => {
      let newPayload = payload as IBillAccountApiRes;
      if (newPayload && newPayload.success && newPayload.data) {
        console.log(newPayload,"newPayload")
        state.amountList = newPayload?.data
        state.billAccountId = newPayload?.bill_account_id !== "0"
            ? String(newPayload?.bill_account_id ?? "")
            : ""
      }
    });
  },
});

export const {
  updatebillDetail,
  updateBillDetailItems,
  getSinglebillDetail,
  updateBillDetailItem,
  setBillDetailLoading,
  deleteBillDetail,
  removeBillItem,
  reloadItems,
  addItems,
  updateBillDragDropItems,
  setItemFilterData,
} = billDetailSlice.actions;
export default billDetailSlice.reducer;
