import { useEffect, useMemo, useState } from "react";
// Atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { InputField } from "~/shared/components/molecules/inputField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import {
  addCOSection,
  updateCOSection,
} from "../../../../redux/action/changeOrderItemsActions";
import {
  useAppSelector,
  useAppDispatch,
} from "~/modules/financials/pages/changeOrder/redux/store";
import * as Yup from "yup";
import {
  addSection,
  updateSection,
} from "../../../../redux/slices/changeOrderItemsSlice";
// Other
import { useParams } from "@remix-run/react";
import { useFormik } from "formik";
import { useTranslation } from "~/hook";
import { getValuableObj } from "~/helpers/helper";
import { canModifyItem } from "../../../../utils/helpers";
import { useCOContext } from "~/context/COContext";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { Tooltip } from "~/shared/components/atoms/tooltip";

const SectionOpen = ({
  sectionOpen,
  setSectionOpen,
  isViewOnly,
  formData,
  isSectionAdd = false,
}: ICOSectionOpenProps) => {
  const dispatch = useAppDispatch();
  const { _t } = useTranslation();
  const { id }: RouteParams = useParams();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const isEditing = Object.keys(formData ?? {})?.length > 0;
  const { sections } = useAppSelector((state) => {
    return state.changeOrderItems;
  });
  const { isReadOnly } = useCOContext();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { details } = useAppSelector((state) => state.changeOrderDetails);

  const [inputValues, setInputValues] = useState({
    section_name: "",
    description: "",
    is_optional_section: 0,
  });
  const disableModification = useMemo(
    () => (details ? !canModifyItem(details) : true) || isReadOnly,
    [details, isReadOnly]
  );
  useEffect(() => {
    if (isSectionAdd === false && formData) {
      setInputValues(formData);
    }
  }, [formData]);

  const initialValues: Partial<IChangeOrderSection> = useMemo(() => {
    return isSectionAdd === false
      ? {
          ...formData,
        }
      : {
          ...inputValues,
        };
  }, [isSectionAdd, formData, inputValues]);

  useEffect(() => {
    formik?.resetForm();
    formik?.setValues(initialValues);
  }, [formData, sectionOpen]);

  const validationSchema = Yup.object().shape({
    section_name: Yup.string()
      .required("This field is required.")
      .test("not-blank", "This field cannot be empty", (value) => {
        return !!(value && value.trim().length > 0); // Ensures the string is not just whitespace
      }),
  });

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values, { setSubmitting, resetForm }) => {
      if (isSectionAdd === false) {
        setIsLoading(true);
        setSubmitting(true);
        // const response = await dispatch(addCOSection(payload));

        const payload = {
          ...values,
          change_order_id: id,
          is_optional_section: values?.is_optional_section || 0,
        };
        const response = (await dispatch(
          updateCOSection(getValuableObj(payload))
        ).unwrap()) as IChangeOrdersSectionApiRes;

        if (response?.success) {
          dispatch(
            updateSection({
              sectionId: formData?.section_id,
              updatedSection: values,
              makeSectionOptional: values?.is_optional_section,
            })
          );
          setSectionOpen(false);
          resetForm();
        } else {
          notification.error({
            description: response?.message,
          });
        }
        setSubmitting(false);
        setIsLoading(false);
        // await  updateSectionHandler(values as ESSection,setSubmitting);
        return;
      } else {
        const payload = getValuableObj({
          ...values,
          custom_section_id: sections?.length + 1,
          change_order_id: id,
        });
        const response = (await dispatch(
          addCOSection(payload as IChangeOrderSection)
        ).unwrap()) as IChangeOrdersSectionApiRes;

        if (response?.success) {
          dispatch(
            addSection({
              sectionId: response?.data?.section_id,
              sectionData: payload,
            })
          );
          setSectionOpen(false);
          resetForm();
        }
      }
      setSubmitting(false);
    },
  });

  const { handleSubmit, setFieldValue, values, errors, isSubmitting } = formik;

  return (
    <Drawer
      open={sectionOpen}
      rootClassName="drawer-open"
      width={718}
      classNames={{
        body: "!p-0 !overflow-hidden",
      }}
      title={
        <div className="flex items-center">
          <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
            <FontAwesomeIcon
              className="w-4 h-4"
              icon="fa-regular fa-rectangle-history-circle-plus"
            />
          </div>
          <Header
            level={5}
            className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
          >
            {isViewOnly || isEditing
              ? _t(`Section #${formData?.custom_section_id}`)
              : _t("Add Section")}
          </Header>
        </div>
      }
      closeIcon={<CloseButton onClick={() => setSectionOpen(false)} />}
    >
      <form className="py-4" onSubmit={handleSubmit}>
        <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
          <SidebarCardBorder addGap={true}>
            <div className="w-full">
              <InputField
                label="Section Name"
                labelPlacement="top"
                name="section_name"
                id="section_name"
                isRequired={true}
                value={values?.section_name}
                disabled={isViewOnly || disableModification}
                errorMessage={
                  formik.touched?.section_name ? errors.section_name : ""
                }
                onChange={(e) => {
                  setFieldValue("section_name", e.target.value);
                }}
                autoComplete="off"
              />
            </div>
            <div className="w-full">
              <TextAreaField
                label="Description"
                name="description"
                id="description"
                value={values?.description}
                labelPlacement="top"
                disabled={isViewOnly || disableModification}
                autoComplete="off"
                onChange={(e) => {
                  setFieldValue("description", e.target.value);
                }}
              />
            </div>
            {/* commented due to this PM Reply https://prnt.sc/Q5FI90g8FFCX (https://app.clickup.com/t/86cz47nuw) */}
            {user && user.company_id == 206 && user.user_id == 108631 && (
              <Tooltip
                title={
                  "This feature is currently unavailable as the Project module is under revision. It will be re-enabled once the updates are complete."
                }
              >
                <CheckBox
                  className="gap-1.5 w-fit"
                  name="is_optional_section"
                  id="is_optional_section"
                  disabled={isViewOnly || disableModification}
                  checked={values?.is_optional_section === 1}
                  onChange={(e) => {
                    setFieldValue(
                      "is_optional_section",
                      e.target.checked ? 1 : 0
                    );
                  }}
                >
                  Make This Section Optional
                </CheckBox>
              </Tooltip>
            )}
          </SidebarCardBorder>
        </div>
        <div className="sidebar-footer flex items-center justify-center w-full gap-6 px-4 pt-4">
          <PrimaryButton
            htmlType="submit"
            className={formik.isSubmitting || isLoading ? "!text-white" : ""}
            buttonText={isEditing ? _t("Update") : _t("Add")}
            disabled={isViewOnly || disableModification}
            isLoading={formik.isSubmitting || isLoading}
            // disabled={formik.isSubmitting || isViewOnly || isLoading}
            ghost={formik.isSubmitting}
          />
        </div>
      </form>
    </Drawer>
  );
};

export default SectionOpen;
