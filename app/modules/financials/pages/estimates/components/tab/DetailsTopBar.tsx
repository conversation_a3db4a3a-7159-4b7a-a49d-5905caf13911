import { useNavigate, useSearchParams } from "@remix-run/react";
// Hooks
import { useTranslation } from "~/hook";
// Atoms
import { Dropdown } from "~/shared/components/atoms/dropDown";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { CheckBox } from "~/shared/components/atoms/checkBox";
// Molecules
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";
import { FieldStatus } from "~/shared/components/molecules/fieldStatus";
import { IconButton } from "~/shared/components/molecules/iconButton";
import { InputField } from "~/shared/components/molecules/inputField";
import { TopBarSkeleton } from "~/shared/components/molecules/topBarSkeleton";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { CommonModal } from "~/shared/components/molecules/commonModal";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
//Store
import { useEffect, useMemo, useState } from "react";
import { parseParamsFromURL } from "~/components/page/$url/helper";
import { replaceDOMParams, sanitizeString } from "~/helpers/helper";
import { getStatusForField } from "~/shared/utils/helper/common";
import {
  getGModuleByKey,
  getGSettings,
  setCommonSidebarCollapse,
  useExistingProjects,
} from "~/zustand"; // In future this code move in redux, developer change this code
import { useAppESDispatch, useAppESSelector } from "../../redux/store";
import {
  ESDetailsField,
  EST_PROJECT_TYPE,
  processApprovedListModalNew,
} from "../../utils/constants";
// import SelectProject from "~/components/sidebars/select-project";

import { MenuProps, Typography } from "antd";
import { SelectProject } from "~/shared/components/organisms/selectProject";
import {
  getEstimateDetail,
  updateStatusProcessApi,
} from "../../redux/action/ESDetailAction";
import EstimateListAction from "../dashboard/EstimateListAction";
import EstimateStatusbar from "./EstimateStatusbar";
import isEmpty from "lodash/isEmpty";
import { getEstimatorGetByProjectId } from "../../redux/action/dashboardAction";
import { fetchProjectDetailsApi } from "~/modules/projectManagement/pages/project/redux/action/projectDetailsAction";
import { getCustomData } from "~/redux/action/customDataAction";
import { getModuleAutoNumber } from "~/redux/action/commonAction";
import { toBoolean } from "../../utils/common";
import { OpportunityFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/opportunityFieldRedirectionIcon";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { addProjectTypeCustomData } from "../../redux/slices/ESProjectTypeListSlice";

const DetailsTopBar = ({
  sidebarCollapse,
  activeStep,
  setActiveStep,
  estimateStatList,
  estimateStatusVal,
  onReloadEstimateDetails,
  isLoading,
  handleChangeFieldStatus = () => {},
  handleUpdateField = (
    data: IEstimateDetailData,
    extraData?: IEstimateDetailData
  ) => {},
  isLoadingStatus,
  selectStatusKey,
  isConfirmCompletedStatus,
  statusCompletedModalHandler = () => {},
  onCloseCompletedModal = () => {},
  isCompletedLoading,
  isConfirmApprovedStatus,
  isConfirmCompletedProjectStatus,
  isCompletedProjectLoading,
  isLoadLock,
  isReadOnly,
  isStatusLost,
  selectedStatusInd,
}: IEDetailsTopBarProps) => {
  const { _t } = useTranslation();
  const sectionTitle: GModule | undefined = getGModuleByKey(
    CFConfig.estimate_module
  );
  const navigate = useNavigate();
  const dispatch = useAppESDispatch();
  const guntChartData = getGModuleByKey(CFConfig.gantt_chart_module);
  const invoiceMergeData = getGModuleByKey(CFConfig.invoice_merge_module_key);
  const projectBudgetData = getGModuleByKey(CFConfig.project_budget_tab_module);
  const [serachParams, setSearchParams] = useSearchParams();
  const {
    is_custom_estimate_id,
    quickbook_sync,
    quickbook_desktop_sync,
    estimate_copy_item_to_sov,
    estimate_generate_invoice,
    estimate_generate_project,
  }: GSettings = getGSettings();
  const { estimateDetail = {}, isEstimateDetailLoading } = useAppESSelector(
    (state) => state.estimateDetail
  );

  const [isSelectProjectOpen, setIsSelectProjectOpen] =
    useState<boolean>(false);
  const [selectedProject, setSelectedProject] = useState<IProject[]>([]);
  const [inputValues, setInputValues] =
    useState<IEstimateDetailData>(ESDetailsField);
  const [estValues, setEstValues] = useState<string | number>(
    inputValues?.custom_estimate_id?.toString() ||
      inputValues?.company_estimate_id?.toString() ||
      ""
  );
  const [approvedCheckInptValue, setApprovedCheckInptValue] =
    useState<IEStatusProcess>({});

  const { loadingStatus, setLoadingStatus } =
    isLoadingStatus !== undefined
      ? isLoadingStatus
      : { loadingStatus: [], setLoadingStatus: () => {} };

  const { setSelectedStatusKey, selectedStatusKey } = selectStatusKey ?? {};
  const { setCompletedConfirmOpen, completedConfirmOpen } =
    isConfirmCompletedStatus || {};
  const { setCompletedIsProjectOpen, completedIsProjectOpen } =
    isConfirmCompletedProjectStatus || {};
  const { approvedConfirmOpen, setApprovedConfirmOpen } =
    isConfirmApprovedStatus || {};
  const [isApprovedLoading, setIsApprovedLoading] = useState<boolean>(false);
  const [isFocused, setIsFocused] = useState(false);
  const { projectTypeList }: IProjectTypeListInitialState = useAppESSelector(
    (state) => state.customProjectTypeList
  );
  const { getGlobalModuleByKey } = useGlobalModule();
  const EstimateModule = useMemo(
    () => getGlobalModuleByKey(CFConfig.estimate_module),
    [getGlobalModuleByKey] // Ensures memoization based on function reference
  );
  const isComponent = useAppESSelector(
    (state) => state.frameComponent.isComponent
  );
  const {
    module_id: estimate_module_id = 0,
    module_key: estimate_module_key = "",
    module_name: estimate_module_name = "",
  } = EstimateModule || {};
  const [projectAutoNumber, setProjectAutoNumber] = useState<string>("");
  const [clicked, setClicked] = useState<boolean>(false);

  const [openConfirmModal, setOpenConfirmModal] = useState<boolean>(false);

  const [tempValue, setTempValue] = useState<string>("");

  const user: IInitialGlobalData["user"] = getGlobalUser();

  const {
    user_id = 0,
    access_group_id = "",
    type = 0,
    image = "",
    display_name = "",
  } = user || {};

  useEffect(() => {
    if (estimateDetail) {
      setInputValues(estimateDetail);
      setEstValues(
        HTMLEntities.decode(
          sanitizeString(
            isFocused
              ? estimateDetail?.custom_estimate_id?.toString() ||
                  estimateDetail?.company_estimate_id?.toString()
              : `Est. #${
                  estimateDetail?.custom_estimate_id?.toString() ||
                  estimateDetail?.company_estimate_id?.toString()
                }`
          )
        )
      );
    }
  }, [estimateDetail]);

  const onChangeProject = async (projects: IProject[]) => {
    const projectDetail = projects?.[0] ?? {};
    handleChangeFieldStatus({
      field: "project_id",
      status: "loading",
      action: "API",
    });
    let prjData: IEstimateDetailData = {};

    //for project API calling=========================================
    if (!!projectDetail?.id && projectDetail?.id != 0) {
      const prjFetchData = await dispatch(
        fetchProjectDetailsApi({
          project_id: Number(projectDetail?.id ?? 0),
          record_type: "project,opportunity",
        })
      );
      const prjRes = prjFetchData?.payload as IprojectDetailsApiRes;
      if (prjRes?.success) {
        const prjSortData = prjRes?.data;
        if (prjRes?.data) {
          prjData = {
            ...prjData,
            estimate_project_type_name: prjSortData?.project_type_name,
            estimate_project_type: prjSortData?.project_type,
            estimate_project_type_key: prjSortData?.project_type,
            est_street1: prjSortData?.address1 ?? "",
            est_street2: prjSortData?.address2 ?? "",
            est_city: prjSortData?.city ?? "",
            est_state: prjSortData?.state ?? "",
            est_zip: prjSortData?.zip ?? "",
          };
        }
      }
    } else {
      prjData = {
        ...prjData,
        prj_address1: "",
        prj_address2: "",
        prj_city: "",
        prj_state: "",
        prj_zip: "",
      };
    }

    const custDetail = !projectDetail?.id
      ? {
          //for cutomer change
          est_street1: estimateDetail?.customer_address1 ?? "",
          est_street2: estimateDetail?.customer_address2 ?? "",
          est_city: estimateDetail?.cust_city ?? "",
          est_state: estimateDetail?.cust_state ?? "",
          est_zip: estimateDetail?.cust_zip ?? "",
          // latitude: estimateDetail?.prj_latitude ?? 0,
          // longitude: estimateDetail?.prj_longitude ?? 0,
        }
      : {};

    if (!estimateDetail?.estimator_id || estimateDetail?.estimator_id == 0) {
      const res: Partial<IResponse<IEstimatorGetByProjectId>> =
        await getEstimatorGetByProjectId({
          project_id: projectDetail?.id?.toString() ?? user_id ?? 0,
        });
      if (res?.success) {
        if (res?.data && Object.keys(res?.data)?.length > 0) {
          const estimator = res?.data;
          prjData = {
            ...prjData,
            estimator_id: estimator?.estimator_id ?? user_id,
            estimator_to_type: estimator?.user_type ?? type,
            image: estimator?.user_image ?? image,
            estimator_to_dir_type: estimator?.user_type ?? type,
            estimator_display_name: estimator?.display_name ?? display_name,
          };
        }
      }
    }
    let apiPayload = {
      project_id: projectDetail?.id ?? 0,
      project_name: projectDetail?.project_name ?? "",
      prj_type: projectDetail?.prj_record_type ?? "",
      customer_id: inputValues?.customer_id,
      // project_manager_id: !!projectDetail?.project_manager_id
      //   ? projectDetail?.project_manager_id
      //   : user_id,
      billed_to_id: projectDetail?.billed_to ?? 0,
      billed_to: projectDetail?.billed_to ?? 0,
      // estimator_id: !!prjData?.estimator_id ? prjData?.estimator_id : user_id,
      estimate_project_type: !isEmpty(prjData?.estimate_project_type)
        ? prjData?.estimate_project_type
        : 0,
      approved_by: "",
      authorized_by: "",
      approved_type: "",
      authorized_by_name: "",
      ...custDetail,
      ...prjData,
    };

    if (
      (!estimateDetail?.project_manager_id ||
        estimateDetail?.project_manager_id == 0) &&
      Object.keys(projectDetail)?.length > 0
    ) {
      apiPayload["project_manager_id"] = !!projectDetail?.project_manager_id
        ? projectDetail?.project_manager_id
        : user_id;
    }
    handleUpdateField(apiPayload, {
      // id: params.id
      project_id: projectDetail?.id || "",
      estimate_project_type: projectDetail?.project_type_key ?? "",
      project_name: projectDetail?.project_name || "",
      project_type: projectDetail?.prj_record_type || "",
      prj_type: projectDetail?.prj_record_type || "",
      prj_customer_name: projectDetail?.customer_name || "",
      // project_customer_email: projectDetail?.project_customer_email || "",
      // quickbook_project_id: projectDetail?.quickbook_project_id,
      address_from: !!projectDetail?.id ? "project" : "directory",
      approved_by: "",
      authorized_by: "",
      approved_type: "",
      authorized_by_name: "",
      ...prjData,
      ...custDetail,
    });
    if (
      projectDetail &&
      projectDetail?.id &&
      projectDetail?.id != 0 &&
      projectDetail?.project_name &&
      projectDetail?.project_name != ""
    ) {
      setSelectedProject([
        {
          id: Number(projectDetail.id),
          project_name: projectDetail.project_name,
          prj_record_type: projectDetail?.prj_record_type,
        },
      ]);
      setInputValues({
        ...inputValues,
        project_id: projectDetail?.id ?? "",
        project_name: projectDetail?.project_name ?? "",
        prj_record_type: projectDetail?.prj_record_type ?? "",
      });
    } else {
      setSelectedProject([]);
      setIsSelectProjectOpen(false);
      setInputValues({
        ...inputValues,
        project_id: "",
        project_name: "",
      });
    }
  };

  let { getExistingProjectsWithApi }: UseExistingProjectsResponse =
    useExistingProjects("project");

  const fetchDefaultProject = async (id: string) => {
    const projects = await getExistingProjectsWithApi(id);
    const { project_name, key, project_type_name, project_type_key } =
      projects?.[0] || {};
    // const projectName = projects && projects[0] && projects[0].project_name;
    const projectId = Number(id) || projects[0]?.key;

    if (projectId && project_name) {
      onChangeProject(projects as IProject[]);
      await dispatch(
        addProjectTypeCustomData({
          item_id: project_type_key,
          name: project_type_name,
          item_type: EST_PROJECT_TYPE,
          key: project_type_key,
          is_status: "0",
        })
      );
    }
    setIsSelectProjectOpen(false);
  };

  const approvedOptions = useMemo(
    () =>
      Object.entries(
        processApprovedListModalNew(
          selectedStatusKey ?? "",
          estimateDetail?.project_id,
          approvedCheckInptValue,
          `${HTMLEntities.decode(
            sanitizeString(estimate_module_name ?? "Estimates")
          )}`,
          access_group_id,
          estimateDetail?.ref_invoice_id,
          estimate_copy_item_to_sov,
          estimate_generate_invoice,
          estimate_generate_project,
          guntChartData?.can_read,
          invoiceMergeData?.can_read,
          projectBudgetData?.can_read
        )
      ),
    [selectedStatusKey, approvedCheckInptValue]
  );

  const status = useMemo(() => {
    const statusList = estimateStatList?.map((item: EStatusList) => ({
      label: replaceDOMParams(sanitizeString(item.label)) || "",
      key: item?.value?.toString() ?? "",
      show_in_progress_bar: item?.show_in_progress_bar,
      sort_order: item?.sort_order,
      does_sync_qb: item?.does_sync_qb,
      icon: (
        <FontAwesomeIcon
          icon="fa-solid fa-square"
          className="h-3.5 w-3.5"
          style={{
            color: item?.default_color,
          }}
        />
      ),
    }));
    const getSelectStatus = estimateStatList?.find(
      (item) => item?.value?.toString() === activeStep?.toString()
    );
    const selectStatus = (
      <Tooltip
        title={replaceDOMParams(sanitizeString(getSelectStatus?.label)) || ""}
      >
        <div
          className={`py-0.5 rounded flex items-center justify-center w-full status-dropdown-block group/status-dropdown px-2.5 ${
            isReadOnly || isStatusLost
              ? "cursor-not-allowed"
              : "hover:px-1 cursor-pointer"
          }`}
          style={{
            backgroundColor: getSelectStatus?.default_color + "1d",
          }}
        >
          <Typography
            style={{
              color: getSelectStatus?.default_color,
            }}
            className="text-xs whitespace-nowrap truncate"
          >
            {replaceDOMParams(sanitizeString(getSelectStatus?.label)) ||
              "Select Status"}
          </Typography>
          {!isReadOnly && (
            <FontAwesomeIcon
              className="w-2.5 h-2.5 pl-0.5 group-hover/status-dropdown:flex hidden ease-in-out duration-300"
              style={{
                color: getSelectStatus?.default_color,
              }}
              icon="fa-regular fa-chevron-down"
            />
          )}
        </div>
      </Tooltip>
    );
    return { statusList, selectStatus, getSelectStatus };
  }, [estimateStatList, activeStep, estimateDetail?.approval_type]);

  const estValue = useMemo(() => {
    return HTMLEntities.decode(
      sanitizeString(
        isFocused
          ? inputValues?.custom_estimate_id?.toString()?.trim() ||
              inputValues?.company_estimate_id?.toString()?.trim()
          : `Est. #${
              inputValues?.custom_estimate_id?.toString()?.trim() ||
              inputValues?.company_estimate_id?.toString()?.trim()
            }`
      )
    );
  }, [inputValues]);
  // const projectModule = getGlobalModuleByKey(CFConfig.project_module);
  // const types = getGlobalTypes();
  const handleStatus: MenuProps["onClick"] = (e) => {
    const sort_Value = estimateStatList?.find((item) => item?.key === e?.key);
    // setSelectedStatusKey(sort_Value?.key);
    // setActiveStep(Number(sort_Value?.sort_order));
    if (sort_Value?.label && !clicked) {
      setClicked(true);
      return handleStatusChange(sort_Value?.label);
    }

    if (inputValues?.approval_type !== e?.key)
    handleUpdateField({
      approval_type: e?.key,
      approval_type_name: sort_Value?.label,
      customer_id: inputValues?.customer_id,
    });
  };

  // status type dropdown Options
  // const projectStatusOptions = useMemo(
  //   () =>
  //     projectTypeList.length
  //       ? projectTypeList
  //           .filter(
  //             (item) =>
  //               String(item.item_type) ===
  //               String(CFConfig.project_status_type_id)
  //           )
  //           .map((item) => {
  //             return {
  //               label: `${HTMLEntities.decode(sanitizeString(item.name))}`,
  //               value: String(item.item_id),
  //             };
  //           })
  //       : [],
  //   [projectTypeList]
  // );

  // // project type dropdown Options
  // const projectTypeOptions = useMemo(
  //   () =>
  //     projectTypeList.length
  //       ? projectTypeList
  //           .filter(
  //             (item) =>
  //               String(item.item_type) === String(CFConfig.project_type_key_id)
  //           )
  //           .map((item) => {
  //             return {
  //               label: `${HTMLEntities.decode(sanitizeString(item.name))}`,
  //               value: String(item.item_id),
  //             };
  //           })
  //       : [],
  //   [projectTypeList]
  // );

  const getAutoNumber = async () => {
    // setNumberLoading(true);
    const autoNumberRes = (await getModuleAutoNumber({
      module_id: estimate_module_id,
      module_key: estimate_module_key,
    })) as GetModuleAutoNumberApiResponse;
    if (autoNumberRes.success) {
      const newId = autoNumberRes?.data?.last_primary_id
        ? Number(autoNumberRes.data.last_primary_id) +
          autoNumberRes.data?.need_to_increment
        : "";
      setProjectAutoNumber(String(newId));
    }
    if (!autoNumberRes?.success) {
      notification.error({
        description: autoNumberRes.message || "Something went wrong",
      });
    }
    // setNumberLoading(false);
  };

  const handleStatusChange = async (val: string) => {
    const selectedOption = estimateStatList?.find(
      (option) => option?.label === val?.toString()
    );
    const existingselectedOption = estimateStatList?.find(
      (option) => option?.value === activeStep
    );
    // check if the active step is greater than 5 and curr step where use clicked is 1 or 2 then open confirm modal and store the temp value
    if (
      activeStep &&
      // 4 is Approved & 8 is Completed
      ["estimate_approved", "estimate_completed"]?.includes(
        existingselectedOption?.key || ""
      ) &&
      // 1 is On hold & 2 is Estimate & 7 is template
      ["estimate_on_hold", "estimate_bidding", "estimate_template"]?.includes(
        selectedOption?.key || ""
      )
      // Number(activeStep) >= 5 &&
      // (selectedOption?.sort_order === 1 || selectedOption?.sort_order === 2 || selectedOption?.sort_order === 7)
    ) {
      setOpenConfirmModal(true);
      setTempValue(val);
    } else {
      await onIconClickChange(val);
    }
  };

  const onIconClickChange = async (val: string) => {
    const selectedOption = estimateStatList.find(
      (option) => option.label === val.toString()
    );

    const existingselectedOption = estimateStatList.find(
      (option) => option.value === activeStep
    );
    try {
      if (selectedOption?.value !== existingselectedOption?.value) {
        setActiveStep(selectedOption?.value!);
        await handleUpdateField({
          approval_type: selectedOption?.value,
          approval_type_name: selectedOption
            ? selectedOption.label
            : "Select Status",
          project_id: estimateDetail?.project_id,
          customer_id: estimateDetail?.customer_id,
        });

        setClicked(false);
      } else {
        handleChangeFieldStatus({
          field: "approval_type",
          status: "button",
          action: "BLUR",
        });
        setActiveStep(existingselectedOption?.value!);

        setClicked(false);
      }
    } catch (error) {
      setActiveStep(existingselectedOption?.value!);
    }
    setTempValue("");
  };

  useEffect(() => {
    if (estimateDetail?.customer_id) {
      dispatch(
        getCustomData({
          types: [188, 226],
          moduleId: estimate_module_id,
        })
      );
      getAutoNumber();
    }
  }, [estimateDetail?.customer_id]);

  useEffect(() => {
    if (
      inputValues &&
      !!inputValues?.project_id &&
      inputValues?.project_id != 0 &&
      !!inputValues?.project_name &&
      inputValues?.project_name != ""
    ) {
      setSelectedProject([
        {
          id: Number(inputValues?.project_id),
          project_name: inputValues?.project_name,
          prj_record_type: inputValues?.prj_type,
        },
      ]);
    } else {
      setSelectedProject([]);
    }
  }, [estimateDetail, inputValues]);

  const handleInpOnChange = ({
    target: { value, name },
  }: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setInputValues({ ...inputValues, [name]: value });
  };

  const handleEstCodeOnChange = ({
    target: { value, name },
  }: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    // setInputValues({ ...inputValues, [name]: value });
    setEstValues(value);
  };

  const statusApprovedModalHandler = async (value: IEStatusProcess) => {
    try {
      if (!isCompletedLoading) {
        setIsApprovedLoading(true);

        const payload: IEStatusProcess = {
          estimate_id: Number(estimateDetail?.estimate_id),
          // customer_id: estimateDetail?.customer_id,
          ...value,
        };
        const isUpdateCLSection = (await updateStatusProcessApi(
          payload
        )) as IEDetailsApiRes;

        if (isUpdateCLSection?.success) {
          // if we get data add get dispatch store the obj ====>
          setIsApprovedLoading(false);
          setApprovedCheckInptValue({});
          setApprovedConfirmOpen({ action: false, value: null });
          dispatch(
            getEstimateDetail({
              estimate_id: estimateDetail?.estimate_id,
              silent: true,
            })
          );
        } else {
          notification.error({
            description: isUpdateCLSection?.message,
          });
          setIsApprovedLoading(false);
          setApprovedCheckInptValue({});
          setApprovedConfirmOpen({ action: false, value: null });
        }
      } else {
        notification.error({
          description: "Something went wrong!",
        });
        setIsApprovedLoading(false);
        setApprovedCheckInptValue({});
        setApprovedConfirmOpen({ action: false, value: null });
      }
    } catch (err) {
      notification.error({
        description: (err as Error)?.message || "Something went wrong!",
      });
      setIsApprovedLoading(false);
    } finally {
      setIsApprovedLoading(false);
      setApprovedCheckInptValue({});
      setApprovedConfirmOpen({ action: false, value: null });
    }
  };
  const isStatusNotShow = useMemo(() => {
    if (status?.statusList)
      return status?.statusList?.find((el) => el.key === selectedStatusKey)
        ?.show_in_progress_bar;
  }, [selectedStatusKey, status?.statusList]);
  return (
    <>
      <div className="sticky top-0 z-[99] bg-[#f8f8f8] p-[15px] pb-0 mb-[15px]">
        <div className="flex items-center bg-white dark:bg-dark-800 py-[5px] px-3.5 shadow-[0_4px_24px_0] shadow-[#22292f1a] rounded-md">
          <div className="w-full flex md:flex-row flex-col-reverse md:items-center justify-between sm:gap-2 gap-1.5">
            {isLoading ? (
              <TopBarSkeleton statusList={true} num={isLoadLock ? 0 : 5} />
            ) : (
              <>
                <div className="flex items-center xl:flex-[1_0_0%] xl:w-[calc(25%-75px)] md:max-w-[calc(100%-125px)] w-full">
                  <div
                    className={`w-11 h-11 flex items-center justify-center ${
                      // estimateStatusVal?.default_color ??
                      "bg-[#44539A]"
                    } rounded-full relative before:absolute before:w-[39px] before:h-[39px] before:top-1/2 before:left-1/2 before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full before:border-2 before:border-white`}
                    style={{
                      backgroundColor:
                        estimateStatusVal?.default_color ?? "#223558",
                    }}
                  >
                    {estimateStatusVal?.icon && (
                      <FontAwesomeIcon
                        className="w-[18px] h-[18px] text-white"
                        icon={estimateStatusVal?.icon}
                      />
                    )}
                  </div>
                  <div
                    className={`flex flex-col gap-0.5 w-[calc(100%-44px)] 2xl:pr-3.5 ${
                      isReadOnly ? "pl-2" : "pl-2.5"
                    }`}
                  >
                    <Tooltip
                      title={`Title: ${HTMLEntities?.decode(
                        sanitizeString(inputValues?.title)
                      )}`}
                      placement="topLeft"
                    >
                      <div className="max-w-[450px]">
                        <InputField
                          placeholder={_t("Title")}
                          labelPlacement="left"
                          className="h-[22px] py-0 text-base font-medium"
                          readOnlyClassName="text-base h-[22px] !font-medium whitespace-nowrap truncate block"
                          formInputClassName="ellipsis-input-field"
                          labelClass="hidden"
                          inputStatusClassName="!w-[15px] !h-[15px]"
                          iconClassName="!w-3 !h-3"
                          editInline={true}
                          iconView={true}
                          disabled={
                            getStatusForField(loadingStatus ?? [], "title") ===
                              "loading" || isReadOnly
                          }
                          // readOnly={isReadOnly}
                          fixStatus={getStatusForField(
                            loadingStatus ?? [],
                            "title"
                          )}
                          name="title"
                          value={HTMLEntities.decode(
                            sanitizeString(inputValues?.title || "")
                          )}
                          maxLength={200}
                          onChange={handleInpOnChange}
                          onMouseEnter={() => {
                            handleChangeFieldStatus({
                              field: "title",
                              status: "edit",
                              action: "ME",
                            });
                          }}
                          onMouseLeaveDiv={() => {
                            handleChangeFieldStatus({
                              field: "title",
                              status: "button",
                              action: "ML",
                            });
                          }}
                          onFocus={() =>
                            handleChangeFieldStatus({
                              field: "title",
                              status: "save",
                              action: "FOCUS",
                            })
                          }
                          onBlur={(e) => {
                            setIsFocused(false);
                            const value = e?.target?.value.trim();
                            if (value === "") {
                              handleChangeFieldStatus({
                                field: "title",
                                status: "error",
                                action: "BLUR",
                              });
                              notification.error({
                                description: _t("Title field is required."),
                              });
                            }
                            if (!isEmpty(value)) {
                              if (value !== estimateDetail?.title) {
                                handleUpdateField({
                                  title: `${HTMLEntities.encode(value)}`,
                                  customer_id: inputValues?.customer_id,
                                });
                              } else {
                                handleChangeFieldStatus({
                                  field: "title",
                                  status: "button",
                                  action: "BLUR",
                                });
                                setInputValues({
                                  ...inputValues,
                                  title: inputValues?.title?.trim(),
                                });
                              }
                            } else {
                              handleChangeFieldStatus({
                                field: "title",
                                status: "button",
                                action: "BLUR",
                              });
                              setInputValues({
                                ...inputValues,
                                title: estimateDetail?.title?.trim(),
                              });
                            }
                          }}
                        />
                      </div>
                    </Tooltip>
                    <ButtonField
                      label=""
                      labelProps={{
                        labelClass: "!hidden",
                      }}
                      name="project_id"
                      labelPlacement="left"
                      editInline={true}
                      iconView={true}
                      placeholder={_t("Select Project or Opportunity")}
                      required={true}
                      className="h-6 py-0.5 w-full gap-0"
                      readOnlyClassName="text-sm font-medium whitespace-nowrap truncate sm:block flex"
                      inputClassName="w-fit"
                      fieldClassName="w-auto"
                      spanWidthClass="w-fit"
                      buttonClassName="!text-sm font-medium"
                      value={
                        !!inputValues?.project_name && !!inputValues?.project_id
                          ? HTMLEntities.decode(
                              sanitizeString(inputValues?.project_name || "")
                            )
                          : ""
                      }
                      headerTooltip={`${
                        inputValues?.prj_type === "project"
                          ? "Project"
                          : "Opportunity"
                      }: ${
                        !!inputValues?.project_name && !!inputValues?.project_id
                          ? HTMLEntities.decode(
                              sanitizeString(inputValues?.project_name || "")
                            )
                          : ""
                      }`}
                      onClick={() => setIsSelectProjectOpen(true)}
                      disabled={
                        getStatusForField(loadingStatus ?? [], "project_id") ===
                          "loading" || isReadOnly
                      }
                      // readOnly={isReadOnly}
                      onBlur={() => {
                        handleChangeFieldStatus({
                          field: "project_id",
                          status: "button",
                          action: "BLUR",
                        });
                      }}
                      statusProps={{
                        status: getStatusForField(
                          loadingStatus ?? [],
                          "project_id"
                        ),
                        className: "right-6 flex",
                        iconProps: {
                          className: "!w-[15px] !h-[15px]",
                        },
                      }}
                      rightIcon={
                        inputValues?.project_id &&
                        inputValues?.project_id != "" ? (
                          inputValues?.prj_type === "project" ? (
                            <ProjectFieldRedirectionIcon
                              projectId={
                                inputValues?.project_id?.toString() || ""
                              }
                              onClick={(e) => {
                                e?.stopPropagation();
                              }}
                            />
                          ) : inputValues?.prj_type === "opportunity" ? (
                            <OpportunityFieldRedirectionIcon
                              projectId={
                                inputValues?.project_id?.toString() || ""
                              }
                              onClick={(e) => {
                                e?.stopPropagation();
                              }}
                            />
                          ) : (
                            <></>
                          )
                        ) : (
                          <></>
                        )
                      }
                    />
                    <div
                      className={`flex items-center gap-2 ${
                        isReadOnly ? "pl-1.5" : ""
                      }`}
                    >
                      <div className="flex gap-2 items-center">
                        <div className="flex gap-2 items-center">
                          <div className="flex gap-2 items-center">
                            {/* Notes: This dropdown use as per to-do module */}
                            <Dropdown
                              menu={{
                                items: status?.statusList?.map((el) => ({
                                  ...el,
                                  label: `${el?.label}${
                                    el?.does_sync_qb &&
                                    (toBoolean(quickbook_sync) ||
                                      toBoolean(quickbook_desktop_sync))
                                      ? " (Post To QuickBooks)"
                                      : ""
                                  }`,
                                })),
                                selectedKeys: [selectedStatusKey ?? ""],
                                selectable: true,
                                defaultSelectedKeys: [
                                  (activeStep as string) || "",
                                ],
                                onClick: handleStatus, // change this in future ( temporary resolve type issue ),
                              }}
                              disabled={
                                isReadOnly ||
                                isStatusLost ||
                                getStatusForField(
                                  loadingStatus ?? [],
                                  "approval_type"
                                ) == "loading"
                              }
                              trigger={["click"]}
                              overlayClassName="dropdown-color-option-block !min-w-40"
                            >
                              {status?.selectStatus}
                            </Dropdown>
                            {["loading", "success", "error"].includes(
                              getStatusForField(
                                loadingStatus ?? [],
                                "approval_type"
                              )
                            ) && (
                              <FieldStatus
                                className="flex items-center"
                                iconProps={{
                                  className: "!w-[15px] !h-[15px]",
                                }}
                                status={getStatusForField(
                                  loadingStatus ?? [],
                                  "approval_type"
                                )}
                              />
                            )}
                          </div>
                        </div>
                      </div>
                      <Tooltip title={estValue} placement="topLeft">
                        <div
                          className={`overflow-hidden ${
                            isReadOnly || is_custom_estimate_id !== 0
                              ? "w-fit"
                              : "w-full"
                          }
                          `}
                        >
                          <InputField
                            placeholder={_t("Est.") + " #"}
                            labelPlacement="left"
                            name="custom_estimate_id"
                            id="custom_estimate_id"
                            formInputClassName="ellipsis-input-field"
                            className="h-[22px] text-sm font-medium py-0"
                            readOnlyClassName="text-sm font-medium whitespace-nowrap truncate block"
                            maxLength={21}
                            inputStatusClassName="!w-[15px] !h-[15px]"
                            iconClassName="!w-3 !h-3"
                            // disabled={isReadOnly}
                            disabled={
                              isReadOnly
                                ? true
                                : is_custom_estimate_id !== 0
                                ? false
                                : true
                            }
                            // readOnly={
                            //   isReadOnly
                            //     ? true
                            //     : is_custom_estimate_id !== 0
                            //     ? false
                            //     : true
                            // }
                            // value={`EST #${
                            //   estimateDetail?.custom_estimate_id ?? ""
                            // }`}
                            value={estValues}
                            editInline={true}
                            iconView={true}
                            onChange={handleEstCodeOnChange} // Temporary resolve type issue
                            // readOnly={isReadOnly}
                            fixStatus={getStatusForField(
                              loadingStatus ?? [],
                              "custom_estimate_id"
                            )}
                            onMouseEnter={() => {
                              handleChangeFieldStatus({
                                field: "custom_estimate_id",
                                status: "edit",
                                action: "ME",
                              });
                            }}
                            onMouseLeaveDiv={() => {
                              handleChangeFieldStatus({
                                field: "custom_estimate_id",
                                status: "button",
                                action: "ML",
                              });
                            }}
                            onFocus={() => {
                              setIsFocused(true);
                              handleChangeFieldStatus({
                                field: "custom_estimate_id",
                                status: "save",
                                action: "FOCUS",
                              });
                              setEstValues(
                                HTMLEntities.decode(
                                  sanitizeString(
                                    estimateDetail?.custom_estimate_id?.toString() ||
                                      estimateDetail?.company_estimate_id?.toString()
                                  )
                                )
                              );
                              if (inputValues?.custom_estimate_id) {
                                setInputValues((prev) => {
                                  return {
                                    ...prev,
                                    custom_estimate_id:
                                      prev?.custom_estimate_id,
                                  };
                                });
                              }
                            }}
                            onBlur={(e) => {
                              setIsFocused(false);
                              const value = e?.target?.value
                                ?.replace(/\s+/g, " ")
                                ?.trim();
                              if (value?.includes("Est. #")) {
                                value?.replace("Est. #", "");
                              }
                              const newValue = value?.includes("Est. #")
                                ? value?.replace("Est. #", "")
                                : value;

                              // Handle saving the updated value
                              if (newValue === "") {
                                notification.error({
                                  description: _t("Est. # field is required."),
                                });
                                // handleUpdateField({
                                //   custom_estimate_id: estValue,
                                //   customer_id: estimateDetail?.customer_id,
                                // });
                              }
                              if (
                                newValue !==
                                  estimateDetail?.custom_estimate_id &&
                                newValue !== ""
                              ) {
                                setInputValues({
                                  ...inputValues,
                                  custom_estimate_id: newValue,
                                });
                                handleUpdateField({
                                  custom_estimate_id: newValue,
                                  customer_id: estimateDetail?.customer_id,
                                });
                              } else {
                                handleChangeFieldStatus({
                                  field: "custom_estimate_id",
                                  status: "button",
                                  action: "BLUR",
                                });
                                setInputValues({
                                  ...inputValues,
                                  custom_estimate_id:
                                    inputValues?.custom_estimate_id
                                      ?.replace(/\s+/g, " ")
                                      ?.trim(),
                                });
                                setEstValues(
                                  HTMLEntities.decode(
                                    sanitizeString(
                                      !isFocused
                                        ? estimateDetail?.custom_estimate_id?.toString() ||
                                            estimateDetail?.company_estimate_id?.toString()
                                        : `Est. #${
                                            estimateDetail?.custom_estimate_id?.toString() ||
                                            estimateDetail?.company_estimate_id?.toString()
                                          }`
                                    )
                                  )
                                );
                              }
                            }}
                          />
                        </div>
                      </Tooltip>
                    </div>
                  </div>
                </div>
                {isStatusNotShow ? (
                  <div className="w-auto flex-[0_0_auto]">
                    <ul className="items-center justify-center 2xl:-ml-3.5 w-[calc(100%-0px)] xl:flex hidden">
                      {!isEstimateDetailLoading && !isLoadLock ? (
                        <EstimateStatusbar
                          handleUpdateField={async (
                            data: IEstimateDetailData
                          ) => handleUpdateField(data)}
                          handleChangeFieldStatus={handleChangeFieldStatus}
                          estimateStatusList={estimateStatList}
                          isReadOnly={Boolean(isReadOnly)}
                          isStatusLost={Boolean(isStatusLost)}
                          activeStep={activeStep}
                          setActiveStep={setActiveStep}
                          selectedStatusInd={selectedStatusInd}
                        />
                      ) : (
                        <></>
                      )}
                    </ul>
                  </div>
                ) : (
                  <></>
                )}
                <div className="xl:flex-[1_0_0%] xl:w-[calc(25%-75px)] md:w-fit w-full">
                  <div className="flex justify-between">
                    <div className="flex gap-2.5">
                      {!window.ENV.PAGE_IS_IFRAME && !isComponent && (
                        <div
                          className="flex items-center cursor-pointer md:!hidden"
                          onClick={() => {
                            const params: Partial<IframeRouteParams> =
                              parseParamsFromURL(window?.location?.pathname);
                            if (params?.page && params?.id && !isComponent) {
                              navigate("/" + params?.page);
                            } else {
                              setSearchParams({});
                            }
                          }}
                        >
                          <IconButton
                            htmlType="button"
                            variant="default"
                            className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                            iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                            icon="fa-regular fa-chevron-left"
                          />
                        </div>
                      )}
                      <IconButton
                        htmlType="button"
                        variant="default"
                        className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                        iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                        icon="fa-regular fa-bars"
                        onClick={() =>
                          setCommonSidebarCollapse(!sidebarCollapse)
                        }
                      />
                    </div>

                    <div className="flex items-center gap-2">
                      {estimateDetail?.is_lock ? (
                        <div className="flex items-center text-red-500 ">
                          {estimateDetail?.locked_by === user_id
                            ? `${replaceDOMParams(
                                sanitizeString(estimate_module_name)
                              )} ${_t(" Locked")}`
                            : `${replaceDOMParams(
                                sanitizeString(estimate_module_name)
                              )} ${_t(" Locked by ")} ${replaceDOMParams(
                                sanitizeString(
                                  estimateDetail?.locked_by_username
                                )
                              )}`}
                        </div>
                      ) : (
                        <></>
                      )}
                      <ul className="flex justify-end gap-2.5">
                        {
                          <li>
                            <ButtonWithTooltip
                              tooltipTitle={_t("Refresh")}
                              tooltipPlacement="top"
                              icon="fa-regular fa-arrow-rotate-right"
                              iconClassName={`!text-primary-900 ${
                                isLoading && "fa-spin"
                              } ${
                                isLoading
                                  ? "group-hover/buttonHover:!text-primary-900"
                                  : "group-hover/buttonHover:!text-deep-orange-500"
                              }`}
                              className={`!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !shadow-primary-200 ${
                                isLoading
                                  ? "hover:bg-transparent"
                                  : "hover:!bg-deep-orange-500/5"
                              }`}
                              disabled={isLoading}
                              onClick={() => {
                                onReloadEstimateDetails();
                              }}
                            />
                          </li>
                        }
                        <li>
                          <ButtonWithTooltip
                            tooltipTitle={
                              inputValues?.is_lock
                                ? _t(
                                    "This Estimate can only be Unlocked by the Creator or Admin"
                                  )
                                : _t(
                                    "When Locked, the Estimate can only be edited by the Creator or Admin"
                                  )
                            }
                            tooltipPlacement="top"
                            icon={
                              inputValues?.is_lock
                                ? "fa-regular fa-lock-open"
                                : "fa-regular fa-lock-keyhole"
                            }
                            iconClassName={`!text-primary-900 ${
                              isLoading || isReadOnly
                                ? "group-hover/buttonHover:!text-primary-900"
                                : "group-hover/buttonHover:!text-deep-orange-500"
                            } `}
                            className={`!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !shadow-primary-200 ${
                              isLoading || isReadOnly
                                ? "hover:bg-transparent"
                                : "hover:!bg-deep-orange-500/5"
                            }`}
                            disabled={isReadOnly}
                            //@ts-ignore fix ButtonWithTooltip props type
                            loading={
                              getStatusForField(
                                loadingStatus ?? [],
                                "is_lock"
                              ) === "loading"
                            }
                            onClick={() => {
                              handleUpdateField({
                                is_lock: inputValues?.is_lock == 1 ? 0 : 1,
                                customer_id: inputValues?.customer_id
                                  ? Number(inputValues?.customer_id)
                                  : undefined,
                              });
                            }}
                          />
                        </li>
                        {!isReadOnly && (
                          <li>
                            <EstimateListAction
                              tooltipcontent={_t("More")}
                              paramsData={estimateDetail}
                              buttonClass={`!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !shadow-primary-200 ${"hover:bg-transparent hover:!bg-deep-orange-500/5"}`}
                              isLoadLock={isLoadLock}
                              refreshAgGrid={onReloadEstimateDetails}
                              iconClassName={`text-primary-900 ${"group-hover/buttonHover:text-deep-orange-500"}`}
                              enableOpenInNewTab={Number(
                                window.ENV.ENABLE_ALL_CLICK
                              )}
                            />
                          </li>
                        )}
                      </ul>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
      {isSelectProjectOpen && (
        <SelectProject
          isSingleSelect={true}
          open={isSelectProjectOpen}
          setOpen={setIsSelectProjectOpen}
          selectedProjects={selectedProject}
          onProjectSelected={(data: IProject[]) => {
            onChangeProject(data);
          }}
          customer_id={estimateDetail?.customer_id?.toString()}
          isRequired={false}
          module_key={estimate_module_key}
          genericProjects="project,opportunity"
          isShowProjectType={true}
          addNewProjectProps={{
            onNewProjectAdd: (insertId) => {
              fetchDefaultProject(insertId);
            },
          }}
        />
      )}

      {Boolean(completedConfirmOpen?.action) && (
        <ConfirmModal
          isOpen={Boolean(completedConfirmOpen?.action)}
          modaltitle={_t("Confirmation")}
          modalIcon="fa-regular fa-file-check"
          description={_t("Set the Project status to Completed?")}
          isLoading={isCompletedLoading}
          onAccept={() => {
            statusCompletedModalHandler(Number(1));
          }}
          onDecline={async () => {
            // onCloseCompletedModal();
            await statusCompletedModalHandler(Number(0));
            if (selectedStatusKey == "estimate_completed") {
              setApprovedConfirmOpen({ action: true, value: null });
            }
          }}
          onCloseModal={() => {
            onCloseCompletedModal();
          }}
        />
      )}
      {/* ==============Approved modal================= */}
      {
        <CommonModal
          isOpen={Boolean(approvedConfirmOpen?.action)}
          onCloseModal={() => {
            setApprovedConfirmOpen({ action: false, value: null });
            setApprovedCheckInptValue({});
          }}
          widthSize="500px"
          modalBodyClass="p-0"
          header={{
            title: _t("Do You want To..."),
            // subTitle:
            //   "After the import, you will be able to map the columns in the CSV to the Estimate fields.",
            // icon: <FontAwesomeIcon className="w-4 h-4" icon={faDownToBracket} />,
            closeIcon: true,
          }}
        >
          {/* Create a New Project based on the Estimate-Slr/Customer details? */}
          {/* Add these items to your Schedule of Values? */}
          {/* Generate an Invoice from your Estimate-Slr items? */}
          {/* Create Schedule from Estimate? */}
          <div>
            {approvedOptions.map(([key, option], index) => {
              if (!option.active) return null; // Early return for inactive options

              const isChecked = Boolean(approvedCheckInptValue[option?.value]);

              const handleCheckboxChange = (checked: boolean) => {
                const updatedValue = { [option?.value]: checked ? 1 : 0 };

                setApprovedCheckInptValue((prev) => {
                  // Special condition for "generate_project"
                  return option?.value === "generate_project" && !checked
                    ? updatedValue
                    : { ...prev, ...updatedValue };
                });
              };

              return (
                <div
                  key={`approved-modal-${index}`}
                  className="my-2 px-4 grid gap-1"
                >
                  <CheckBox
                    key={option?.value}
                    className="gap-1.5 w-fit"
                    checked={isChecked}
                    onChange={(e) => handleCheckboxChange(e.target.checked)}
                  >
                    {option?.label}
                  </CheckBox>
                </div>
              );
            })}
            <div className="flex items-center justify-center py-3">
              <PrimaryButton
                htmlType="submit"
                isLoading={isApprovedLoading}
                buttonText="Ok"
                className="flex w-12"
                onClick={() => {
                  if (Object.keys(approvedCheckInptValue)?.length > 0) {
                    statusApprovedModalHandler(approvedCheckInptValue);
                  } else {
                    setApprovedConfirmOpen({ action: false, value: null });
                    setApprovedCheckInptValue({});
                  }
                }}
                disabled={isApprovedLoading}
              />
            </div>
          </div>
        </CommonModal>
      }

      {/* ================If Project not selected============= */}
      <ConfirmModal
        // isOpen={true}
        isOpen={Boolean(completedIsProjectOpen?.action)}
        modaltitle={_t("Confirmation")}
        modalIcon="fa-regular fa-file-check"
        description={_t(
          "There is a Project associated with this Customer. Do you want to assign it to this estimate now?"
        )}
        isLoading={isCompletedProjectLoading}
        onAccept={() => {
          // statusCompletedModalHandler(Number(1));
          setIsSelectProjectOpen(true);
          setCompletedIsProjectOpen({
            action: false,
            value: null,
          });
        }}
        onDecline={() => {
          // onCloseCompletedModal();
          // statusCompletedModalHandler(Number(0));
          setCompletedIsProjectOpen({
            action: false,
            value: null,
          });
          setApprovedConfirmOpen({ action: true, value: null });

          // setCompletedConfirmOpen({
          //   action: true,
          //   value: null,
          // });
        }}
        onCloseModal={() => {
          // onCloseCompletedModal();
          setCompletedIsProjectOpen({
            action: false,
            value: null,
          });
          setApprovedConfirmOpen({ action: true, value: null });
        }}
      />
      {openConfirmModal && (
        <ConfirmModal
          isOpen={openConfirmModal}
          modaltitle={_t("Warning")}
          modalIcon="fa-regular fa-triangle-exclamation"
          onAccept={() => {
            onIconClickChange(tempValue);
            setOpenConfirmModal(false);
          }}
          onDecline={() => {
            // onCloseCompletedModal();
            setOpenConfirmModal(false);
            setClicked(false);
            setTempValue("");
          }}
          onCloseModal={() => {
            setOpenConfirmModal(false);
            setClicked(false);
            setTempValue("");
          }}
          description={_t(
            "You are about to make changes to an Approved Estimate. This action will affect your SOV and other financial modules such as Invoices. This action can NOT BE UNDONE and may cause errors. Do you want to continue?"
          )}
        />
      )}
      {/* <p>{"There is a Project associated with this Customer. Do you want to assign it to this estimate now?"}</p>
      </ConfirmModal> */}
      {/* )} */}
    </>
  );
};

export default DetailsTopBar;
