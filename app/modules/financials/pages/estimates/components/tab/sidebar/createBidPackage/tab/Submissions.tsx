import { Typography } from "~/shared/components/atoms/typography";
import { FormikProps } from "formik";
import { useEffect, useState } from "react";
import { getApiDefaultParams, sanitizeString } from "~/helpers/helper";
import { useTranslation } from "~/hook";
import {
  getOneEstBidding,
  updateEstBidder,
} from "~/modules/financials/pages/estimates/redux/action/ESBiddingAction";
import {
  useAppESDispatch,
  useAppESSelector,
} from "~/modules/financials/pages/estimates/redux/store";
import { BIDDERS_BID_STATUS } from "~/modules/financials/pages/estimates/utils/constants";
// Atoms
import { Dropdown } from "~/shared/components/atoms/dropDown";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Popover } from "~/shared/components/atoms/popover";
import { Tooltip } from "~/shared/components/atoms/tooltip";
// molecules
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { getGlobalUser } from "~/zustand/global/user/slice";
import { getEstimateItems } from "~/modules/financials/pages/estimates/redux/action/ESItemAction";

const Submissions = ({
  formik,
  biddingId,
  isEdit,
  isReadOnly,
  isBidClosed,
}: BiddingBidderProps) => {
  const { _t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [selectedBidder, setSelectedBidder] = useState<
    TselectedContactSendMail[] | null
  >(null);
  const [bidderTooltipIndex, setBidderTooltipIndex] = useState<number>();
  const [confirmApplyBidPriceDialogOpen, setConfirmApplyBidPriceDialogOpen] =
    useState<boolean>(false);
  const [applyBidPrice, setApplyBidPrice] = useState<0 | 1>(0);
  const [selectedBidderId, setSelectedBidderId] = useState<number>(0);
  const [
    confirmSendBidNotificationDialogOpen,
    setConfirmSendBidNotificationDialogOpen,
  ] = useState<boolean>(false);
  const [confirmRebidDialogOpen, setConfirmRebidDialogOpen] =
    useState<boolean>(false);
  const [selectedBidderForStatus, setSelectedBidderForStatus] =
    useState<Bidder | null>(null);

  const { formatter } = useCurrencyFormatter();
  const dispatch = useAppESDispatch();
  const { estimateDetail = {} } = useAppESSelector(
    (state) => state.estimateDetail
  );

  const submittedBidders = (formik?.values?.bidders ?? [])?.filter(
    (bidder: Bidder) =>
      bidder?.bid_status !== null && bidder?.bid_status !== undefined
  );

  useEffect(() => {
    if (selectedBidder?.length) {
      const bidderData: Partial<Bidder>[] = selectedBidder?.map((item) => {
        return {
          bidder_id: item?.user_id,
          bidder_company_name: item?.company_name ?? "",
          bidder_name: item?.display_name,
        };
      });
      formik?.setFieldValue("bidders", [
        ...(formik?.values?.bidders ?? []),
        ...bidderData,
      ]);
    }
  }, [selectedBidder]);

  async function fetchBiddingData() {
    try {
      const response = await dispatch(
        getOneEstBidding({
          estimate_id: estimateDetail?.estimate_id,
          bidding_id: biddingId,
        })
      );
      if ((response?.payload as IDefaultAPIRes)?.success) {
        const data = response?.payload?.data;
        formik?.setValues({
          ...formik.values,
          ...data,
          bid_items: data?.items ?? [],
          bidders: data?.bidder ?? [],
        });
      } else {
        notification.error({
          description: (response?.payload as IDefaultAPIRes)?.message,
        });
      }
    } catch (e: unknown) {
      notification.error({
        description: (e as Error).message,
      });
    }
  }

  async function updateEstimateBidderApicall({
    bidderStatus,
    applyBidPrice,
    sendBidNotification,
  }: {
    bidderStatus: { bidder_id: number; bid_status: string }[];
    applyBidPrice: 0 | 1;
    sendBidNotification: 0 | 1;
  }) {
    const response = (await updateEstBidder({
      estimate_id: estimateDetail?.estimate_id,
      bidding_id: biddingId,
      apply_bid_price: applyBidPrice,
      bidder_status: bidderStatus,
      send_bid_notification: sendBidNotification,
    })) as IDefaultAPIRes;

    if (response?.success) {
      fetchBiddingData();
      if (applyBidPrice === 1) {
        dispatch(
          getEstimateItems({ estimate_id: estimateDetail?.estimate_id })
        );
      }
      // notification.success({
      //   description: response?.message,
      // });
    } else {
      notification.error({
        description: response?.message,
      });
    }
  }

  async function changeBidStatus(
    selectedValue: { key: number | string },
    bypassConfirmation: boolean = false,
    bidderId: number,
    sendBidNotification: 0 | 1 = 0,
    addRebidBidder: boolean = false
  ) {
    let updatedBidderStatus = [];
    if (!bypassConfirmation && selectedValue?.key === "bid_status_awarded") {
      setConfirmApplyBidPriceDialogOpen(true);
      return;
    }

    if (!bypassConfirmation && selectedValue?.key === "bid_status_rebid") {
      setConfirmRebidDialogOpen(true);
      return;
    }

    if (addRebidBidder) {
      // add same bidder again
      if (isEdit) {
        updateEstBidder({
          estimate_id: estimateDetail?.estimate_id,
          bidding_id: Number(biddingId),
          bidders: [
            { item_id: 0, user_id: selectedBidderForStatus?.bid_user_id },
          ],
        })?.then((response) => {
          const res = response as IDefaultAPIRes;
          if (res?.success) {
            fetchBiddingData();
          } else {
            notification.error({
              description: res?.message,
            });
          }
        });
      }
    }

    if (selectedValue?.key === "bid_status_awarded") {
      updatedBidderStatus = formik?.values?.bidders
        ?.map((item: Bidder) => {
          const isAwarded = item.bid_status === "bid_status_awarded";
          const isInReview = item.bid_status === "bid_status_in_review";
          if (isInReview && item?.bidder_id !== bidderId) {
            return {
              bidder_id: item?.bidder_id,
              bid_status: "bid_status_lost",
            };
          }

          if (isAwarded && item?.bidder_id !== bidderId) {
            return {
              bidder_id: item?.bidder_id,
              bid_status: "",
            };
          }
          if (item?.bidder_id === bidderId) {
            return {
              bidder_id: item?.bidder_id,
              bid_status: selectedValue?.key?.toString(),
            };
          }
          return null;
        })
        .filter((item: Bidder) => item !== null);
    } else {
      updatedBidderStatus = [
        {
          bidder_id: bidderId,
          bid_status: selectedValue?.key?.toString(),
        },
      ];
    }

    updateEstimateBidderApicall({
      bidderStatus: updatedBidderStatus,
      applyBidPrice,
      sendBidNotification,
    });
  }

  return (
    <>
      <div className="grid grid-col gap-3">
        <SidebarCardBorder>
          <Typography className="text-sm text-[#343a40e3] font-semibold">
            {_t("Bidders Submissions")}
          </Typography>

          {/*DEVELOPER NEW UI */}
          <div className="common-card p-2 mt-2">
            {submittedBidders?.length === 0 ? (
              <NoRecords
                image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
              />
            ) : (
              <>
                <div className="multiple-table-combine bidders-submission-table hover-scroll">
                  <table width={"100%"}>
                    <thead>
                      <tr>
                        <td></td>
                        <td></td>
                        {submittedBidders?.map((bidder: Bidder) => (
                          <td key={bidder.bidder_id}>
                            <div className="flex items-center gap-1 justify-end">
                              <Tooltip
                                title={HTMLEntities.decode(
                                  sanitizeString(bidder?.bidder_company_name)
                                )}
                              >
                                <Typography className="text-13 truncate max-w-[calc(100%-20px)] text-black">
                                  {HTMLEntities.decode(
                                    sanitizeString(bidder?.bidder_company_name)
                                  )}
                                </Typography>
                              </Tooltip>
                              <Tooltip
                                title={
                                  <>
                                    {_t("Submitted")} <br />
                                    {bidder?.submitted_date || "-"} {_t("at")}{" "}
                                    {bidder?.submitted_time || "-"} <br />
                                    {_t("IP")}: {bidder?.ip_address || "-"}
                                  </>
                                }
                              >
                                <FontAwesomeIcon
                                  className="w-[13px] h-[15px] ml-1"
                                  icon="fa-regular fa-circle-info"
                                />
                              </Tooltip>
                            </div>
                          </td>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td></td>
                        <td></td>
                        {submittedBidders?.map((bidder: Bidder) => {
                          const displayStatus =
                            bidder?.bid_status_name ?? "Status";
                          const color = BIDDERS_BID_STATUS.find(
                            (status) =>
                              status.key === (bidder?.bid_status ?? "")
                          )?.color;
                          return (
                            <td>
                              <Dropdown
                                menu={{
                                  items: BIDDERS_BID_STATUS,
                                  selectable: true,
                                  selectedKeys: [
                                    String(bidder?.bid_status ?? ""),
                                  ],
                                  onClick: (selectedValue) => {
                                    setSelectedBidderId(bidder?.bidder_id);
                                    setSelectedBidderForStatus(bidder);
                                    changeBidStatus(
                                      selectedValue,
                                      false,
                                      bidder?.bidder_id
                                    );
                                  },
                                }}
                                disabled={isReadOnly || isBidClosed || !isEdit}
                                trigger={["click"]}
                                className={
                                  isReadOnly || isBidClosed || !isEdit
                                    ? " !cursor-not-allowed"
                                    : ""
                                }
                                overlayClassName={
                                  "dropdown-color-option-block !min-w-40"
                                }
                              >
                                <div
                                  className={`py-0.5 rounded flex items-center justify-center w-fit ml-auto status-dropdown-block group/status-dropdown px-2.5 min-w-[75px] ${
                                    isReadOnly || isBidClosed || !isEdit
                                      ? ""
                                      : "hover:px-1 cursor-pointer"
                                  }`}
                                  style={{
                                    backgroundColor: color + "1d",
                                  }}
                                >
                                  <Typography
                                    style={{
                                      color: color,
                                    }}
                                    className="text-xs whitespace-nowrap truncate"
                                  >
                                    {HTMLEntities.decode(
                                      sanitizeString(displayStatus)
                                    )}
                                  </Typography>
                                  {isReadOnly || isBidClosed || !isEdit ? (
                                    ""
                                  ) : (
                                    <FontAwesomeIcon
                                      className="w-2.5 h-2.5 pl-0.5 group-hover/status-dropdown:flex hidden ease-in-out duration-300"
                                      style={{
                                        color: color,
                                      }}
                                      icon="fa-regular fa-chevron-down"
                                    />
                                  )}
                                </div>
                              </Dropdown>
                            </td>
                          );
                        })}
                      </tr>
                      <tr>
                        <td>{_t("Items")}</td>
                        <td>{_t("QTY")}</td>
                        {submittedBidders?.map((bidder: Bidder) => (
                          <td key={bidder.bidder_id}>Item Price</td>
                        ))}
                      </tr>
                      {formik?.values?.items?.map((item: Bidder) => (
                        <tr key={item.bid_item_id}>
                          {/* Item Name */}
                          <td>
                            <Tooltip
                              title={HTMLEntities.decode(
                                sanitizeString(item.subject)
                              )}
                            >
                              <Typography className="text-13 truncate">
                                {HTMLEntities.decode(
                                  sanitizeString(item.subject)
                                )}
                              </Typography>
                            </Tooltip>
                          </td>

                          {/* Quantity */}
                          <td>
                            <Tooltip title={item?.quantity?.toString()}>
                              <Typography className="text-13 ml-auto">
                                {item?.quantity ?? "-"}
                              </Typography>
                            </Tooltip>
                          </td>

                          {/* Item Prices per Bidder */}
                          {submittedBidders?.map((bidder: Bidder) => {
                            const itemData = bidder?.items?.find(
                              (innerItem: BidItem) =>
                                innerItem?.bid_item_id === item?.bid_item_id
                            );
                            const itemPrice = isNaN(
                              itemData?.bid_submitted_price
                            )
                              ? "0"
                              : formatter(
                                  (
                                    +(itemData?.bid_submitted_price ?? 0) / 100
                                  )?.toFixed(2)
                                )?.value_with_symbol;
                            return (
                              <td
                                key={`${bidder?.bidder_id}-${item?.bid_item_id}`}
                              >
                                <Tooltip title={itemPrice}>
                                  <Typography className="table-tooltip-text">
                                    {itemPrice}
                                  </Typography>
                                </Tooltip>
                              </td>
                            );
                          })}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                  <ul className="flex items-center">
                    <li className="min-w-[250px] w-[calc(100%-180px)] px-[10px] py-[7px] font-semibold border-b min-h-9 text-13">
                      {_t("Total")}
                    </li>
                    <li className="border-b">
                      <ul className="flex">
                        {submittedBidders?.map((bidder: Bidder) => {
                          const bidTotal = isNaN(+bidder?.bid_amount)
                            ? "0"
                            : formatter((+bidder?.bid_amount / 100)?.toFixed(2))
                                ?.value_with_symbol;
                          return (
                            <li className="w-[180px] min-w-[180px] flex justify-end px-[10px] py-[7px]">
                              <div className="flex gap-1 max-w-full w-fit pl-2">
                                <Tooltip title={bidTotal}>
                                  <Typography className="inline-block text-13 truncate font-semibold">
                                    {bidTotal}
                                  </Typography>
                                </Tooltip>
                              </div>
                            </li>
                          );
                        })}
                      </ul>
                    </li>
                  </ul>
                  <div className="grid gap-1 mt-2">
                    <ul className="flex items-center bg-[#f8f8f8]">
                      <li className="min-w-[250px] w-[calc(100%-180px)] px-[10px] py-[7px] font-semibold text-13">
                        {_t("Files")}
                      </li>
                      <div className="flex">
                        {submittedBidders?.map(
                          (bidder: Bidder, bidderIndex: number) => {
                            if (!bidder?.files?.length) {
                              return (
                                <div className="w-[180px] min-w-[180px] flex justify-end px-[10px] py-[7px]"></div>
                              );
                            }

                            return (
                              <li className="w-[180px] min-w-[180px] flex justify-end px-[10px] py-[7px]">
                                <div
                                  className={`border rounded flex gap-1 max-w-full w-fit pl-2 ${
                                    bidder?.files?.length > 1 ? "" : "pr-2"
                                  }`}
                                >
                                  <Tooltip
                                    title={bidder?.files?.[0]?.file_name}
                                  >
                                    <a
                                      target="_blank"
                                      className={`!text-primary-900 text-13 inline-block truncate ${
                                        bidder?.files?.length > 1
                                          ? "w-[calc(100%-34px)]"
                                          : "w-full"
                                      }`}
                                      href={bidder?.files?.[0]?.file_path}
                                    >
                                      {bidder?.files?.[0]?.file_name}
                                    </a>
                                  </Tooltip>
                                  {bidder?.files?.length > 1 && (
                                    <div className="border-l px-2">
                                      <Popover
                                        placement="bottomRight"
                                        content={
                                          <div className="dark:bg-dark-900 min-w-[155px] p-3">
                                            <ul className="grid gap-1">
                                              {bidder?.files?.map((file, i) => {
                                                if (i === 0) {
                                                  return <></>;
                                                }
                                                return (
                                                  <li>
                                                    <a
                                                      target="_blank"
                                                      href={file?.file_path}
                                                      className="hover:text-primary-900"
                                                    >
                                                      {file?.file_name}
                                                    </a>
                                                  </li>
                                                );
                                              })}
                                            </ul>
                                          </div>
                                        }
                                        trigger="click"
                                        open={
                                          open &&
                                          bidderTooltipIndex === bidderIndex
                                        }
                                        onOpenChange={(newOpen: boolean) =>
                                          setOpen(newOpen)
                                        }
                                      >
                                        <Typography
                                          className="font-semibold text-13 whitespace-nowrap cursor-pointer"
                                          onClick={() => {
                                            setOpen(true);
                                            setBidderTooltipIndex(bidderIndex);
                                          }}
                                        >
                                          +{bidder?.files?.length - 1}
                                        </Typography>
                                      </Popover>
                                    </div>
                                  )}
                                </div>
                              </li>
                            );
                          }
                        )}
                      </div>
                    </ul>
                    <ul className="flex items-center bg-[#f8f8f8] mt-1">
                      <li className="min-w-[250px] w-[calc(100%-180px)] px-[10px] py-[7px] font-semibold text-13">
                        {_t("Notes")}
                      </li>
                      <div className="flex">
                        {submittedBidders?.map((bidder: Bidder) => {
                          return bidder?.notes ? (
                            <li className="w-[180px] min-w-[180px] flex justify-end px-[10px] py-[7px]">
                              <Typography>
                                <Tooltip
                                  title={HTMLEntities.decode(
                                    sanitizeString(bidder?.notes)
                                  )}
                                >
                                  <FontAwesomeIcon icon="fa-regular fa-memo" />
                                </Tooltip>
                              </Typography>
                            </li>
                          ) : (
                            <li className="w-[180px] min-w-[180px] flex justify-end px-[10px] py-[7px]"></li>
                          );
                        })}
                      </div>
                    </ul>
                  </div>
                </div>
              </>
            )}
          </div>
        </SidebarCardBorder>
      </div>

      {confirmApplyBidPriceDialogOpen && (
        <ConfirmModal
          isOpen={confirmApplyBidPriceDialogOpen}
          // isLoading={isLoading}
          modaltitle={_t("Confirm")}
          description="Would you like to apply the bid price to the estimated item price?"
          onAccept={() => {
            setApplyBidPrice(1);
            setConfirmApplyBidPriceDialogOpen(false);
            setConfirmSendBidNotificationDialogOpen(true);
          }}
          onDecline={() => {
            setApplyBidPrice(0);
            setConfirmApplyBidPriceDialogOpen(false);
            setConfirmSendBidNotificationDialogOpen(true);
          }}
          onCloseModal={() => {
            setApplyBidPrice(0);
            setConfirmApplyBidPriceDialogOpen(false);
          }}
          descriptionclassName="flex text-center justify-center"
          modalIcon="fa-regular fa-triangle-exclamation"
          zIndex={9999}
        />
      )}

      {confirmSendBidNotificationDialogOpen && (
        <ConfirmModal
          isOpen={confirmSendBidNotificationDialogOpen}
          // isLoading={isLoading}
          modaltitle={_t("Confirm")}
          description="Would you like to send an email to the winning bidder as well as those who lost the bid?"
          onAccept={() => {
            setConfirmSendBidNotificationDialogOpen(false);
            changeBidStatus(
              { key: "bid_status_awarded" },
              true,
              selectedBidderId,
              1
            );
          }}
          onDecline={() => {
            setConfirmSendBidNotificationDialogOpen(false);
            changeBidStatus(
              { key: "bid_status_awarded" },
              true,
              selectedBidderId,
              0
            );
          }}
          onCloseModal={() => {
            setConfirmSendBidNotificationDialogOpen(false);
          }}
          descriptionclassName="flex text-center justify-center"
          modalIcon="fa-regular fa-triangle-exclamation"
          zIndex={9999}
        />
      )}

      {confirmRebidDialogOpen && (
        <ConfirmModal
          isOpen={confirmRebidDialogOpen}
          // isLoading={isLoading}
          modaltitle={_t("Confirm")}
          description="Do you want to add this Bidder to the list again so that you can send them a new bid invite?"
          onAccept={() => {
            setConfirmRebidDialogOpen(false);
            changeBidStatus(
              { key: "bid_status_rebid" },
              true,
              selectedBidderId,
              0,
              true
            );
          }}
          onDecline={() => {
            setConfirmRebidDialogOpen(false);
            changeBidStatus(
              { key: "bid_status_rebid" },
              true,
              selectedBidderId,
              0,
              false
            );
          }}
          onCloseModal={() => {
            setConfirmRebidDialogOpen(false);
          }}
          descriptionclassName="flex text-center justify-center"
          modalIcon="fa-regular fa-triangle-exclamation"
          zIndex={9999}
        />
      )}
    </>
  );
};

export default Submissions;
