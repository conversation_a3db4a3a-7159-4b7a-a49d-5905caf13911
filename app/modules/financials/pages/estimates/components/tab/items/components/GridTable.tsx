import React, { forwardRef } from "react";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { ColDef, ColGroupDef, RowDragEvent } from "ag-grid-community";

interface GridTableProps {
  columnDefs: (ColDef | ColGroupDef)[];
  rowData: any[];
  isReadOnly: boolean;
  singleSection: any;
  onRowDragEnd: (event: RowDragEvent<any, any>, sectionDet: any) => void;
  onGridReady: (params: any) => void;
  getRowClass: (params: any) => string;
}

export const GridTable = forwardRef<any, GridTableProps>(({
  columnDefs,
  rowData,
  isReadOnly,
  singleSection,
  onRowDragEnd,
  onGridReady,
  getRowClass,
}, ref) => {
  return (
    <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
      <div className="ag-theme-alpine">
        <StaticTable
          ref={ref}
          key={`${singleSection?.section_id}-${singleSection?.code_id}`}
          suppressContextMenu={true}
          suppressDragLeaveHidesColumns={true}
          suppressMoveWhenRowDragging={true}
          className="static-table"
          suppressRowClickSelection={true}
          rowDragManaged={!isReadOnly}
          animateRows={true}
          onRowDragEnd={(e) => onRowDragEnd(e, singleSection)}
          onGridReady={onGridReady}
          stopEditingWhenCellsLoseFocus={true}
          columnDefs={columnDefs}
          getRowClass={getRowClass}
          rowData={rowData}
          noRowsOverlayComponent={() => (
            <NoRecords
              image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
            />
          )}
        />
      </div>
    </div>
  );
});

GridTable.displayName = "GridTable";
