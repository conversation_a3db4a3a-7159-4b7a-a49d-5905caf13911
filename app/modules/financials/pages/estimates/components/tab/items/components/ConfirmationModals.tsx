import React from "react";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { formatAmount } from "~/helpers/helper";
import { useTranslation } from "~/hook";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";

interface ConfirmationModalsProps {
  costChangeConfirmOpen: any;
  isLoadingCheckBox: Record<number, boolean>;
  newUnitName: string;
  isAddingCustomData: boolean;
  onCostChangeAccept: () => Promise<void>;
  onCostChangeDecline: () => void;
  onUnitAddAccept: () => Promise<void>;
  onUnitAddDecline: () => void;
  onUnitModalClose: () => void;
  isReadOnly: boolean;
}

export const ConfirmationModals: React.FC<ConfirmationModalsProps> = React.memo(({
  costChangeConfirmOpen,
  isLoadingCheckBox,
  newUnitName,
  isAddingCustomData,
  onCostChangeAccept,
  onCostChangeDecline,
  onUnitAddAccept,
  onUnitAddDecline,
  onUnitModalClose,
  isReadOnly,
}) => {
  const { _t } = useTranslation();
  const { formatter } = useCurrencyFormatter();

  return (
    <>
      <ConfirmModal
        isOpen={Boolean(
          costChangeConfirmOpen !== null &&
            costChangeConfirmOpen?.item_id &&
            !isReadOnly
        )}
        modaltitle={_t("Confirmation")}
        isLoading={
          costChangeConfirmOpen?.item_id !== undefined &&
          isLoadingCheckBox?.[costChangeConfirmOpen?.item_id]
        }
        description={`The item price in the Estimate does not match the price in your Cost Items Database. Do you want to import the current price of ${
          formatter(
            formatAmount(
              +(costChangeConfirmOpen?.updated_unit_cost ?? "0") / 100
            )
          ).value_with_symbol
        }?`}
        onAccept={onCostChangeAccept}
        onDecline={onCostChangeDecline}
        onCloseModal={onCostChangeDecline}
        descriptionclassName="flex text-center justify-center"
        modalIcon="fa-regular fa-file-check"
      />
      {newUnitName.trim() && window.ENV.ENABLE_UNIT_DROPDOWN && (
        <ConfirmModal
          isOpen={Boolean(newUnitName.trim())}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${newUnitName}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={onUnitModalClose}
          onAccept={onUnitAddAccept}
          onDecline={onUnitAddDecline}
        />
      )}
    </>
  );
});

ConfirmationModals.displayName = "ConfirmationModals";
