import { useMemo, useCallback } from "react";
import {
  ColDef,
  ColGroupDef,
  ValueGetterParams,
  ValueSetterParams,
} from "ag-grid-community";
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { sanitizeString } from "~/helpers/helper";
import { useTranslation } from "~/hook";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
import { ICON_MAP } from "~/modules/financials/pages/changeOrder/components/sidebar/utils";

interface UseColumnDefinitionsProps {
  isReadOnly: boolean;
  ParentIsReadOnly: boolean;
  singleSection: any;
  selectedItems: any;
  flags: any;
  filteredCodeCostData: any[];
  units: any[];
  isLoadingCheckBox: Record<number, boolean>;
  updateItemField: (params: any) => Promise<any>;
  handleSelection: (selected: boolean, params: any) => void;
  setCostChangeConfirmation: (data: any) => void;
  setNewUnitName: (name: string) => void;
  setSelectedData: (data: any) => void;
  setItemsOpen: (open: boolean) => void;
  setItemsData: (data: any) => void;
  setSelectedItemId: (id: number) => void;
  setSelectedSectionId: (id: number) => void;
  setIsDeleteConfirmOpen: (open: boolean) => void;
  selectAll: (params: any) => void;
  unselectAll: (params: any) => void;
}

export const useColumnDefinitions = ({
  isReadOnly,
  ParentIsReadOnly,
  singleSection,
  selectedItems,
  flags,
  filteredCodeCostData,
  units,
  isLoadingCheckBox,
  updateItemField,
  handleSelection,
  setCostChangeConfirmation,
  setNewUnitName,
  setSelectedData,
  setItemsOpen,
  setItemsData,
  setSelectedItemId,
  setSelectedSectionId,
  setIsDeleteConfirmOpen,
  selectAll,
  unselectAll,
}: UseColumnDefinitionsProps) => {
  const { _t } = useTranslation();
  const { formatter } = useCurrencyFormatter();

  const singleSectionItems = singleSection?.items?.filter((el: any) => {
    const totalCost = Number(el?.quantity) * Number(el?.unit_cost);
    return flags?.showZeroQuantityItemsOnly ? totalCost == 0 : true;
  });

  const suppressKeyboardEvent = useCallback((params: any) => {
    const { event, api } = params;

    if (event.key === "ArrowUp" || event.key === "ArrowDown") {
      event.preventDefault();
      event.stopPropagation();
      return true;
    }

    if (event.key === "Enter") {
      event.preventDefault();
      event.stopPropagation();
      api.stopEditing();
      return true;
    }

    return true;
  }, []);

  const MuHeader = useCallback(() => {
    return (
      <div className="w-full flex items-center justify-end gap-1.5">
        <span className="text-[#181d1f] font-semibold">{_t("MU%")}</span>
        {flags?.isMarkupHidden && (
          <Tooltip title="Markup is currently hidden from all totals while 'hide' is enabled. Markup is hidden for reference only and is still applied to all PDFs">
            <FontAwesomeIcon
              className="h-3.5 w-3.5 -mt-px"
              icon="fa-regular fa-info-circle"
            />
          </Tooltip>
        )}
      </div>
    );
  }, [_t, flags?.isMarkupHidden]);

  const columnDefs: (ColDef | ColGroupDef)[] = useMemo(
    () => [
      // Move column
      {
        headerName: "",
        minWidth: isReadOnly ? 0 : 30,
        maxWidth: isReadOnly ? 0 : 30,
        field: "move",
        suppressMenu: true,
        hide: Boolean(isReadOnly),
        rowDrag: !isReadOnly,
        cellClass: () =>
          `ag-cell-center ag-move-cell custom-move-icon-set cell-invinsible ${
            Object.keys(selectedItems)?.length ? "" : " hover-visibility"
          }`,
        cellRenderer: () => {
          return (
            <div className="w-6 h-6 flex items-center justify-center absolute top-1 left-1 !z-0 opacity-100">
              <FontAwesomeIcon
                className="w-4 h-4 text-[#4b5a76]"
                icon="fa-solid fa-grip-dots"
              />
            </div>
          );
        },
      },
      // Checkbox column
      {
        headerName: "",
        headerComponent: () => {
          const allSelected =
            singleSection?.items?.every(
              (item: any) => selectedItems?.[item?.item_id]?.selected
            ) && singleSection?.items?.length > 0;
          return (singleSection?.items?.length || 0) > 0 &&
            !flags.showZeroQuantityItemsOnly ? (
            <CheckBox
              checked={allSelected}
              onChange={(e) => {
                if (e.target.checked)
                  selectAll({
                    sectionId: singleSection.section_id as number,
                  });
                else
                  unselectAll({
                    sectionId: singleSection.section_id as number,
                  });
              }}
            />
          ) : null;
        },
        field: "checkbox",
        minWidth: isReadOnly ? 0 : 40,
        maxWidth: isReadOnly ? 0 : 40,
        hide: Boolean(isReadOnly || flags.showZeroQuantityItemsOnly),
        checkboxSelection: false,
        headerCheckboxSelection: false,
        suppressMenu: true,
        suppressMovable: true,
        cellClass: () =>
          `ag-cell-center ad-call-pr-0 ad-call-pl-0 hover-visibility ${
            Object.keys(selectedItems)?.length ? "visible" : "cell-invinsible"
          }`,
        cellRenderer: (params: any) => {
          if (!params.data) {
            return;
          }
          const data = params?.data;
          const is_checked: boolean = Boolean(
            data?.selected || selectedItems?.[data?.item_id]
          );
          return (
            <CheckBox
              checked={is_checked}
              onChange={(e) => {
                if (params && params.node) {
                  const selected = Boolean(e.target.checked);
                  const updatedData = {
                    ...params.data,
                    selected,
                  };
                  handleSelection(e.target.checked, {
                    data: updatedData,
                  });
                  params.node.setData(updatedData);
                  params.api.refreshCells({
                    rowNodes: [params.node],
                    force: true,
                  });
                }
              }}
            />
          );
        },
      },
      // Type column
      {
        headerName: _t("Type"),
        field: "item_type_name",
        maxWidth: 50,
        minWidth: 50,
        suppressMovable: false,
        suppressMenu: true,
        headerClass: "ag-header-center",
        cellClass: "ag-cell-center",
        cellRenderer: ({ data }: { data: any }) => {
          const value = data?.item_type_name;
          return value ? (
            <Tooltip title={value}>
              <FontAwesomeIcon
                className="w-4 h-4 text-primary-900 mx-auto"
                icon={
                  ICON_MAP[value as keyof typeof ICON_MAP] ||
                  ICON_MAP["default"]
                }
              />
            </Tooltip>
          ) : (
            <>-</>
          );
        },
      },
      // Item Name column
      {
        headerName: _t("Item Name"),
        field: "subject",
        minWidth: 150,
        flex: 2,
        headerClass: "ag-header-left",
        cellClass: "ag-cell-left",
        resizable: true,
        suppressMovable: false,
        suppressMenu: true,
        editable: !isReadOnly,
        cellRenderer: ({ data }: { data: any }) => {
          const itemName =
            HTMLEntities.decode(sanitizeString(data?.subject)) ?? "-";
          return (
            <Tooltip title={itemName}>
              <Typography className="table-tooltip-text">
                {itemName}
                <span className="text-gray-500 ml-1">
                  {Boolean(data?.is_optional_item) ? "(Optional)" : ""}
                </span>
              </Typography>
            </Tooltip>
          );
        },
        valueGetter: (params: ValueGetterParams) => {
          return HTMLEntities.decode(sanitizeString(params?.data?.subject));
        },
        valueSetter: (params: ValueSetterParams) => {
          if (params && params.node) {
            if (!params.newValue.trim().length) {
              notification.error({
                description: _t("Item Name is required."),
              });
              return false;
            }
            const updatedData = {
              ...params.data,
              subject: params.newValue.trim(),
            };
            params.node.setData(updatedData);
            updateItemField({
              itemId: updatedData.item_id,
              itemData: params.data,
              updatedItem: {
                subject: params.newValue.trim(),
              },
            });
          }
          return true;
        },
      },
    ],
    [
      isReadOnly,
      selectedItems,
      singleSection,
      flags,
      handleSelection,
      selectAll,
      unselectAll,
      _t,
      updateItemField,
    ]
  );

  return {
    columnDefs,
    suppressKeyboardEvent,
    MuHeader,
  };
};
