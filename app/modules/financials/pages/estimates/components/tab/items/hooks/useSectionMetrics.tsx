import { useMemo } from "react";
import { itemTotalCalculator } from "../../details/EstimatesCalc";

interface UseSectionMetricsProps {
  singleSection: any;
  flags: any;
  taxIsReversible: boolean;
  taxRate: number;
}

export const useSectionMetrics = ({
  singleSection,
  flags,
  taxIsReversible,
  taxRate,
}: UseSectionMetricsProps) => {
  const sectionMetrics = useMemo(() => {
    const items = singleSection?.items;

    const filteredItems =
      items
        ?.filter((el: any) => {
          const totalCost = Number(el?.quantity) * Number(el?.unit_cost);
          return (
            !el?.is_optional_item &&
            (flags?.showZeroQuantityItemsOnly ? totalCost == 0 : true)
          );
        })
        ?.map((el: any) => ({
          ...el,
          unit_cost: `${Number(el.unit_cost) / 100}`,
          markup: el?.is_markup_percentage
            ? Number(el?.markup)
            : Number(el?.markup) / 100,
        })) ?? [];

    const calculateSum = (
      array: any[],
      callback: (item: any) => number
    ): number =>
      array?.map(callback)?.reduce((sum, value) => sum + value, 0) || 0;

    const estimatedCost = calculateSum(filteredItems, (ite) =>
      itemTotalCalculator(ite, false)
    );
    const subtotal = calculateSum(filteredItems, (ite) =>
      itemTotalCalculator(ite, !flags?.isMarkupHidden)
    );

    const subtotalTaxeble = calculateSum(
      filteredItems.filter((ite) =>
        typeof ite?.apply_global_tax === "boolean"
          ? ite?.apply_global_tax
          : ite?.apply_global_tax?.toString() === "1"
      ),
      (ite) => itemTotalCalculator(ite, !flags?.isMarkupHidden)
    );

    const markup = calculateSum(filteredItems, (item) =>
      item?.is_markup_percentage
        ? Number(item?.unit_cost) *
          (Number(item?.markup) / 100) *
          item?.quantity
        : Number(item?.unit_cost) * item?.quantity
        ? Number(item?.markup) - Number(item?.unit_cost) * item?.quantity
        : 0
    );

    const profitMargin = estimatedCost
      ? ((markup / estimatedCost) * 100).toFixed(2) + "%"
      : "0%";

    const tax = taxIsReversible ? 0 : subtotalTaxeble * (taxRate / 100);
    const grandTotal = subtotal + tax;

    return {
      estimatedCost,
      subtotalTaxeble,
      subtotal,
      markup,
      profitMargin,
      tax,
      grandTotal,
    };
  }, [singleSection?.items, flags, taxRate, taxIsReversible]);

  return sectionMetrics;
};
