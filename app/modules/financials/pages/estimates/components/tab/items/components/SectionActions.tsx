import React from "react";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { useTranslation } from "~/hook";

interface SectionActionsProps {
  isReadOnly: boolean;
  dragAndDropRef?: (node: HTMLElement) => void;
}

export const SectionActions: React.FC<SectionActionsProps> = React.memo(({
  isReadOnly,
  dragAndDropRef,
}) => {
  const { _t } = useTranslation();

  if (isReadOnly) {
    return null;
  }

  return (
    <div ref={dragAndDropRef as any}>
      <ButtonWithTooltip
        icon="fa-solid fa-grip-dots"
        tooltipTitle={_t("Move")}
        tooltipPlacement="top"
        iconClassName="w-3.5 h-3.5"
        className="hover:!bg-primary-8 active:!bg-primary-8 cursor-move absolute top-3.5 left-[5px]"
        onClick={() => {}}
      />
    </div>
  );
});

SectionActions.displayName = "SectionActions";
