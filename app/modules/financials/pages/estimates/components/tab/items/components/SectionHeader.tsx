import React from "react";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Typography } from "~/shared/components/atoms/typography";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { formatAmount } from "~/helpers/helper";
import { useTranslation } from "~/hook";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";

interface SectionHeaderProps {
  sectionName: string;
  grandTotal: number;
  isReadOnly: boolean;
  dragAndDropRef?: (node: HTMLElement) => void;
  addItemsOptions: Array<{
    label: string;
    value: string;
    disabled?: boolean;
    onClick?: (section: any) => void;
    itemClass?: string;
  }>;
  singleSection: any;
  onViewSection: () => void;
}

export const SectionHeader: React.FC<SectionHeaderProps> = React.memo(({
  sectionName,
  grandTotal,
  isReadOnly,
  dragAndDropRef,
  addItemsOptions,
  singleSection,
  onViewSection,
}) => {
  const { _t } = useTranslation();
  const { formatter } = useCurrencyFormatter();

  return (
    <div className="flex items-center gap-1 flex-wrap justify-end">
      <div className="flex items-center gap-1.5 mr-2 bg-primary-8 text-primary-900 py-[3px] px-[9px] rounded-sm dark:bg-dark-800 dark:text-white/90 w-fit whitespace-nowrap">
        <FontAwesomeIcon
          className="w-4 h-4"
          icon="fa-duotone fa-solid fa-money-check-dollar"
        />
        <Typography className="!mb-0 !text-sm !text-primary-900 leading-4 !font-semibold dark:!text-white/90 whitespace-nowrap">
          {formatter(formatAmount(grandTotal))?.value_with_symbol}
        </Typography>
      </div>
      {!isReadOnly && (
        <DropdownMenu
          options={addItemsOptions?.map((item) => ({
            ...item,
            onClick: () => item.onClick && item.onClick(singleSection),
          }))}
          buttonClass="w-fit h-auto m-0"
          contentClassName="w-fit add-items-drop-down"
          placement="bottomRight"
        >
          <div className="py-1 sm:px-2.5 px-2 bg-[#DFE1E4] rounded flex items-center gap-[5px]">
            <Typography className="text-primary-900 text-sm">
              {_t("Add Items / Modify Section")}
            </Typography>
            <FontAwesomeIcon
              className="w-3 h-3 text-primary-900"
              icon="fa-regular fa-chevron-down"
            />
          </div>
        </DropdownMenu>
      )}
      <ButtonWithTooltip
        icon="fa-solid fa-eye"
        tooltipTitle={_t("View")}
        tooltipPlacement="top"
        iconClassName="w-3.5 h-3.5"
        className="hover:!bg-primary-8 active:!bg-primary-8 ml-1"
        onClick={onViewSection}
      />
    </div>
  );
});

SectionHeader.displayName = "SectionHeader";
