import { useState, useEffect, useCallback } from "react";
import { useInView } from "react-intersection-observer";

interface UseGridOperationsProps {
  singleSection: any;
  flags: any;
  index: number;
  onGridReady: (index: number, params: any, singleSection: any) => void;
}

export const useGridOperations = ({
  singleSection,
  flags,
  index,
  onGridReady,
}: UseGridOperationsProps) => {
  const [gridApi, setGridApi] = useState<any>(null);
  const { ref, inView: isInView } = useInView({
    threshold: 0.1,
    triggerOnce: false,
  });

  const singleSectionItems = singleSection?.items?.filter((el: any) => {
    const totalCost = Number(el?.quantity) * Number(el?.unit_cost);
    return flags?.showZeroQuantityItemsOnly ? totalCost == 0 : true;
  });

  const handleGridReady = useCallback((params: any) => {
    setGridApi(params.api);
    onGridReady(index, params, singleSection);
  }, [index, onGridReady, singleSection]);

  const getRowClass = useCallback((params: any) => {
    const sectionId = params.node.data.section_id;
    const rowIndex = params.node.rowIndex;
    return `section-${sectionId} row-${rowIndex} hoverablerow`;
  }, []);

  const sortedRowData = singleSectionItems?.sort(
    (a: any, b: any) => Number(a?.order_number!) - Number(b?.order_number)
  ) ?? [];

  useEffect(() => {
    if (gridApi) {
      if (!isInView) {
        gridApi?.showLoadingOverlay?.();
      } else if (singleSectionItems?.length === 0) {
        gridApi?.showNoRowsOverlay?.();
      } else {
        gridApi?.hideOverlay?.();
      }
    }
  }, [isInView, singleSectionItems, gridApi]);

  return {
    gridApi,
    ref,
    isInView,
    handleGridReady,
    getRowClass,
    sortedRowData,
  };
};
