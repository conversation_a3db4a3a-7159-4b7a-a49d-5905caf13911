import { useState, useEffect, useRef } from "react";
import { getUnitList, addUnit } from "~/redux/action/unitActions";

export const useUnitOperations = () => {
  const [units, setUnits] = useState<any[]>([]);
  const [isUnitsGetting, setIsUnitsGetting] = useState<boolean>(true);
  const [newUnitName, setNewUnitName] = useState<string>("");
  const [selectedData, setSelectedData] = useState<any>();
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const gridRef = useRef<any>(null);

  useEffect(() => {
    if (window.ENV.ENABLE_UNIT_DROPDOWN) {
      (async function () {
        try {
          const response = await getUnitList();
          if (!response.success) {
            setIsUnitsGetting(false);
            return;
          }
          const units = response.data?.units || [];
          setUnits(units);
          const api = gridRef.current?.api;
          if (!api) return;

          const existingColDefs = api.getColumnDefs();
          if (!existingColDefs) return;

          const updatedColDefs = existingColDefs.map((col: any) =>
            "field" in col && col.field === "unit"
              ? {
                  ...col,
                  filterParams: {
                    values:
                      units.map((unit) => ({
                        label: unit.name?.toString(),
                        value: unit.name?.toString(),
                      })) ?? [],
                  },
                }
              : col
          );

          api.setColumnDefs(updatedColDefs);
          api.refreshHeader();
        } catch (error) {}
        setIsUnitsGetting(false);
      })();
      return () => {
        if (gridRef?.current?.api?.destroy) {
          gridRef?.current?.api?.destroy();
        }
      };
    }
  }, []);

  const handleUnitAdd = async (updateItemField: any) => {
    if (!isAddingCustomData && newUnitName) {
      setIsAddingCustomData(true);
      const response = (await addUnit({
        name: newUnitName,
      })) as any;
      if (response.success) {
        const newUnits = [response.data.data, ...units];
        setUnits(newUnits);
        setNewUnitName("");
        setSelectedData(undefined);
        const api = gridRef.current?.api;
        if (!api) return;

        const renderedNodes = api.getRenderedNodes();

        if (renderedNodes && selectedData?.item_id) {
          const currentRowNode = renderedNodes.find(
            (node: any) => node.data?.item_id === selectedData?.item_id
          );
          if (currentRowNode && currentRowNode.data) {
            const oldData = { ...currentRowNode.data };
            currentRowNode?.setData({
              ...currentRowNode.data,
              unit: newUnitName,
            });
            const response = await updateItemField({
              itemId: selectedData.item_id,
              itemData: selectedData,
              updatedItem: {
                unit: newUnitName,
              },
            });
            if (!response.success) {
              currentRowNode?.setData(oldData);
              notification.error({ description: response.message });
            }
          }
        }
        const existingColDefs = api.getColumnDefs();
        if (!existingColDefs) return;

        const updatedColDefs = existingColDefs.map((col: any) =>
          "field" in col && col.field === "unit"
            ? {
                ...col,
                filterParams: {
                  values:
                    newUnits.map((unit) => ({
                      label: unit.name?.toString(),
                      value: unit.name?.toString(),
                    })) ?? [],
                },
                cellEditorParams: {
                  ...col.cellEditorParams,
                  values: newUnits,
                },
              }
            : col
        );

        api.setColumnDefs(updatedColDefs);
        api.refreshHeader();
      } else {
        notification.error({
          description: response.message,
        });
      }
      setIsAddingCustomData(false);
    }
  };

  const handleUnitModalClose = () => {
    setNewUnitName("");
    setSelectedData(undefined);
  };

  const handleUnitDecline = () => {
    setNewUnitName("");
    setSelectedData(undefined);
  };

  return {
    units,
    isUnitsGetting,
    newUnitName,
    setNewUnitName,
    selectedData,
    setSelectedData,
    isAddingCustomData,
    gridRef,
    handleUnitAdd,
    handleUnitModalClose,
    handleUnitDecline,
  };
};
