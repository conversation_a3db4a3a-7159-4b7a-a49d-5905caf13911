import { useCallback, useState } from "react";
import { useAppESDispatch, useAppESSelector } from "../../../../redux/store";
import { updateItem } from "../../../../redux/slices/ESItemSlice";
import { updateEstimateItems } from "../../../../redux/action/ESItemAction";
import { itemTotalCalculator } from "../../details/EstimatesCalc";

interface UseItemOperationsProps {
  singleSection: any;
}

export const useItemOperations = ({ singleSection }: UseItemOperationsProps) => {
  const dispatch = useAppESDispatch();
  const { estimateDetail } = useAppESSelector((state) => state.estimateDetail);
  const [isLoadingCheckBox, setIsLoadingCheckBox] = useState<Record<number, boolean>>({});

  const updateItemField = useCallback(async ({
    itemId,
    updatedItem,
    itemData,
  }: {
    itemId: number;
    updatedItem: Partial<any>;
    itemData?: any;
  }) => {
    // if unit and quantity is updated then send total value to backend
    const updatedItemKeys = Object.keys(updatedItem);
    if (updatedItemKeys.includes("apply_global_tax")) {
      setIsLoadingCheckBox((prev) => ({ ...prev, [itemId]: true }));
    }
    if (
      itemData &&
      (updatedItemKeys.includes("unit") ||
        updatedItemKeys.includes("quantity") ||
        updatedItemKeys.includes("markup") ||
        updatedItemKeys.includes("unit_cost"))
    ) {
      updatedItem["total"] = itemTotalCalculator(
        {
          ...itemData,
          ...updatedItem,
        },
        true
      );
    }
    dispatch(
      updateItem({
        sectionId: singleSection?.section_id,
        itemId,
        updatedItem,
      })
    );
    const apiRes = await dispatch(
      updateEstimateItems({
        estimate_id: estimateDetail?.estimate_id,
        items: [
          {
            ...updatedItem,
            section_id: singleSection?.section_id,
            item_id: itemId,
          },
        ],
      })
    );

    const response = apiRes.payload as any;

    if (response?.success) {
      setIsLoadingCheckBox((prev) => ({ ...prev, [itemId]: false }));
    } else {
      dispatch(
        updateItem({
          sectionId: singleSection?.section_id,
          itemId,
          updatedItem: itemData,
        })
      );
      notification.error({
        description: response?.message,
      });
      setIsLoadingCheckBox((prev) => ({ ...prev, [itemId]: false }));
    }
    return response;
  }, [dispatch, estimateDetail?.estimate_id, singleSection?.section_id]);

  return {
    updateItemField,
    isLoadingCheckBox,
    setIsLoadingCheckBox,
  };
};
