import React, { useState } from "react";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { ProgressBarHeader } from "~/shared/components/molecules/ProgressBarHeader";
import { useAppESSelector } from "../../redux/store";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { useTranslation } from "~/hook";

const EstimateStatusbar: React.FC<EstimateStatusbarProps> = ({
  handleUpdateField = (data: IEstimateDetailData) => {},
  handleChangeFieldStatus,
  estimateStatusList,
  isReadOnly,
  activeStep,
  setActiveStep,
  isStatusLost,
  selectedStatusInd,
}) => {
  const { estimateDetail }: IEstimatesDetailState = useAppESSelector(
    (state) => state.estimateDetail
  );

  const { _t } = useTranslation();

  const [clicked, setClicked] = useState<boolean>(false);
  const [currntTooltip, setCurrntTooltip] = useState<string>("");

  const [openConfirmModal, setOpenConfirmModal] = useState<boolean>(false);

  const [tempValue, setTempValue] = useState<string>("");

  // const activeHeaders = useMemo(() => {
  //   const estimateStatusName = inputValues?.approval_type as EstimateStatusName;

  //   // Find the index of the current status
  //   const currentIndex = estimateStatusList?.findIndex(
  //     (status) => status.label === estimateStatusName
  //   );

  //   // Slice the array of status data up to the current status index (inclusive)
  //   if (currentIndex !== -1) {
  //     const activeStatusData =
  //       estimateStatusList?.slice(0, currentIndex + 1) || [];

  //     // Map the labels and filter out any undefined values
  //     const activeLabels = activeStatusData
  //       .map((status) => status.label)
  //       .filter((label): label is string => label !== undefined);

  //     return activeLabels;
  //   }

  //   return [];
  // }, [inputValues?.approval_type, inputValues, estimateStatusList]);

  const handleStatusChange = async (val: string) => {
    const selectedOption = estimateStatusList.find(
      (option) => option.label === val?.toString()
    );

    // check if the active step is greater than 5 and curr step where use clicked is 1 or 2 then open confirm modal and store the temp value
    if (
      activeStep &&
      Number(activeStep) >= 5 &&
      (selectedOption?.sort_order === 1 || selectedOption?.sort_order === 2)
    ) {
      setOpenConfirmModal(true);
      setTempValue(val);
    } else {
      await onIconClickChange(val);
    }
  };

  const onIconClickChange = async (val: string) => {
    const selectedOption = estimateStatusList.find(
      (option) => option.label === val?.toString()
    );

    const existingselectedOption = estimateStatusList.find(
      (option) => option.sort_order === activeStep
    );
    try {
      if (selectedOption?.value !== existingselectedOption?.value) {
        setActiveStep(selectedOption?.value!);
        await handleUpdateField({
          approval_type: selectedOption?.value,
          approval_type_name: selectedOption
            ? selectedOption.label
            : "Select Status",
          customer_id: estimateDetail?.customer_id,
          project_id: estimateDetail?.project_id,
        });

        setClicked(false);
      } else {
        handleChangeFieldStatus({
          field: "approval_type",
          status: "button",
          action: "BLUR",
        });
        setActiveStep(existingselectedOption?.value!);

        setClicked(false);
      }
    } catch (error) {
      setActiveStep(existingselectedOption?.value!);
    }
    setTempValue("");
  };

  const isEmailSentIcon =
    estimateDetail?.est_submit_approval_email_status === 1;
  const isEmailOpenIcon =
    estimateDetail?.est_submit_approval_email_status === 2;
  const emailToolTipText = isEmailSentIcon
    ? `Submitted on ${estimateDetail?.date_modified} ${estimateDetail?.time_modified}`
    : `Opened on ${estimateDetail?.date_modified} at ${estimateDetail?.time_modified}`;

  return (
    <>
      {estimateStatusList
        ?.filter((item) => item?.show_in_progress_bar)
        ?.map((item: EStatusList, index: number) => {
          // const isActive = Number(activeStep) >= Number(item?.value);
          const isActive =
            selectedStatusInd != undefined && selectedStatusInd >= index;
          const statusTooltipName = "status_" + (item.value || index);
          const emailTooltipName = "email_" + (item.value || index);
          return (
            <li
              key={index}
              onClick={() => {}}
              className={`${
                isReadOnly || isStatusLost ? "cursor-not-allowed" : ""
              } relative 2xl:min-w-[125px] xl:min-w-24 lg:w-24 w-20 grid justify-end first:before:hidden before:absolute before:h-[2px] before:!w-[calc(100%-54px)]  ${
                isActive ? "before:bg-primary-900" : "before:bg-[#ACAEAF]"
              } before:top-[30%] lg:before:left-[-22px] before:left-[-13px]`}
            >
              <ProgressBarHeader
                tooltipProps={{
                  open: currntTooltip === statusTooltipName,
                  onOpenChange: (visible) => {
                    setCurrntTooltip(visible ? statusTooltipName : "");
                  },
                }}
                option={item as EStatusList}
                isActive={isActive}
                onClick={(e: IProgressBarHeaderPropOption) => {
                  if (isReadOnly || isStatusLost) {
                    return;
                  }
                  if (e.label && !clicked) {
                    setClicked(true);
                    return handleStatusChange(e.label);
                  }
                }}
                // open text="Opened on Date at Time"
                // icon: "fa-regular fa-envelope-open"
                children={
                  item.key === "estimate_pending_approval" &&
                  (isEmailSentIcon || isEmailOpenIcon) && (
                    <Tooltip
                      title={emailToolTipText}
                      placement="top"
                      open={currntTooltip === emailTooltipName}
                      onOpenChange={(visible) => {
                        setCurrntTooltip(visible ? emailTooltipName : "");
                      }}
                    >
                      <FontAwesomeIcon
                        className="w-3.5 h-3.5 text-primary-900 relative z-10 pr-1"
                        icon={
                          "fa-regular " +
                          (isEmailSentIcon ? "fa-envelope" : "fa-envelope-open")
                        }
                      />
                    </Tooltip>
                  )
                }
              />
            </li>
          );
        })}
      {openConfirmModal && (
        <ConfirmModal
          isOpen={openConfirmModal}
          modaltitle={_t("Warning")}
          modalIcon="fa-regular fa-triangle-exclamation"
          onAccept={() => {
            onIconClickChange(tempValue);
            setOpenConfirmModal(false);
          }}
          onDecline={() => {
            // onCloseCompletedModal();
            setOpenConfirmModal(false);
            setClicked(false);
            setTempValue("");
          }}
          onCloseModal={() => {
            setOpenConfirmModal(false);
            setClicked(false);
            setTempValue("");
          }}
          description={_t(
            "You are about to make changes to an Approved Estimate. This action will affect your SOV and other financial modules such as Invoices. This action can NOT BE UNDONE and may cause errors. Do you want to continue?"
          )}
        />
      )}
    </>
  );
};

export default EstimateStatusbar;
