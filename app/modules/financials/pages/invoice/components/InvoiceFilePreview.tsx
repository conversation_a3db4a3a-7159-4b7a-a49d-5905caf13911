// React + ag-grid
import { useState } from "react";
// Hook
import { useTranslation } from "~/hook";
// Molecules
import { PDFFilePreview } from "~/shared/components/molecules/pdfFilePreview";
// Constants, Shared & Common
import { Number, sanitizeString } from "~/helpers/helper";
// Redux
import { getGConfig, getGSettings } from "~/zustand";
import { useAppIVDispatch } from "../redux/store";
import {
  fetchIVDetails,
  generateWepayPaymentLink,
} from "~/modules/financials/pages/invoice/redux/action";
import { sendCommonEmailApi } from "~/redux/action/sendEmailAction";
import { defaultConfig } from "~/data";
import axios from "axios";
import dayjs from "dayjs";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { apiRoutes } from "~/route-services/routes";

const InvoiceFilePreview = ({
  viewEmailPdf,
  setViewEmailPdf,
  billedToData,
  setBilledToData,
  isDetailDropDown,
  onActionComplete,
}: IInvoiceFilePreview) => {
  const { _t } = useTranslation();
  const dispatch = useAppIVDispatch();
  const { module_id, module_key, module_singular_name }: GConfig = getGConfig();
  const gSetting: GSettings = getGSettings();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { invoice_msg } = gSetting;
  const { user_id = 0, company_id = 0 } = user || {};
  const moduleSLName =
    _t(HTMLEntities.decode(sanitizeString(module_singular_name))) ||
    _t("Invoice");

  // State
  const [pdfTempId, setPdfTempId] = useState<string>("");
  const [selectedTemplateItem, setSelectedTemplateItem] =
    useState<IPDFFilePreviewTemplateOptions>();
  const [wepayPaymentLinkResponse, setWepayPaymentLinkResponse] =
    useState<IGenerateWepayPaymentLinkApiresponse>();

  const optionsViewPdf: CustomerEmailTab[] = [
    defaultConfig.employee_key,
    "my_crew",
    defaultConfig.customer_key,
    defaultConfig.contractor_key,
    defaultConfig.vendor_key,
    defaultConfig.misc_contact_key,
    "by_service",
    "my_project",
  ];

  const handleEmailApiCall = async (
    tempFormData: SendEmailFormDataWithApiDefault,
    closeSendMailSidebar: () => void,
    ccMailCopy: boolean
  ) => {
    const op =
      selectedTemplateItem?.pdf_value === "pdf_invoice_lump_sum"
        ? "pdf_invoice_lump_sum_total"
        : selectedTemplateItem?.pdf_value === "pdf_AIA_Style"
        ? "invoice_AIA_style"
        : selectedTemplateItem?.pdf_value == "excel_AIA_Style"
        ? "invoice_AIA"
        : "pdf_invoice";

    let show_description = undefined;
    let show_item_detail = undefined;
    let payment_history = undefined;

    switch (selectedTemplateItem?.pdf_value || op) {
      case "pdf_invoice":
      case "pdf_invoice_basic":
      case "pdf_invoice_with_payment_history":
        show_description = 0;
        show_item_detail = 1;
        break;

      case "pdf_invoice_description":
      case "pdf_invoice_description_basic":
      case "pdf_invoice_with_payment_history_description":
        show_description = 1;
        show_item_detail = 1;
        break;

      case "pdf_AIA_Style":
      case "excel_AIA_Style":
        show_description = 0;
        show_item_detail = 0;
        payment_history = 0;
        break;

      case "pdf_invoice_aia":
      case "pdf_invoice_lump_sum":
        break;

      case "pdf_invoice_lump_sum_detail":
        show_description = 1;
        show_item_detail = 0;
        break;
    }

    const formData: IIVSendEmailForm = {
      ...tempFormData,
      send_me_copy: ccMailCopy ? 1 : 0,
      send_custom_email: 0,
      record_id: Number(viewEmailPdf.invoice_id),
      module_id: module_id,
      module_key: module_key,
      t_id: pdfTempId,
      invoice_id: viewEmailPdf.invoice_id,
      action: "send",
      op: op,
      ...(show_description !== undefined && show_description !== null
        ? { show_description: show_description.toString() }
        : {}),
      ...(show_item_detail !== undefined && show_item_detail !== null
        ? { show_item_detail: show_item_detail.toString() }
        : {}),
      ...(payment_history !== undefined && payment_history !== null
        ? { show_item_detail: payment_history.toString() }
        : {}),
    };
    try {
      const res = (await sendCommonEmailApi(formData)) as ISendEmailCommonRes;
      if (res) {
        if (res.success) {
          closeSendMailSidebar();
          if (res.sent_by_customer === "1") {
            if (isDetailDropDown) {
              dispatch(
                fetchIVDetails({
                  id: viewEmailPdf?.invoice_id || "",
                  add_event: true,
                })
              );
            } else if (!isDetailDropDown && onActionComplete) {
              onActionComplete();
            }
            setViewEmailPdf({});
            setPdfTempId("");
            setBilledToData([]);
          }
        } else {
          notification.error({
            description: res.message,
          });
        }
      }
    } catch (error) {
      notification.error({
        description: (error as Error).message || "",
      });
    }
  };

  const handleDownloadPdf = async (
    tId: string,
    op?: string,
    show_description?: number,
    show_item_detail?: number,
    payment_history?: number
  ) => {
    // Determine operation type explicitly
    const isAIAStyleExcel =
      selectedTemplateItem?.pdf_value === "excel_AIA_Style";
    const operation = isAIAStyleExcel ? "invoice_AIA" : op;

    // Prepare URL
    const testUrl = `${apiRoutes.COMMON.genrate_php_excel}?op=${operation}&invoice_id=${viewEmailPdf.invoice_id}&company_id=${company_id}&user_id=${user_id}&from=panel`;
    const apiUrl = `${
      apiRoutes.COMMON.genrate_php_pdf
    }?op=${operation}&invoice_id=${
      viewEmailPdf.invoice_id
    }&t_id=${tId}&master_tpl=${
      selectedTemplateItem?.master_tpl
    }&user_id=${user_id}&company_id=${company_id}&tz=${dayjs().format(
      "ZZ"
    )}&from=panel&curr_time=${dayjs().format(
      "YYYY-MM-DD HH:mm:ss"
    )}&show_description=${show_description}&show_item_detail=${show_item_detail}&payment_history=${payment_history}`;

    try {
      // Perform API request
      const response = await axios.get(isAIAStyleExcel ? testUrl : apiUrl, {
        method: "GET",
        responseType: "blob",
        headers: {
          Accept:
            "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
          "Accept-Encoding": "gzip, deflate, br, zstd",
        },
      });

      // Determine Content-Type and handle file download
      const contentType = response.headers["content-type"];
      const contentDisposition = response.headers["content-disposition"];
      const filenameMatch = contentDisposition?.match(/filename="(.+)"/);
      let sanitizedPrefix = viewEmailPdf.prefix_company_invoice_id
        ? `Invoice ${viewEmailPdf.prefix_company_invoice_id.replace(
            /[^a-zA-Z0-9\s]/g,
            ""
          )}`
        : "Invoice "; // Removes all symbols except letters, numbers, and spaces
      if (selectedTemplateItem?.pdf_value?.endsWith("AIA_Style")) {
        sanitizedPrefix = "CF Progress Invoice";
      }
      const filename = isAIAStyleExcel
        ? `${sanitizedPrefix}.xlsx`
        : `${sanitizedPrefix}.pdf`;

      const blob = new Blob([response.data], { type: contentType });
      const url = window.URL.createObjectURL(blob);

      // Trigger download
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error("Error downloading the file:", error);
    }
  };

  const handleGenerateWepayPaymentLink = async (
    selectedItem: ISelectedTemplateData
  ) => {
    const params: IGenerateWepayPaymentLink = {
      record_id: viewEmailPdf.invoice_id,
      module_key: module_key,
      module_page:
        gSetting?.wepay_activated?.toString() == "1"
          ? "wepay_checkout.php"
          : "stripe_checkout.php",
      template_id: Number(selectedItem?.template_id),
      current_user_id: user_id,
      redirect_pdf_name:
        selectedItem?.pdf_value == "pdf_invoice_lump_sum"
          ? "pdf_invoice_lump_sum_total"
          : "pdf_invoice",
    };
    const response = await generateWepayPaymentLink(params);
    if ("responseTime" in response && "data" in response) {
      setWepayPaymentLinkResponse(
        response as IGenerateWepayPaymentLinkApiresponse
      );
    }
  };

  return (
    <>
      {viewEmailPdf.invoice_id && (
        <PDFFilePreview
          projectId={viewEmailPdf.project_id}
          isOpen={Boolean(viewEmailPdf.invoice_id)}
          isAddUserId={true}
          onCloseModal={() => {
            setViewEmailPdf({});
            setBilledToData([]);
          }}
          moduleId={module_id}
          op="pdf_invoice_lump_sum_total"
          idName="invoice_id"
          isLoading={false}
          id={viewEmailPdf.invoice_id.toString()}
          options={optionsViewPdf}
          emailSubject={viewEmailPdf.email_subject}
          handleEmailApiCall={handleEmailApiCall}
          handleDownload={(
            tId,
            op,
            show_description,
            show_item_detail,
            payment_history
          ) =>
            handleDownloadPdf(
              tId,
              op,
              show_description || 0,
              show_item_detail || 0,
              payment_history || 0
            )
          }
          isViewAttachment={false}
          moduleName={moduleSLName}
          setPdfTempId={setPdfTempId}
          isEmailRestricted={viewEmailPdf?.due_date === null ? true : false}
          errorMessage={"Due date is required to send the email."}
          isInvoiceModule={true}
          handleGenerateWepayPaymentLink={handleGenerateWepayPaymentLink}
          wepayPaymentLinkResponse={wepayPaymentLinkResponse}
          selectedCustomer={
            (viewEmailPdf?.billed_to && viewEmailPdf.billed_to != 0) ||
            (viewEmailPdf?.customer_id && viewEmailPdf?.customer_id != 0)
              ? (billedToData as TselectedContactSendMail[])
              : []
          }
          setSelectedTemplateItem={setSelectedTemplateItem}
          defaultEditorMessage={
            !!invoice_msg
              ? invoice_msg
                  .replace(/\\\"/g, '"') // Unescape \"
                  .replace(/&apos;/g, "'") // Convert &apos; to '
                  .replace(/\n+/g, "<br />") // Replace newlines with <br>
              : ""
          }
        />
      )}
    </>
  );
};

export default InvoiceFilePreview;
