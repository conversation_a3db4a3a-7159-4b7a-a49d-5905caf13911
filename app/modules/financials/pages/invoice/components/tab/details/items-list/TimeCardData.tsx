// atoms
import { <PERSON>lt<PERSON> } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { SelectField } from "~/shared/components/molecules/selectField";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Hook
import { useTranslation } from "~/hook";
import { useEffect, useMemo, useState } from "react";
import { TimeCardDetails } from "../../sidebar";
import { useAppIVDispatch, useAppIVSelector } from "../../../../redux/store";
import { formatAmount, Number, sanitizeString } from "~/helpers/helper";
import { INVOICE_ITEM } from "../../../../utils/constants";
import {
  deleteInvoiceItem,
  updateInvoiceItems,
} from "../../../../redux/slices/InvoiceItemsSlice";
import { useGModules } from "~/zustand";
import { useParams } from "@remix-run/react";
import {
  deleteInvoiceItems,
  updateInvoiceItem,
} from "../../../../redux/action/InvoiceItemsActions";
import { defaultConfig } from "~/data";
import { ValueSetterParams } from "ag-grid-community";
import { updateIVDetailApi } from "../../../../redux/action";
import { updateIVDetail } from "../../../../redux/slices/iVDetailsSlice";

interface IIvTimeCardDataProps {
  isReadOnly: boolean;
}

const TimeCardData = ({ isReadOnly }: IIvTimeCardDataProps) => {
  const { _t } = useTranslation();
  const [timeCardDetailsOpen, setTimeCardDetailsOpen] =
    useState<boolean>(false);
  const [timeCardsFilter, setTimeCardsFilter] = useState<string>(
    "tm_tc_group_by_employee"
  );
  const [timeCardItemToBeUpdate, setTimeCardItemToBeUpdate] =
    useState<IInvoiceItemData>(INVOICE_ITEM);
  const dispatch = useAppIVDispatch();
  const { formatter } = useCurrencyFormatter();
  const { getGModuleByKey } = useGModules();
  const params = useParams();
  const [selectedItemToDelete, setSelectedItemToDelete] = useState<number>();
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] =
    useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);

  const timeCardGroupingFilter = useAppIVSelector(
    (state) => state.proInvoiceItemList.timeCardGroupingFilter
  );

  const invoiceItems = useAppIVSelector(
    (state) => state.proInvoiceItemList.invoiceItems
  );
  const { details } = useAppIVSelector((state) => state.invoiceDetails);
  const [tableData, setTableData] = useState<
    Array<IInvoiceItemData> | { [key: string]: IInvoiceItemData[] }
  >();

  const [sectionTotal, setSectionTotal] = useState<number>(0);

  const iconByItemTypeName: { [key: string]: IFontAwesomeIconProps["icon"] } =
    useMemo(() => {
      return {
        item_equipment: "fa-regular fa-screwdriver-wrench",
        item_material: "fa-regular fa-block-brick",
        item_labour: "fa-regular fa-user-helmet-safety",
        item_sub_contractor: "fa-regular fa-file-signature",
        item_other: "fa-regular fa-boxes-stacked",
      };
    }, []);

  useEffect(() => {
    setTimeCardsFilter(timeCardGroupingFilter);
  }, [timeCardGroupingFilter]);

  useEffect(() => {
    let total = 0;
    invoiceItems.timecard_section.map((item) => {
      total += Number(item.total) || 0;
    });
    setSectionTotal(total);
  }, [invoiceItems.timecard_section]);

  useEffect(() => {
    if (timeCardsFilter === "tm_tc_group_by_employee") {
      const data = invoiceItems.timecard_section;
      const sortedData = [...data].sort(
        (a, b) => Number(a?.timecard_detail_id) - Number(b.timecard_detail_id)
      );

      const groupedItems = sortedData.reduce(
        (
          acc: { [key: string]: IInvoiceItemData[] },
          item: IInvoiceItemData
        ) => {
          const key = item.tm_tc_group_by_employee_key_name;
          if (!acc[key]) {
            acc[key] = [];
          }
          acc[key].push(item);
          return acc;
        },
        {}
      );
      setTableData(groupedItems);
    } else if (timeCardsFilter === "tm_tc_group_by_cost_code") {
      const data = invoiceItems.timecard_section;
      const sortedData = [...data].sort(
        (a, b) => Number(a?.timecard_detail_id) - Number(b.timecard_detail_id)
      );
      const groupedItems = sortedData.reduce(
        (
          acc: { [key: string]: IInvoiceItemData[] },
          item: IInvoiceItemData
        ) => {
          const key = item.tm_tc_group_by_cost_code_key_name;
          if (!acc[key]) {
            acc[key] = [];
          }
          acc[key].push(item);
          return acc;
        },
        {}
      );
      setTableData(groupedItems);
    } else if (timeCardsFilter === "tm_tc_group_by_day") {
      const data = invoiceItems.timecard_section;
      const sortedData = [...data].sort(
        (a, b) => Number(b?.timecard_detail_id) - Number(a?.timecard_detail_id)
      );
      const groupedItems = sortedData.reduce(
        (
          acc: { [key: string]: IInvoiceItemData[] },
          item: IInvoiceItemData
        ) => {
          const key = item.tm_tc_group_by_day_key_name;
          if (!acc[key]) {
            acc[key] = [];
          }
          acc[key].push(item);
          return acc;
        },
        {}
      );
      setTableData(groupedItems);
    } else {
      const data = invoiceItems.timecard_section;
      const sortedData = [...data].sort(
        (a, b) => Number(a?.timecard_detail_id) - Number(b.timecard_detail_id)
      );
      setTableData(sortedData);
    }
  }, [invoiceItems, timeCardsFilter]);

  const handleInvoiceItemDelete = async (itemId: number) => {
    setIsDeleting(true);
    const response = (await deleteInvoiceItems({
      id: Number(params.id),
      itemId: itemId,
    })) as IUpdateInvoiceItemsApiRes;

    if (response.success) {
      const newItems: IInvoiceItemsListData = {
        ...invoiceItems,
        timecard_section: invoiceItems.timecard_section.filter(
          (item) => item.item_id !== itemId
        ),
        items: invoiceItems.items.filter((item) => item.item_id !== itemId),
      };
      dispatch(deleteInvoiceItem({ items: newItems }));
      setIsDeleting(false);
      setIsDeleteConfirmOpen(false);
    } else {
      setIsDeleting(false);
      setIsDeleteConfirmOpen(false);
      notification.error({
        description: response.message || "Something went wrong!",
      });
    }
  };

  const handleApplyGlobalTaxUpdate = async (
    invoiceItem: IInvoiceItemData,
    applyGlobalTax: number
  ) => {
    const itemToBeUpdate: IUpdateInvoiceItemsParams = {
      id: Number(params.id),
      items: [
        {
          item_id: invoiceItem.item_id,
          subject: invoiceItem.subject,
          item_type: invoiceItem.item_type,
          apply_global_tax: applyGlobalTax,
          user_id: invoiceItem.timecard_user_id.toString(),
          timecard_user_rate: invoiceItem.timecard_user_rate,
          billing_rate:
            invoiceItem.timecard_user_rate !== "" &&
            invoiceItem.timecard_user_rate === "billing_rate"
              ? Number(invoiceItem.billing_rate)
              : Number(invoiceItem.user_billing_rate),
          emp_wage:
            invoiceItem.timecard_user_rate !== "" &&
            invoiceItem.timecard_user_rate === "emp_wage"
              ? Number(invoiceItem.billing_rate)
              : Number(invoiceItem.user_emp_wage_rate),
          burden_rate:
            invoiceItem.timecard_user_rate !== "" &&
            invoiceItem.timecard_user_rate === "burden_rate"
              ? Number(invoiceItem.billing_rate)
              : Number(invoiceItem.user_burden_rate),
          total_mins: Number(invoiceItem.total_mins),
          details: Number(invoiceItem.timecard_details),
          markup:
            invoiceItem?.is_markup_percentage === 0
              ? Number(invoiceItem?.markup)?.toString()
              : (invoiceItem?.markup || 0)?.toString(),
          detail_id: invoiceItem.timecard_details.toString(),
          cost_code_id: invoiceItem.cost_code_id || 0,
          tm_tc_group_by_key_id: invoiceItem.tm_tc_group_by_key_id,
          tm_tc_group_by_key_name: invoiceItem.tm_tc_group_by_key_name,
          ot_hours: invoiceItem.ot_hours.toString(),
          tm_tc_group_by_cost_code_key_id:
            invoiceItem.tm_tc_group_by_cost_code_key_id,
          tm_tc_group_by_cost_code_key_name:
            invoiceItem.tm_tc_group_by_cost_code_key_name,
          tm_tc_group_by_employee_key_id:
            invoiceItem.tm_tc_group_by_employee_key_id,
          tm_tc_group_by_employee_key_name:
            invoiceItem.tm_tc_group_by_employee_key_name,
          tm_tc_group_by_day_key_id: invoiceItem.tm_tc_group_by_day_key_id,
          tm_tc_group_by_day_key_name: invoiceItem.tm_tc_group_by_day_key_name,
          tm_tc_group_by_none_key_id: invoiceItem.tm_tc_group_by_none_key_id,
          tm_tc_group_by_none_key_name:
            invoiceItem.tm_tc_group_by_none_key_name,
        },
      ],
    };

    const response = (await updateInvoiceItem(
      itemToBeUpdate as IUpdateInvoiceItemsParams
    )) as IUpdateInvoiceItemsApiRes;

    if (response.success) {
      const newItems: IInvoiceItemsListData = {
        ...invoiceItems,
        timecard_section: invoiceItems.timecard_section.map((item) => {
          if (item.item_id === invoiceItem.item_id) {
            return {
              ...item,
              apply_global_tax: applyGlobalTax,
            };
          } else {
            return { ...item };
          }
        }),
      };
      dispatch(updateInvoiceItems({ items: newItems }));
    }

    if (!response.success) {
      notification.error({
        description: response.message || "Something went wrong",
      });
    }
  };

  const handleTimeCardFilterChange = async (value: string) => {
    setTimeCardsFilter(value);

    const data = {
      tm_tc_group_by: value,
    };
    const updateRes = (await updateIVDetailApi({
      invoice_id: details?.invoice_id || "",
      ...data,
    })) as IIVDetailsUpdateApiRes;
    if (updateRes.success) {
      dispatch(updateIVDetail({ ...data }));
      setTimeCardsFilter(value);
    } else {
      notification.error({
        description: updateRes.message || "Something went wrong!",
      });
      if (details.tm_tc_group_by) {
        setTimeCardsFilter(details.tm_tc_group_by);
      }
    }
  };

  return (
    <>
      {tableData && invoiceItems.timecard_section.length !== 0 && (
        <CollapseSingleTable
          title={_t(
            getGModuleByKey(defaultConfig.time_card_module)?.plural_name ||
              "Time Cards"
          )}
          totalRecord={`${
            formatter(
              formatAmount(((Number(sectionTotal) || 0) / 100).toFixed(2))
            ).value_with_symbol
          }`}
          defaultActiveKey={
            tableData && invoiceItems.timecard_section.length ? 1 : 0
          }
          totalRecordIcon={true}
          beforeContant={
            <div className="flex gap-1 items-center text-primary-900 font-semibold text-xs">
              <Typography className="text-primary-900 font-semibold">
                {_t("Group By")}:
              </Typography>
              <div className="max-w-fit relative bg-blue-50 shadow-[0px_3px_20px] shadow-black/5 rounded">
                <SelectField
                  labelPlacement="left"
                  name="filter_records_header"
                  className="text-sm !h-7 pl-2 edit-block-select-view"
                  fieldClassName="before:hidden"
                  value={timeCardsFilter}
                  options={[
                    {
                      value: "tm_tc_group_by_cost_code",
                      label: _t("Cost Code"),
                    },
                    {
                      value: "tm_tc_group_by_employee",
                      label: _t("Employee"),
                    },
                    {
                      value: "tm_tc_group_by_day",
                      label: _t("Day"),
                    },
                    {
                      value: "tm_tc_group_by_none",
                      label: _t("None"),
                    },
                  ]}
                  onChange={(value) => {
                    setTimeCardsFilter(value.toString());
                    // dispatch(updateTimeCardGroupingFilter(value.toString()));
                    handleTimeCardFilterChange(value.toString());
                  }}
                  showSearch={false}
                  popupClassName="!w-[140px]"
                  placement="bottomRight"
                />
                <FontAwesomeIcon
                  className="w-3 h-3 text-primary-900 absolute right-2 top-1/2 -translate-y-1/2"
                  icon="fa-regular fa-chevron-down"
                />
              </div>
            </div>
          }
          children={
            <div className="grid gap-2">
              {!Array.isArray(tableData) &&
                Object.keys(tableData).map((costCode: string) => {
                  return (
                    <div className="grid gap-1">
                      <div className="flex gap-1 items-center !text-primary-900 font-semibold text-xs">
                        <Typography className="text-primary-900 font-semibold">
                          {_t(
                            getGModuleByKey(defaultConfig.time_card_module)
                              ?.plural_name || "Time Cards"
                          )}
                          :{" "}
                          {timeCardsFilter === "tm_tc_group_by_employee" &&
                            tableData[costCode][0]
                              .tm_tc_group_by_employee_key_name}
                          {timeCardsFilter === "tm_tc_group_by_cost_code" &&
                            `${
                              tableData[costCode][0]
                                .tm_tc_group_by_cost_code_key_name ?? ""
                            }${
                              tableData[costCode][0].csi_code
                                ? ` (${tableData[costCode][0].csi_code})`
                                : ""
                            }`}
                          {timeCardsFilter === "tm_tc_group_by_day" &&
                            tableData[costCode][0].module_reference_date}
                        </Typography>
                      </div>
                      <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[33px] before:top-6 before:bg-gradient-to-r from-primary-500">
                        <div className="ag-theme-alpine customs-table-height customs-vertical-scroll-auto max-h-[680px]">
                          <StaticTable
                            className="static-table"
                            columnDefs={[
                              {
                                headerName: "",
                                field: "move",
                                minWidth: 30,
                                maxWidth: 30,
                                suppressMenu: true,
                              },
                              {
                                headerName: _t("Type"),
                                field: "type",
                                maxWidth: 50,
                                minWidth: 50,
                                suppressMovable: false,
                                suppressMenu: true,
                                headerClass: "ag-header-center",
                                cellClass: "ag-cell-center",
                                cellRenderer: (
                                  params: IIvItemsCellRenderer
                                ) => {
                                  const { data } = params;
                                  return data.item_type_name ? (
                                    <Tooltip title={_t(data.item_type_name)}>
                                      <FontAwesomeIcon
                                        className="w-4 h-4 text-primary-900 mx-auto"
                                        icon={
                                          iconByItemTypeName[data.item_type_key]
                                        }
                                      />
                                    </Tooltip>
                                  ) : (
                                    "-"
                                  );
                                },
                              },
                              {
                                headerName: _t("Employee"),
                                field: "employee_name",
                                minWidth: 220,
                                maxWidth: 220,
                                suppressMovable: false,
                                suppressMenu: true,
                                headerClass: "ag-header-left",
                                cellClass: "ag-cell-left",
                                cellRenderer: (
                                  params: IIvItemsCellRenderer
                                ) => {
                                  const { data } = params;

                                  return data.tc_employee_name ? (
                                    <Tooltip
                                      title={HTMLEntities.decode(
                                        sanitizeString(data.tc_employee_name)
                                      )}
                                    >
                                      <Typography className="table-tooltip-text">
                                        {HTMLEntities.decode(
                                          sanitizeString(data.tc_employee_name)
                                        )}
                                      </Typography>
                                    </Tooltip>
                                  ) : (
                                    "-"
                                  );
                                },
                              },
                              {
                                headerName: _t("Cost Code"),
                                field: "cost_code",
                                minWidth: 540,
                                flex: 2,
                                resizable: true,
                                suppressMovable: false,
                                suppressMenu: true,
                                headerClass: "ag-header-left",
                                cellClass: "ag-cell-left",
                                cellRenderer: (
                                  params: IIvItemsCellRenderer
                                ) => {
                                  const { data } = params;
                                  const costCode = `${
                                    data?.cost_code_name ?? ""
                                  }${
                                    data?.csi_code ? ` (${data?.csi_code})` : ""
                                  }${
                                    data?.code_is_deleted === 1
                                      ? ` (Archived)`
                                      : ""
                                  }`;

                                  return (
                                    <>
                                      {costCode ? (
                                        <Tooltip
                                          title={HTMLEntities.decode(
                                            sanitizeString(costCode)
                                          )}
                                        >
                                          <Typography className="table-tooltip-text">
                                            {HTMLEntities.decode(
                                              sanitizeString(costCode || "-")
                                            )}
                                          </Typography>
                                        </Tooltip>
                                      ) : (
                                        "-"
                                      )}
                                    </>
                                  );
                                },
                              },
                              {
                                headerName: _t("Hours"),
                                field: "hours",
                                maxWidth: 100,
                                minWidth: 100,
                                suppressMovable: false,
                                suppressMenu: true,
                                headerClass: "ag-header-right",
                                cellClass: "ag-cell-right",
                                cellRenderer: (
                                  params: IIvItemsCellRenderer
                                ) => {
                                  const { data } = params;
                                  const hours = Math.floor(
                                    Number(data.total_mins) / 60
                                  );
                                  const remainingMinutes =
                                    Number(data.total_mins) % 60;

                                  const formatedHours = `${hours
                                    .toString()
                                    .padStart(2, "0")}:${remainingMinutes
                                    .toString()
                                    .padStart(2, "0")}`;
                                  return formatedHours ? (
                                    <Tooltip title={formatedHours}>
                                      <Typography className="table-tooltip-text">
                                        {formatedHours}
                                      </Typography>
                                    </Tooltip>
                                  ) : (
                                    "-"
                                  );
                                },
                              },
                              {
                                headerName: _t("Rate"),
                                field: "rate",
                                maxWidth: 130,
                                minWidth: 130,
                                suppressMovable: false,
                                suppressMenu: true,
                                headerClass: "ag-header-right",
                                cellClass: "ag-cell-right",
                                cellRenderer: (
                                  params: IIvItemsCellRenderer
                                ) => {
                                  const { data } = params;

                                  return data.billing_rate ? (
                                    <Tooltip
                                      title={
                                        formatter(
                                          formatAmount(
                                            (
                                              (Number(data.billing_rate) || 0) /
                                              100
                                            ).toFixed(2)
                                          )
                                        ).value_with_symbol
                                      }
                                    >
                                      <Typography className="table-tooltip-text">
                                        {
                                          formatter(
                                            formatAmount(
                                              (
                                                (Number(data.billing_rate) ||
                                                  0) / 100
                                              ).toFixed(2)
                                            )
                                          ).value_with_symbol
                                        }
                                      </Typography>
                                    </Tooltip>
                                  ) : (
                                    "-"
                                  );
                                },
                              },
                              {
                                headerName: _t("MU") + "%",
                                field: "mu",
                                maxWidth: 140,
                                minWidth: 140,
                                suppressMovable: false,
                                suppressMenu: true,
                                headerClass: "ag-header-right",
                                cellClass: "ag-cell-right",
                                cellRenderer: (
                                  params: IIvItemsCellRenderer
                                ) => {
                                  const { data } = params;
                                  const is_markup_percentage =
                                    data?.is_markup_percentage ?? "";
                                  let total =
                                    (Number(data?.total_mins ?? 0) / 60) *
                                    (Number(data?.unit_cost ?? 0) / 100);
                                  if (
                                    data.ot_hours.toString() !== "" &&
                                    data.ot_hours
                                  ) {
                                    total +=
                                      (((Number(data.ot_hours) / 60) *
                                        Number(data.unit_cost)) /
                                        100 /
                                        60) *
                                      0.5;
                                  }

                                  const markup = Number(data?.markup);

                                  let markupToShow = formatter("0.00").value;

                                  if (
                                    is_markup_percentage?.toString() === "1"
                                  ) {
                                    markupToShow = formatter(
                                      markup?.toString() ?? "0"
                                    ).value;
                                  } else {
                                    if (total != 0) {
                                      const markupPercentage =
                                        markup / total - 100;
                                      markupToShow =
                                        markupPercentage.toFixed(2);
                                      markupToShow = formatter(
                                        Number(markupToShow || 0).toFixed(2)
                                      )?.value;
                                    }
                                  }
                                  return data.markup ? (
                                    <Tooltip title={markupToShow}>
                                      <Typography className="table-tooltip-text">
                                        {markupToShow}
                                      </Typography>
                                    </Tooltip>
                                  ) : (
                                    <>-</>
                                  );
                                },
                              },
                              {
                                headerName: _t("Total"),
                                field: "total",
                                maxWidth: 130,
                                minWidth: 130,
                                suppressMovable: false,
                                suppressMenu: true,

                                headerClass: "ag-header-right",
                                cellClass: "ag-cell-right",
                                cellRenderer: (
                                  params: IIvItemsCellRenderer
                                ) => {
                                  const { data } = params;
                                  return data.total ? (
                                    <Tooltip
                                      title={
                                        formatter(
                                          formatAmount(
                                            (
                                              (Number(data.total) || 0) / 100
                                            ).toFixed(2)
                                          )
                                        ).value_with_symbol
                                      }
                                    >
                                      <Typography className="table-tooltip-text">
                                        {
                                          formatter(
                                            formatAmount(
                                              (
                                                (Number(data.total) || 0) / 100
                                              ).toFixed(2)
                                            )
                                          ).value_with_symbol
                                        }
                                      </Typography>
                                    </Tooltip>
                                  ) : (
                                    "-"
                                  );
                                },
                              },
                              {
                                headerName: _t("Tax"),
                                field: "apply_global_tax",
                                minWidth: 50,
                                maxWidth: 50,
                                suppressMovable: false,
                                suppressMenu: true,
                                headerClass: "ag-header-center",

                                cellClass: "ag-cell-center flex justify-center",
                                editable:
                                  !isReadOnly &&
                                  (details?.approval_type_key ===
                                    "invoice_open" ||
                                    details?.approval_type_key ===
                                      "invoice_on_hold"),
                                cellRenderer: "agCheckboxCellRenderer",
                                cellEditor: "agCheckboxCellEditor",
                                valueGetter: ({
                                  data,
                                }: IIvItemsCellRenderer) => {
                                  return data?.apply_global_tax == 1;
                                },
                                valueSetter: (params: ValueSetterParams) => {
                                  if (params && params.node) {
                                    let nVal = params.newValue;
                                    const updatedData = {
                                      ...params.data,
                                      apply_global_tax: nVal,
                                    };
                                    params.node.setData(updatedData);
                                    handleApplyGlobalTaxUpdate(
                                      params.data,
                                      nVal === true ? 1 : 0
                                    );
                                  }
                                  return true;
                                },
                              },
                              {
                                headerName: "",
                                field: "",
                                maxWidth: 80,
                                minWidth: 80,
                                suppressMenu: true,
                                cellRenderer: (
                                  params: IIvItemsCellRenderer
                                ) => {
                                  const { data } = params;
                                  return (
                                    <div className="flex items-center gap-1.5 justify-end">
                                      <ButtonWithTooltip
                                        tooltipTitle={_t("View")}
                                        tooltipPlacement="top"
                                        icon="fa-solid fa-eye"
                                        onClick={() => {
                                          setTimeCardItemToBeUpdate(data);
                                          setTimeCardDetailsOpen(true);
                                        }}
                                      />
                                      {!isReadOnly &&
                                        (details?.approval_type_key ===
                                          "invoice_open" ||
                                          details?.approval_type_key ===
                                            "invoice_on_hold") && (
                                          <ButtonWithTooltip
                                            tooltipTitle={_t("Delete")}
                                            tooltipPlacement="top"
                                            icon="fa-regular fa-trash-can"
                                            onClick={() => {
                                              setSelectedItemToDelete(
                                                Number(data.item_id)
                                              );
                                              setIsDeleteConfirmOpen(true);
                                            }}
                                          />
                                        )}
                                    </div>
                                  );
                                },
                              },
                            ]}
                            rowData={tableData[costCode]}
                            noRowsOverlayComponent={() => (
                              <NoRecords
                                image={`${window.ENV.CDN_URL}assets/images/no-records-time-cards.svg`}
                              />
                            )}
                          />
                        </div>
                      </div>
                    </div>
                  );
                })}

              {Array.isArray(tableData) && (
                <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[33px] before:top-6 before:bg-gradient-to-r from-primary-500">
                  <div className="ag-theme-alpine customs-table-height customs-vertical-scroll-auto max-h-[680px]">
                    <StaticTable
                      className="static-table"
                      columnDefs={[
                        {
                          headerName: "",
                          field: "move",
                          minWidth: 30,
                          maxWidth: 30,
                          suppressMenu: true,
                        },
                        {
                          headerName: _t("Type"),
                          field: "type",
                          maxWidth: 50,
                          minWidth: 50,
                          suppressMovable: false,
                          suppressMenu: true,
                          headerClass: "ag-header-center",
                          cellClass: "ag-cell-center",
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;
                            return data.item_type_name ? (
                              <Tooltip title={_t(data.item_type_name)}>
                                <FontAwesomeIcon
                                  className="w-4 h-4 text-primary-900 mx-auto"
                                  icon={iconByItemTypeName[data.item_type_key]}
                                />
                              </Tooltip>
                            ) : (
                              "-"
                            );
                          },
                        },
                        {
                          headerName: _t("Employee"),
                          field: "employee_name",
                          minWidth: 220,
                          maxWidth: 220,
                          suppressMovable: false,
                          suppressMenu: true,
                          headerClass: "ag-header-left",
                          cellClass: "ag-cell-left",
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;

                            return data.tc_employee_name ? (
                              <Tooltip
                                title={HTMLEntities.decode(
                                  sanitizeString(data.tc_employee_name)
                                )}
                              >
                                <Typography className="table-tooltip-text">
                                  {HTMLEntities.decode(
                                    sanitizeString(data.tc_employee_name)
                                  )}
                                </Typography>
                              </Tooltip>
                            ) : (
                              "-"
                            );
                          },
                        },
                        {
                          headerName: _t("Cost Code"),
                          field: "cost_code",
                          minWidth: 540,
                          flex: 2,
                          resizable: true,
                          suppressMovable: false,
                          suppressMenu: true,
                          headerClass: "ag-header-left",
                          cellClass: "ag-cell-left",
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;
                            const costCode = `${data?.cost_code_name ?? ""}${
                              data?.csi_code ? ` (${data?.csi_code})` : ""
                            }${
                              data?.code_is_deleted === 1 ? ` (Archived)` : ""
                            }`;
                            return (
                              <>
                                {costCode ? (
                                  <Tooltip
                                    title={HTMLEntities.decode(
                                      sanitizeString(costCode)
                                    )}
                                  >
                                    <Typography className="table-tooltip-text">
                                      {HTMLEntities.decode(
                                        sanitizeString(costCode || "-")
                                      )}
                                    </Typography>
                                  </Tooltip>
                                ) : (
                                  "-"
                                )}
                              </>
                            );
                          },
                        },
                        {
                          headerName: _t("Hours"),
                          field: "hours",
                          maxWidth: 100,
                          minWidth: 100,
                          suppressMovable: false,
                          suppressMenu: true,
                          headerClass: "ag-header-right",
                          cellClass: "ag-cell-right",
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;
                            const hours = Math.floor(
                              Number(data.total_mins) / 60
                            );
                            const remainingMinutes =
                              Number(data.total_mins) % 60;

                            const formatedHours = `${hours
                              .toString()
                              .padStart(2, "0")}:${remainingMinutes
                              .toString()
                              .padStart(2, "0")}`;
                            return formatedHours ? (
                              <Tooltip title={formatedHours}>
                                <Typography className="table-tooltip-text">
                                  {formatedHours}
                                </Typography>
                              </Tooltip>
                            ) : (
                              "-"
                            );
                          },
                        },
                        {
                          headerName: _t("Rate"),
                          field: "rate",
                          maxWidth: 130,
                          minWidth: 130,
                          suppressMovable: false,
                          suppressMenu: true,
                          headerClass: "ag-header-right",
                          cellClass: "ag-cell-right",
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;
                            return data.billing_rate ? (
                              <Tooltip
                                title={
                                  formatter(
                                    formatAmount(
                                      (
                                        (Number(data.billing_rate) || 0) / 100
                                      ).toFixed(2)
                                    )
                                  ).value_with_symbol
                                }
                              >
                                <Typography className="table-tooltip-text">
                                  {
                                    formatter(
                                      formatAmount(
                                        (
                                          (Number(data.billing_rate) || 0) / 100
                                        ).toFixed(2)
                                      )
                                    ).value_with_symbol
                                  }
                                </Typography>
                              </Tooltip>
                            ) : (
                              "-"
                            );
                          },
                        },
                        {
                          headerName: _t("MU") + "%",
                          field: "mu",
                          maxWidth: 140,
                          minWidth: 140,
                          suppressMovable: false,
                          suppressMenu: true,
                          headerClass: "ag-header-right",
                          cellClass: "ag-cell-right",
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;
                            const is_markup_percentage =
                              data?.is_markup_percentage ?? "";
                            let total =
                              (Number(data?.total_mins ?? 0) / 60) *
                              (Number(data?.unit_cost ?? 0) / 100);
                            if (
                              data.ot_hours.toString() !== "" &&
                              data.ot_hours
                            ) {
                              total +=
                                (((Number(data.ot_hours) / 60) *
                                  Number(data.unit_cost)) /
                                  100 /
                                  60) *
                                0.5;
                            }
                            const markup = Number(data?.markup);

                            let markupToShow = formatter("0.00").value;

                            if (is_markup_percentage?.toString() === "1") {
                              markupToShow = formatter(
                                markup?.toString() ?? "0.00"
                              ).value;
                            } else {
                              if (total != 0) {
                                const markupPercentage = markup / total - 100;
                                markupToShow = markupPercentage.toFixed(2);
                                markupToShow = formatter(
                                  Number(markupToShow || 0).toFixed(2)
                                )?.value;
                              }
                            }
                            return data.markup ? (
                              <Tooltip title={markupToShow}>
                                <Typography className="table-tooltip-text">
                                  {markupToShow}
                                </Typography>
                              </Tooltip>
                            ) : (
                              <></>
                            );
                          },
                        },
                        {
                          headerName: _t("Total"),
                          field: "total",
                          maxWidth: 130,
                          minWidth: 130,
                          suppressMovable: false,
                          suppressMenu: true,
                          headerClass: "ag-header-right",
                          cellClass: "ag-cell-right",
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;
                            return data.total ? (
                              <Tooltip
                                title={
                                  formatter(
                                    formatAmount(
                                      ((Number(data.total) || 0) / 100).toFixed(
                                        2
                                      )
                                    )
                                  ).value_with_symbol
                                }
                              >
                                <Typography className="table-tooltip-text">
                                  {
                                    formatter(
                                      formatAmount(
                                        (
                                          (Number(data.total) || 0) / 100
                                        ).toFixed(2)
                                      )
                                    ).value_with_symbol
                                  }
                                </Typography>
                              </Tooltip>
                            ) : (
                              "-"
                            );
                          },
                        },
                        {
                          headerName: _t("Tax"),
                          field: "apply_global_tax",
                          minWidth: 50,
                          maxWidth: 50,
                          suppressMovable: false,
                          suppressMenu: true,

                          headerClass: "ag-header-center",
                          cellClass: "ag-cell-center flex justify-center",
                          editable:
                            !isReadOnly &&
                            (details?.approval_type_key === "invoice_open" ||
                              details?.approval_type_key === "invoice_on_hold"),
                          cellRenderer: "agCheckboxCellRenderer",
                          cellEditor: "agCheckboxCellEditor",
                          valueGetter: ({ data }: IIvItemsCellRenderer) => {
                            return data?.apply_global_tax == 1;
                          },
                          valueSetter: (params: ValueSetterParams) => {
                            if (params && params.node) {
                              let nVal = params.newValue;
                              const updatedData = {
                                ...params.data,
                                apply_global_tax: nVal,
                              };
                              params.node.setData(updatedData);
                              handleApplyGlobalTaxUpdate(
                                params.data,
                                nVal === true ? 1 : 0
                              );
                            }
                            return true;
                          },
                        },
                        {
                          headerName: "",
                          field: "",
                          maxWidth: 80,
                          minWidth: 80,
                          suppressMenu: true,
                          cellRenderer: (params: IIvItemsCellRenderer) => {
                            const { data } = params;
                            return (
                              <div className="flex items-center gap-1.5 justify-end">
                                <ButtonWithTooltip
                                  tooltipTitle={_t("View")}
                                  tooltipPlacement="top"
                                  icon="fa-solid fa-eye"
                                  onClick={() => {
                                    setTimeCardItemToBeUpdate(data);
                                    setTimeCardDetailsOpen(true);
                                  }}
                                />
                                {!isReadOnly && (
                                  <ButtonWithTooltip
                                    tooltipTitle={_t("Delete")}
                                    tooltipPlacement="top"
                                    icon="fa-regular fa-trash-can"
                                    onClick={() => {
                                      setSelectedItemToDelete(
                                        Number(data.item_id)
                                      );
                                      setIsDeleteConfirmOpen(true);
                                    }}
                                  />
                                )}
                              </div>
                            );
                          },
                        },
                      ]}
                      rowData={tableData}
                      noRowsOverlayComponent={() => (
                        <NoRecords
                          image={`${window.ENV.CDN_URL}assets/images/no-records-time-cards.svg`}
                        />
                      )}
                    />
                  </div>
                </div>
              )}
            </div>
          }
        />
      )}

      {isDeleteConfirmOpen && (
        <ConfirmModal
          isOpen={isDeleteConfirmOpen}
          modaltitle={_t("Delete")}
          description={_t("Are you sure you want to delete this Item?")}
          modalIcon="fa-regular fa-trash-can"
          isLoading={isDeleting}
          onAccept={() => {
            if (selectedItemToDelete) {
              handleInvoiceItemDelete(selectedItemToDelete);
            }
          }}
          onDecline={() => {
            setIsDeleteConfirmOpen(false);
          }}
          onCloseModal={() => {
            setIsDeleteConfirmOpen(false);
          }}
        />
      )}

      {timeCardDetailsOpen && (
        <TimeCardDetails
          formData={timeCardItemToBeUpdate}
          timeCardDetailsOpen={timeCardDetailsOpen}
          setTimeCardDetailsOpen={setTimeCardDetailsOpen}
          isViewOnly={
            isReadOnly ||
            details?.approval_type_key === "invoice_submitted" ||
            details?.approval_type_key === "invoice_paid"
          }
        />
      )}
    </>
  );
};

export default TimeCardData;
