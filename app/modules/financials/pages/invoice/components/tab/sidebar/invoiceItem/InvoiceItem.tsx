import isEmpty from "lodash/isEmpty";
import * as Yup from "yup";
// Hook + redux
import { useTranslation } from "~/hook";
import InvoiceSendEmail from "~/modules/financials/pages/invoice/components/InvoiceSendEmail";
import { updateInvoiceItems } from "~/modules/financials/pages/invoice/redux/slices/InvoiceItemsSlice";
// Antd
import { type RadioChangeEvent } from "antd";
// Atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { SelectField } from "~/shared/components/molecules/selectField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { ContactDetails } from "~/shared/components/molecules/contactDetails";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useFormik } from "formik";
import { formatAmount, Number, sanitizeString } from "~/helpers/helper";
import { useAppIVDispatch, useAppIVSelector } from "../../../../redux/store";
import {
  filterOptionBySubstring,
  getItemTypeIcon,
  onKeyDownCurrency,
  wholeNumberRegex,
} from "~/shared/utils/helper/common";
import { getGConfig, getGSettings, getGTypes } from "~/zustand";
import { onKeyDownNumber } from "~/helpers/key-down.helper";
import {
  getDirectaryIdByKey,
  getDirectaryKeyById,
} from "~/components/sidebars/multi-select/customer/zustand/action";
import { defaultConfig } from "~/data";
import { Form, useParams } from "@remix-run/react";
import {
  addInvoiceItems,
  getInvoiceItemsList,
  updateInvoiceItem,
} from "../../../../redux/action/InvoiceItemsActions";
import { floatNumberRegex } from "../../../../utils/helper";
import { addUnit, getUnitList } from "~/redux/action/unitActions";

type ModifiedCustomerEmail = Omit<CustomerEmail, "type"> & {
  type: string; // New type for `type`
};

const InvoiceItem = ({
  invoiceItem,
  setInvoiceItem,
  isViewOnly = false,
  formData,
  isAddInvoiceItem,
  setIsAddInvoiceItem,
}: IInvoiceItemProps) => {
  const { _t } = useTranslation();
  const params = useParams();
  const gConfig = getGConfig();
  const gSettings: GSettings = getGSettings();
  const { currency_symbol, is_cidb_auto_save } = gSettings;
  const [markup, setMarkup] = useState<string>("markup_percent");
  const costUnitRef = useRef<HTMLDivElement>(null);
  const unitCostContainerRef = useRef<HTMLDivElement>(null);
  const [showUnitInputs, setShowUnitInputs] = useState<boolean>(!isViewOnly);
  const [unitCost, setUnitCost] = useState<number | string | undefined>(
    formData?.unit_cost
  );
  const currentCurrency = currency_symbol ?? "$";

  const { details }: IIVDetailsInitialState = useAppIVSelector(
    (state) => state.invoiceDetails
  );
  const invoiceItems = useAppIVSelector(
    (state) => state.proInvoiceItemList.invoiceItems
  );
  const [submitAction, setSubmitAction] = useState<string>("save_n_close");
  const { module_singular_name }: GConfig = getGConfig();
  const dispatch = useAppIVDispatch();

  const [mainTotal, setMainTotal] = useState<string | number>("");
  const [isOpenSelectAssignedTo, setIsOpenSelectAssignedTo] =
    useState<boolean>(false);
  const { inputFormatter, unformatted, formatter } = useCurrencyFormatter();
  const unitCostRef = useRef<InputRef>(null);
  const [isMuPercentFieldChanged, setIsMuPercentFieldChanged] =
    useState<boolean>(false);
  const [isOpenContactDetails, setIsOpenContactDetails] =
    useState<boolean>(false);
  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);
  const [selectedData, setSelectedData] = useState<Partial<IDirectoryData>>({});
  const { codeCostData }: IGetCostCodeList = useAppIVSelector(
    (state) => state.costCode
  );
  const [unit, setUnit] = useState<number | string | undefined>(formData?.unit);
  const [confirmSaveData, setConfirmSaveData] = useState<{
    rid: number;
    message?: string;
  }>({ rid: 0 });
  const itemTypes: GType[] = getGTypes();
  const itemTypesWithMarkup = useAppIVSelector(
    (state) => state.invoiceItemTypes.itemTypes
  );
  const [isExistingLoading, setIsExistingLoading] = useState<boolean>(false);

  const [newTypeName, setNewTypeName] = useState<string>("");
  const [unitData, setUnitData] = useState<IUnitData[]>([]);
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);

  const getUnit = async () => {
    const unit: IUnitListResponse = await getUnitList();
    setUnitData(unit?.data?.units ?? []);
  };

  useEffect(() => {
    getUnit();
  }, []);
  const taxSettingsMap: Record<number, number> = {
    161: gSettings.is_taxable_material_items,
    162: gSettings.is_taxable_equipment_items,
    163: gSettings.is_taxable_labor_items,
    164: gSettings.is_taxable_subcontractor_items,
    165: gSettings.is_taxable_other_items,
  };

  const filteredItemsTypes = useMemo(
    () =>
      itemTypes?.filter(
        (item: Partial<GType>) => item?.type === "company_items"
      ),
    [itemTypes]
  );

  const reorderedItemsTypes = (() => {
    if (!Array.isArray(filteredItemsTypes)) return [];
    const items = [...filteredItemsTypes];
    const index = items.findIndex((item) => item.type_id == "163");
    if (index > -1) {
      const [labourItem] = items.splice(index, 1); // remove item
      items.splice(1, 0, labourItem); // insert at 2nd place
    }
    return items;
  })();

  const handleParagraphClick = () => {
    if (!isViewOnly && isAddInvoiceItem === true) {
      setShowUnitInputs(true);
      setTimeout(() => {
        unitCostRef?.current?.focus();
      }, 10);
    }
  };

  const costCodeOptions = useMemo(() => {
    let costCodeOpts = codeCostData.map((item: ICostCode) => {
      return {
        label: `${item.cost_code_name}${
          item.csi_code ? ` (${item.csi_code})` : ""
        }${item.is_deleted === 1 ? ` (Archived)` : ""}`,
        value: item.code_id,
      };
    });

    const isCostCodeAvailable = codeCostData.some((item: ICostCode) => {
      return (
        Number(item.code_id) === Number(formData.cost_code_id) &&
        item.csi_name === formData.cost_code_name &&
        Number(item.csi_code) === Number(formData.csi_code)
      );
    });

    if (!isCostCodeAvailable && !isAddInvoiceItem) {
      costCodeOpts = [
        ...costCodeOpts,
        {
          label: `${formData?.cost_code_name}${
            formData.csi_code ? ` (${formData.csi_code})` : ""
          } (Archived)`,
          value: formData.cost_code_id as string,
        },
      ];
    }
    return costCodeOpts;
  }, [codeCostData, formData]);

  const undefinedTypeMarkup = useMemo(() => {
    return itemTypesWithMarkup?.find(
      (i: IWorkOrderType) => i.type_id?.toString() === ""
    )?.mark_up;
  }, [itemTypesWithMarkup]);

  const initialValues: IInvoiceItemData = useMemo(() => {
    return {
      ...formData,
      item_type: isAddInvoiceItem
        ? ""
        : formData.item_type
        ? formData.item_type?.toString()
        : "",
      unit_cost: (Number(formData?.unit_cost || 0) / 100)
        ?.toFixed(2)
        .toString(),
      add_item_to_database: isAddInvoiceItem
        ? is_cidb_auto_save
        : formData?.item_on_database,
      markup: isAddInvoiceItem
        ? undefinedTypeMarkup !== "null" && undefinedTypeMarkup
          ? undefinedTypeMarkup?.toString()
          : ""
        : formData?.is_markup_percentage === 0
        ? formData?.markup != null
          ? (Number(formData?.markup) / 100)?.toString()
          : null
        : formData?.markup,
      internal_notes: formData.notes || formData.internal_notes,
      is_markup_percentage: isAddInvoiceItem
        ? 1
        : formData.is_markup_percentage,
      item_on_database: isAddInvoiceItem
        ? is_cidb_auto_save
        : formData?.item_on_database,
    };
  }, [formData, invoiceItem, itemTypesWithMarkup]);

  const validationSchema = Yup.object().shape({
    subject: Yup.string().required("This field is required."),
    item_type: Yup.string().required("This field is required."),
  });

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values, { setSubmitting, resetForm }) => {
      if (!values.subject?.trim()) {
        formik.setFieldError("subject", _t("This field is required."));
        return;
      }
      const formData = values;
      if (isAddInvoiceItem) {
        const itemsToBeAdd: IAddInvoiceItemsParams = {
          project_id: details.project_id,
          id: Number(params.id),
          items: [
            {
              item_type: Number(formData.item_type) || 0,
              subject: formData.subject || "",
              unit_cost: Number(formData.unit_cost) * 100,
              quantity: Number(formData.quantity),
              is_markup_percentage: Number(formData.is_markup_percentage),
              markup_amount: Number(formData.markup),
              markup: formData?.markup
                ? formData?.is_markup_percentage === 0
                  ? Number(formData?.markup) * 100
                  : Number(formData?.markup)
                : null,
              cost_code_id: Number(formData.cost_code_id) || 0,
              total: Number(mainTotal) * 100,
              invoice_total: Number(mainTotal) * 100,
              apply_global_tax: formData.apply_global_tax === 1 ? true : false,
              assigned_to: Number(formData.assigned_to),
              assigned_to_contact_id: Number(formData.assigned_to_contact_id),
              description: formData.description,
              unit: formData.unit,
              notes: formData.internal_notes,
              add_item_to_database: Number(formData.add_item_to_database),
              reference_item_id:
                confirmSaveData?.rid > 0 ? confirmSaveData.rid : undefined,
            },
          ],
        };

        const response = (await addInvoiceItems(
          itemsToBeAdd
        )) as IAddInvoiceItemRes;

        if (Number(response?.data?.reference_item_id || "") > 0) {
          setConfirmSaveData({
            rid: Number(response?.data?.reference_item_id || ""),
            message: response?.message,
          });
          return false;
        }

        if (response.success) {
          if (Number(response?.data?.reference_item_id || "") <= 0) {
            setConfirmSaveData({
              rid: 0,
              message: "",
            });
            setIsExistingLoading(false);
            const getInvoiceItemsResponse = (await getInvoiceItemsList({
              id: Number(params.id),
              data_for: "all",
              need_section: details.is_new_tm_invoice === 1 ? 1 : 0,
              is_separate_change_order_sections:
                details.is_new_tm_invoice === 0 ? true : undefined,
              is_separate_estimate_sections: false,
            })) as IGetInvoiceItemsApiRes;
            if (getInvoiceItemsResponse.success) {
              dispatch(
                updateInvoiceItems({ items: getInvoiceItemsResponse.data })
              );
              if (submitAction === "save_n_close") {
                setIsAddInvoiceItem(false);
                setInvoiceItem(false);
              } else {
                resetForm();
              }
            } else {
              notification.error({
                description:
                  getInvoiceItemsResponse?.message || "Something went wrong!",
              });
            }
          }
        } else {
          notification.error({
            description: response?.message || "Something went wrong!",
          });
        }
      } else {
        const toBill = (
          (Number(formData.original_item_total) * Number(formData.paid_bill)) /
          10000
        ).toFixed(2);

        const updatedBillingRatio: number = getUpdatedBillingRatio(
          Number(formData.original_item_total) / 100,
          Number(toBill)
        );

        const itemToBeUpdate: IUpdateInvoiceItemsParams = {
          id: Number(params.id),
          items: [
            {
              item_id: formData.item_id,
              invoice_item_no: formData.invoice_item_no,
              unit_cost: Number(formData?.unit_cost) * 100,
              quantity: formData.quantity,
              is_markup_percentage: formData.is_markup_percentage,
              cost_code_id: formData.cost_code_id || 0,
              markup: formData?.markup.toString()
                ? formData?.is_markup_percentage === 0
                  ? (Number(formData?.markup) * 100)?.toString()
                  : formData?.markup?.toString()
                : null,
              total: Number(mainTotal) * 100,
              invoice_total: Number(mainTotal) * 100,
              apply_global_tax: formData.apply_global_tax,
              item_type: formData.item_type,
              billing_ratio: updatedBillingRatio.toFixed(3),
              assigned_to: formData.assigned_to,
              change_order_id: formData.change_order_id,
              add_item_to_database: formData.item_on_database,
              reference_item_id:
                confirmSaveData?.rid > 0
                  ? confirmSaveData.rid
                  : formData.reference_primary_id,
              paid_bill: formData.paid_bill,
              subject: formData.subject,
              description: formData.description || "",
              notes: formData.notes || "",
              unit: formData.unit,
              billing_option: formData.billing_option,
              internal_notes: formData.internal_notes,
              assigned_to_contact_id: formData.assigned_to_contact_id,
              tax_id: formData.tax_id,
            },
          ],
        };

        const costCode = codeCostData.find((item) => {
          return (
            formData.cost_code_id?.toString() === item?.code_id?.toString()
          );
        });

        const newValue = {
          item_id: formData.item_id,
          invoice_item_no: formData.invoice_item_no,
          unit_cost: Number(formData?.unit_cost) * 100,
          quantity: formData.quantity,
          is_markup_percentage: formData.is_markup_percentage,
          cost_code_id: formData.cost_code_id,
          markup: formData?.markup.toString()
            ? formData?.is_markup_percentage === 0
              ? (Number(formData?.markup) * 100)?.toString()
              : formData?.markup?.toString()
            : null,
          csi_code:
            codeCostData.filter(
              (costDode) => costDode.code_id === formData.cost_code_id
            )[0]?.csi_code ||
            formData.csi_code ||
            "",
          total: Number(mainTotal) * 100,
          apply_global_tax: formData.apply_global_tax,
          item_type: formData.item_type,
          assigned_to: formData.assigned_to,
          change_order_id: formData.change_order_id,
          item_on_database: formData.item_on_database,
          reference_item_id:
            confirmSaveData?.rid > 0
              ? confirmSaveData.rid
              : formData.reference_primary_id,
          paid_bill: formData.paid_bill,
          subject: formData.subject,
          description: formData.description || "",
          notes: formData.notes || "",
          unit: formData.unit,
          billing_option: formData.billing_option,
          internal_notes: formData.internal_notes,
          cost_code_name:
            codeCostData.filter(
              (costDode) => costDode.code_id === formData.cost_code_id
            )[0]?.cost_code_name ||
            formData.cost_code_name ||
            "",
          item_type_key:
            filteredItemsTypes.filter(
              (item) =>
                item.type_id.toString() === formData.item_type.toString()
            )[0]?.key ?? null,
          item_type_display_name:
            filteredItemsTypes.filter(
              (item) =>
                item.type_id.toString() === formData.item_type.toString()
            )[0]?.display_name ?? "",
          assignee_name: formData.assignee_name,
          assigned_to_contact_id: formData.assigned_to_contact_id,
        };

        const response = (await updateInvoiceItem(
          itemToBeUpdate as IUpdateInvoiceItemsParams
        )) as IUpdateInvoiceItemsApiRes;

        if (Number(response?.data?.reference_item_id || "") > 0) {
          setConfirmSaveData({
            rid: Number(response?.data?.reference_item_id || ""),
            message: response?.message,
          });
          return false;
        }

        if (response.success) {
          const updatedItems = {
            ...invoiceItems,
            items: invoiceItems.items.map((item) => {
              if (item.item_id === newValue.item_id) {
                return {
                  ...item,
                  ...newValue,
                };
              } else {
                return {
                  ...item,
                };
              }
            }),
            bill_sections: invoiceItems.bill_sections.map((section) => {
              return {
                ...section,
                items: section.items.map((item) => {
                  if (item.item_id === newValue.item_id) {
                    return {
                      ...item,
                      ...newValue,
                    };
                  } else {
                    return {
                      ...item,
                    };
                  }
                }),
              };
            }),
            change_order_sections: invoiceItems.change_order_sections.map(
              (section) => {
                return {
                  ...section,
                  items: section.items.map((item) => {
                    if (item.item_id === newValue.item_id) {
                      return {
                        ...item,
                        ...newValue,
                      };
                    } else {
                      return {
                        ...item,
                      };
                    }
                  }),
                };
              }
            ),
            purchase_order_sections: invoiceItems.purchase_order_sections.map(
              (section) => {
                return {
                  ...section,
                  items: section.items.map((item) => {
                    if (item.item_id === newValue.item_id) {
                      return {
                        ...item,
                        ...newValue,
                      };
                    } else {
                      return {
                        ...item,
                      };
                    }
                  }),
                };
              }
            ),
          };

          dispatch(updateInvoiceItems({ items: updatedItems }));

          setInvoiceItem(false);
        }
      }

      setSubmitting(false);
    },
  });

  const getUpdatedBillingRatio = useCallback(
    (original_item_total: number, newValue: number) => {
      const mainTotal = (newValue * 100) / original_item_total;
      return mainTotal;
    },
    []
  );
  const { handleSubmit, setFieldValue, values, errors, isSubmitting } = formik;

  useEffect(() => {
    formik.setValues(initialValues);
  }, [formData, invoiceItem, isAddInvoiceItem]);

  useEffect(() => {
    if (isAddInvoiceItem === false) {
      if (formData?.is_markup_percentage === 1) {
        setMarkup("markup_percent");
      } else {
        setMarkup("markup_dolar");
      }
    }
  }, [formData?.is_markup_percentage]);

  const assignedTo = useMemo(() => {
    if (
      values?.assigned_to !== 0 &&
      values?.assigned_to?.toString() !== "" &&
      values?.assigned_to &&
      !!values?.assignee_name
    ) {
      const assigned_to: Partial<ModifiedCustomerEmail>[] = [
        {
          display_name: values?.assignee_name,
          user_id: Number(values?.assigned_to),
          type: values?.assignee_type,
          type_key: getDirectaryKeyById(Number(values?.assignee_type), gConfig),
          image: values?.user_image,
        },
      ];

      return assigned_to as TselectedContactSendMail[];
    } else {
      return [];
    }
  }, [values?.assigned_to, values.assignee_name, values.assignee_type]);

  const handleSaveItem = (key: string) => {
    setSubmitAction(key);
  };

  const handleFocusOut = (e: React.FocusEvent<HTMLInputElement>) => {
    if (!unitCostContainerRef.current?.contains(e.relatedTarget as Node)) {
      if (
        values?.unit_cost &&
        !isEmpty(values?.unit_cost) &&
        values.unit &&
        !isEmpty(values.unit)
      ) {
        setShowUnitInputs(false);
      }
    }
  };

  const itemTypeAndSaveItemToListField: {
    show: boolean;
    disable: boolean;
  } = useMemo(() => {
    const show =
      isAddInvoiceItem ||
      (values.reference_item_id === 0 && values.reference_module_item_id === 0);

    const disable = !isAddInvoiceItem && !!values.item_on_database;

    return { show, disable };
  }, [isAddInvoiceItem, values]);

  const saveItemKey = useMemo(() => {
    const itemType = itemTypes?.find(
      (i: GType) => i.type_id?.toString() === values?.item_type?.toString()
    );

    return itemType?.name;
  }, [itemTypes, values?.item_type]);

  useMemo(() => {
    if (
      values?.unit_cost !== "" &&
      !isEmpty(values?.unit_cost) &&
      values?.unit_cost !== undefined &&
      values?.unit !== "" &&
      !isEmpty(values?.unit) &&
      values?.unit !== undefined &&
      !unitCostContainerRef.current?.contains(document.activeElement as Node)
    ) {
      setShowUnitInputs(false);
    } else {
      setShowUnitInputs(true);
    }
  }, [invoiceItem, formData, values]);

  useEffect(() => {
    if (
      values?.quantity?.toString() !== "" &&
      values?.unit_cost !== "" &&
      values?.quantity &&
      values?.unit_cost
    ) {
      const total = Number(values?.quantity) * Number(values?.unit_cost);
      setFieldValue("total", total.toString());
      setMainTotal(Number(values?.markup_amount) + Number(values?.total));
    } else {
      setFieldValue("total", "");
      setMainTotal("");
    }
    if (
      values?.total !== "" &&
      values.total &&
      values?.markup !== "" &&
      values?.markup
    ) {
      if (markup === "markup_percent") {
        const markup = (Number(values?.total) * Number(values?.markup)) / 100;
        setFieldValue("markup_amount", markup);
        setMainTotal(Number(markup) + Number(values?.total));
      } else {
        const markup = Number(values?.markup);
        if (
          markup !== 0 &&
          values?.unit_cost != "0.00" &&
          values?.unit_cost != "0"
        ) {
          const markupPercentage =
            (Number(markup) * 100) / (Number(values?.total) || 1) - 100;
          setFieldValue("markup_amount", markupPercentage.toFixed(2));
          setMainTotal(markup);
        } else {
          setFieldValue("markup_amount", "0");
          setMainTotal(Number(values?.total));
        }
      }
    } else {
      setFieldValue("markup_amount", "");
      setMainTotal(Number(values?.total));
    }
  }, [
    values?.quantity,
    values?.unit_cost,
    values?.total,
    values?.markup,
    values?.is_markup_percentage,
    markup,
  ]);

  const isTypeDisabled = useMemo(() => {
    return formData.item_type !== 0 && isAddInvoiceItem === false
      ? true
      : false;
  }, [isAddInvoiceItem, formData.item_type]);

  const parseNumber = (_value: string | string[]) => {
    const value = Array.isArray(_value) ? _value[0] : _value;
    let finalValue = 0;
    const parsedNumber = parseFloat(value);
    if (isNaN(parsedNumber) || !value) {
      finalValue = 0;
    } else {
      finalValue = parsedNumber;
    }

    return finalValue;
  };

  const INVOICE_ITEMS_LIST_TAB = [
    {
      label: (
        <FontAwesomeIcon
          className="text-base w-3.5 h-3.5"
          icon="fa-regular fa-percent"
        />
      ),
      value: "markup_percent",
    },
    {
      label: (
        <Typography className="text-sm min-w-3.5 h-3.5 flex items-center justify-center">
          {inputFormatter().currency_symbol || "$"}
        </Typography>
      ),
      value: "markup_dolar",
    },
  ];

  const applyGlobalTax = useMemo(() => {
    return taxSettingsMap[Number(values.item_type)] ?? 0;
  }, [values.item_type, gSettings]);

  useEffect(() => {
    if (isAddInvoiceItem) {
      setFieldValue("apply_global_tax", applyGlobalTax);
    }
  }, [applyGlobalTax, setFieldValue]);

  const progressiveBillingTotal = useMemo(() => {
    if (values.is_progressive_bill_item === 1) {
      const total = Number(values.progressive_bill_total || 0) / 100;
      const formatedTotal = formatter(
        formatAmount(total.toFixed(2))
      ).value_with_symbol;
      return formatedTotal;
    } else {
      const formatedTotal = formatter(formatAmount("0.00")).value_with_symbol;
      return formatedTotal;
    }
  }, [values.is_progressive_bill_item, values.progressive_bill_total]);
  const handlePaste = (event: React.ClipboardEvent<HTMLInputElement>) => {
    event.preventDefault();
  };
  return (
    <>
      <Drawer
        open={invoiceItem}
        rootClassName="drawer-open"
        width={718}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-file-invoice-dollar"
              />
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {/* {!isAddInvoiceItem ? _t("Invoice Item") : _t("Add Invoice Item")} */}
              {`${isAddInvoiceItem ? _t("Add") : ""} ${HTMLEntities.decode(
                sanitizeString(module_singular_name)
              )} ${_t("Item")}`}
            </Header>
          </div>
        }
        closeIcon={
          <CloseButton
            onClick={() => {
              setInvoiceItem(false);
              setIsAddInvoiceItem(false);
            }}
          />
        }
      >
        <Form
          method="post"
          className="py-4"
          onSubmit={handleSubmit}
          noValidate
          onKeyDown={(e) => {
            if (
              e.key === "Enter" &&
              (e.target as HTMLElement).tagName !== "TEXTAREA"
            ) {
              e.preventDefault();
            }
          }}
        >
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <div className="grid gap-4">
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <InputField
                    label={_t("Item Name")}
                    labelPlacement="top"
                    name="subject"
                    id="subject"
                    value={HTMLEntities.decode(sanitizeString(values?.subject))}
                    errorMessage={
                      formik.touched?.subject && !values.subject.trim()
                        ? errors.subject
                        : ""
                    }
                    disabled={isViewOnly}
                    isRequired={true}
                    onChange={(e) => {
                      setFieldValue("subject", e.target.value);
                    }}
                    onBlur={(e) => {
                      setFieldValue("subject", e.target.value.trim());
                      formik.handleBlur(e);
                    }}
                    autoComplete="off"
                  />
                </div>
                <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                  <div className="w-full">
                    <SelectField
                      label={_t("Item Type")}
                      labelPlacement="top"
                      isRequired={true}
                      disabled={isTypeDisabled}
                      value={values.item_type.toString()}
                      options={reorderedItemsTypes.map((item: GType) => ({
                        label: (
                          <div className="flex items-center gap-1.5">
                            <FontAwesomeIcon
                              icon={getItemTypeIcon({
                                type: item?.type_id?.toString(),
                              })}
                            />
                            {item?.name}
                          </div>
                        ),
                        value: item.type_id,
                        ...item,
                      }))}
                      errorMessage={
                        formik.touched?.item_type && !values.item_type
                          ? errors.item_type
                          : ""
                      }
                      onChange={(value, option) => {
                        setFieldValue("item_type", parseNumber(value));
                        setFieldValue("item_type_name", option?.name);
                        if (option?.name?.trim()) {
                          formik.setFieldError("item_type", "");
                        } else {
                          formik.setFieldError(
                            "item_type",
                            _t("This field is required.")
                          );
                        }
                        if (!isMuPercentFieldChanged) {
                          const itemType = itemTypesWithMarkup?.find(
                            (i: IWorkOrderType) =>
                              i.type_id?.toString() === value?.toString()
                          );

                          setFieldValue("markup", itemType?.mark_up || "");
                        }
                      }}
                    />
                  </div>
                  <div className="w-full">
                    <ButtonField
                      label={_t("Assigned To")}
                      name="assigned_to"
                      isDisabled={isViewOnly}
                      value={HTMLEntities.decode(
                        sanitizeString(
                          values?.assigned_to !== 0 ? values?.assignee_name : ""
                        )
                      )}
                      onClick={() => {
                        setIsOpenSelectAssignedTo(true);
                      }}
                      avatarProps={{
                        user: {
                          name: HTMLEntities.decode(
                            sanitizeString(values?.assignee_name)
                          ),
                          image: values?.user_image,
                        },
                      }}
                      addonBefore={
                        <div className="flex items-center gap-1">
                          {values?.assigned_to ? (
                            <ContactDetailsButton
                              onClick={(e: React.MouseEvent) => {
                                e.stopPropagation();
                                setIsOpenContactDetails(true);
                              }}
                            />
                          ) : (
                            <></>
                          )}
                        </div>
                      }
                    />
                  </div>
                </div>

                <div className="w-full">
                  <SelectField
                    label={_t("Cost Code")}
                    labelPlacement="top"
                    value={
                      values?.cost_code_id
                        ? costCodeOptions.filter((item) => {
                            return (
                              values?.cost_code_id?.toString() ===
                              item?.value?.toString()
                            );
                          })
                        : []
                    }
                    onChange={(value) => {
                      setFieldValue("cost_code_id", value);
                    }}
                    showSearch
                    filterOption={(input, option) =>
                      filterOptionBySubstring(input, option?.label as string)
                    }
                    options={costCodeOptions}
                    disabled={isViewOnly}
                    allowClear={true}
                    onClear={() => {
                      setFieldValue("cost_code_id", 0);
                    }}
                  />
                </div>
              </SidebarCardBorder>
              <SidebarCardBorder cardTitle={_t("Pricing")}>
                <ul className="border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                  <li className="flex justify-between items-center">
                    <Typography className="text-13 block text-primary-900 dark:text-white/90">
                      {_t("Qty")}
                    </Typography>
                    <div className="sm:w-40 w-28">
                      <InputNumberField
                        name="quantity"
                        id="quantity"
                        rootClassName="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input"
                        placeholder={_t("Item Quantity")}
                        disabled={isAddInvoiceItem === false ? true : false}
                        errorMessage={errors.quantity}
                        labelPlacement="left"
                        formInputClassName={
                          isViewOnly ? "flex items-center justify-end" : ""
                        }
                        defaultValue={
                          Number(values.quantity) !== 0 ? values.quantity : ""
                        }
                        onPaste={handlePaste}
                        value={
                          Number(values.quantity) !== 0
                            ? values.quantity?.toString()
                            : ""
                        }
                        formatter={(value) => {
                          return inputFormatter(value?.toString()).value;
                        }}
                        onChange={(value) => {
                          setFieldValue("quantity", value?.toString());
                        }}
                        parser={(value) => {
                          const inputValue = value
                            ? unformatted(value.toString())
                            : "";
                          return inputValue;
                        }}
                        onKeyDown={(event) => {
                          onKeyDownCurrency(event, {
                            integerDigits: 8,
                            decimalDigits: 2,
                            unformatted,
                            allowNegative: true,
                            decimalSeparator:
                              inputFormatter().decimal_separator,
                          });
                          if (
                            event.key === "Tab" &&
                            !event.shiftKey &&
                            !event.altKey &&
                            !event.ctrlKey &&
                            !event.metaKey
                          ) {
                            event.preventDefault();
                            setShowUnitInputs(true);

                            setTimeout(() => {
                              const unitCostInput =
                                document.getElementById("unit_cost");
                              if (unitCostInput) {
                                unitCostInput.focus();
                              }
                            }, 0);
                          }
                        }}
                      />
                    </div>
                  </li>
                  <li>
                    <ul className="py-0.5 relative">
                      <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                        <FontAwesomeIcon
                          className="w-3 h-3 text-primary-900 dark:text-white"
                          icon="fa-regular fa-xmark"
                        />
                      </li>
                    </ul>
                  </li>
                  <li className="flex justify-between items-center">
                    <Typography className="text-13 block text-primary-900 dark:text-white/90">
                      {_t("Unit Cost/Unit")}
                    </Typography>
                    <div
                      className="sm:w-[260px] w-28 h-[22px]"
                      ref={costUnitRef}
                    >
                      <div
                        ref={unitCostContainerRef}
                        className="text-right !text-[#008000] leading-[22px] !font-semibold text-sm"
                      >
                        {!isViewOnly && (
                          <>
                            {showUnitInputs ? (
                              <div className="flex gap-2">
                                <div className="w-[calc(100%-52px)]">
                                  <InputNumberField
                                    name="unit_cost"
                                    id="unit_cost"
                                    rootClassName="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal cf-work-order-currency-input placeholder:text-13 !w-[112px]"
                                    placeholder={_t("Item Unit Cost")}
                                    disabled={
                                      isAddInvoiceItem === false ? true : false
                                    }
                                    labelPlacement="left"
                                    errorMessage={errors.unit_cost}
                                    onPaste={handlePaste}
                                    autoFocus={Boolean(
                                      values?.unit_cost &&
                                        !isEmpty(values?.unit_cost) &&
                                        values.unit &&
                                        !isEmpty(values.unit)
                                    )}
                                    defaultValue={
                                      Number(values.unit_cost) !== 0
                                        ? values.unit_cost
                                        : ""
                                    }
                                    value={
                                      Number(values.unit_cost) !== 0
                                        ? values.unit_cost
                                        : ""
                                    }
                                    onChange={(value) => {
                                      setFieldValue(
                                        "unit_cost",
                                        value?.toString()
                                      );
                                    }}
                                    formatter={(value, info) => {
                                      return inputFormatter(value?.toString())
                                        .value;
                                    }}
                                    parser={(value) => {
                                      const inputValue = value
                                        ? unformatted(value.toString())
                                        : "";
                                      return inputValue;
                                    }}
                                    onKeyDown={(event) =>
                                      onKeyDownCurrency(event, {
                                        integerDigits: 10,
                                        decimalDigits: 2,
                                        unformatted,
                                        allowNegative: false,
                                        decimalSeparator:
                                          inputFormatter().decimal_separator,
                                      })
                                    }
                                    onBlur={handleFocusOut}
                                  />
                                </div>
                                <div className="w-[65px]">
                                  {window.ENV.ENABLE_UNIT_DROPDOWN ? (
                                    <SelectField
                                      className="!p-0 text-success select-field-text-13 text-left select-unit-filed !h-[22px] !font-semibold placeholder:font-normal"
                                      placeholder="Unit"
                                      name="unit"
                                      disabled={
                                        isAddInvoiceItem === false
                                          ? true
                                          : false
                                      }
                                      labelPlacement="left"
                                      maxLength={15}
                                      value={values?.unit || null}
                                      iconView={true}
                                      popupClassName="!w-[260px]"
                                      showSearch
                                      options={
                                        unitData.map((type) => ({
                                          label: type.name.toString(),
                                          value: type.name.toString(),
                                        })) ?? []
                                      }
                                      allowClear
                                      filterOption={(input, option) =>
                                        filterOptionBySubstring(
                                          input,
                                          option?.label as string
                                        )
                                      }
                                      onChange={(value) => {
                                        setFieldValue(
                                          "unit",
                                          value ? value.toString() : ""
                                        );
                                      }}
                                      addItem={{
                                        text: "Add Unit: Type Unit & Press Enter",
                                        icon: "fa-regular fa-plus",
                                      }}
                                      onInputKeyDown={(e) => {
                                        if (e.key === "Enter") {
                                          const value =
                                            e?.currentTarget?.value?.trim();
                                          const newType =
                                            onEnterSelectSearchValue(
                                              e,
                                              unitData?.map((unit) => ({
                                                label: unit?.name,
                                                value: "",
                                              })) || []
                                            );
                                          if (newType) {
                                            setNewTypeName(newType);
                                          } else if (value) {
                                            notification.error({
                                              description:
                                                "Records already exist, no new records were added.",
                                            });
                                          }
                                        }
                                      }}
                                      onClear={() => {
                                        setFieldValue("unit", "");
                                      }}
                                      errorMessage={errors.unit}
                                      onBlur={handleFocusOut}
                                    />
                                  ) : (
                                    <InputField
                                      className={`!p-0 !pl-1.5 text-sucess field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right ${
                                        !showUnitInputs && "!hidden"
                                      }`}
                                      placeholder={_t("Unit")}
                                      labelPlacement="left"
                                      errorMessage={errors.unit}
                                      maxLength={15}
                                      onBlur={handleFocusOut}
                                      value={values?.unit}
                                      disabled={
                                        isAddInvoiceItem === false
                                          ? true
                                          : false
                                      }
                                      onPaste={handlePaste}
                                      type="text"
                                      onChange={(e) => {
                                        setFieldValue("unit", e.target.value);
                                      }}
                                    />
                                  )}
                                </div>
                              </div>
                            ) : (
                              <Typography
                                className={`text-[#008000] text-13 !font-medium ${
                                  isViewOnly || isAddInvoiceItem === false
                                    ? "cursor-no-drop"
                                    : "cursor-pointer"
                                }`}
                                onClick={handleParagraphClick}
                              >
                                {
                                  formatter(
                                    formatAmount(
                                      Number(values?.unit_cost).toFixed(2)
                                    )
                                  ).value_with_symbol
                                }
                                /{values?.unit}
                              </Typography>
                            )}
                          </>
                        )}

                        {isViewOnly &&
                          (!isEmpty(unitCost) &&
                          unitCost !== 0.0 &&
                          unitCost !== "0.00" &&
                          !isEmpty(unit) &&
                          unit !== 0.0 ? (
                            <Typography
                              className={`!text-[#008000] font-medium text-13 ${
                                isViewOnly ? "cursor-no-drop" : ""
                              }`}
                              disabled={
                                isAddInvoiceItem === false ? true : false
                              }
                            >
                              {values?.unit_cost}/{values?.unit}
                            </Typography>
                          ) : (
                            <div className="flex gap-2">
                              <div className="w-[calc(100%-52px)]">
                                <InputField
                                  className="!p-0 !pl-1.5 !border-0 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                                  placeholder={_t("Item Unit Cost")}
                                  type="number"
                                  name="unit_cost"
                                  id="unit_cost"
                                  maxLength={10}
                                  disabled={isViewOnly}
                                  value={values?.unit_cost}
                                  onChange={() => {}}
                                />
                              </div>
                              <div className="w-11">
                                <InputField
                                  className="!p-0 !pl-1.5 !border-0 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                                  placeholder={_t("Unit")}
                                  maxLength={15}
                                  name="unit"
                                  id="unit"
                                  disabled={isViewOnly}
                                  value={values?.unit}
                                  type="text"
                                  onChange={() => {}}
                                />
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  </li>
                  <li className="flex justify-between border-t border-dashed border-gray-300 pt-2 mt-2">
                    <Typography className="text-13 block text-primary-900 font-semibold">
                      {_t("Total Cost")}
                    </Typography>
                    <div className="sm:w-[260px] w-32 flex justify-end items-center">
                      <Typography
                        className="!text-red-600 text-13 font-semibold"
                        disabled={isAddInvoiceItem === false ? true : false}
                      >
                        {values?.total === ""
                          ? `${
                              formatter(formatAmount("0.00")).value_with_symbol
                            }`
                          : `${
                              formatter(
                                formatAmount(
                                  Number(values?.total || 0).toFixed(2)
                                )
                              ).value_with_symbol
                            }`}
                      </Typography>
                    </div>
                  </li>
                </ul>
                <ul className="my-3.5 border-t border-dashed border-[#ddd] relative">
                  <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full bg-white shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                    <FontAwesomeIcon
                      className="w-3 h-3 text-primary-900 dark:text-white"
                      icon="fa-regular fa-plus"
                    />
                  </li>
                </ul>
                <ul className="grid gap-2 border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                  <li className="flex items-center justify-between">
                    <div className="flex items-center gap-1.5">
                      <div className="w-fit p-0.5 bg-[#EEEFF0] rounded dark:bg-dark-800 ml-auto md:ml-0 sidemenu-listbutton">
                        <ListTabButton
                          value={markup ? markup : ""}
                          options={INVOICE_ITEMS_LIST_TAB}
                          disabled={
                            values.is_progressive_bill_item === 1 || isViewOnly
                          }
                          className="min-w-[26px] !border-transparent bg-[#EEEFF0] py-0 px-1 h-5"
                          activeclassName="active:bg-[#ffffff]"
                          onChange={(e: RadioChangeEvent) => {
                            setMarkup(e.target.value);
                            setFieldValue("markup", "");
                            setIsMuPercentFieldChanged(true);
                            if (e.target.value === "markup_percent") {
                              setFieldValue("is_markup_percentage", 1);
                            } else {
                              setFieldValue("is_markup_percentage", 0);
                            }
                          }}
                        />
                      </div>
                      <div className="w-5 h-5 flex items-center justify-center group/buttonHover hover:bg-[#f0f0f0]">
                        <Tooltip
                          title={_t(
                            `% -- Add the % amount that the item should be marked up. ${currentCurrency} -- Add the ${currentCurrency} amount that should be charged for the item.`
                          )}
                          rootClassName="!max-w-[265px]"
                        >
                          <FontAwesomeIcon
                            className="w-3.5 h-3.5 !text-primary-900/80 group-hover/buttonHover:!text-primary-900"
                            icon="fa-regular fa-circle-info"
                          />
                        </Tooltip>
                      </div>
                    </div>
                    <div className="sm:w-40 w-28">
                      <InputField
                        className="!p-0 !pl-1.5 text-success field-text-13 !h-[22px] !text-[#008000] !font-semibold placeholder:font-normal !text-right"
                        placeholder={
                          markup === "markup_percent"
                            ? _t("Item Markup") + " %"
                            : _t("Total Sales Price")
                        }
                        onPaste={handlePaste}
                        onChange={(e) => {
                          const inputVal = e.target.value;
                          if (inputVal === "") {
                            setFieldValue("markup", "");
                            return;
                          }

                          if (!floatNumberRegex.test(inputVal)) {
                            return;
                          }

                          if (values?.is_markup_percentage) {
                            if (inputVal.length > 3) {
                              return;
                            }
                            if (!wholeNumberRegex.test(inputVal)) {
                              return;
                            }
                          }

                          const cleanedInput = inputVal
                            .split(".")[0]
                            .replace("-", "");
                          if (cleanedInput.length > 10) {
                            return;
                          }

                          setFieldValue(
                            "markup",
                            markup === "markup_percent"
                              ? Number(inputVal)
                              : inputVal
                          );
                        }}
                        value={values?.markup ?? ""}
                        labelPlacement="left"
                        type="text"
                        disabled={
                          values.is_progressive_bill_item === 1 || isViewOnly
                        }
                        onKeyDown={(
                          event: React.KeyboardEvent<HTMLInputElement>
                        ) => {
                          if (event.key === "Enter") {
                            if (Number(event?.currentTarget?.value) === 0) {
                              setFieldValue("markup", "");
                            } else {
                              setFieldValue(
                                "markup",
                                event?.currentTarget?.value
                              );
                            }
                          } else {
                            return onKeyDownNumber(event, {
                              decimalDigits:
                                markup === "markup_percent" ? 0 : 2,
                              integerDigits:
                                markup === "markup_percent" ? 3 : 8,
                              allowNegative: false,
                            });
                          }
                        }}
                      />
                    </div>
                  </li>
                  <li className="flex items-center justify-between">
                    <Typography className="text-13 block text-primary-900">
                      {_t("Markup")}
                    </Typography>
                    <div className="sm:w-[260px] w-32 flex justify-end items-center">
                      <Typography
                        className="!text-[#008000] text-13 font-medium"
                        disabled={isViewOnly}
                      >
                        {markup === "markup_percent"
                          ? values?.markup_amount === ""
                            ? `${
                                formatter(formatAmount("0.00"))
                                  .value_with_symbol
                              }`
                            : `${
                                formatter(
                                  formatAmount(
                                    Number(values?.markup_amount || 0).toFixed(
                                      2
                                    )
                                  )
                                ).value_with_symbol
                              }`
                          : values?.markup_amount === ""
                          ? "0.00%"
                          : `${Number(values?.markup_amount || 0)?.toFixed(
                              2
                            )}%`}
                      </Typography>
                    </div>
                  </li>
                </ul>
                <ul className="my-3.5 border-t border-dashed border-[#ddd] relative">
                  <li className="w-[18px] h-[18px] flex items-center justify-center rounded-full bg-white shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                    <FontAwesomeIcon
                      className="w-3 h-3 text-primary-900 dark:text-white"
                      icon="fa-regular fa-equals"
                    />
                  </li>
                </ul>
                <ul className="border border-gray-300 dark:border-none dark:shadow-[0_4px_24px_0] dark:shadow-dark-900 rounded-md py-2 px-[15px] bg-white">
                  <li className="flex items-center justify-between">
                    <Typography className="text-13 block text-primary-900 font-semibold">
                      {values.is_progressive_bill_item !== 1
                        ? _t("Total Revenue")
                        : _t("Billed Total")}
                    </Typography>
                    <div className="sm:w-[260px] w-32 flex justify-end items-center">
                      {values.is_progressive_bill_item !== 1 && (
                        <Typography
                          className="!text-red-600 text-13 font-semibold"
                          disabled={isViewOnly}
                        >
                          {mainTotal === ""
                            ? `${
                                formatter(formatAmount("0.00"))
                                  .value_with_symbol
                              }`
                            : `${
                                formatter(
                                  formatAmount(
                                    Number(mainTotal || 0).toFixed(2)
                                  )
                                ).value_with_symbol
                              }`}
                        </Typography>
                      )}
                      {values.is_progressive_bill_item === 1 && (
                        <Typography
                          className="!text-red-600 text-13 font-semibold"
                          disabled={isViewOnly}
                        >
                          {`${progressiveBillingTotal} (Billed ${Number(
                            values.progressive_bill_percentage || 0
                          ).toFixed(2)}%)`}
                        </Typography>
                      )}
                    </div>
                  </li>
                </ul>
              </SidebarCardBorder>
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <TextAreaField
                    required={false}
                    label={_t("Description")}
                    labelPlacement="top"
                    placeholder={_t(
                      "Notes added here are transferred to other records (such as an Estimate or PO) and can be optionally made visible to the recipient when submitted."
                    )}
                    disabled={isViewOnly}
                    value={HTMLEntities.decode(
                      sanitizeString(values?.description)
                    )}
                    onChange={(e) => {
                      setFieldValue("description", e.target.value);
                    }}
                  />
                </div>
                <div className="w-full">
                  <TextAreaField
                    label={_t("Internal Notes")}
                    labelPlacement="top"
                    placeholder={_t(
                      "Notes added here are transferred to other records (such as an Estimate or PO) and are not visible to the recipient."
                    )}
                    disabled={isViewOnly}
                    value={HTMLEntities.decode(
                      sanitizeString(values?.internal_notes)
                    )}
                    onChange={(e) => {
                      setFieldValue("internal_notes", e.target.value);
                    }}
                  />
                </div>
                <CheckBox
                  className="gap-1.5 text-primary-900 w-fit"
                  checked={!!values?.apply_global_tax}
                  onChange={(event) => {
                    const valueToSet: number = event.target.checked ? 1 : 0;
                    setFieldValue("apply_global_tax", valueToSet);
                  }}
                  disabled={isViewOnly}
                >
                  {_t("Collect Tax on this Item?")}
                </CheckBox>
                {values.item_type && isAddInvoiceItem ? (
                  <CheckBox
                    className="gap-1.5 text-primary-900 w-fit"
                    checked={!!values?.item_on_database}
                    onChange={(event) => {
                      const valueToSet: number = event.target.checked ? 1 : 0;
                      setFieldValue("item_on_database", valueToSet);
                      setFieldValue("add_item_to_database", valueToSet);
                    }}
                    disabled={isViewOnly}
                  >
                    {_t(`Save this item into my ${saveItemKey} Items list?`)}
                  </CheckBox>
                ) : (
                  <></>
                )}
              </SidebarCardBorder>
            </div>
          </div>
          <div className="sidebar-footer flex items-center justify-center w-full gap-4 px-4 pt-4">
            <PrimaryButton
              htmlType="submit"
              buttonText={_t("Save & Close")}
              disabled={
                (isSubmitting && submitAction === "save_n_close") || isViewOnly
              }
              isLoading={isSubmitting && submitAction === "save_n_close"}
              onClick={() => handleSaveItem("save_n_close")}
              name="save_n_close"
            />
            {isAddInvoiceItem && (
              <PrimaryButton
                type="primary"
                buttonText={_t("Save & Add Another Item")}
                className="w-full justify-center primary-btn"
                htmlType="submit"
                disabled={
                  (isSubmitting && submitAction === "save_n_add_another") ||
                  isViewOnly
                }
                loading={isSubmitting && submitAction === "save_n_add_another"}
                onClick={() => handleSaveItem("save_n_add_another")}
                name="save_n_add_another"
              />
            )}
          </div>
        </Form>
      </Drawer>

      {isOpenSelectAssignedTo && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectAssignedTo}
          closeDrawer={() => {
            setIsOpenSelectAssignedTo(false);
          }}
          singleSelecte={true}
          setCustomer={(data) => {
            if (data.length) {
              if (data[0].contact_id == "0") {
                setFieldValue("assigned_to_contact_id", data[0].contact_id);
              } else {
                setFieldValue("assigned_to_contact_id", data[0].contact_id);
              }
              setFieldValue("assigned_to", data[0].user_id);
              setFieldValue("assignee_name", data[0].display_name);
              setFieldValue("assigned_to_contact_id", data[0].contact_id);
              setFieldValue("assigned_to_image", data[0].contact_id);
              setFieldValue("user_image", data[0]?.image);

              setFieldValue(
                "assignee_type",
                data[0].type ||
                  getDirectaryIdByKey(data[0].type_key as CustomerTabs, gConfig)
              );
              setFieldValue("type_key", data[0].type_key);
              setFieldValue("type_name", data[0].type_name);
            } else {
              setFieldValue("assigned_to", 0);
              setFieldValue("assignee_name", "");
            }
          }}
          options={[
            defaultConfig.employee_key,
            "my_crew",
            defaultConfig.customer_key,
            defaultConfig.misc_contact_key,
            defaultConfig.contractor_key,
            defaultConfig.vendor_key,
            "by_service",
            "my_project",
          ]}
          selectedCustomer={assignedTo as TselectedContactSendMail[]}
          groupCheckBox={true}
          projectId={details?.project_id as number}
          activeTab={defaultConfig.contractor_key}
        />
      )}

      {confirmSaveData.rid > 0 && (
        <ConfirmModal
          isOpen={confirmSaveData?.rid > 0}
          modaltitle={_t("This Item Already Exists")}
          description={confirmSaveData?.message || ""}
          modalIcon="fa-regular fa-triangle-exclamation"
          yesButtonLabel={_t("Use Existing")}
          noButtonLabel={_t("Rename")}
          onAccept={() => {
            setIsExistingLoading(true);
            handleSubmit();
          }}
          isLoading={isExistingLoading && isExistingLoading}
          onDecline={() => {
            setConfirmSaveData({ rid: 0 });
          }}
          onCloseModal={() => {
            setConfirmSaveData({ rid: 0 });
          }}
        />
      )}

      {newTypeName.trim() && (
        <ConfirmModal
          isOpen={Boolean(newTypeName.trim())}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${newTypeName}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={() => setNewTypeName("")}
          onAccept={async () => {
            if (!isAddingCustomData && newTypeName) {
              setIsAddingCustomData(true);
              const response: IUnitAddResponse = await addUnit({
                name: newTypeName,
              });
              if (response.success && response.data) {
                setUnitData([{ name: newTypeName }, ...unitData]);
                setFieldValue("unit", newTypeName);
                setNewTypeName("");
              } else {
                notification.error({
                  description: response.message,
                });
              }
              setIsAddingCustomData(false);
            }
          }}
          onDecline={() => setNewTypeName("")}
        />
      )}

      {isOpenContactDetails && (
        <ContactDetails
          isOpenContact={isOpenContactDetails}
          contactId={values?.assigned_to || ""}
          onCloseModal={() => {
            setIsOpenContactDetails(false);
          }}
          onEmailClick={(data) => {
            setSelectedData(data);
            setIsSendEmailSidebarOpen(true);
          }}
          readOnly={isViewOnly}
          additional_contact_id={values?.assigned_to_contact_id}
        />
      )}
      <InvoiceSendEmail
        isOpen={isSendEmailSidebarOpen}
        options={[
          defaultConfig.employee_key,
          "my_crew",
          defaultConfig.customer_key,
          defaultConfig.lead_key,
          defaultConfig.contractor_key,
          defaultConfig.vendor_key,
          defaultConfig.misc_contact_key,
          "by_service",
        ]}
        onSendResponse={() => {
          setSelectedData({});
        }}
        onClose={() => {
          setSelectedData({});
          setIsSendEmailSidebarOpen(false);
        }}
        groupCheckBox={true}
        projectId={Number(details?.project_id)}
        selectedCustomer={
          selectedData?.user_id
            ? ([selectedData] as TselectedContactSendMail[])
            : []
        }
        app_access={false}
      />
    </>
  );
};

export default InvoiceItem;
