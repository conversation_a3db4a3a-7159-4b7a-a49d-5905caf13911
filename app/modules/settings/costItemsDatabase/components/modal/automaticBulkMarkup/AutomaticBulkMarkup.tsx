import { useEffect, useState } from "react";
import { Form } from "@remix-run/react";
// Atoms
import { Button } from "~/shared/components/atoms/button";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Typography } from "~/shared/components/atoms/typography";
import { Radio } from "~/shared/components/atoms/radioButton";
import { RadioGroup } from "~/shared/components/atoms/radioGroup";
import { Image } from "~/shared/components/atoms/image";
// Molecules
import { CommonModal } from "~/shared/components/molecules/commonModal";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
// Shared
import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";
import { onKeyDownNumber } from "~/shared/utils/helper/common";

// Formik
import { useFormik } from "formik";

// Other
import { useTranslation } from "~/hook";
import { cidbItemRoutes } from "~/route-services/cidb-item.routes";
import { RadioChangeEvent } from "antd";
const AutomaticBulkMarkup = ({
  isOpen,
  onClose,
  module,
  getMarkupItems,
}: IAutomaticBulkMarkupProps) => {
  const { _t } = useTranslation();

  const MARKUP_BUTTON_TAB = [
    {
      label: (
        <div className="flex gap-1.5 items-center font-medium">
          <FontAwesomeIcon icon="fa-regular fa-clipboard-check" />
          {_t("All Items")}
        </div>
      ),
      // value: false,
      value: "all_items",
      key: 0,
    },
    {
      label: (
        <div className="flex gap-1.5 items-center font-medium">
          <FontAwesomeIcon icon="fa-regular fa-ballot-check" />
          {_t("Selected Items")}
        </div>
      ),
      // value: true,
      value: "selected_items",
      key: 1,
    },
  ];
  // const [markup, setMarkup] = useState<string>("0");

  const [loading, setLoading] = useState(false);

  const formik = useFormik({
    initialValues: {
      markup_tab: "all_items" as
        | (typeof MARKUP_BUTTON_TAB)[0]["value"]
        | (typeof MARKUP_BUTTON_TAB)[1]["value"],
      markup_option: "",
      increase_no_markup: "",
      increase_all: "",
      increase_conditional: "",
      increase_conditional_max: "",
      decrease_all: "",
      decrease_conditional: "",
      decrease_conditional_min: "",
      item_ids: {} as IGetMarkupItemsIds["ids"],
    },
    onSubmit: async (values) => {
      let formData = {
        markup_option: values.markup_option,
        markup: "",
        markup_limit: "",
        database_item: module?.module_id,
        ids: undefined as IGetMarkupItemsIds["ids"] | undefined,
      };

      if (
        formik.values.markup_tab === "selected_items" &&
        Object.keys(formik.values.item_ids).length
      ) {
        formData = {
          ...formData,
          ids: formik.values.item_ids,
        };
      }

      switch (formData.markup_option) {
        case "increase_no_markup":
          formData = {
            ...formData,
            markup: values.increase_no_markup || "",
          };
          break;
        case "increase_all":
          formData = {
            ...formData,
            markup: values.increase_all || "",
          };
          break;
        case "increase_conditional":
          formData = {
            ...formData,
            markup: values.increase_conditional || "",
            markup_limit: values.increase_conditional_max || "",
          };
          if (!formData.markup_limit) {
            notification.error({
              description: "Missing Markup Limit.",
            });
            return;
          }
          break;
        case "decrease_all":
          formData = {
            ...formData,
            markup: values.decrease_all || "",
          };
          break;
        case "decrease_conditional":
          formData = {
            ...formData,
            markup: values.decrease_conditional || "",
            markup_limit: values.decrease_conditional_min || "",
          };
          if (!formData.markup_limit) {
            notification.error({
              description: "Missing Markup Limit.",
            });
            return;
          }
          break;
        case "reset":
          break;
        default:
          notification.error({
            description: "Please select one of the options.",
          });
          return;
      }
      if (formData.markup_option !== "reset" && !formData.markup) {
        notification.error({
          description: "Missing Markup Percentage.",
        });
        return;
      }

      setLoading(true);
      try {
        const params = await getWebWorkerApiParams({
          otherParams: formData,
        });

        const response = (await webWorkerApi({
          url: cidbItemRoutes.automatic_bulk_markup,
          method: "post",
          data: params,
        })) as IApiCallResponse;

        if (response.success) {
          formik.resetForm();
          onClose(true);
        } else {
          notification.error({
            description: response.message,
          });
        }
      } catch (error) {
        notification.error({
          description: "Something went wrong!",
        });
        console.error(
          `\n File: #DetailsTopBar.tsx -> Line: #84 ->  `,
          (error as Error).message
        );
      }
      setLoading(false);
    },
  });

  useEffect(() => {
    if (isOpen) {
      const markupItems = getMarkupItems();
      if (markupItems) {
        formik.setValues((prev) => ({
          ...prev,
          item_ids: markupItems.ids,
          markup_tab: "selected_items",
        }));
      } else {
        formik.setFieldValue("markup_tab", "all_items");
      }
    }
  }, [isOpen]);
  const selectedItemsMarkup = () => {
    return (
      <div className="flex items-center">
        <Radio name="markup_option" value="increase_all"></Radio>
        <Typography className="text-primary-900 text-sm">
          {_t("Add a")}
        </Typography>
        <div className="w-[60px] px-1">
          <InputNumberField
            label=""
            placeholder=""
            className="!text-primary-900 bg-[#f4f5f6] placeholder:!text-[#bdbdbd] border border-[#DFE2E7] rounded-[3px]"
            labelPlacement="left"
            maxLength={3}
            name="increase_all"
            value={formik.values.increase_all}
            onChange={(value) => {
              formik.setValues({
                ...formik.values,
                "increase_all" : value,
                markup_option: "increase_all",
              });
            }}
            onKeyDown={(e) =>
              onKeyDownNumber(e, {
                decimalDigits: 0,
                integerDigits: 3,
                allowNegative: false,
              })
            }
          />
        </div>
        <Typography className="text-primary-900 text-sm">
          {_t(
            `% markup to ${
              formik.values.markup_tab === "selected_items"
                ? "Selected items"
                : "all items"
            } with or without an existing markup`
          )}
        </Typography>
      </div>
    );
  };

  const stopPastingOfDot = (e: React.ClipboardEvent<HTMLInputElement>) => {
    // Get the text data from the clipboard when user pastes
    const pastedText = e.clipboardData.getData("text");

    // Get a reference to the input element
    const input = e.target as HTMLInputElement;

    // Get the current input value
    const currentValue = input.value;

    // Get the current selection range (cursor position or highlighted text)
    const start = input.selectionStart ?? 0;
    const end = input.selectionEnd ?? 0;

    const regexPattern = new RegExp(`^(?!\\.)[0-9]{0,3}?$`);

    const newValueWithKey =
      currentValue.slice(0, start) + pastedText + currentValue.slice(end);

    if (!regexPattern.test(newValueWithKey)) {
      e.preventDefault(); // Prevent the invalid input.
    }
  };

  return (
    <CommonModal
      isOpen={isOpen}
      widthSize="750px"
      onCloseModal={() => {
        formik.resetForm();
        onClose();
      }}
      modalBodyClass="p-0"
      header={{
        title: _t("Bulk Markup"),
        icon: (
          <FontAwesomeIcon className="w-4 h-4" icon="fa-solid fa-users-rays" />
        ),
        closeIcon: true,
      }}
    >
      {module ? (
        <Form
          className="py-4"
          onSubmit={formik.handleSubmit}
          noValidate
          method="post"
        >
          <div className="modal-body grid gap-3.5 max-h-[calc(100vh-200px)] px-4 pb-4">
            {/* Note for devleoper: uncomment this code when you start developing */}
            <div className="w-fit p-1 bg-[#EBF1F9] rounded dark:bg-dark-800">
              <ListTabButton
                value={formik.values.markup_tab}
                options={MARKUP_BUTTON_TAB}
                className="sm:min-w-[100px] min-w-fit sm:px-1.5 py-0 !text-[#868D8D] px-2 h-6 !border-transparent bg-[#EBF1F9]"
                activeclassName="active:bg-[#ffffff] !text-primary-900 !rounded-md"
                onChange={(e: RadioChangeEvent) => {
                  formik.resetForm();
                  switch (e.target.value) {
                    case "selected_items":
                      const markupItems = getMarkupItems();
                      if (markupItems) {
                        formik.setValues((prev) => ({
                          ...prev,
                          item_ids: markupItems.ids,
                          markup_tab: e.target.value,
                        }));
                      } else {
                        formik.setFieldValue("markup_tab", e.target.value);
                      }
                      break;
                    case "all_items":
                      formik.setFieldValue("markup_tab", e.target.value);
                      break;
                  }
                }}
              />
            </div>
            <div className="flex flex-col gap-1 max-h-[calc(100vh-260px)] overflow-auto">
              {formik.values.markup_tab === "all_items" ||
              (formik.values.markup_tab === "selected_items" &&
                Object.keys(formik.values.item_ids).length) ? (
                <RadioGroup
                  name="markup_option"
                  className="flex flex-col gap-2"
                  value={formik.values.markup_option}
                  onChange={formik.handleChange}
                >
                  {formik.values.markup_tab === "selected_items" &&
                    selectedItemsMarkup()}
                  <div className="flex items-center">
                    <Radio
                      name="markup_option"
                      value="increase_no_markup"
                    ></Radio>
                    <Typography className="text-primary-900 text-sm">
                      {_t("Add a")}
                    </Typography>
                    <div className="w-[60px] px-1">
                      <InputNumberField
                        label=""
                        placeholder=""
                        className="!text-primary-900 bg-[#f4f5f6] placeholder:!text-[#bdbdbd] border border-[#DFE2E7] rounded-[3px]"
                        labelPlacement="left"
                        maxLength={3}
                        name="increase_no_markup"
                        value={formik.values.increase_no_markup}
                        onChange={(value) =>{
                          formik.setValues({
                            ...formik.values,
                            "increase_no_markup" : value,
                            markup_option: "increase_no_markup",
                          });
                        }
                        }
                        onKeyDown={(e) =>
                          onKeyDownNumber(e, {
                            integerDigits: 3,
                            decimalDigits: 0,
                            allowNegative: false,
                          })
                        }
                        onPaste={stopPastingOfDot}
                      />
                    </div>
                    <Typography className="text-primary-900 text-sm">
                      {_t(
                        `% markup to ${
                          formik.values.markup_tab === "selected_items"
                            ? "Selected items"
                            : "all items"
                        } with a 0% (blank) markup`
                      )}
                    </Typography>
                  </div>
                  {formik.values.markup_tab !== "selected_items" &&
                    selectedItemsMarkup()}
                  <div className="flex items-center">
                    <Radio
                      name="markup_option"
                      value="increase_conditional"
                    ></Radio>
                    <Typography className="text-primary-900 text-sm">
                      {_t(
                        `Increase the markup of ${
                          formik.values.markup_tab === "selected_items"
                            ? "Selected items"
                            : "all items"
                        } by`
                      )}
                    </Typography>
                    <div className="w-[60px] px-1">
                      <InputNumberField
                        label=""
                        placeholder=""
                        className="!text-primary-900 bg-[#f4f5f6] placeholder:!text-[#bdbdbd] border border-[#DFE2E7] rounded-[3px]"
                        labelPlacement="left"
                        maxLength={3}
                        name="increase_conditional"
                        value={formik.values.increase_conditional}
                        onChange={(value) => {
                          formik.setValues({
                            ...formik.values,
                            "increase_conditional" : value,
                            markup_option: "increase_conditional",
                          });
                        }
                        }
                        onKeyDown={(e) =>
                          onKeyDownNumber(e, {
                            decimalDigits: 0,
                            integerDigits: 3,
                            allowNegative: false,
                          })
                        }
                        onPaste={stopPastingOfDot}
                      />
                    </div>
                    <Typography className="text-primary-900 text-sm">
                      {_t("% but maintain a maximum of")}
                    </Typography>
                    <div className="w-[60px] px-1">
                      <InputNumberField
                        label=""
                        placeholder=""
                        className="!text-primary-900 bg-[#f4f5f6] placeholder:!text-[#bdbdbd] border border-[#DFE2E7] rounded-[3px]"
                        labelPlacement="left"
                        maxLength={3}
                        name="increase_conditional_max"
                        value={formik.values.increase_conditional_max}
                        onChange={(value) =>{
                          formik.setValues({
                            ...formik.values,
                            "increase_conditional_max" : value,
                            markup_option: "increase_conditional",
                          });
                          
                        }
                        }
                        onKeyDown={(e) =>
                          onKeyDownNumber(e, {
                            decimalDigits: 0,
                            integerDigits: 3,
                            allowNegative: false,
                          })
                        }
                        onPaste={stopPastingOfDot}
                      />
                    </div>
                    <Typography className="text-primary-900 text-sm">
                      {_t("%")}
                    </Typography>
                  </div>
                  <div className="flex items-center">
                    <Radio name="markup_option" value="reset"></Radio>
                    <Typography className="text-primary-900 text-sm">
                      {_t(
                        `Reset the markup for ${
                          formik.values.markup_tab === "selected_items"
                            ? "Selected items"
                            : "all items"
                        } to 0%`
                      )}
                    </Typography>
                  </div>
                  <div className="flex items-center">
                    <Radio name="markup_option" value="decrease_all"></Radio>
                    <Typography className="text-primary-900 text-sm">
                      {_t(
                        `Reduce the markup of ${
                          formik.values.markup_tab === "selected_items"
                            ? "Selected items"
                            : "all items"
                        } by`
                      )}
                    </Typography>
                    <div className="w-[60px] px-1">
                      <InputNumberField
                        label=""
                        placeholder=""
                        className="!text-primary-900 bg-[#f4f5f6] placeholder:!text-[#bdbdbd] border border-[#DFE2E7] rounded-[3px]"
                        labelPlacement="left"
                        maxLength={3}
                        name="decrease_all"
                        value={formik.values.decrease_all}
                        onChange={(value) =>{
                          formik.setValues({
                            ...formik.values,
                            "decrease_all" : value,
                            markup_option: "decrease_all",
                          });
                        }
                        }
                        onKeyDown={(e) =>
                          onKeyDownNumber(e, {
                            decimalDigits: 0,
                            integerDigits: 3,
                            allowNegative: false,
                          })
                        }
                        onPaste={stopPastingOfDot}
                      />
                    </div>
                    <Typography className="text-primary-900 text-sm">
                      {_t("%")}
                    </Typography>
                  </div>
                  <div className="flex items-center">
                    <Radio
                      name="markup_option"
                      value="decrease_conditional"
                    ></Radio>
                    <Typography className="text-primary-900 text-sm">
                      {_t(
                        `Reduce the markup of ${
                          formik.values.markup_tab === "selected_items"
                            ? "Selected items"
                            : "all items"
                        } by`
                      )}
                    </Typography>
                    <div className="w-[60px] px-1">
                      <InputNumberField
                        label=""
                        placeholder=""
                        className="!text-primary-900 bg-[#f4f5f6] placeholder:!text-[#bdbdbd] border border-[#DFE2E7] rounded-[3px]"
                        labelPlacement="left"
                        maxLength={3}
                        name="decrease_conditional"
                        value={formik.values.decrease_conditional}
                        onChange={(value) => {
                          formik.setValues({
                            ...formik.values,
                            "decrease_conditional" : value,
                            markup_option: "decrease_conditional",
                          });
                        }
                        }
                        onKeyDown={(e) =>
                          onKeyDownNumber(e, {
                            decimalDigits: 0,
                            integerDigits: 3,
                            allowNegative: false,
                          })
                        }
                        onPaste={stopPastingOfDot}
                      />
                    </div>
                    <Typography className="text-primary-900 text-sm">
                      {_t("% but maintain a maximum of")}
                    </Typography>
                    <div className="w-[60px] px-1">
                      <InputNumberField
                        label=""
                        placeholder=""
                        className="!text-primary-900 bg-[#f4f5f6] placeholder:!text-[#bdbdbd] border border-[#DFE2E7] rounded-[3px]"
                        labelPlacement="left"
                        maxLength={3}
                        name="decrease_conditional_min"
                        value={formik.values.decrease_conditional_min}
                        onChange={(value) =>{
                          formik.setValues({
                            ...formik.values,
                            "decrease_conditional_min" : value,
                            markup_option: "decrease_conditional",
                          });
                        }
                        }
                        onKeyDown={(e) =>
                          onKeyDownNumber(e, {
                            decimalDigits: 0,
                            integerDigits: 3,
                            allowNegative: false,
                          })
                        }
                        onPaste={stopPastingOfDot}
                      />
                    </div>
                    <Typography className="text-primary-900 text-sm">
                      {_t("%")}
                    </Typography>
                  </div>
                </RadioGroup>
              ) : (
                <div className="flex items-center justify-center h-full min-h-44">
                  <div className="text-center">
                    <Image
                      src={`${window.ENV.CDN_URL}assets/images/no-items-bulk-markup.svg`}
                      preview={false}
                      className={`mx-auto `}
                    />
                    <Typography className="text-black text-sm font-semibold block mt-2.5">
                      First Select Items from list to <br /> apply the Bulk
                      Markup
                    </Typography>
                  </div>
                </div>
              )}
            </div>
          </div>
          <div className="modal-footer px-4">
            <div className="gap-2.5 justify-center flex">
              {formik.values.markup_tab === "selected_items" &&
              !Object.keys(formik.values.item_ids).length ? (
                <PrimaryButton
                  onClick={() => {
                    formik.resetForm();
                    onClose();
                  }}
                  className="min-w-20 w-auto"
                  buttonText={_t("Ok")}
                />
              ) : (
                <PrimaryButton
                  htmlType="submit"
                  className="min-w-20 w-auto"
                  disabled={loading}
                  isLoading={loading}
                  buttonText={_t("Apply")}
                />
              )}
            </div>
          </div>
        </Form>
      ) : (
        <>Opps! Something went wrong!</>
      )}
    </CommonModal>
  );
};

export default AutomaticBulkMarkup;
