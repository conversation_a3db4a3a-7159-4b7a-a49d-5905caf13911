// Atoms
import { Typography } from "~/shared/components/atoms/typography";
import { TypographyLink } from "~/shared/components/atoms/typographyLink";
import { SkeletonInput } from "~/shared/components/atoms/skeleton";
// Molecules
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
// organisms
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";
// Other
import { useTranslation } from "~/hook";
import SummaryInfoCardList from "./SummaryInfoCardList";
import { routes } from "~/route-services/routes";
import { useState } from "react";
import { Number, sanitizeString } from "~/helpers/helper";
import { defaultConfig } from "~/data";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { useParams } from "@remix-run/react";

interface ICustomerCardProps {
  details: IProjectDetails;
  isLoading: boolean;
}

const CustomerCard = ({ details, isLoading }: ICustomerCardProps) => {
  const { _t } = useTranslation();
  const { id } = useParams();
  const [isContactDetails, setIsContactDetails] = useState<boolean>(false);

  return (
    <>
      <SummaryInfoCardList
        title={_t("Customer")}
        containerClassName="h-full !shadow-none"
        iconProps={{
          icon: "fa-solid fa-user-group",
          containerClassName:
            "bg-[linear-gradient(180deg,#FFB2981a_0%,#FA6D3D1a_100%)] !h-10 !w-10 !min-w-10",
          id: "customer_summary_icon",
          colors: ["#FFB298", "#FA6D3D"],
        }}
        iconClassName="w-6 h-6"
        contantWidth="w-[calc(100%-47px)]"
        children={
          isLoading ? (
            <SkeletonInput className="!w-[150px] !h-[15px]" />
          ) : (
            <div className="flex items-center justify-between gap-2">
              {details.customer_name !== "" ? (
                <TypographyLink
                  href={`${routes.MANAGE_DIRECTORY.url}/${details.customer_id}`}
                  target="_blank"
                  className="w-[calc(100%-62px)]"
                >
                  <div className="flex items-center gap-2 pl-px">
                    <AvatarProfile
                      user={{
                        name: details?.customer_name?.trim(),
                        image:
                          !details?.customer_contact_id ||
                          details?.customer_contact_id == 0
                            ? details.cust_image
                            : "",
                      }}
                      iconClassName="text-[11px] font-semibold"
                    />

                    <Typography className="font-semibold !text-primary-900 dark:text-white/90 !text-sm hover:!text-deep-orange-500 word-wrap-break max-w-[calc(100%-32px)]">
                      {HTMLEntities.decode(
                        sanitizeString(details.customer_name)
                      )}
                    </Typography>
                  </div>
                </TypographyLink>
              ) : (
                <Typography className="text-primary-900 text-sm font-semibold hover:!text-deep-orange-500">
                  -
                </Typography>
              )}
              {details.customer_id && details.customer_id !== "" && (
                <div className="flex items-center gap-1">
                  <ContactDetailsButton
                    onClick={() => {
                      setIsContactDetails(true);
                    }}
                  />
                  <DirectoryFieldRedirectionIcon
                    directoryId={details?.customer_id.toString()}
                    directoryTypeKey={defaultConfig.customer_key}
                  />
                </div>
              )}
            </div>
          )
        }
      />
      {isContactDetails && (
        <ContactDetailsModal
          isOpenContact={isContactDetails}
          onCloseModal={() => setIsContactDetails(false)}
          contactId={Number(details.customer_id)}
          additional_contact_id={details.customer_contact_id}
          sendEmailDrawer={{ projectId: Number(id) }}
          isShowMap={true}
        />
      )}
    </>
  );
};

export default CustomerCard;
