// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { LightGalleryModel } from "~/shared/components/atoms/lightGalleryModel";
// Molecules
import { GalleryFilePhotos } from "~/shared/components/molecules/galleryFilePhotos";
import { LoadingGallery } from "~/shared/components/molecules/loadingGallery";
import { VirtuosoGridCommon } from "~/shared/components/molecules/virtuosoGrid";
// Other
import { getGConfig, getGSettings, useGModules } from "~/zustand";
import { useTranslation } from "~/hook";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { useAppProDispatch, useAppProSelector } from "../../../../redux/store";
import { forwardRef, useMemo, useState } from "react";
import { DropZone } from "~/modules/document/fileAndPhoto/components/dropZone";
import { GoogleDrivePicker } from "~/modules/document/fileAndPhoto/components/googleDrivePicker";
import DropboxPicker from "~/modules/document/fileAndPhoto/components/dropboxPicker/DropboxPicker";
import { updateFileCount } from "../../../../redux/slices/projectFilePhotoLeftSlice";
import {
  setImageDataRedux,
  setSendEmailDrawerOpenRedux,
  toggleSelectedFiles,
  callAgainFileList,
} from "../../../../redux/slices/projectFilePhotoRightSlice";
// import { SendEmailDrawer } from "~/shared/components/molecules/sendEmailDrawer";
// import {
//   setActiveField,
//   setSelectedContact,
// } from "~/redux/slices/sendEmailSlice";
import { defaultConfig } from "~/data";
import { sendCommonEmailApi } from "~/redux/action/sendEmailAction";
import { acceptedFileTypes } from "~/modules/document/fileAndPhoto/utils/allowedFileExtension";
import UploadFilesInNoData from "./UploadFilesInNoData.client";
import { type Accept, useDropzone } from "react-dropzone";
import { getFileExtension } from "~/modules/document/fileAndPhoto/utils/googleDrivePicker";
import { addFile, uploadFile } from "~/redux/action/fileAttachmentAction";
import axios from "axios";
import isEmpty from "lodash/isEmpty";
import FileSelect from "~/shared/components/organisms/fileSelect/FileSelect";
import { useParams } from "@remix-run/react";
import { filterAttachmentFiles } from "~/shared/utils/helper/common";
import { useProjectDetail } from "../../../../hook/useProjectsDetails";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import AddOrEditFile from "~/modules/document/fileAndPhoto/sidebar/AddOrEditFile";
import { getFolderList } from "../../../../redux/action/projectFilePhotoLeftAction";
import { getFolderListAfterFileAdd } from "../utils/getFolderListAfterFileAdd";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import Typography from "~/shared/components/atoms/typography/Typography";

const gridComponents = {
  List: forwardRef<HTMLDivElement, IFilePhotoVirtusoListProps>(
    ({ style, children, ...props }, ref) => (
      <LightGalleryModel
        zoom={true}
        thumbnail={true}
        backdropDuration={150}
        showZoomInOutIcons={true}
        actualSize={false}
        mode="lg-slide"
        alignThumbnails="left"
        mousewheel={true}
      >
        <div
          ref={ref}
          className="flex gap-[15px] flex-wrap"
          style={{
            ...style,
          }}
        >
          {children}
        </div>
      </LightGalleryModel>
    )
  ),
  Item: forwardRef<HTMLDivElement, IFilePhotoVirtusoListProps>(
    ({ children, ...props }) => (
      <div {...props} className="virtuoso-grid file-folder-list-view">
        {children}
      </div>
    )
  ),
};

const FilePhotoRightSide = ({
  // fileList,
  loadMore,
  hasMore = true,
  infiniteScrollHideLoadingComponent = true,
  isInfiniteScrollLoading,
  changeAddOrEditFileOpen,
  changeMarkupModelOpen,
  changeConfirmModelOpen,
  markupedFileData,
  changeSendEmailDrawerOpen,
}: FilesDefaultViewProps) => {
  const { _t } = useTranslation();
  const { id }: RouteParams = useParams();
  const { module_id, module_key, module_access }: GConfig = getGConfig();
  const { checkModuleAccessByKey } = useGModules();
  const { checkGlobalModulePermissionByKey } = useGlobalModule();
  const {
    isfileLoading,
    fileList,
    imageData,
    editView,
    isConfirmModalOpen,
    isMarkupModalOpen,
    // addOrEditFileOpen,
    sendEmailDrawerOpen,
    selectedFiles,
  }: IFilePhotoRight = useAppProSelector((state) => state.filePhotoFileList);
  const { selectedFolder, parentFolderName }: IFilePhotoLeftInitialState =
    useAppProSelector((state) => state.filePhotoFolderList);
  const selectedAllFiles: boolean = useAppProSelector(
    (state) => state.filePhotoFileList.selectedAllFiles
  );
  const { date_format, image_resolution, save_a_copy_of_sent_pdf }: GSettings =
    getGSettings();
  const fileSupportAccess = checkModuleAccessByKey(CFConfig.file_support_key);
  const markupFileData: IMarkupData = useAppProSelector(
    (state) => state.fileEdit?.markupFileData || {}
  );
  const { isReadOnly, handleUpdateField } = useProjectDetail();
  const [isLoadingWhenNoData, setIsLoadingWhenNoData] =
    useState<boolean>(false);
  const [addOrEditFileOpen, setAddOrEditFileOpen] = useState<boolean>(false);
  const uploadFileList: IUploadFileList[] = useAppProSelector(
    (state) => state.filePhotoFileList.uploadFileList
  );
  const [selectedTab, setSelectedTab] = useState<
    TFileAttachmentTabsValues | ""
  >("");
  const [load, isLoad] = useState(false);
  const [selectedFile, setSelectedFile] = useState<IFile[]>([]);
  const dispatch = useAppProDispatch();

  const handleSelectImage = (
    selectedFile: FilePhotoRightDetail,
    is_checked: boolean
  ) => {
    dispatch(
      toggleSelectedFiles({
        selectedFile,
        is_file_checked: !is_checked,
        currentView: "folder",
      })
    );
  };
  const getRootFolderList = async () => {
    const data = {
      type: 2,
      allProjectCheck: 0,
      globalProject: "",
      version: "web",
      from: "panel",
      tz: "+5:30",
      tzid: "Asia/Calcutta",
      moduleId: null,
      projectId: id,
    };
    const promise = await dispatch(getFolderList(data));
  };
  const handleSelectImageWhenNoData = (imageData: IFile) => {
    setSelectedFile((prevSelectedFiles) => {
      if (
        prevSelectedFiles.some((file) => file.image_id === imageData.image_id)
      ) {
        return prevSelectedFiles.filter(
          (file) => file.image_id !== imageData.image_id
        );
      } else {
        return [...prevSelectedFiles, imageData];
      }
    });
  };
  // const fileListData = useMemo(() => {
  //   if (fileList?.data) {
  //     // let uploadFileListJSX: IFileUploadListURL[] = [];
  //     // if (uploadFileList && uploadFileList.length) {
  //     //   uploadFileListJSX = uploadFileList
  //     //     .map((file: IUploadFileList, key: number) => {
  //     //       return file.loading && { file_url: "loading" };
  //     //     })
  //     //     .filter(Boolean) as IFileUploadListURL[];
  //     // }

  //     // Prepend the upload object to the existing file list
  //     const fileListData_ = [...fileList.data];
  //     if (module_access !== "read_only") {
  //       fileListData_.unshift({ file_url: "upload" });
  //     }
  //     // add loading skeleton using prop
  //     if (hasMore && fileList?.data.length) {
  //       fileListData_.push({ file_url: "loading" });
  //     }
  //     return fileListData_;
  //   }
  //   return [];
  // }, [fileList?.data]);

  const handleCreateFile = async (files: IFile[]) => {
    const formData: TCreateCoverImage = {
      primary_id: Number(id),
      module_id: module_id,
      module_key: module_key,
    };

    if (files.length) {
      const { attachImage, awsFilesUrl }: IFilterAttachmentFiles =
        filterAttachmentFiles(files);

      formData.attach_image = attachImage.length
        ? attachImage.join(",")
        : undefined;

      const updatedAwsFilesUrl = awsFilesUrl.map((file) => ({
        ...file,
        isheic: file.file_ext.toLowerCase() === "heic" ? 1 : 0, // Add 'isheic' key
      }));

      formData.files = updatedAwsFilesUrl.length
        ? updatedAwsFilesUrl
        : undefined;

      formData.project_id = id;
      formData.module_id =
        !isEmpty(selectedFolder) &&
        selectedFolder?.project_id === defaultConfig.company_files_project_id
          ? defaultConfig.company_files_folder_id
          : selectedFolder?.project_id ===
            defaultConfig.unassigned_files_project_id
          ? defaultConfig.unassigned_files_folder_id
          : selectedFolder.module_id !== null
          ? selectedFolder.module_id ?? module_id
          : module_id ?? defaultConfig.projects_module_id;
      formData.folder_id = isEmpty(selectedFolder)
        ? 0
        : selectedFolder?.project_id ===
            defaultConfig.company_files_project_id ||
          selectedFolder?.project_id ===
            defaultConfig.unassigned_files_project_id
        ? 0
        : selectedFolder.folder_id;
    } else {
      return false;
    }

    const filePromise = [
      addFile(formData) as Promise<IGetAddFileRes>,
      handleUpdateField({
        data_to_send_in_api: {
          client_cover_image: files[0]?.cdnUrl || files[0]?.file_path,
        },
      }),
    ];

    const [addFilesRes] = await Promise.all(filePromise);

    if (!addFilesRes?.success) {
      notification.error({
        description: addFilesRes?.message,
      });
    } else {
      dispatch(callAgainFileList());
      if (isEmpty(selectedFolder)) {
        getRootFolderList();
      }
      if (fileSupportAccess === "no_access") {
        const onlyImageData = files?.filter(
          (item) => item?.type?.startsWith("image") === true
        );
        dispatch(
          updateFileCount({
            selectedFolder: selectedFolder,
            selectedProject: selectedFolder.project_id,
            fileLength:
              (Number(selectedFolder?.total_file_count) ?? 0) +
              (onlyImageData?.length ?? 0),
            action: "addOrDeleteOrUpdate",
          })
        );
      } else {
        dispatch(
          updateFileCount({
            selectedFolder: selectedFolder,
            selectedProject: selectedFolder.project_id,
            fileLength:
              (Number(selectedFolder?.total_file_count) ?? 0) +
              (files?.length ?? 0),
            action: "addOrDeleteOrUpdate",
          })
        );
      }
      if (selectedFolder?.next_request_type === "2") {
        getFolderListAfterFileAdd(dispatch, selectedFolder);
      }
      if (isEmpty(selectedFolder)) {
        getRootFolderList();
      }
    }
  };

  const handleAddNewFile = () => {
    setSelectedTab("new");
  };

  const handleUploadFileWhenNoData = async (filesData: File[]) => {
    // Ensure the filesData is an array before mapping
    if (!filesData || !Array.isArray(filesData)) return;
    // Use Promise.all to handle asynchronous operations within the map
    const newFilesArray = await Promise.all(
      filesData.map(async (file) => {
        // Extract or set file-specific information here, like fileName and fileExtension
        let fileName = file.name;
        const fileExtension = file.type;
        const extension = getFileExtension(file.name).toLowerCase();
        const thumbValue = file.type?.startsWith("image") ? true : false;
        const fileWithProps = file as File | Blob;
        // Dispatch the uploadFile action and await its response
        if (extension === "heic") {
          fileName = file.name.replace(/\.heic$/i, ".HEIC"); // Convert extension to uppercase
        }
        const response = (await dispatch(
          uploadFile({
            moduleName: module_key,
            fileName: fileName,
            fileType: fileExtension,
            saveAsNew: 1,
            isThumbRequired: thumbValue,
            module_id: selectedFolder?.project_id
              ? selectedFolder?.project_id
              : module_id ?? defaultConfig.projects_module_id,
          })
        )) as { payload: IGetUploadFileRes };

        if (
          response &&
          response.payload &&
          response.payload.data &&
          response.payload.statusCode == 200
        ) {
          await fetch(response.payload.data?.fileUrl).then((res) => res.blob());
          let res;

          res = await axios.put(
            response.payload.data?.signedUrl,
            fileWithProps,
            {
              headers: {
                "Content-Type": fileWithProps.type, // Dynamically set content type
              },
            }
          );

          if (response.payload.data?.signedUrl) {
            const newFilesArray = [
              {
                file_url: response.payload.data?.fileUrl,
                is_image: thumbValue ? 1 : 0,
              },
            ];
            if (res?.status === 200) {
              const addFileRes = (await addFile({
                files: newFilesArray,
                module_id:
                  !isEmpty(selectedFolder) &&
                  selectedFolder?.project_id ===
                    defaultConfig.company_files_project_id
                    ? defaultConfig.company_files_folder_id
                    : selectedFolder?.project_id ===
                      defaultConfig.unassigned_files_folder_id
                    ? defaultConfig.unassigned_files_folder_id
                    : selectedFolder.module_id !== null
                    ? selectedFolder.module_id ?? module_id
                    : module_id ?? defaultConfig.projects_module_id,
                folder_id: isEmpty(selectedFolder)
                  ? 0
                  : selectedFolder?.project_id ===
                      defaultConfig.company_files_project_id ||
                    selectedFolder?.project_id ===
                      defaultConfig.unassigned_files_project_id
                  ? 0
                  : selectedFolder.folder_id,
                project_id: isEmpty(selectedFolder)
                  ? id
                  : selectedFolder?.project_id ===
                      defaultConfig.company_files_project_id ||
                    selectedFolder?.project_id ===
                      defaultConfig.unassigned_files_project_id
                  ? 0
                  : selectedFolder?.project_id,
                ...(!isEmpty(parentFolderName)
                  ? { file_tags: parentFolderName }
                  : {}),
                ...((!isEmpty(selectedFolder) &&
                  selectedFolder?.project_id ===
                    defaultConfig.company_files_project_id) ||
                selectedFolder?.project_id ===
                  defaultConfig.unassigned_files_project_id
                  ? {
                      static_folder: 1,
                    }
                  : {}),
                ...((!isEmpty(selectedFolder) &&
                  selectedFolder?.project_id ===
                    defaultConfig.company_files_project_id) ||
                selectedFolder?.project_id ===
                  defaultConfig.unassigned_files_project_id
                  ? {
                      module_name: selectedFolder?.project_id,
                    }
                  : {}),
                is_file_shared: 0,
              })) as IAddFileApiRes;
              if (addFileRes.success) {
                setIsLoadingWhenNoData(false);
                if (isEmpty(selectedFolder)) {
                  getRootFolderList();
                }
                dispatch(callAgainFileList());
                if (fileSupportAccess === "no_access") {
                  const onlyImageData = filesData?.filter(
                    (item) => item.type?.startsWith("image") === true
                  );
                  dispatch(
                    updateFileCount({
                      selectedFolder: selectedFolder,
                      selectedProject: selectedFolder.project_id,
                      fileLength:
                        (Number(selectedFolder?.total_file_count) ?? 0) +
                        (onlyImageData?.length ?? 0),
                      action: "addOrDeleteOrUpdate",
                    })
                  );
                } else {
                  dispatch(
                    updateFileCount({
                      selectedFolder: selectedFolder,
                      selectedProject: selectedFolder.project_id,
                      fileLength:
                        (Number(selectedFolder?.total_file_count) ?? 0) +
                        (filesData?.length ?? 0),
                      action: "addOrDeleteOrUpdate",
                    })
                  );
                }
                if (selectedFolder?.next_request_type === "2") {
                  getFolderListAfterFileAdd(dispatch, selectedFolder);
                }
              }
            }
          }
        }
        // Return whatever structure you want for the newFilesArray
        return response.payload; // Assuming IGetUploadFileRes is the response payload
      })
    );
  };

  const dropzoneAcceptedTypes: Accept = useMemo(() => {
    const result: Accept = {};

    Object.entries(acceptedFileTypes).forEach(([mimeType, extensions]) => {
      result[mimeType] = extensions;
    });

    return result;
  }, []);
  const onDropRejected = (fileRejections: any[]) => {
    setIsLoadingWhenNoData(false);
    const newRejectedFiles = fileRejections.map((rejection) => {
      const { file } = rejection;
      notification.error({
        description: `Rejected: ${file.name} (Invalid file type or missing extension)`,
      });
      return file;
    });
    // Update the state by adding new rejected files to the existing ones
  };

  const { getRootProps, getInputProps } = useDropzone({
    onDrop: (acceptedFiles) => {
      if (isReadOnly) return;
      // Handle the files dropped here
      setIsLoadingWhenNoData(true);
      requestAnimationFrame(() => {
        // Proceed with file validation and uploading after ensuring UI is updated
        !addOrEditFileOpen && handleUploadFileWhenNoData(acceptedFiles);
      });
    },
    onDropRejected,
    accept: dropzoneAcceptedTypes,
    noClick: true, // To prevent the default file dialog from appearing on click
    noKeyboard: true, // To prevent the default keyboard handling
  });
  const folderView: IFilePhotoViewSlice["folderView"] = useAppProSelector(
    (state) => state.fileAndPhotoView.folderView
  );
  return (
    <>
      <div
        className={`grid items-start gap-3.5 w-full
           ${
             module_access === "read_only"
               ? "xl:h-[calc(100dvh-440px)] md:h-[calc(100dvh-503px)] h-[calc(100dvh-513px)]"
               : "xl:h-[calc(100dvh-402px)] md:h-[calc(100dvh-471px)] h-[calc(100dvh-443px)]"
           } 
          ${
            folderView.viewAllFiles
              ? "gap-0 w-full"
              : `gap-3.5 xl:w-[calc(100%-376px)] md:w-[calc(100%-230px)] w-full`
          }`}
        {...getRootProps()}
      >
        {!isInfiniteScrollLoading &&
        !hasMore &&
        fileList &&
        fileList.data?.length === 0 &&
        uploadFileList.filter((file) => file.loading).length === 0 ? (
          <div
            className={`flex justify-center items-center ${
              fileList.data?.length === 0
                ? "h-full"
                : "2xl:h-[calc(100vh-375px)] xl:h-[calc(100vh-366px)] h-[calc(100vh-385px)]"
            }`}
          >
            {/* {isLoadingWhenNoData && (
                <LoadingGallery skeleton={uploadFileList?.length} />
              )} */}
            {!isLoadingWhenNoData && module_access === "read_only" ? (
              <NoRecords
                rootClassName="w-full max-w-[280px]"
                image={`${window.ENV.CDN_URL}assets/images/create-record-directory.svg`}
                imageWSize="280"
                imageHSize="227"
                text={
                  <Typography className="sm:text-base text-xs text-black font-semibold">
                    {_t("No Record Found")}
                  </Typography>
                }
              />
            ) : (
              <UploadFilesInNoData handleAddNewFile={handleAddNewFile} />
            )}
          </div>
        ) : (
          <>
            {fileList &&
            fileList.data?.length === 0 &&
            hasMore &&
            !infiniteScrollHideLoadingComponent ? (
              <div className="grid gap-3.5 file-photos-folder-view">
                <LoadingGallery skeleton={40} />
              </div>
            ) : (
              <VirtuosoGridCommon<
                | FilePhotoRightDetail
                | {
                    file_url: string;
                  }
              >
                className={`w-full md:max-h-[calc(100dvh-240px)] h-[calc(100dvh-240px)] ${
                  fileList && fileList?.data?.length === 0
                    ? "md:overflow-hidden"
                    : "hover-scroll md:overflow-y-auto md:overflow-hidden"
                  // }`
                  // : ""
                }`}
                // className="w-full w-full md:max-h-[calc(100dvh-240px)] h-[calc(100dvh-240px)] hover-scroll md:overflow-y-auto md:overflow-hidden"
                components={gridComponents}
                loadMore={loadMore}
                data={Array.isArray(fileList?.data) ? fileList?.data : []}
                itemContent={(index, imageData) => {
                  // if (imageData.file_url === "upload") {
                  //   return (
                  //     <div key={index} className="w-full h-full">
                  //       <div className="grid gap-2.5 h-full">
                  //         <DropZone
                  //         // onClick={onFileSelectFromClick}
                  //         // onDrop={handleDrop}
                  //         />
                  //         <div className="grid grid-cols-2 gap-2.5">
                  //           <Tooltip title="This feature is temporarily disabled while Google is issuing the certificate. We hope to have it enabled soon">
                  //             <div className="opacity-40 cursor-no-drop">
                  //               {/* <GoogleDrivePicker /> */}
                  //             </div>
                  //           </Tooltip>
                  //           {/* <DropboxPicker /> */}
                  //         </div>
                  //       </div>
                  //     </div>
                  //   );
                  // }

                  if (imageData.file_url === "loading") {
                    return (
                      <div key={index} className="w-full">
                        <LoadingGallery skeleton={1} />
                      </div>
                    );
                  }

                  imageData = imageData as FilePhotoRightDetail;

                  const selectedTags: string[] = !imageData.file_tags
                    ? []
                    : imageData?.tag_names && imageData.tag_names.trim().length
                    ? imageData.tag_names.trim().split(",")
                    : // : parentFolderName && parentFolderName.trim().length
                      // ? parentFolderName.trim().split(",")
                      [];
                  return (
                    <div key={imageData.image_id} className="w-full">
                      <GalleryFilePhotos
                        handleSelectImage={handleSelectImage}
                        selectedFiles={selectedFiles}
                        imageData={imageData}
                        selectedAllFiles={selectedAllFiles}
                        changeAddOrEditFileOpen={changeAddOrEditFileOpen}
                        changeMarkupModelOpen={changeMarkupModelOpen}
                        changeConfirmModelOpen={changeConfirmModelOpen}
                        changeSendEmailDrawerOpen={changeSendEmailDrawerOpen}
                        selectedTags={selectedTags}
                        isReadOnly={module_access === "read_only"}
                        dispatch={dispatch}
                        markupFileData={markupFileData}
                        module_access={module_access}
                        fileSupportAccess={fileSupportAccess}
                        date_format={date_format}
                        markupedFileData={markupedFileData}
                        isProjectModule={true}
                        isShowDelete={
                          (imageData?.primary_id?.toString() === id &&
                            imageData?.project_id?.toString() === id) ||
                          (!imageData?.primary_id &&
                            imageData?.project_id?.toString() === id)
                        }
                      />
                    </div>
                  );
                }}
              />
            )}
          </>
        )}

        {fileList?.data?.length === 0 && isLoadingWhenNoData && (
          <LoadingGallery skeleton={uploadFileList?.length} />
        )}
        {addOrEditFileOpen && (
          <AddOrEditFile
            addOrEditFileOpen={addOrEditFileOpen}
            setAddOrEditFileOpen={setAddOrEditFileOpen}
            moduleId={module_id}
          />
        )}
      </div>
      {!!selectedTab && (
        <FileSelect
          projectid={id}
          selectedTab={selectedTab}
          setSelectedTab={setSelectedTab}
          setSelectedFileData={(data) => {
            handleCreateFile(data as IFile[]);
          }}
          handleSelectImage={handleSelectImageWhenNoData}
          selectedFiles={selectedFile}
          setSelectedFiles={setSelectedFile}
          useAppSelector={useAppProSelector}
          dispatch={dispatch}
          validationParams={{
            date_format: CFConfig.day_js_date_format,
            file_support_module_access: checkGlobalModulePermissionByKey(
              CFConfig.file_support_key
            ),
            image_resolution,
            module_key,
            module_id,
            module_access,
          }}
          addFilesRes={{}}
          load={load}
          isLoad={isLoad}
        />
      )}
    </>
  );
};

export default FilePhotoRightSide;
