// molecules
import { IconButton } from "~/shared/components/molecules/iconButton";
import { ReadOnlyPermissionMsgForFileSupportedFormat } from "~/shared/components/molecules/readOnlyPermissionMsgForFileSupportedFormat";
import { ReadOnlyPermissionMsg } from "~/shared/components/molecules/readOnlyPermissionMsg";
import TimelineView from "./components/TimeLineView";
// Other
import { getGSettings } from "~/zustand";

import { useTranslation } from "~/hook";
import FilesPhotpsTopBar from "./components/FilesPhotpsTopBar";
import FilePhotoPath from "./components/FilePhotoPath";
import FileAndPhoto from "./components/FileAndPhotoDefaultView";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";

// action
import { getFolderList } from "~/modules/projectManagement/pages/project/redux/action/projectFilePhotoLeftAction";
import { useEffect, useMemo, useState } from "react";
import {
  deleteFiles,
  getFileList,
} from "../../../redux/action/projectFilePhotoRightAction";
import { useParams } from "@remix-run/react";
import { useAppProDispatch, useAppProSelector } from "../../../redux/store";
import ProjectFilePhotoActionHeader from "./components/ProjectFilePhotoActionHeader";
import {
  deleteFilesFromList,
  incrementFileListPage,
  setAddOrEditFileOpenRedux,
  setImageDataRedux,
  setIsConfirmModalOpenRedux,
  setIsMarkupModalOpenRedux,
  callAgainFileList as setCallAgainFileList,
  setSelectedAllFiles,
  setSuccessForDetailView,
  setCommonModal,
  setPunchListPdfModal,
  incrementFileTimelineListPage,
  setSendEmailDrawerOpenRedux,
} from "../../../redux/slices/projectFilePhotoRightSlice";
import {
  setActiveField,
  setSelectedContact,
} from "~/redux/slices/sendEmailSlice";
import { CheckboxChangeEvent } from "antd/es/checkbox";
import AddOrEditFile from "~/modules/projectManagement/pages/project/components/tab/sidebar/editFile/EditFile";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import {
  collapseAllFolders,
  updateFileCount,
} from "../../../redux/slices/projectFilePhotoLeftSlice";
import { getFolderListAfterFileAdd } from "../../utils/getFolderListAfterFileAdd";
import { useFilePreview } from "~/shared/hooks/useFilePreview";
import { MarkupModal } from "~/shared/components/molecules/markupModal";
import { getMimeType } from "~/shared/utils/helper/getMimeType";
import { uploadFile } from "~/redux/action/fileAttachmentAction";
import { putFilesToSignedUrl } from "~/shared/utils/helper/putFilesToSignedUrl";
import { updateMarkupFile } from "~/redux/action/fileEditAction";
import { CommonModal } from "~/shared/components/molecules/commonModal";
import { PdfModal } from "~/shared/components/molecules/pdfModal";
import { SendEmailDrawer } from "~/shared/components/molecules/sendEmailDrawer";
import { sendCommonEmailApi } from "~/redux/action/sendEmailAction";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { getTagCategories } from "~/redux/action/tagCategoriesAction";
import { addProject } from "../../../redux/slices/fileAndPhotoHeaderSlice";
import isEmpty from "lodash/isEmpty";

const FilePhotosProject = () => {
  const { _t } = useTranslation();
  const { id }: RouteParams = useParams();
  const [mobileMenu, setMobileMenu] = useState<boolean>(false);
  const { date_format, image_resolution, save_a_copy_of_sent_pdf }: GSettings =
    getGSettings();
  const { checkGlobalModulePermissionByKey } = useGlobalModule();
  const selectedAllFiles: boolean = useAppProSelector(
    (state) => state.filePhotoFileList.selectedAllFiles
  );
  const {
    fileList,
    selectedFiles,
    isfileLoading,
    callAgainFileList,
    fileTimelineList,
    addOrEditFileOpen,
    imageData,
    editView,
    isConfirmModalOpen,
    isMarkupModalOpen,
    sendEmailDrawerOpen,
    detailViewLength,
    isCommonModalOpen,
    selectedFileTitle,
    selectedFilePath,
    selectedFileUrl,
    isPunchListPdfViewOpen,
  }: IFilePhotoRight = useAppProSelector((state) => state.filePhotoFileList);
  const { selectedFolder }: IFilePhotoLeftInitialState = useAppProSelector(
    (state) => state.filePhotoFolderList
  );
  const currentView: IFilePhotoViewSlice["currentView"] = useAppProSelector(
    (state) => state.fileAndPhotoView.currentView
  );

  const selectView: IFilePhotoViewSlice = useAppProSelector(
    (state) => state.fileAndPhotoView
  );
  const { retain_original_image }: GSettings = getGSettings();

  const isCurrentViewFolder = selectView.folderView.fileStructure;
  const selectedProjectDropbox: ISelectedFolder = useAppProSelector(
    (state) => state.filePhotoFolderList.selectedFolder
  );
  const dispatch = useAppProDispatch();
  const currentModule = getCurrentMenuModule();
  const { module_access, module_key, module_id } = currentModule || {};
  const [isFileLoader, setIsFileLoader] = useState<boolean>(false);
  const [markupLoading, setMarkupLoading] = useState(false);
  const [markupedFileData, setMarkupedFileData] = useState({});

  useEffect(() => {
    getRootFolderList();
  }, []);

  const handleCheckAll = (e: CheckboxChangeEvent | boolean) => {
    dispatch(
      setSelectedAllFiles({
        checked: typeof e !== "boolean" ? e.target.checked : e,
        currentView: selectView.currentView,
        // currentView: "folder",
        // fileStructure: "thumbnail",
        fileStructure: selectView.folderView.fileStructure,
      })
    );
  };
  const fileListMore = () => {
    dispatch(incrementFileListPage());
  };

  const getRootFolderList = async () => {
    const data = {
      type: 2,
      allProjectCheck: 0,
      globalProject: "",
      version: "web",
      from: "panel",
      tz: "+5:30",
      tzid: "Asia/Calcutta",
      moduleId: null,
      projectId: id,
    };
    const promise = await dispatch(getFolderList(data));
  };

  const closeModalHandler = () => {
    dispatch(setIsMarkupModalOpenRedux(false));
    dispatch(setImageDataRedux({}));
    setPreviewUrlOpen(false);
    setMarkupLoading(false);
  };
  useEffect(() => {
    if (
      selectView.folderView.fileStructure !== "detail" ||
      selectView?.currentView === "timeline"
    ) {
      let getFileListParams: IGetFileListParam = {
        // search:
        //   HTMLEntities.encode(headerDebounceSearch?.trim()) ||
        //   filter?.search?.trim() ||
        //   undefined,
        page:
          selectView.currentView === "folder"
            ? fileList.page
            : fileTimelineList.page,
        limit: 40,
        type: selectedFolder?.next_request_type ?? 2,
        allProjectCheck: 0,
        from: "panel",
        isTimelineView: selectView.currentView === "folder" ? 0 : 1,
        clientOnlyFiles: false,
        onlyImage: 0,
        staticFolder: selectedFolder?.module_key === "static" ? 1 : 0,
        projectId: id ?? "",
        module_id:
          selectedFolder?.next_request_type === "2"
            ? null
            : selectedFolder?.module_id,
        folderId: selectedFolder?.folder_id,
        // filter: filtered,
      };

      if (selectView.currentView === "folder" && fileList.sort) {
        getFileListParams["sortOrder"] =
          fileList.sort === "by_date" ? "date_added" : "file_name";
      }

      const promise = dispatch(
        getFileList({
          ...getFileListParams,
        })
      );
      return () => {
        // `createAsyncThunk` attaches an `abort()` method to the promise
        promise.abort();
      };
    }
  }, [
    // headerDebounceSearch,
    fileList.changeSort,
    // filter,
    callAgainFileList,
    fileList.page,
    fileTimelineList.page,
    selectView.currentView,
    selectView.folderView.fileStructure,
    // updatedData?.data,
  ]);
  const isActionHeader = useMemo(
    () => selectedFiles.length > 0,
    [selectedFiles]
  );
  const isCheckAll: boolean = selectedAllFiles;
  const changeConfirmModelOpen = (
    value: boolean,
    imageData: FilePhotoRightDetail
  ) => {
    dispatch(setImageDataRedux(imageData));
    dispatch(setIsConfirmModalOpenRedux(value));
    if (!value) {
      dispatch(setImageDataRedux({}));
    }
  };
  useEffect(() => {
    dispatch(
      getTagCategories({
        is_deleted: 0,
        module_id: null,
      })
    );
    dispatch(addProject(null));
  }, []);
  const changeAddOrEditFileOpen = (
    { addOrEditFileOpen, editView }: IActionPayloadSetAddOrEditFileOpenRedux,
    imageData: FilePhotoRightDetail
  ) => {
    dispatch(setImageDataRedux(imageData));
    dispatch(
      setAddOrEditFileOpenRedux({
        addOrEditFileOpen: addOrEditFileOpen,
        editView: editView,
      })
    );
    if (!addOrEditFileOpen) {
      dispatch(setImageDataRedux({}));
    }
  };

  const {
    handleFilePreview,
    previewUrlOpen,
    previewUrl,
    previewUrlLoding,
    setPreviewUrlOpen,
  } = useFilePreview();
  const changeMarkupModelOpen = (
    value: boolean | "previewUrl",
    imageData: FilePhotoRightDetail
  ) => {
    if (value === "previewUrl") {
      handleFilePreview(imageData.file_path ?? "");
    } else {
      dispatch(setImageDataRedux(imageData));
      dispatch(setIsMarkupModalOpenRedux(value));
      if (!value) {
        dispatch(setImageDataRedux({}));
      }
    }
  };
  const onDelete = async () => {
    setIsFileLoader(true);
    const response = (await deleteFiles({
      fileIds: [(imageData as FilePhotoRightDetail).image_id],
    })) as IFilePhotoDeleteApiRes;
    if (response?.success) {
      dispatch(setSuccessForDetailView(true));
      dispatch(setIsConfirmModalOpenRedux(false));
      setIsFileLoader(false);
      if (isCurrentViewFolder) {
        const fileLength =
          isCurrentViewFolder === "detail"
            ? detailViewLength ?? 0
            : Number(selectedProjectDropbox?.total_file_count) ?? 0;

        dispatch(setSuccessForDetailView(true));
        dispatch(setIsConfirmModalOpenRedux(false));

        dispatch(
          deleteFilesFromList({
            file_id: (imageData as FilePhotoRightDetail).image_id,
          })
        );
        if (selectedProjectDropbox?.next_request_type === "2") {
          getFolderListAfterFileAdd(dispatch, selectedFolder);
        } else if (isEmpty(selectedFolder)) {
          getRootFolderList();
        } else {
          if (selectedFolder?.total_file_count?.toString() === "1") {
            getRootFolderList();
            dispatch(collapseAllFolders());
            dispatch(setCallAgainFileList());
          }
          dispatch(
            updateFileCount({
              selectedFolder: selectedProjectDropbox,
              selectedProject: selectedProjectDropbox.project_id,
              fileLength: fileLength ? fileLength - 1 : 0,
              action: "addOrDeleteOrUpdate",
            })
          );
        }
      }
    } else {
      notification.error({
        description: response?.message,
      });
    }
    dispatch(setIsConfirmModalOpenRedux(false));
    dispatch(setImageDataRedux({}));
  };
  const updateFile = async (
    fetchFileDetails: IFilePhotoRight,
    info: ISavedImageData | IPdfjsExpressPropsOnSaveValue,
    signedUrl: string
  ) => {
    const urlObject = new URL(signedUrl);
    const baseUrl = `${urlObject.origin}${urlObject.pathname}`;
    
    let fileIdToSend = fetchFileDetails.image_id;
    let fileUrlToSend = baseUrl;

    // ✅ Detect if this is updating original image (so we must refer to the markup's ID/url)
    const isUpdatingOriginal = fileList?.data?.some(
      (item) => item.original_image_id === fetchFileDetails.image_id
    );

    const markupFile = fileList?.data?.find(
      (item) => item.original_image_id === fetchFileDetails.image_id
    );

    const originalFile = fileList?.data?.find(
      (item) => item.image_id === fetchFileDetails.original_image_id
    );

    // ✅ If updating original → use markup file's ID + fileUrl
    if (isUpdatingOriginal && markupFile) {
      fileIdToSend = markupFile.image_id;
      fileUrlToSend = markupFile.fileUrl || baseUrl;
    }
    dispatch(setSuccessForDetailView(true));

    const response = await dispatch(
      updateMarkupFile({
        projectId:
          fetchFileDetails.project_id && fetchFileDetails.project_id > 0
            ? fetchFileDetails.project_id
            : undefined,
            fileUrl: fileUrlToSend,
            fileId: fileIdToSend,
        annotationData: "xfdf" in info ? info.xfdf : undefined,
      })
    );
    setMarkupedFileData(response?.payload?.data);
  };

  const onAcceptOrDecline = async (
    info: ISavedImageData | IPdfjsExpressPropsOnSaveValue
  ) => {
    const saveMarkupFileInfo = info as ISavedImageData;
    try {
      let fullFileName;
      try {
        fullFileName = decodeURI(
          saveMarkupFileInfo.fullName ??
            `${(imageData as FilePhotoRightDetail)?.file_name}.${
              (imageData as FilePhotoRightDetail)?.file_ext
            }`
        );
      } catch (error) {
        fullFileName =
          saveMarkupFileInfo.fullName ??
          `${(imageData as FilePhotoRightDetail)?.file_name}.${
            (imageData as FilePhotoRightDetail)?.file_ext
          }`;
        notification.error({
          description: `Invalid file name`,
        });
      }
      const fileExt = getMimeType(
        (imageData as FilePhotoRightDetail)?.file_ext?.toString() ?? ""
      );
      const isThumbRequired = [
        "jpg",
        "jpeg",
        "png",
        "gif",
        "bmp",
        "webp",
      ].includes((imageData as FilePhotoRightDetail)?.file_ext?.toLowerCase());

      // ✅ Apply retain_original_image logic using fileList?.data
      let fileIdToSend = imageData?.image_id;

      const isUpdatingOriginal = fileList?.data?.some(
        (item) => item.original_image_id == imageData?.image_id
      );

      const markupFile = fileList?.data?.find(
        (item) => item.original_image_id == imageData?.image_id
      );
      
      if (isUpdatingOriginal && markupFile) {
        fileIdToSend = markupFile.image_id;
      }

      const originalFile = fileList?.data?.find(
        (item) => item.image_id == imageData?.original_image_id
      );

      const saveAsNew =
        retain_original_image === 0
          ? 0
          : fileList?.data?.some(
              (item) =>
                item.original_image_id === imageData?.image_id ||
                item.image_id === imageData?.original_image_id
            )
          ? 0
          : retain_original_image;

      const response = (await dispatch(
        uploadFile({
          moduleName: module_key,
          fileName: fullFileName,
          fileType: fileExt,
          saveAsNew,
          isThumbRequired,
          fileId: fileIdToSend,
        })
      )) as { payload: IGetUploadFileRes };

      if (response?.payload?.data?.signedUrl) {
        const signedUrl = response.payload.data.signedUrl;

        const fileToSend =
          isUpdatingOriginal && originalFile ? originalFile : imageData;

        await putFilesToSignedUrl(
          signedUrl,
          info as ISavedImageData | IPdfjsExpressPropsOnSaveValue
        );

        await updateFile(
          fileToSend as IFilePhotoRight,
          info as ISavedImageData | IPdfjsExpressPropsOnSaveValue,
          signedUrl
        );

        setMarkupedFileData(fileToSend);
        dispatch(setCallAgainFileList());
        if (saveAsNew) {
          dispatch(
            updateFileCount({
              selectedFolder: selectedProjectDropbox,
              selectedProject: selectedProjectDropbox.project_id,
              fileLength: fileList?.data?.length
                ? fileList?.data?.length + 1
                : fileList?.data?.length,
              action: "addOrDeleteOrUpdate",
            })
          );
          if (isEmpty(selectedFolder)) {
            getRootFolderList();
          }
        }
      }
    } catch (error) {
      return error;
    } finally {
      setIsFileLoader(false);
      dispatch(setIsConfirmModalOpenRedux(false));
      dispatch(setIsMarkupModalOpenRedux(false));
      dispatch(setImageDataRedux({}));
      setMarkupLoading(false);
    }
  };

  const handleSave = async (
    info: ISavedImageData | IPdfjsExpressPropsOnSaveValue
  ) => {
    setMarkupLoading(true);
    onAcceptOrDecline(info);
    if (!Object.keys(imageData).length) {
      return;
    }
  };
  const handleAccept = () => {
    onDelete();
  };
  const handleDecline = () => {
    dispatch(setIsConfirmModalOpenRedux(false));
    dispatch(setImageDataRedux({}));
  };
  const loadMore = () => {
    dispatch(incrementFileTimelineListPage());
  };

  const handleEmailApiCall = async (
    tempFormData: SendEmailFormDataWithApiDefault,
    closeSendMailSidebar: () => void,
    ccMailCopy: boolean
  ) => {
    const formData: ISendEmailFormDataWithApiDefaultNew = {
      ...tempFormData,
      send_me_copy: ccMailCopy ? 1 : 0,
      send_custom_email: 0,
      op: "pdf_files",
    };
    try {
      const responseApi = (await sendCommonEmailApi(
        formData
      )) as ISendEmailCommonRes;
      if (responseApi?.success) {
        closeSendMailSidebar();
        dispatch(setSelectedContact("reset"));
        dispatch(setActiveField(CFConfig.employee_key));
      }
      if (!responseApi?.success) {
        notification.error({
          description: responseApi.message,
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error).message || "",
      });
    }
    closeSendMailSidebar();
  };

  const changeSendEmailDrawerOpen = (
    sendEmailDrawerOpen: boolean,
    imageData: FilePhotoRightDetail
  ) => {
    dispatch(setImageDataRedux(imageData));
    dispatch(setSendEmailDrawerOpenRedux(sendEmailDrawerOpen));
    if (!sendEmailDrawerOpen) {
      dispatch(setImageDataRedux({}));
    }
  };

  return (
    <>
      <div
        className={`hover-scroll overflow-y-auto overflow-hidden ${
          module_access === "read_only"
            ? "xl:h-[calc(100dvh-350px)] md:h-[calc(100dvh-419px)] h-[calc(100dvh-413px)]"
            : "xl:h-[calc(100dvh-312px)] md:h-[calc(100dvh-381px)] h-[calc(100dvh-353px)]"
        }`}
      >
        <div className="common-card py-1.5 px-[15px] sticky top-0 z-[99]">
          {isActionHeader ? (
            <ProjectFilePhotoActionHeader
              selectedFiles={selectedFiles}
              handleCheckAll={handleCheckAll}
              checkAll={isCheckAll}
              check
              isReadOnly={module_access === "read_only"}
            />
          ) : (
            <FilesPhotpsTopBar />
          )}
        </div>
        {module_access !== "read_only" && (
          <ReadOnlyPermissionMsgForFileSupportedFormat view={false} />
        )}
        <div className="pt-2">
          {currentView === "folder" && (
            <div className="flex h-7 items-center justify-between mb-2">
              <div className="sm:w-fit w-[calc(100%-100px)] md:block hidden whitespace-nowrap header-breadcrumb">
                <FilePhotoPath title={"All Folders"} />
              </div>
              <IconButton
                htmlType="button"
                variant="default"
                className="md:!hidden group/module-menu relative w-[34px] h-[28px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                icon="fa-regular fa-bars"
                onClick={() => setMobileMenu(!mobileMenu)}
              />
            </div>
          )}
          {currentView === "folder" && (
            <FileAndPhoto
              loadMore={fileListMore}
              fileList={fileList}
              mobileMenu={mobileMenu}
              handleClose={() => setMobileMenu(false)}
              changeAddOrEditFileOpen={changeAddOrEditFileOpen}
              isInfiniteScrollLoading={isfileLoading}
              selectedFiles={selectedFiles}
              changeConfirmModelOpen={changeConfirmModelOpen}
              changeMarkupModelOpen={changeMarkupModelOpen}
              markupedFileData={markupedFileData}
              changeSendEmailDrawerOpen={changeSendEmailDrawerOpen}
            />
          )}
          {currentView === "timeline" && (
            <TimelineView
              loadMore={loadMore}
              hasMore={fileTimelineList.infiniteFlieTimelineScrollhasMore}
              infiniteScrollHideLoadingComponent={fileTimelineList.error}
              isInfiniteScrollLoading={isfileLoading}
              changeAddOrEditFileOpen={changeAddOrEditFileOpen}
              changeMarkupModelOpen={changeMarkupModelOpen}
              changeConfirmModelOpen={changeConfirmModelOpen}
              changeSendEmailDrawerOpen={changeSendEmailDrawerOpen}
            />
          )}
        </div>
      </div>
      {addOrEditFileOpen && (
        <AddOrEditFile
          addOrEditFileOpen={addOrEditFileOpen}
          editView={true}
          setAddOrEditFileOpen={(value: boolean) => {
            dispatch(setAddOrEditFileOpenRedux({ addOrEditFileOpen: value }));
            if (!value) {
              dispatch(setImageDataRedux({}));
            }
          }}
          singleFileDetails={imageData as ISingleFileDetail}
          moduleId={1}
          // setUpdatedData={setUpdatedData}
          // selectedProjectDropbox={selectedProjectDropbox}
          isReadOnly={module_access === "read_only"}
        />
      )}
      {(isMarkupModalOpen || previewUrlOpen) && (
        <MarkupModal
          open={isMarkupModalOpen}
          closeModalHandler={closeModalHandler}
          onSave={handleSave}
          previewUrl={previewUrl ?? ""}
          previewUrlLoading={previewUrlLoding}
          previewUrlOpen={previewUrlOpen}
          file={imageData as IAttachedFile}
          markupLoading={markupLoading}
        />
      )}
      {isPunchListPdfViewOpen && (
        <PdfModal
          isOpen={isPunchListPdfViewOpen}
          modaltitle={selectedFileTitle}
          onCloseModal={() =>
            dispatch(
              setPunchListPdfModal({
                isPunchListPdfViewOpen: false,
                selectedFileTitle: "",
                selectedFileUrl: "",
              })
            )
          }
          fileUrl={selectedFileUrl}
        />
      )}
      {isCommonModalOpen && (
        <CommonModal
          isOpen={isCommonModalOpen}
          widthSize="1000px"
          draggable={false}
          onCloseModal={() =>
            dispatch(
              setCommonModal({
                isCommonModalOpen: false,
                selectedFileTitle: "",
                selectedFilePath: "",
              })
            )
          }
          modalBodyClass="p-0"
          header={{
            title: selectedFileTitle,
            closeIcon: true,
          }}
        >
          <div className="p-4 h-[calc(100vh-200px)]">
            <iframe
              id="documentViewer"
              data-src="'+zohodata['document_url']+'"
              src={(() => {
                const baseUrl = "https://drive.google.com/viewerng/viewer";
                const params = new URLSearchParams({
                  embedded: "true",
                  origin: window.ENV.PANEL_URL,
                  url: selectedFilePath,
                });

                return `${baseUrl}?${params.toString()}`;
              })()}
              style={{ width: "100%", height: "100%" }}
            />
          </div>
        </CommonModal>
      )}
      {isConfirmModalOpen && (
        <ConfirmModal
          isOpen={isConfirmModalOpen}
          isLoading={isFileLoader}
          // isNoLoading={isNoLoading}
          description={_t("Are you sure you want to delete this file?")}
          modalIcon="fa-regular fa-trash-can"
          modaltitle={_t("Delete")}
          yesButtonLabel={_t("Yes")}
          noButtonLabel={_t("No")}
          onAccept={handleAccept}
          onDecline={handleDecline}
          onCloseModal={() => {
            dispatch(setIsConfirmModalOpenRedux(false));
            dispatch(setImageDataRedux({}));
          }}
          zIndex={9999}
        />
      )}
      {sendEmailDrawerOpen && (
        <SendEmailDrawer
          projectId={(imageData as FilePhotoRightDetail).project_id}
          closeDrawer={() => {
            dispatch(setActiveField(CFConfig.employee_key));
            dispatch(setSendEmailDrawerOpenRedux(false));
            dispatch(setImageDataRedux({}));
          }}
          openSendEmailSidebar={sendEmailDrawerOpen}
          options={[
            CFConfig.employee_key,
            "my_crew",
            CFConfig.customer_key,
            CFConfig.misc_contact_key,
            CFConfig.contractor_key,
            CFConfig.vendor_key,
            "by_service",
            "my_project",
          ]}
          singleSelecte={false}
          emailApiCall={handleEmailApiCall}
          groupCheckBox={false}
          customEmailData={{
            subject: "Project Details",
            files: [imageData],
          }}
          validationParams={{
            save_a_copy_of_sent_pdf,
            date_format,
            file_support_module_access: checkGlobalModulePermissionByKey(
              CFConfig.file_support_key
            ),
            image_resolution,
            module_key: module_key || "",
            module_id: module_id || 0,
            module_access: module_access || undefined,
          }}
          isShowAddIcon={false}
        />
      )}
    </>
  );
};

export default FilePhotosProject;
