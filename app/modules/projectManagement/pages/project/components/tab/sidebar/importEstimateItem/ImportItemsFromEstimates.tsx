import debounce from "lodash/debounce";

// Hook
import { useTranslation } from "~/hook";

// Atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { SelectField } from "~/shared/components/molecules/selectField";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { RadioGroupList } from "~/shared/components/molecules/radioGroupList";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { type RadioChangeEvent } from "antd";
import {
  lazy,
  memo,
  Suspense,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";
import { useAppProSelector } from "../../../../redux/store";
import {
  addApprovedEstimatesItemsToProject,
  getApprovedEstimates,
  getApprovedEstimatesItems,
} from "../../../../redux/action/projectSovAction";
import { Number, sanitizeString } from "~/helpers/helper";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { filterOptionBySubstring } from "~/shared/utils/helper/common";
import { GridApi, GridReadyEvent, IRowNode } from "ag-grid-community";
import { SelectionChangedEvent } from "ag-grid-community";
import { CheckboxChangeEvent } from "antd/es/checkbox";

const StaticTable = lazy(() =>
  import("~/shared/components/molecules/staticTable").then((module) => ({
    default: module.StaticTable,
  }))
);

const ImportItemsFromEstimates = ({
  importEstItem,
  setImportEstItem,
  estimateItems,
  callSummaryApi,
  callProjectSOVItemsApi,
}: IImportItemsFromEstimatesProps) => {
  const { _t } = useTranslation();
  const { getGlobalModuleByKey } = useGlobalModule();
  const { formatter } = useCurrencyFormatter();
  const [selectedItems, setSelectedItems] = useState<
    IGetApprovedEstimatesItemsApiResSectionsItems[]
  >([]);

  const [value, setValue] = useState<string>("import_item");
  const [isAddItemIsLoading, setIsAddItemIsLoading] = useState(false);
  const [updateKey, setUpdateKey] = useState<number>(0);

  const moduleEst: IModule | undefined = getGlobalModuleByKey(
    CFConfig.estimate_module
  );

  const [confirmDialogOpen, setConfirmDialogOpen] = useState<boolean>(false);

  const { details } = useAppProSelector((state) => state.proDetails);
  const [listSection, setListSection] = useState<IgetApprovedEstimatesData[]>(
    []
  );
  const [isSubmit, setIsSubmit] = useState<boolean>(false);

  const [sectionCheckListItems, setSectionCheckListItems] = useState<
    IGetApprovedEstimatesItemsApiResSections[]
  >([]);
  const [selectTemplate, setSelectTemplate] = useState<string>("");
  const [isImportCheckListLoading, setIsImportCheckListLoading] =
    useState<boolean>(false);

  const [gridApiRef, setGridApiRef] = useState<
    Record<string, GridApi<IGetApprovedEstimatesItemsApiResSectionsItems>>
  >({});

  const allItems = useMemo(() => {
    return sectionCheckListItems.reduce(
      (
        acc: IGetApprovedEstimatesItemsApiResSectionsItems[],
        section: IGetApprovedEstimatesItemsApiResSections
      ) => {
        return acc.concat(section.items);
      },
      []
    );
  }, [sectionCheckListItems]);

  useEffect(() => {
    const getEstimateApprovedList = async () => {
      try {
        const { success, data, message } = (await getApprovedEstimates({
          limited_fields: 1,
          filter: {
            status: "0",
            project: !!details?.id
              ? details?.id?.toString() != "0"
                ? details?.id
                : undefined
              : undefined,
            approval_type: "estimate_approved,estimate_completed",
          },
          ignore_filter: 1,
        })) as IgetApprovedEstimatesApiRes;
        if (success) {
          setListSection(data?.estimates ?? []);
          if (data.estimates.length > 0) {
            setSelectTemplate(data.estimates[0]?.estimate_id?.toString());
          }
        } else {
          notification.error({
            description: message,
          });
        }
      } catch (error) {
        notification.error({
          description: "Something went wrong!",
        });
      }
    };
    getEstimateApprovedList();
  }, []);

  const columnDefs = useMemo(
    () => [
      {
        headerName: _t("Section") + " #",
        field: "custom_section_id",
        minWidth: 120,
        maxWidth: 120,
        suppressMenu: true,
        flex: 2,
        cellClass: "ag-cell-left",
        headerClass: "ag-header-left",
        cellRenderer: (params: {
          data: IGetApprovedEstimatesItemsApiResSections;
        }) => {
          const { data } = params;

          return data.custom_section_id ? (
            <Tooltip title={data.custom_section_id}>
              <Typography className="table-tooltip-text">
                {data.custom_section_id || "-"}
              </Typography>
            </Tooltip>
          ) : (
            "-"
          );
        },
      },
      {
        headerName: _t("Name"),
        field: "name",
        minWidth: 150,
        flex: 2,
        suppressMenu: true,
        cellClass: "ag-cell-left",
        headerClass: "ag-header-left",
        cellRenderer: (params: {
          data: IGetApprovedEstimatesItemsApiResSections;
        }) => {
          const { data } = params;

          return data.section_name ? (
            <Tooltip
              title={HTMLEntities.decode(sanitizeString(data.section_name))}
            >
              <Typography className="table-tooltip-text">
                {HTMLEntities.decode(sanitizeString(data.section_name)) || "-"}
              </Typography>
            </Tooltip>
          ) : (
            "-"
          );
        },
      },
      {
        headerName: _t("Total"),
        field: "total",
        minWidth: 120,
        maxWidth: 120,
        suppressMenu: true,
        cellClass: "ag-cell-right",
        headerClass: "ag-header-right",
        cellRenderer: (params: {
          data: IGetApprovedEstimatesItemsApiResSections;
        }) => {
          const total = params?.data?.section_total;
          return total ? (
            <>
              {Number(total ?? 0) > 0 ? (
                <Tooltip
                  title={`${
                    formatter((Number(total) / 100).toFixed(2))
                      .value_with_symbol
                  }`}
                >
                  <Typography className="table-tooltip-text">
                    {
                      formatter((Number(total) / 100).toFixed(2))
                        .value_with_symbol
                    }
                  </Typography>
                </Tooltip>
              ) : (
                <div>{formatter("0.00").value_with_symbol}</div>
              )}
            </>
          ) : (
            "-"
          );
        },
      },
    ],
    []
  );

  const sectionColumnDefs = useMemo(
    () => [
      {
        headerName: "",
        field: "checkbox",
        minWidth: 38,
        maxWidth: 38,
        suppressMenu: true,
        showDisabledCheckboxes: true,
        cellClass: "ag-cell-center",
        headerClass: "ag-header-center",
        flex: 2,
        checkboxSelection: (params: any) => {
          const node = params.node;
          let getCurrentSection = estimateItems?.find(
            (item) => item.reference_item_id == Number(node?.data?.item_id)
          )?.reference_item_id;

          return !getCurrentSection;
        },
      },
      {
        headerName: _t("Item Name"),
        field: "item_name",
        minWidth: 120,
        suppressMenu: true,
        flex: 2,
        cellClass: "ag-cell-left",
        headerClass: "ag-header-left",
        cellRenderer: (row: {
          data: IGetApprovedEstimatesItemsApiResSectionsItems;
        }) => {
          const item = row?.data?.subject;
          return item ? (
            <Tooltip title={HTMLEntities.decode(sanitizeString(item))}>
              <Typography className="table-tooltip-text">
                {HTMLEntities.decode(sanitizeString(item)) || "-"}
              </Typography>
            </Tooltip>
          ) : (
            "-"
          );
        },
      },
      {
        headerName: _t("QTY"),
        field: "qty",
        minWidth: 80,
        maxWidth: 80,
        suppressMenu: true,
        cellClass: "ag-cell-right",
        headerClass: "ag-header-right",
        cellRenderer: (row: {
          data: IGetApprovedEstimatesItemsApiResSectionsItems;
        }) => {
          const quantity = row?.data?.quantity;
          return quantity ? (
            <Tooltip title={quantity}>
              <Typography className="table-tooltip-text">
                {quantity || "-"}
              </Typography>
            </Tooltip>
          ) : (
            "-"
          );
        },
      },
      {
        headerName: _t("Unit Cost"),
        field: "unit_cost",
        minWidth: 120,
        maxWidth: 120,
        suppressMenu: true,
        cellClass: "ag-cell-right",
        headerClass: "ag-header-right",
        cellRenderer: (row: {
          data: IGetApprovedEstimatesItemsApiResSectionsItems;
        }) => {
          const unit_cost = row?.data?.unit_cost;
          return unit_cost ? (
            <>
              {Number(unit_cost ?? 0) > 0 ? (
                <Tooltip
                  title={`${
                    formatter((Number(unit_cost) / 100).toFixed(2))
                      .value_with_symbol
                  }`}
                >
                  <Typography className="table-tooltip-text">
                    {
                      formatter((Number(unit_cost) / 100).toFixed(2))
                        .value_with_symbol
                    }
                  </Typography>
                </Tooltip>
              ) : (
                <div>{formatter("0.00").value_with_symbol}</div>
              )}
            </>
          ) : (
            "-"
          );
        },
      },
      {
        headerName: _t("Total"),
        field: "total",
        minWidth: 120,
        maxWidth: 120,
        suppressMenu: true,
        cellClass: "ag-cell-right",
        headerClass: "ag-header-right",
        cellRenderer: (row: {
          data: IGetApprovedEstimatesItemsApiResSectionsItems;
        }) => {
          const total = row?.data?.total;
          return total ? (
            <>
              {Number(total ?? 0) > 0 ? (
                <Tooltip
                  title={`${
                    formatter((Number(total) / 100).toFixed(2))
                      .value_with_symbol
                  }`}
                >
                  <Typography className="table-tooltip-text">
                    {
                      formatter((Number(total) / 100).toFixed(2))
                        .value_with_symbol
                    }
                  </Typography>
                </Tooltip>
              ) : (
                <div>{formatter("0.00").value_with_symbol}</div>
              )}
            </>
          ) : (
            "-"
          );
        },
      },
    ],
    [estimateItems]
  );

  const selectedEstimateItems = useMemo(() => {
    const selected = allItems.filter((item) => {
      return estimateItems.some((eItem) => {
        return (
          eItem.reference_item_id === Number(item.item_id) &&
          eItem.reference_item_type_id === Number(item.reference_item_id)
        );
      });
    });

    return selected;
  }, [estimateItems, allItems, importEstItem]);

  useEffect(() => {
    if (selectedEstimateItems.length > 0) {
      setSelectedItems(selectedEstimateItems);
    }
  }, [selectedEstimateItems]);

  const estimateTemplateList = useMemo(
    () =>
      listSection?.map(
        (item: IImportItemsEstitmateApprovedSelectEstimateResponse) => {
          const customerName = HTMLEntities.decode(
            sanitizeString(item.customer_name)
          );
          const projectName = HTMLEntities.decode(
            sanitizeString(item.project_name)
          );

          // Apply condition for project_name starting with "-"
          let defaultLabel = `Est. #${item.company_estimate_id} `;
          if (!!customerName) {
            defaultLabel += `- ${customerName}`;
          }
          if (!!projectName) {
            defaultLabel += `- ${projectName}`;
          }

          return {
            label: defaultLabel,
            value: item.estimate_id.toString(),
          };
        }
      ),
    [listSection]
  );

  const getEstimateApprovedTableList = async (value: string) => {
    setIsImportCheckListLoading(true);
    try {
      const { success, data, message } = (await getApprovedEstimatesItems({
        estimate_id: value,
        module_id: Number(moduleEst?.module_id),
        need_section_response: 1,
      })) as IGetApprovedEstimatesItemsApiRes;
      if (success) {
        setSectionCheckListItems(data.sections);
      } else {
        notification.error({
          description: message,
        });
      }
    } catch (error) {
      notification.error({
        description: "Something went wrong!",
      });
    } finally {
      setIsImportCheckListLoading(false);
    }
  };

  useEffect(() => {
    if (selectTemplate !== "") {
      getEstimateApprovedTableList(selectTemplate);
    }
  }, [selectTemplate]);

  const onChange = async (value: string | string[]) => {
    if (!!value) {
      setSelectTemplate(value as string);
      // getEstimateApprovedTableList(value as string);
    } else {
      setSelectTemplate("");
      setSectionCheckListItems([]);
    }
  };

  const handleSelectionChanged = useCallback(
    debounce((event: SelectionChangedEvent) => {
      const selectedNodes = event.api.getSelectedNodes();
      const unSelectedNodes = event.api
        .getRenderedNodes()
        .filter((node: IRowNode) => node.isSelected() === false)
        .map((node) => node.data);

      const selected = selectedNodes.map((node: IRowNode) => node.data);

      setSelectedItems((prev) => {
        const updatedSelection = [
          ...prev,
          ...selected.filter(
            (newItem) => !prev.some((item) => item.item_id === newItem.item_id)
          ),
        ];
        const selectedItem = updatedSelection.filter(
          (node) =>
            !unSelectedNodes.some((item) => item.item_id === node.item_id)
        );

        return [...selectedItem];
      });
    }, 50),
    []
  );

  const handleSectionSelectionChanged = (
    e: CheckboxChangeEvent,
    rowData: IGetApprovedEstimatesItemsApiResSectionsItems[],
    sectionId: string
  ) => {
    setUpdateKey((prev) => prev + 1);

    const gridToConsider = gridApiRef[sectionId];
    if (!gridToConsider) return;

    if (e.target.checked) {
      setSelectedItems((prev) => {
        const updatedSelection = [
          ...prev,
          ...rowData.filter(
            (newItem) => !prev.some((item) => item.item_id === newItem.item_id)
          ),
        ];
        return updatedSelection;
      });

      rowData.forEach((row) => {
        const node = gridToConsider.getRowNode(String(row.item_id));
        if (node) {
          node.setSelected(true, false); // suppress event
          // gridToConsider.ensureNodeVisible(node);
        }
      });
    } else {
      setSelectedItems((prev) => {
        const updatedSelection = prev.filter(
          (item) => !rowData.some((row) => row.item_id === item.item_id)
        );
        return updatedSelection;
      });

      rowData.forEach((row) => {
        const getCurrentSection = estimateItems?.find(
          (item) => item.reference_item_id === Number(row.item_id)
        )?.reference_item_id;

        const node = gridToConsider.getRowNode(String(row.item_id));
        if (node && !getCurrentSection) {
          node.setSelected(false, false); // suppress event
        }
      });
    }
  };

  function areItemsSame(
    arr1: IGetApprovedEstimatesItemsApiResSectionsItems[],
    arr2: IGetApprovedEstimatesItemsApiResSectionsItems[]
  ) {
    if (arr1.length !== arr2.length) return false; // Check length first

    const itemIds1 = new Set(arr1.map((item) => item.item_id));
    const itemIds2 = new Set(arr2.map((item) => item.item_id));

    return (
      itemIds1.size === itemIds2.size &&
      [...itemIds1].every((id) => itemIds2.has(id))
    );
  }

  const onGridReady = useCallback(
    (params: GridReadyEvent, sectionId: string) => {
      const gridApi = params.api;

      setGridApiRef((prev) => ({
        ...prev,
        [sectionId]: gridApi,
      }));

      const section = sectionCheckListItems.find(
        (s) => s.section_id === sectionId
      );
      if (!section) return;

      // Deselect all first
      gridApi.deselectAll();

      selectedEstimateItems.forEach((row) => {
        if (row.section_id === sectionId) {
          const node = gridApi.getRowNode(String(row.item_id));
          if (node) {
            node.setSelected(true, false); // suppressEvent = true to avoid recursion
          }
        }
      });
    },
    [sectionCheckListItems, selectedEstimateItems]
  );

  const handleAddEstiamteItemsToProject = async () => {
    setIsAddItemIsLoading(true);
    setIsSubmit(true);
    const itemsToBeAdd = selectedItems.filter(
      (jobItem) =>
        !(estimateItems || []).some((item) => {
          return Number(item.reference_item_id) === Number(jobItem.item_id);
        })
    );
    const itemsToBeAddIds = itemsToBeAdd.map((item) => item.item_id);

    if (
      (value === "import_sec" || itemsToBeAddIds.length > 0) &&
      selectTemplate &&
      selectTemplate !== ""
    ) {
      try {
        let formdata: IAddApprovedEstimatesItemsToProjectApiParams = {
          estimate_id: selectTemplate,
        };

        if (value === "import_sec") {
          formdata.project_id = Number(details.id);
          formdata.items_from = "section";
          formdata.from_section = 0;
        } else {
          formdata.project_id = Number(details.id);
          formdata.items_from = "items";
          formdata.item_ids = itemsToBeAddIds.join(",");
          formdata.from_section = 1;
        }

        const resdata = (await addApprovedEstimatesItemsToProject({
          ...formdata,
        })) as DefaultResponse;

        if (resdata.success) {
          await Promise.all([callSummaryApi(), callProjectSOVItemsApi()]);
          setIsSubmit(false);
          setConfirmDialogOpen(false);
          setIsAddItemIsLoading(false);
          setImportEstItem(false);
        } else {
          notification.error({
            description: resdata.message || "Something went Wrong!",
          });
          setIsAddItemIsLoading(false);
          setIsSubmit(false);
          setConfirmDialogOpen(false);
        }
      } catch (error) {
        notification.error({
          description: (error as Error).message || "Something went Wrong!",
        });
        setIsAddItemIsLoading(false);
        setImportEstItem(false);
        setIsSubmit(false);
        setConfirmDialogOpen(false);
      }
    } else {
      notification.error({
        description: "Please select atleast one item.",
      });
      setIsAddItemIsLoading(false);
      setConfirmDialogOpen(false);
      setIsSubmit(false);
    }
  };

  const emptyTable = useMemo(() => {
    return (
      <div className="p-2 common-card">
        <div className="ag-theme-alpine h-full max-h-[calc(100vh-250px)] overflow-auto">
          <StaticTable
            key="empty_table"
            className="static-table mb-1.5"
            columnDefs={sectionColumnDefs}
            rowData={[]}
            noRowsOverlayComponent={() => (
              <NoRecords
                image={`${window.ENV.CDN_URL}assets/images/no-records-expenses.svg`}
              />
            )}
          />
        </div>
      </div>
    );
  }, []);

  return (
    <>
      <Drawer
        open={importEstItem}
        rootClassName="drawer-open"
        width={718}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-calculator"
              />
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {_t(`Import Items from ${moduleEst?.module_name}`)}
            </Header>
          </div>
        }
        closeIcon={
          <CloseButton
            onClick={() => {
              setImportEstItem(false);
              setIsSubmit(false);
              setSelectedItems(selectedEstimateItems);
            }}
          />
        }
      >
        <form className="py-4">
          <div className="sidebar-body overflow-y-auto sm:h-[calc(100vh-132px)] h-[calc(100vh-173px)] px-4">
            <div className="grid gap-4">
              <SidebarCardBorder addGap={true}>
                <div className="grid gap-3.5">
                  <div className="w-full">
                    <SelectField
                      label={_t(
                        `${HTMLEntities.decode(
                          sanitizeString(moduleEst?.module_name)
                        )}`
                      )}
                      placeholder={`Select ${moduleEst?.module_name} (If Any)`}
                      labelPlacement="top"
                      isRequired={true}
                      value={selectTemplate}
                      options={estimateTemplateList}
                      showSearch
                      filterOption={(input, option) =>
                        filterOptionBySubstring(input, option?.label as string)
                      }
                      allowClear
                      onChange={onChange}
                      errorMessage={
                        isSubmit
                          ? !selectTemplate
                            ? "This field is required."
                            : ""
                          : ""
                      }
                    />
                  </div>
                  <div className="w-full">
                    <RadioGroupList
                      key={updateKey}
                      onChange={(e: RadioChangeEvent) => {
                        setValue(e.target.value);
                        setSelectedItems(selectedEstimateItems);
                        setUpdateKey((prev) => prev + 1);
                      }}
                      formInputClassName="!p-0"
                      view="row"
                      value={value}
                      options={[
                        {
                          label: (
                            <div className="flex items-center">
                              <Typography className="text-primary-900">
                                {_t("Import SECTIONS into the SOV")}
                              </Typography>
                              <Tooltip
                                title={_t(
                                  "Importing the section into the SOV as a single item is a great way to mask the costs of the individual items that make up the Estimate while still maintaining the correct amount."
                                )}
                                className="h-3.5 w-3.5 cursor-pointer"
                              >
                                <FontAwesomeIcon
                                  icon="fa-regular fa-circle-info"
                                  className="text-primary-900 ml-1.5"
                                />
                              </Tooltip>
                            </div>
                          ),
                          value: "import_sec",
                        },
                        {
                          label: "Import ITEMS into the SOV",
                          value: "import_item",
                        },
                      ]}
                    />
                  </div>
                  <div
                    className={`transition-all duration-300 ease-in-out mt-2.5 ${
                      importEstItem ? "block" : "hidden"
                    }`}
                  >
                    <Suspense
                      fallback={
                        <div className="h-[150px] flex items-center justify-center">
                          <Spin />
                        </div>
                      }
                    >
                      {!!selectTemplate ? (
                        isImportCheckListLoading ? (
                          <Spin className="w-full h-[150px] flex items-center justify-center" />
                        ) : value === "import_sec" ? (
                          <div className="p-2 common-card">
                            <div className="ag-theme-alpine customs-table-height customs-vertical-scroll-auto max-h-[680px]">
                              <StaticTable
                                key="import_sec_table"
                                className="static-table checkbox-ml-none"
                                rowSelection={"multiple"}
                                columnDefs={columnDefs}
                                rowData={sectionCheckListItems}
                                rowBuffer={10}
                                cacheBlockSize={50}
                                suppressMovableColumns={true}
                                getRowId={(params) => params.data?.section_id}
                                noRowsOverlayComponent={() => (
                                  <NoRecords
                                    image={`${window.ENV.CDN_URL}assets/images/no-records-expenses.svg`}
                                  />
                                )}
                              />
                            </div>
                          </div>
                        ) : !!sectionCheckListItems?.length ? (
                          sectionCheckListItems?.map(
                            (
                              sections: IGetApprovedEstimatesItemsApiResSections,
                              index: number
                            ) => {
                              const isAllItemsSelected = areItemsSame(
                                sections.items,
                                selectedItems.filter(
                                  (item) =>
                                    item.section_id.toString() ===
                                    sections.section_id.toString()
                                )
                              );

                              const checkBoxDisabled = areItemsSame(
                                sections.items,
                                selectedEstimateItems.filter(
                                  (item) =>
                                    item.section_id.toString() ===
                                    sections.section_id.toString()
                                )
                              );

                              return (
                                <div
                                  className={`flex flex-col gap-2 ${
                                    index !== 0 && "mt-3"
                                  }`}
                                  key={sections.section_id}
                                >
                                  <div className="flex items-center">
                                    <CheckBox
                                      key={updateKey}
                                      checked={
                                        !sections.items.length
                                          ? false
                                          : isAllItemsSelected
                                      }
                                      disabled={
                                        checkBoxDisabled ||
                                        Number(sections.is_optional_section) ===
                                          1
                                      }
                                      className="gap-1.5"
                                      onChange={(e) => {
                                        handleSectionSelectionChanged(
                                          e,
                                          sections.items,
                                          sections.section_id
                                        );
                                      }}
                                    >
                                      {_t(sections.section_name)}
                                    </CheckBox>
                                    {Number(sections.is_optional_section) ===
                                    1 ? (
                                      <Tooltip
                                        title={_t(
                                          "This is an Optional section and will not be included in the SOV"
                                        )}
                                        className="h-3.5 w-3.5 cursor-pointer"
                                      >
                                        <FontAwesomeIcon
                                          icon="fa-regular fa-circle-info"
                                          className="text-primary-900 ml-1.5"
                                        />
                                      </Tooltip>
                                    ) : (
                                      ""
                                    )}
                                  </div>
                                  <div className="p-2 common-card">
                                    <div className="ag-theme-alpine customs-table-height customs-vertical-scroll-auto max-h-[680px]">
                                      <StaticTable
                                        key={sections.section_id}
                                        className="static-table checkbox-ml-none"
                                        rowSelection={"multiple"}
                                        columnDefs={sectionColumnDefs}
                                        rowMultiSelectWithClick={true}
                                        suppressRowClickSelection={true}
                                        onSelectionChanged={
                                          handleSelectionChanged
                                        }
                                        onGridReady={(params) =>
                                          onGridReady(
                                            params,
                                            sections.section_id
                                          )
                                        }
                                        isRowSelectable={() => true}
                                        rowData={
                                          Number(
                                            sections.is_optional_section
                                          ) === 0
                                            ? sections.items
                                            : []
                                        }
                                        rowBuffer={10}
                                        cacheBlockSize={50}
                                        suppressMovableColumns={true}
                                        getRowId={(params) =>
                                          String(params.data?.item_id)
                                        }
                                      />
                                    </div>
                                  </div>
                                </div>
                              );
                            }
                          )
                        ) : (
                          emptyTable
                        )
                      ) : (
                        emptyTable
                      )}
                    </Suspense>
                  </div>
                </div>
              </SidebarCardBorder>
            </div>
          </div>
          <div className="sidebar-footer w-full px-4 pt-4">
            <PrimaryButton
              htmlType="button"
              disabled={
                isAddItemIsLoading ||
                (value === "import_item" && !selectedItems.length) ||
                (value === "import_sec" && !selectTemplate)
              }
              loading={isAddItemIsLoading}
              onClick={() => {
                if (selectTemplate) {
                  setConfirmDialogOpen(true);
                } else {
                  setIsSubmit(true);
                }
              }}
              buttonText={_t(`Add Items to List`)}
            />
          </div>
        </form>
      </Drawer>

      {confirmDialogOpen && (
        <ConfirmModal
          isOpen={confirmDialogOpen}
          modaltitle={_t("Confirmation")}
          description={_t(
            "Are you sure you want to add these items to Schedule of Values?"
          )}
          isLoading={isAddItemIsLoading}
          modalIcon="fa-regular fa-file-check"
          onAccept={() => {
            handleAddEstiamteItemsToProject();
          }}
          onDecline={() => setConfirmDialogOpen(false)}
          onCloseModal={() => setConfirmDialogOpen(false)}
        />
      )}
    </>
  );
};

export default memo(ImportItemsFromEstimates);
