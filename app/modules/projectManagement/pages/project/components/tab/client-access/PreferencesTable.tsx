import groupBy from "lodash/groupBy";

// atoms
import { <PERSON><PERSON> } from "~/shared/components/atoms/button";
import { Toolt<PERSON> } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { SelectField } from "~/shared/components/molecules/selectField";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
// Hook
import { useTranslation } from "~/hook";
import { ITEM_SHOWN_OPTION } from "../../../utils/constants";
import { useAppProDispatch, useAppProSelector } from "../../../redux/store";
import { useProjectDetail } from "../../../hook/useProjectsDetails";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useBoolean } from "../../../hook/use-boolean";
import { Number, sanitizeString } from "~/helpers/helper";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import {
  getUpdatedProjectDetails,
  setDefaultSettingSaved,
  updateProjectDetail,
} from "../../../redux/slices/proDetailsSlice";
import {
  saveClientPortalModuleSettings,
  updateClientAccessModuleData,
} from "../../../redux/action/projectClientAccessAction";
import { pdfTemplateActions } from "~/redux/action/pdfTemplateActions";
import {
  filterOptionBySubstring,
  getStatusForField,
} from "~/shared/utils/helper/common";
import { CheckboxChangeEvent } from "antd/es/checkbox";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { getGModuleDashboard } from "~/zustand";
import { ICellRendererParams } from "ag-grid-community";

const moduleKeys = [
  "bills",
  "change_orders",
  "create_new_co",
  "daily_logs",
  "documents",
  "estimates",
  "expenses",
  "financial_summery",
  "inspections",
  "invoice_merge",
  "messaging",
  "notes",
  "project_permits",
  "correspondences",
  "gantt_chart",
  "submittals",
  "todos",
  "work_orders",
];

const PreferencesTable: React.FC<IPrefsTableProps> = ({
  isClientAccessEnabled,
  shouldSavePrefsToSettingsBtnShow,
}) => {
  const { _t } = useTranslation();

  const { details, defaultSettingSaved } = useAppProSelector(
    (state) => state.proDetails
  );
  const { getGlobalModuleByKey } = useGlobalModule();

  const currentMenuModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();

  const { module_id = 0, module_key = "projects" } = currentMenuModule || {};

  const gModuleDashboard: Partial<GModuleDashboard> = getGModuleDashboard();
  const cPortalAccessSetting: Array<any> | undefined =
    gModuleDashboard?.module_setting?.project_client_access_settings;

  const { isReadOnly, handleUpdateField, loadingStatus } = useProjectDetail();

  const dispatch = useAppProDispatch();
  const { onTrue, onFalse, bool } = useBoolean();

  const [loadingKey, setLoadingKey] =
    useState<Record<string | number, boolean>>();

  const [templateOptions, setTemplateOptions] = useState<{
    custom: Array<ITemplateOpts>;
    default: Array<ITemplateOpts>;
  }>({
    custom: [],
    default: [],
  });

  const templateCacheRef = useRef<
    Record<string, { template_id: string; pdf_key: string }>
  >({});

  const clientAcceessModules = useMemo(() => {
    const updatedDetails = dispatch(getUpdatedProjectDetails());
    return updatedDetails?.client_access ?? [];
  }, [details]);

  const createUniqueValue = (template: any) => {
    return template?.template_id
      ? `${template?.template_id}-${template?.pdf_value}`
      : `${template?.company_template_id}-${template?.pdf_value}`;
  };

  const fetchTemplates = async () => {
    const response = (await pdfTemplateActions({
      module_id: [4, 15, 70, 11, 62, 78, 12],
      status: 1,
      from_client_access: 1,
    })) as IPDFFileTemplateRes;

    const getOptions = (templates: IPDFFileTemplateData[]) => {
      const groupedByModuleId = groupBy(templates, "module_id");

      const opts = Object.keys(groupedByModuleId).reduce((acc, mId) => {
        const data = groupedByModuleId[mId];
        acc.push({
          module_id: mId,
          options: data.map((item: Partial<IPDFFileTemplateData>) => {
            const uniqueValue = createUniqueValue(item);
            return {
              value: uniqueValue ?? "",
              label: _t(
                HTMLEntities.decode(sanitizeString(item.template_name))
              ),
            };
          }),
        });

        return acc;
      }, [] as ITemplateOpts[]);

      return opts;
    };

    if (response.success) {
      const customizedTemplates = getOptions(response?.customize_tpl);
      const defaultTemplates = getOptions(response?.default_tpl);

      setTemplateOptions({
        custom: customizedTemplates,
        default: defaultTemplates,
      });
    } else {
      notification.error({
        description: response.message || "Failed to fetch templates",
      });
    }
  };

  useEffect(() => {
    fetchTemplates();
  }, []);

  const staticModuleKeys = [
    "create_new_co",
    "correspondences",
    "gantt_chart",
    "documents",
    "financial_summery",
    "messaging",
  ];

  const mapWithModulekey: Record<string, { mName: string; itemShown: string }> =
    useMemo(() => {
      return {
        bills: { mName: CFConfig.bill_module, itemShown: "Shared with client" },
        change_orders: {
          mName: CFConfig.change_order_module,
          itemShown: "Pending Approval, Unbilled/Approved, Billed",
        },
        create_new_co: {
          mName: "Create a New CO",
          itemShown: "Allows Client to initiate a new CO",
        },
        correspondences: {
          mName: "RFI",
          itemShown: "Submitted, Closed",
        },
        daily_logs: {
          mName: CFConfig.daily_log_module,
          itemShown: "All Items",
        },
        documents: {
          // mName: CFConfig.document_writer_module,
          mName: "Documents",
          itemShown: "All Items",
        },
        estimates: {
          mName: CFConfig.estimate_module,
          itemShown: "Pending Approval, Approved",
        },
        expenses: {
          mName: CFConfig.expense_module,
          itemShown: "Shared with client",
        },
        financial_summery: {
          // mName: CFConfig.financial_setting_module,
          mName: "Financial Summary",
          itemShown: "Show/Hide Financial Summary",
        },
        inspections: {
          mName: CFConfig.inspection_module,
          itemShown: "Shared with client",
        },
        invoice_merge: {
          mName: CFConfig.invoice_merge_module_key,
          itemShown: "Submitted, Approved, Paid",
        },
        messaging: {
          // mName: CFConfig.project_messaging_module,
          mName: "Messaging",
          itemShown: "All client messages",
        },
        notes: {
          mName: CFConfig.notes_module,
          itemShown: "Shared with Client",
        },
        project_permits: {
          mName: CFConfig.project_permit_module,
          itemShown: "All Items",
        },
        gantt_chart: {
          // mName: CFConfig.gantt_chart_module,
          mName: "Schedule",
          itemShown: "Project schedule",
        },
        submittals: {
          mName: CFConfig.submittal_module,
          itemShown: "Submitted, Approved",
        },
        todos: {
          mName: CFConfig.todo_module,
          itemShown: "Shared with client (regardless of Assigned To)",
        },
        work_orders: {
          mName: CFConfig.work_order_module,
          itemShown: "Submitted, Approved, Complete",
        },
      };
    }, []);

  const rowDataForTable = useMemo(() => {
    const data = moduleKeys.map((key) => {
      const module = clientAcceessModules?.find(
        (item) => item.module_key === key
      );

      const mdl: IModule | undefined = getGlobalModuleByKey(
        mapWithModulekey[key].mName
      );

      return {
        ...module,
        module_display: staticModuleKeys.includes(key)
          ? mapWithModulekey[key].mName
          : mdl?.plural_name || "",
        item_shown: mapWithModulekey[key].itemShown || "",
        module_key: module?.module_key || key || "",
      };
    });

    return data;
  }, [moduleKeys, clientAcceessModules, mapWithModulekey]);

  useEffect(() => {
    if (clientAcceessModules?.length) {
      onTrue();
    }
  }, [clientAcceessModules]);

  const updateClientModuleAccessData = useCallback(
    async (data: Partial<TClientAccessModules>) => {
      setLoadingKey((prev) => ({ ...prev, [data?.module_key ?? ""]: true }));

      let cData = {
        module_key: data?.module_key,
        portal_access_id: data?.portal_access_id,
        is_access: Boolean(data?.is_access),
        template_id: data?.template_id,
        pdf_template: data?.template_id,
        pdf_key: data?.pdf_key,
        mdl_additional_options: data?.mdl_additional_options,
      };

      const updatedClientAccess = clientAcceessModules?.map((c) => {
        if (
          c.portal_access_id?.toString() === data.portal_access_id?.toString()
        ) {
          return {
            ...c,
            ...cData,
          };
        }
        return c;
      });

      dispatch(updateProjectDetail({ client_access: updatedClientAccess }));

      const payload = {
        client_access_json: JSON.stringify(cData),
        module_key: module_key,
      };

      const updateRes = (await updateClientAccessModuleData(
        details?.id?.toString() ?? "",
        payload
      )) as IUpdateClientPrefsApiRes;

      if (!updateRes.success) {
        notification.error({
          description: updateRes.message || "Faile to update",
        });
        dispatch(updateProjectDetail({ client_access: clientAcceessModules }));
      }

      if (updateRes.success) {
        if (data.module_key === "change_orders" && !data.is_access) {
          const newCOAccessModule = clientAcceessModules?.find(
            (module) => module.module_key === "create_new_co"
          );
          if (newCOAccessModule && newCOAccessModule.is_access) {
            let NewCOData = {
              module_key: newCOAccessModule?.module_key,
              portal_access_id: newCOAccessModule?.portal_access_id,
              is_access: Boolean(0),
              template_id: newCOAccessModule?.template_id,
              pdf_template: newCOAccessModule?.template_id,
              pdf_key: newCOAccessModule?.pdf_key,
              mdl_additional_options: newCOAccessModule?.mdl_additional_options,
            };
            const payloadNewCO = {
              client_access_json: JSON.stringify(NewCOData),
            };
            const updateNewCORes = await updateClientAccessModuleData(
              details?.id?.toString() ?? "",
              payloadNewCO
            );

            const updatedClientAccessWithNewCO = updatedClientAccess?.map(
              (c) => {
                if (
                  c.portal_access_id?.toString() ===
                  NewCOData.portal_access_id?.toString()
                ) {
                  return {
                    ...c,
                    ...NewCOData,
                  };
                }
                return c;
              }
            );

            dispatch(
              updateProjectDetail({
                client_access: updatedClientAccessWithNewCO,
              })
            );
          }
        } else {
          dispatch(
            updateProjectDetail({
              client_access: updateRes.data.modules,
            })
          );
        }
      }

      setLoadingKey((prev) => ({ ...prev, [data?.module_key ?? ""]: false }));
    },
    [details?.id, clientAcceessModules]
  );

  const newCoDisabled = useMemo(() => {
    const mainCo = clientAcceessModules?.find(
      (item) => item.module_key === "change_orders"
    );
    return Number(mainCo?.is_access) === 0;
  }, [clientAcceessModules]);

  var templatesShouldBeEnabled = useCallback((mKey: string) => {
    return [
      "daily_logs",
      "estimates",
      "invoice_merge",
      "change_orders",
      "work_orders",
      "bills",
      "expenses",
    ].includes(mKey);
  }, []);

  const getTemplateOptions = useCallback(
    (data: IClientAccessModuleCellRendereParams["data"]) => {
      const customOpts = (templateOptions.custom?.find(
        (item) => item.module_id?.toString() === data?.module_id?.toString()
      )?.options ?? []) as IOption[];

      const defaultOpts = (templateOptions.default?.find(
        (item) => item.module_id?.toString() === data?.module_id?.toString()
      )?.options ?? []) as IOption[];

      if (customOpts?.length === 0 && defaultOpts?.length === 0) return [];

      const options = [];

      if (customOpts.length > 0) {
        options.push({
          label: "My Customized Templates",
          title: "",
          options: [...customOpts],
        });
      }
      if (defaultOpts.length > 0) {
        options.push({
          label: "Original Templates",
          title: "",
          options: [...defaultOpts],
        });
      }

      return options;
    },
    [templateOptions]
  );

  const columnDefs = useMemo(
    () => [
      {
        headerName: _t("Modules to Display"),
        field: "module_display",
        minWidth: 200,
        resizable: true,
        suppressMovable: false,
        suppressMenu: true,
        headerClass: "ag-header-left",
        cellClass: "ag-cell-left",
        flex: 1,
        cellRenderer: (
          params: ICellRendererParams<
            TClientAccessModules & {
              module_display: string;
              item_shown: string;
            }
          >
        ) => {
          const { data } = params;

          const isNewCORow = data?.module_key === "create_new_co";

          const shouldDisable =
            (isNewCORow && newCoDisabled) ||
            !isClientAccessEnabled ||
            // loadingKey?.[data?.module_key] ||
            isReadOnly;

          return (
            <CustomCheckBox
              className={`gap-1.5 w-fit text-13 checkbox-padding-remove ${
                shouldDisable ? "opacity-50" : "opacity-100"
              }`}
              checked={Boolean(data?.is_access)}
              disabled={shouldDisable}
              onChange={(e) => {
                const nVal = e.target.checked;

                if (!nVal) {
                  templateCacheRef.current[data?.module_key ?? ""] = {
                    template_id: data?.template_id?.toString() ?? "",
                    pdf_key: data?.pdf_key ?? "",
                  };
                }

                const newData = {
                  ...data,
                  is_access: nVal ? 1 : 0,
                  template_id: nVal
                    ? templateCacheRef.current[data?.module_key ?? ""]
                        ?.template_id || ""
                    : "",
                  pdf_key: nVal
                    ? templateCacheRef.current[data?.module_key ?? ""]
                        ?.pdf_key || ""
                    : "",
                };

                params.node.setData(
                  newData as TClientAccessModules & {
                    module_display: string;
                    item_shown: string;
                  }
                );

                updateClientModuleAccessData(newData);
              }}
            >
              {_t(data?.module_display ?? "")}
            </CustomCheckBox>
          );
        },
      },
      {
        headerName: _t("Items Shown"),
        field: "item_shown",
        minWidth: 200,
        resizable: true,
        suppressMovable: false,
        suppressMenu: true,
        headerClass: "ag-header-left",
        cellClass: "ag-cell-left",
        flex: 2,
        cellRenderer: (params: IClientAccessModuleCellRendereParams) => {
          const { data } = params;

          const itemShown = _t(data?.item_shown ?? "");

          return itemShown ? (
            <div className="flex items-center gap-2">
              <Typography className="text-13">{_t(itemShown)}</Typography>

              {data?.module_key === "submittals" && (
                <SelectField
                  label=""
                  placeholder=""
                  labelPlacement="top"
                  fieldClassName="before:hidden items_shown_dropdown"
                  containerClassName="overflow-visible"
                  applyBorder={true}
                  className="border-select-filed rounded h-7 bg-white"
                  showSearch={false}
                  value={data?.mdl_additional_options}
                  disabled={isReadOnly || !isClientAccessEnabled}
                  popupClassName="w-[190px]"
                  formInputClassName="w-[190px] overflow-visible"
                  options={ITEM_SHOWN_OPTION}
                  onChange={(selectedVal) => {
                    const newData = {
                      ...data,
                      mdl_additional_options: selectedVal as string,
                    };

                    updateClientModuleAccessData(newData);
                  }}
                />
              )}
            </div>
          ) : (
            "-"
          );
        },
      },
      {
        headerName: _t("Template/View to Display"),
        field: "template_view_display",
        minWidth: 300,
        maxWidth: 300,
        resizable: true,
        suppressMovable: false,
        suppressMenu: true,
        headerClass: "ag-header-left",
        cellClass: "ag-cell-left",
        cellRenderer: (params: IClientAccessModuleCellRendereParams) => {
          const { data } = params;

          let templateOpts: IOptionGroup[] = [];

          const isTemplateEnabled = templatesShouldBeEnabled(data?.module_key);
          const hasAccess = data?.is_access;

          if (isTemplateEnabled) {
            templateOpts = getTemplateOptions(data);
          }

          const currentValue = `${data?.template_id}-${data?.pdf_key}`;
          const isValidValue = templateOpts?.some((opt) =>
            opt?.options.some((o) => o.value === currentValue)
          );

          if (isTemplateEnabled && hasAccess) {
            return (
              <div className="flex items-center gap-2">
                <SelectField
                  label=""
                  placeholder={_t("Select Template to Use")}
                  labelPlacement="top"
                  fieldClassName="before:hidden template_select_dropdown"
                  value={isValidValue ? currentValue : undefined}
                  disabled={isReadOnly || !isClientAccessEnabled}
                  applyBorder={true}
                  className="border-select-filed rounded h-7"
                  showSearch={true}
                  filterOption={(input, option) =>
                    filterOptionBySubstring(input, option?.label as string)
                  }
                  allowClear
                  popupClassName="min-w-[255px] template_select_dropdown_option"
                  formInputClassName="min-w-[255px] overflow-visible"
                  options={templateOpts}
                  onChange={(selectedItem) => {
                    if (!selectedItem) {
                      updateClientModuleAccessData({
                        ...data,
                        template_id: "",
                        pdf_key: "",
                      });
                      return;
                    }

                    const val = selectedItem as string;
                    const [templateId, pdfKey] = val.split("-");

                    updateClientModuleAccessData({
                      ...data,
                      template_id: templateId || "",
                      pdf_key: pdfKey || "",
                    });
                  }}
                />
                <Tooltip
                  title={_t(
                    "Select the template based on the view that you want your client to see. Templates can be managed in Company Settings."
                  )}
                  placement="top"
                >
                  <FontAwesomeIcon
                    className="w-[15px] h-[15px] text-primary-900"
                    icon="fa-regular fa-info-circle"
                  />
                </Tooltip>
              </div>
            );
          } else {
            if (!isTemplateEnabled) {
              return (
                <Typography className="text-center text-[212559] w-[180px]">
                  -
                </Typography>
              );
            }
          }

          return <div className="w-[180px]">&nbsp;</div>;
        },
      },
    ],
    [
      isReadOnly,
      newCoDisabled,
      loadingKey,
      isClientAccessEnabled,
      getTemplateOptions,
    ]
  );
  const disableField = useMemo(() => {
    const isPhoneDisabled =
      getStatusForField(loadingStatus, "show_client_phone") === "loading";
    const isEmailDisabled =
      getStatusForField(loadingStatus, "show_client_email") === "loading";

    return { isPhoneDisabled, isEmailDisabled };
  }, [loadingStatus]);

  const handleChangeCheckbox = (e: CheckboxChangeEvent, field: string) => {
    const newVal = e.target.checked ? 1 : 0;
    handleUpdateField({
      data_to_send_in_api: {
        [field]: newVal,
      },
    });
  };

  const shouldDisableTopbarCheckbox = useMemo(() => {
    return isReadOnly || !isClientAccessEnabled;
  }, [isReadOnly, isClientAccessEnabled]);

  const handleSaveDefaultSettings = useCallback(async () => {
    setLoadingKey((prev) => ({ ...prev, saveDefaultSettings: true }));
    const payload = {
      module_id: Number(module_id) ?? 0,
      show_phone_to_client_portal: Number(details?.show_client_phone),
      show_email_to_client_portal: Number(details?.show_client_email),
      client_access_ary: rowDataForTable.map((item) => {
        const clientModule = cPortalAccessSetting?.find(
          (m) => m.module_key === item.module_key
        );

        return {
          module_key: item.module_key ?? "",
          preference_id: Number(clientModule?.preference_id ?? ""),
          is_access: Number(item.is_access),
          pdf_template: Number(item.template_id),
          pdf_key: item.pdf_key ?? "",
        };
      }),
    };

    const saveSettingsRes = await saveClientPortalModuleSettings(payload);

    if (!saveSettingsRes.success) {
      notification.error({
        description: saveSettingsRes.message || "Failed to save settings",
      });
    } else {
      dispatch(setDefaultSettingSaved(true));
    }

    setLoadingKey((prev) => ({ ...prev, saveDefaultSettings: false }));
  }, [rowDataForTable, cPortalAccessSetting, module_id]);

  return (
    <>
      <CollapseSingleTable
        title={_t("Preferences")}
        rightsideContant={
          shouldSavePrefsToSettingsBtnShow && !defaultSettingSaved ? (
            <Tooltip
              title={_t(
                "Default values can be changed later within Project > Settings > Client Portal Preferences."
              )}
            >
              <Button
                onClick={handleSaveDefaultSettings}
                className="hover:!border-primary-900 hover:!text-primary-900 active:!border-primary-900 active:!text-primary-900 gap-2 !text-[12px]"
                disabled={
                  isReadOnly ||
                  !isClientAccessEnabled ||
                  loadingKey?.saveDefaultSettings
                }
              >
                {_t("Save as Default Settings")}
                {loadingKey?.saveDefaultSettings && (
                  <FontAwesomeIcon
                    className="w-3.5 h-3.5 fa-spin"
                    icon={"fa-duotone fa-solid fa-spinner-third"}
                  />
                )}
              </Button>
            </Tooltip>
          ) : (
            ""
          )
        }
        activeKey={bool ? ["1"] : []}
        onChange={(val) => {
          val.length ? onTrue() : onFalse();
        }}
        children={
          <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
            <div className="flex items-center justify-between gap-2 flex-wrap">
              <div>
                <Typography className="text-sm font-semibold text-primary-900 mr-1">
                  {_t("Project Manager:")}
                </Typography>
                <Typography className="text-sm text-primary-900">
                  {_t(
                    HTMLEntities.decode(
                      sanitizeString(details?.project_manager_name ?? "")
                    )
                  ) || "-"}
                </Typography>
              </div>
              <div className="flex items-center gap-2">
                <CustomCheckBox
                  className={`gap-1.5 ${
                    shouldDisableTopbarCheckbox ? "opacity-50" : "opacity-100"
                  }`}
                  name="show_client_phone"
                  checked={Boolean(details.show_client_phone)}
                  onChange={(e) => handleChangeCheckbox(e, "show_client_phone")}
                  disabled={
                    shouldDisableTopbarCheckbox || disableField.isPhoneDisabled
                  }
                  loadingProps={{
                    isLoading:
                      getStatusForField(loadingStatus, "show_client_phone") ===
                      "loading",
                    className: "bg-[#ffffff]",
                  }}
                >
                  {_t("Show Phone")}
                </CustomCheckBox>
                <CustomCheckBox
                  className={`gap-1.5 ${
                    shouldDisableTopbarCheckbox ? "opacity-50" : "opacity-100"
                  }`}
                  name="show_client_email"
                  checked={Boolean(details.show_client_email)}
                  onChange={(e) => handleChangeCheckbox(e, "show_client_email")}
                  disabled={
                    shouldDisableTopbarCheckbox || disableField.isEmailDisabled
                  }
                  loadingProps={{
                    isLoading:
                      getStatusForField(loadingStatus, "show_client_email") ===
                      "loading",
                    className: "bg-[#ffffff]",
                  }}
                >
                  {_t("Show Email")}
                </CustomCheckBox>
              </div>
            </div>
            <div className="ag-theme-alpine mt-3">
              <StaticTable
                className="static-table preferences-table"
                columnDefs={columnDefs}
                getRowClass={() => "hover-table-row"}
                rowData={rowDataForTable}
                noRowsOverlayComponent={() => (
                  <NoRecords
                    image={`${window.ENV.CDN_URL}assets/images/no-records-preferences.svg`}
                  />
                )}
              />
            </div>
          </div>
        }
      />
    </>
  );
};

export default PreferencesTable;
