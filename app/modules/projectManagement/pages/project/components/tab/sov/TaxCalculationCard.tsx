// Hook
import { useTranslation } from "~/hook";
// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { CalcList } from "~/shared/components/molecules/calcList";
import { FieldLabel } from "~/shared/components/molecules/fieldLabel";
import SelectTaxRate from "~/shared/components/molecules/selectTaxRate/SelectTaxRate";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { useAppProSelector } from "../../../redux/store";
import { memo, useCallback, useMemo } from "react";
import { Number, sanitizeString } from "~/helpers/helper";
import { useProjectDetail } from "../../../hook/useProjectsDetails";
import {
  filterOptionBySubstring,
  getStatusForField,
} from "~/shared/utils/helper/common";
import { useSummary } from "./use-summary";
import { getGSettings } from "~/zustand";

const TaxCalculationCard: React.FC<TOtherItemsTableProps> = ({
  callSummaryApi,
}) => {
  const { _t } = useTranslation();
  const { formatter, unformatted } = useCurrencyFormatter();

  const { taxDetailsList } = useAppProSelector((state) => state.taxDetails);
  const { details } = useAppProSelector((state) => state.proDetails);
  const { taxable_total } = useAppProSelector((state) => state.proSov);
  const gSettings: GSettings = getGSettings();

  const { isReadOnly, loadingStatus, handleUpdateField } = useProjectDetail();

  const { valueToBeConsidered } = useSummary();

  const taxRateOptions = useMemo(() => {
    return (
      taxDetailsList?.map((t) => ({
        label: `${HTMLEntities.decode(sanitizeString(t.tax_name))} (${Number(
          t.tax_rate
        )}%)`,
        value: t.tax_id?.toString(),
        tax_rate : t.tax_rate,
      })) || []
    );
  }, [taxDetailsList]);

  const validTaxRateValue = useMemo(() => {
    return taxRateOptions?.find(
      (t) => t.value === details?.default_tax_rate_id?.toString()
    );
  }, [taxRateOptions, details?.default_tax_rate_id]);

  const taxName = useMemo(() => {
    return gSettings?.tax_format === "gst" ? "GST" : "Tax";
  }, [gSettings?.tax_format]);

  const tax_rate = useMemo(() => {
    const currentTaxObject = taxRateOptions?.find(
      (t) => Number(t.value) === Number(details.default_tax_rate_id)
    );
    const tax = Number(currentTaxObject?.tax_rate);
    return tax;
  }, [taxDetailsList, details.default_tax_rate_id]);

  const isReversal = useMemo(
    () => details?.is_prj_reversible_tax?.toString() === "1",
    [details?.is_prj_reversible_tax]
  );

  const calculateTaxAmount = useMemo(() => {
    const taxableTotal = Number(taxable_total);
    const tax_amount = (taxableTotal * tax_rate) / 100 / 100;
    return formatter(tax_amount?.toFixed(2));
  }, [tax_rate, taxable_total]);

  const calculateTotalAmount = useMemo(() => {
    const subTotal = Number(valueToBeConsidered?.sub_total) / 100;

    const taxAmount = Number(unformatted(calculateTaxAmount?.value));
    const reversalTaxAmount = isReversal ? -taxAmount : 0;

    const total = subTotal + taxAmount + reversalTaxAmount;
    return formatter(total?.toFixed(2)).value_with_symbol;
  }, [valueToBeConsidered, calculateTaxAmount, isReversal]);

  const taxChangeHanlder = useCallback(
    async (e: string | string[]) => {
      if (!Array.isArray(e)) {
        const value = e?.toString();

        if (value && value !== details?.tax_id?.toString()) {
          const tax = taxDetailsList.find(
            (o) => o.tax_id?.toString() === value
          );
          const label = tax?.tax_name;

          await handleUpdateField({
            data_to_send_in_api: {
              tax_id: Number(value),
              default_tax_rate_id: Number(value),
            },
            data_to_update_in_store: {
              prj_tax_name: label,
              default_tax_name : label,
              is_reversible: Number(tax?.is_reversible),
              is_prj_reversible_tax: Number(tax?.is_reversible),
            },
          });

          callSummaryApi();
        } else if (!value) {
          await handleUpdateField({
            data_to_send_in_api: {
              tax_id: "",
              default_tax_rate_id: "",
            },
            data_to_update_in_store: {
              prj_tax_name: "",
              is_reversible: 0,
              is_prj_reversible_tax: 0,
            },
          });
        }
      }
    },
    [details?.tax_id, taxDetailsList]
  );

  return (
    <div className="grid grid-cols-1 gap-3">
      <div className="grid grid-cols-1 gap-4">
        <div className="flex flex-col gap-[15px] py-[15px] px-5 ml-auto common-card lg:w-1/2 w-full">
          <div className="">
            <FieldLabel labelClass="pb-2.5 !w-full block !text-[13px]">
              {_t(`${taxName}/Amount`)}
            </FieldLabel>
            <ul className="grid gap-2.5">
              <li className="w-full">
                <div className="w-full">
                  <SelectTaxRate
                    value={
                      details?.default_tax_rate_id
                      ? validTaxRateValue
                        ? details.default_tax_rate_id.toString()
                        : undefined
                      : undefined
                    }
                    placeholder={`${_t(`Select ${taxName}`)}`}
                    readOnly={isReadOnly}
                    labelPlacement="left"
                    formInputClassName="bg-[#F8F8F8] tax-select-filed"
                    className="!text-[13px]"
                    allowClear
                    showSearch
                    disabled={isReadOnly}
                    options={taxRateOptions}
                    fixStatus={getStatusForField(loadingStatus, "tax_id")}
                    filterOption={(input, option) =>
                      filterOptionBySubstring(input, option?.label as string)
                    }
                    iconView={true}
                    onChange={taxChangeHanlder}
                  />
                </div>
              </li>
            </ul>
          </div>
          <ul className="sm:py-[5px] py-2 px-[15px] border border-[#ddd] rounded relative flex flex-col gap-1 w-full">
            <CalcList
              label={_t("Sub Total")}
              valueClassName="text-[#008000]"
              value={
                formatter(
                  (Number(valueToBeConsidered?.sub_total) / 100)?.toFixed(2)
                ).value_with_symbol
              }
            />
            <>
              <li>
                <ul className="md:py-0 py-0.5 relative">
                  <li className="w-5 h-5 flex items-center justify-center rounded-full shadow-[0px_0px_10px] !shadow-black/10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2">
                    <FontAwesomeIcon
                      className="w-3 h-3 text-primary-900 dark:text-white"
                      icon="fa-regular fa-plus"
                    />
                  </li>
                </ul>
              </li>
              <CalcList
                label={`${_t(taxName)}${
                  details?.default_tax_name
                    ? ": " +
                      `${HTMLEntities.decode(
                        sanitizeString(validTaxRateValue?.label)
                      )}`
                    : ""
                }`}
                valueClassName="text-[#008000]"
                value={`${calculateTaxAmount?.value_with_symbol}`}
              />
            </>
          </ul>
          {isReversal && (
            <ul className="sm:py-[5px] py-2 px-[15px] border border-[#ddd] rounded relative flex flex-col gap-1 w-full">
              <CalcList
                label={_t(
                  `Reversal ${taxName}: ${
                    details?.prj_tax_name
                      ? `${HTMLEntities.decode(
                          sanitizeString(details?.prj_tax_name)
                        )} (${tax_rate?.toFixed(2)}%)`
                      : ""
                  }`
                )}
                valueClassName="text-[#E25A32]"
                value={`${calculateTaxAmount?.value_with_symbol}`}
              />
              <div className="h-5 w-5 rounded-full bg-white absolute shadow-[0px_5px_10px_rgba(0,_0,_0,_0.15)] leading-5 text-center bottom-auto -top-[14px] left-0 right-0 mx-auto">
                <FontAwesomeIcon
                  className="w-3 h-3 text-primary-900"
                  icon="fa-regular fa-minus"
                />
              </div>
            </ul>
          )}
          <ul className="sm:py-[5px] py-2 px-[15px] rounded relative bg-primary-100 dark:bg-dark-900 w-full">
            <CalcList
              label={_t("Total")}
              labelClassName="font-semibold"
              value={calculateTotalAmount}
            />
          </ul>
        </div>
      </div>
    </div>
  );
};

export default memo(TaxCalculationCard);
