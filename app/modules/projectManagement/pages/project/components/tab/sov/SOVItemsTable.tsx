// React
import { memo, useCallback, useEffect, useMemo, useState } from "react";
// Hook
import { useTranslation } from "~/hook";

// Molecules
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { useAppProDispatch, useAppProSelector } from "../../../redux/store";
import { Number } from "~/helpers/helper";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import {
  deleteSOVItem,
  updateProjectItemTodb,
} from "../../../redux/action/projectSovAction";
import {
  updateSOVItems,
  updateWholeBudgetItems,
} from "../../../redux/slices/proSovSlice";
import { RowClassParams, RowStyle } from "ag-grid-community";
import { RowDragCallbackParams } from "ag-grid-community";
import { GridOptions } from "ag-grid-community";
import { IRowNode } from "ag-grid-community";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import {
  CostCellRenderer,
  InvoicedCellRenderer,
  ItemNameCellRenderer,
  MoveCellRenderer,
  MUCellRenderer,
  QtyCellRenderer,
  RemainCellRenderer,
  TaxCellRenderer,
  TotalCellRenderer,
  TypeCellRenderer,
  UnitCellRenderer,
} from "./other-items/CellRendererComps";
import { Spin } from "~/shared/components/atoms/spin";

const SOVItemsTable = ({
  items,
  setBudgetItem,
  setSOVItemsToBeView,
  noRecordImage,
  isRowSortable = false,
  isReadOnly,
  isEstimateSection = false,
  gridKey,
  handleDeleteClick,
}: ISOVItemsTableProps) => {
  const { _t } = useTranslation();
  const { formatter } = useCurrencyFormatter();

  const { details } = useAppProSelector((state) => state.proDetails);
  const { projectSOVItems } = useAppProSelector((state) => state.proSov);
  const dispatch = useAppProDispatch();

  const isPercentageBilling = useMemo(
    () => details.billing_option === "percentage",
    [details.billing_option]
  );

  const columnDefs = useMemo(() => {
    return [
      {
        headerName: "",
        field: "",
        maxWidth: 30,
        minWidth: 30,
        hide: isRowSortable,
        suppressMenu: true,
      },
      {
        headerName: "",
        maxWidth: 30,
        minWidth: 30,
        hide: !isRowSortable,
        field: "move",
        rowDrag: (params: RowDragCallbackParams) =>
          !params.node.rowPinned && !isReadOnly,
        cellClass: "ag-cell-center ag-move-cell custom-move-icon-set",
        suppressMenu: true,
        cellRenderer: MoveCellRenderer,
      },
      {
        headerName: _t("Type"),
        maxWidth: 50,
        minWidth: 50,
        field: "type",
        suppressMovable: false,
        suppressMenu: true,
        headerClass: "ag-header-center",
        cellClass: "ag-cell-center",
        cellRenderer: TypeCellRenderer,
        cellRendererParams: {
          formatter,
          _t,
        },
      },
      {
        headerName: _t("Item"),
        field: "item_name",
        minWidth: 190,
        flex: 2,
        resizable: true,
        suppressMovable: false,
        suppressMenu: true,
        headerClass: "ag-header-left",
        cellClass: "ag-cell-left",
        cellRenderer: ItemNameCellRenderer,
        cellRendererParams: {
          formatter,
          _t,
        },
      },
      {
        headerName: _t("QTY"),
        field: "qty",
        maxWidth: 80,
        minWidth: 80,
        suppressMovable: false,
        suppressMenu: true,
        headerClass: "ag-header-right",
        cellClass: "ag-cell-right",
        cellRenderer: QtyCellRenderer,
        cellRendererParams: {
          formatter,
        },
      },
      {
        headerName: _t("Cost"),
        field: "cost",
        maxWidth: 130,
        minWidth: 130,
        suppressMovable: false,
        suppressMenu: true,
        headerClass: "ag-header-right",
        cellClass: "ag-cell-right",
        cellRenderer: CostCellRenderer,
        cellRendererParams: {
          formatter,
        },
      },
      {
        headerName: _t("Unit"),
        field: "unit",
        maxWidth: 100,
        minWidth: 100,
        suppressMovable: false,
        suppressMenu: true,
        headerClass: "ag-header-left",
        cellClass: "ag-cell-left",
        cellRenderer: UnitCellRenderer,
        cellRendererParams: {
          formatter,
          _t,
        },
      },
      {
        headerName: _t("MU"),
        field: "markup",
        minWidth: 80,
        maxWidth: 80,
        suppressMovable: false,
        suppressMenu: true,
        cellClass: "ag-cell-right",
        headerClass: "ag-header-right",
        cellRenderer: MUCellRenderer,
        cellRendererParams: {
          formatter,
        },
      },
      {
        headerName: isEstimateSection ? _t("Billed") : _t("Invoiced"),
        field: "invoiced",
        minWidth: 160,
        maxWidth: 160,
        suppressMovable: false,
        suppressMenu: true,
        // cellClass: "ag-cell-right",
        headerClass: "ag-header-right",
        cellRenderer: InvoicedCellRenderer,
        cellRendererParams: { formatter, isPercentageBilling },
      },
      {
        headerName: _t("Remain"),
        field: "remain",
        minWidth: 160,
        maxWidth: 160,
        suppressMovable: false,
        suppressMenu: true,
        // cellClass: "ag-cell-right",
        headerClass: "ag-header-right",
        cellRenderer: RemainCellRenderer,
        cellRendererParams: {
          formatter,
          isPercentageBilling,
        },
      },
      {
        headerName: _t("Total"),
        field: "total",
        minWidth: 130,
        maxWidth: 130,
        suppressMovable: false,
        suppressMenu: true,
        cellClass: "ag-cell-right",
        headerClass: "ag-header-right",
        cellRenderer: TotalCellRenderer,
        cellRendererParams: {
          formatter,
        },
      },
      {
        headerName: _t("Tax"),
        field: "tax",
        minWidth: 50,
        maxWidth: 50,
        suppressMenu: true,
        headerClass: "ag-header-center",
        cellClass: "ag-cell-center pointer-events-none",
        cellRenderer: TaxCellRenderer,
        cellRendererParams: {
          disabled: true,
        },
      },
      {
        headerName: "",
        field: "",
        maxWidth: 80,
        minWidth: 80,
        suppressMenu: true,
        cellClass: "ag-cell-left",
        cellRenderer: (params: ISOVItemsTanleCellRenderer) => {
          const { data } = params;
          if (params.node.rowPinned) {
            return params.value;
          }

          return (
            <div className="flex items-center gap-1.5 justify-center">
              <ButtonWithTooltip
                tooltipTitle={_t("View")}
                tooltipPlacement="top"
                icon="fa-solid fa-eye"
                onClick={() => {
                  setBudgetItem(true);
                  setSOVItemsToBeView(data);
                }}
              />
              {!isReadOnly && Number(data.current_amount) / 100 === 0 && (
                <ButtonWithTooltip
                  tooltipTitle={_t("Delete")}
                  tooltipPlacement="top"
                  icon="fa-regular fa-trash-can"
                  onClick={() => handleDeleteClick?.(data?.item_id as number)}
                />
              )}
            </div>
          );
        },
      },
    ];
  }, [isRowSortable, isReadOnly, isPercentageBilling]);

  const computeTotalRow = useCallback(() => {
    let totalInvoiced = 0;
    let totalRemaining = 0;
    let subTotal = 0;

    items.forEach((item) => {
      totalInvoiced += Number(item?.current_amount) / 100;
      totalRemaining +=
        (Number(item?.total) - Number(item.current_amount)) / 100;
      subTotal += Number(item?.total) / 100;
    });

    return {
      markup: "Total",
      invoiced: formatter(
        Number(totalInvoiced || 0) !== 0
          ? Number(totalInvoiced)?.toFixed(2)
          : "0.00"
      ).value_with_symbol,
      remain: formatter(
        Number(totalRemaining || 0) !== 0
          ? Number(totalRemaining)?.toFixed(2)
          : "0.00"
      ).value_with_symbol,
      total: formatter(
        Number(subTotal || 0) !== 0 ? Number(subTotal)?.toFixed(2) : "0.00"
      ).value_with_symbol,
      isTotalRow: true,
    };
  }, [items]);

  const handleItemDragAndDrop = async (items: ISOVBudgetItemsData[]) => {
    const indexedItems = items.map(
      (row: ISOVBudgetItemsData, index: number) => ({
        ...row,
        item_no: index + 1,
      })
    );

    const remainItems = projectSOVItems.budget_items?.filter(
      (itm) =>
        !indexedItems.some((i) => Number(itm.item_id) === Number(i.item_id))
    );

    dispatch(updateWholeBudgetItems([...remainItems, ...indexedItems]));

    const projectItemsPayload = {
      need_update_item_no_only: 1,
      items: indexedItems,
    };

    const formData = getValuableObj(projectItemsPayload);

    const response = (await updateProjectItemTodb(
      Number(details.id),
      formData
    )) as IAddProjectItemApiRes;

    if (response?.success === false) {
      notification.error({
        description: response?.message || "Failed to reorder items",
      });
      dispatch(updateWholeBudgetItems(projectSOVItems.budget_items));
    }
  };

  const gridOptions: GridOptions = {
    stopEditingWhenCellsLoseFocus: true,
    onRowDragEnd: function (event) {
      // ag grid community provide "RowNode" But not working that's why use any
      const { node, overIndex } = event as {
        node: IRowNode;
        overIndex: number;
      };
      if (!gridOptions.api || !node) return;

      const rowData: ISOVBudgetItemsData[] = [];
      gridOptions.api.forEachNode((node) => rowData.push(node.data));

      if (rowData.length === 1) {
        return;
      } else {
        if (node.rowIndex !== undefined && node.rowIndex !== null) {
          rowData.splice(overIndex, 0, rowData.splice(node?.rowIndex, 1)[0]);
          handleItemDragAndDrop(rowData);
        }
      }
    },
  };

  return (
    <>
      <StaticTable
        key={gridKey || items?.[0]?.item_id?.toString()}
        className="static-table"
        columnDefs={columnDefs}
        gridOptions={gridOptions}
        rowData={items}
        rowDragManaged={isRowSortable}
        animateRows={isRowSortable}
        pinnedBottomRowData={items.length ? [computeTotalRow()] : undefined}
        getRowStyle={(params: RowClassParams): RowStyle | undefined => {
          if (params.node.rowPinned) {
            return {
              backgroundColor: "#f8f8f8",
              fontWeight: "600",
              color: "#878787 !important",
              border: "none !important",
              textAlign: "right",
            };
          }
          return {};
        }}
        cacheBlockSize={100}
        rowBuffer={20}
        noRowsOverlayComponent={() => <NoRecords image={noRecordImage} />}
      />
    </>
  );
};

export default memo(SOVItemsTable);
