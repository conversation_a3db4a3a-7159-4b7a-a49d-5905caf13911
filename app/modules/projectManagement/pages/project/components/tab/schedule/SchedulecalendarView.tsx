import { memo, useEffect, useMemo, useRef, useState } from "react";
import { useParams } from "@remix-run/react";

// Molecules
import { SchedulerSkeleton } from "~/shared/components/molecules/schedulerSkeleton";
// Hook
import { useTranslation } from "~/hook";
// Other
import isEmpty from "lodash/isEmpty";
import { getGModuleDashboard } from "~/zustand";
import { Int, Number, sanitizeString } from "~/helpers/helper";
import "dhtmlx-scheduler/codebase/dhtmlxscheduler.css";
import "../../../../../../../assets/scheduler.css";
import { getProjectScheduleApi } from "../../../redux/action/proDashAction";
import {
  faArrowUpRightFromSquare,
  faCalendarDays,
  faPrint,
  faArrowRotateRight,
} from "@fortawesome/pro-regular-svg-icons";
import { icon } from "@fortawesome/fontawesome-svg-core";
import { useScheduler } from "~/hook/scheduler/useScheduler.client";
import moment from "moment";

const SchedulecalendarView = ({
  setExportCalendar,
}: IScheduleCalendarViewProps) => {
  let { _t } = useTranslation();
  const { id } = useParams();
  const schedulerContainer = useRef<HTMLDivElement>(null);

  const [mapsData, setMapsData] = useState<IProjectDashScheduleData[]>([]);
  const gModuleDashboard = getGModuleDashboard();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [schedulerView, setSchedulerView] = useState<string>("month");
  const dateFormatToConsider = useMemo(() => {
    return CFConfig.day_js_date_format;
  }, [CFConfig.day_js_date_format]);

  const tooltipConfig = [
    {
      iconClass: ".gantt-print-icon",
      tooltip: _t("Export Calendar"),
    },
    {
      iconClass: ".gantt-cal-icon",
      tooltip: _t("Calendar"),
    },
    {
      iconClass: ".gantt-redirection-icon",
      tooltip: _t("Opens the Gantt Schedule in a new tab"),
    },
    {
      iconClass: ".gantt-refresh-icon",
      tooltip: _t("Refresh Gantt Schedule"),
    },
  ];

  const getData = async () => {
    setIsLoading(true);
    try {
      const response = (await getProjectScheduleApi({
        project: Number(id),
        is_refresh: 1,
      })) as IProjectDashScheduleApiRes;
      if (response.success) {
        setMapsData(response.data);
        setIsLoading(false);
      }
    } catch (err) {
      notification.error({
        description: (err as Error).message,
      });
    }
  };

  useEffect(() => {
    if (id) {
      getData();
    }
  }, [id]);

  const printIconHTML = icon(faPrint, { classes: ["gantt-print-icon"] })
    .html[0];

  const calendarIconHTML = icon(faCalendarDays, { classes: ["gantt-cal-icon"] })
    .html[0];

  const redirectionIconHTML = icon(faArrowUpRightFromSquare, {
    classes: ["gantt-redirection-icon"],
  }).html[0];

  const refrshIconHTML = icon(faArrowRotateRight, {
    classes: ["gantt-refresh-icon"],
  }).html[0];

  const openCalendar = (scheduler: Window["scheduler"]) => {
    // Use DHTMLX Scheduler's mini calendar
    if (scheduler.isCalendarVisible()) {
      scheduler.destroyCalendar();
    } else {
      scheduler.renderCalendar({
        position: "calId",
        date: scheduler.getState().date,
        navigation: true,
        handler: function (date: Date) {
          scheduler.setCurrentView(date);
          scheduler.destroyCalendar();
        },
      });
    }
  };

  const applyTooltipConfig = () => {
    tooltipConfig.forEach(({ iconClass, tooltip }) => {
      const iconEl = document.querySelector(iconClass);
      if (iconEl) {
        const buttonEl = iconEl.closest(".dhx_cal_nav_button");
        if (buttonEl) {
          buttonEl.setAttribute("data-tooltip", tooltip);
          buttonEl.classList.add("tooltip-wrapper");
        }
      }
    });
  };

  useScheduler(schedulerContainer, {
    config: (scheduler) => {
      scheduler.config.wide_form = true;
      scheduler.config.readonly_form = true;
      scheduler.config.dblclick_create = false;

      if (schedulerView === "month") {
        scheduler.config.start_on_monday = true;
      }

      scheduler.config.header = [
        "day",
        "week",
        "month",
        {
          html: printIconHTML, // Print Button
          click: () => {
            setExportCalendar(true);
          },
        },
        {
          html: `<div id="calId" style="position: relative">${calendarIconHTML}</div>`,
          style: {
            position: "relative",
          }, // Calendar Picker
          click: () => openCalendar(scheduler),
        },
        {
          html: `${redirectionIconHTML}`, // Export Button
          click: () => {
            var link = document.createElement("a");

            link.href = `${window.ENV.PANEL_URL}manage_schedule.php`;
            link.textContent = "Visit Example";
            link.target = "_blank"; // Open in new tab (optional)
            link.click();
          },
        },
        {
          html: `${refrshIconHTML}`, // Refresh Button
          click: () => {
            scheduler.updateView();
            getData();
          },
        },
        "date",
        "prev",
        "today",
        "next",
      ];

      if (
        !isEmpty(scheduler) &&
        gModuleDashboard?.module_setting?.weekend_schedule !== undefined
      ) {
        scheduler.ignore_week = function (date: any) {
          if (Int(gModuleDashboard?.module_setting?.weekend_schedule) == 1) {
            //Ignore Sunday
            if (date.getDay() == 0) {
              return true;
            }
          } else if (
            Int(gModuleDashboard?.module_setting?.weekend_schedule) == 2
          ) {
            if (date.getDay() == 6 || date.getDay() == 0) {
              //hides Saturdays and Sundays
              return true;
            }
          } else if (
            Int(gModuleDashboard?.module_setting?.weekend_schedule) == 3
          ) {
            if (
              date.getDay() == 6 ||
              date.getDay() == 5 ||
              date.getDay() == 0
            ) {
              return true;
            }
          }
        };
      }

      if (
        !isEmpty(scheduler) &&
        gModuleDashboard?.module_setting?.weekend_schedule !== undefined
      ) {
        scheduler.ignore_month = function (date: any) {
          if (Int(gModuleDashboard?.module_setting?.weekend_schedule) == 1) {
            //Ignore Sunday
            if (date.getDay() == 0) {
              return true;
            }
          } else if (
            Int(gModuleDashboard?.module_setting?.weekend_schedule) == 2
          ) {
            if (date.getDay() == 6 || date.getDay() == 0) {
              //hides Saturdays and Sundays
              return true;
            }
          } else if (
            Int(gModuleDashboard?.module_setting?.weekend_schedule) == 3
          ) {
            if (
              date.getDay() == 6 ||
              date.getDay() == 5 ||
              date.getDay() == 0
            ) {
              return true;
            }
          }
        };
      }

      scheduler.templates.event_class = function (start, end, ev) {
        return ev.classname || "";
      };

      scheduler.templates.day_date = scheduler.date.date_to_str("%D, %d %M %Y");

      scheduler.templates.week_date = function (start, end) {
        const schedule = Int(
          gModuleDashboard?.module_setting?.weekend_schedule
        );

        // Build array of visible days in the week
        const visibleDays = [];
        for (let i = 0; i < 7; i++) {
          const date = scheduler.date.add(start, i, "day");
          const day = date.getDay();

          const isHidden =
            (schedule === 1 && day === 0) ||
            (schedule === 2 && (day === 0 || day === 6)) ||
            (schedule === 3 && (day === 0 || day === 5 || day === 6));

          if (!isHidden) visibleDays.push(date);
        }

        if (visibleDays.length === 0) return "";

        return (
          scheduler.templates.day_date(visibleDays[0]) +
          " - " +
          scheduler.templates.day_date(visibleDays[visibleDays.length - 1])
        );
      };

      scheduler.templates.lightbox_header = function (start, end, ev) {
        const title = HTMLEntities.decode(ev.text || "");
        const dateFormatter = scheduler.date.date_to_str("%d/%m/%Y"); // ✅ Only date
        const timeText = `${dateFormatter(start)} - ${dateFormatter(end)}`;

        return `
    <div class="custom-lightbox-header">
      <span class="text-sm !font-semibold">${timeText}</span>
      <span class="text-sm !font-normal ml-1 whitespace-normal">${title}</span>
    </div>
  `;
      };

      scheduler.templates.tooltip_text = function (start, end, event) {
        const eventName = HTMLEntities.decode(sanitizeString(event.text));
        const startDate = moment(start).format(`${dateFormatToConsider}`);
        const endDate = moment(end).format(`${dateFormatToConsider}`);

        return `
            <b>Event:</b> ${eventName}<br/>
            <b>Start date:</b> ${startDate}<br/>
            <b>End date:</b> ${endDate}
        `;
      };

      scheduler.config.lightbox.sections = [
        {
          name: "title",
          height: 30,
          map_to: "text",
          type: "textarea",
          focus: true,
        },
        {
          name: "description",
          height: 70,
          map_to: "description",
          type: "textarea",
        },
        { name: "time", height: 72, type: "time", map_to: "auto" },
      ];

      scheduler.plugins({
        tooltip: true,
        export_api: true,
        minical: true,
      });
    },
    events: (scheduler) => {
      const beforeViewChangeId = scheduler.attachEvent(
        "onBeforeViewChange",
        function (_old_mode, _old_date, mode, _date) {
          setSchedulerView(mode);
          if (mode == "month") {
            scheduler.config.start_on_monday = true;
          } else {
            scheduler.config.start_on_monday = false;
          }
          return true;
        }
      );

      const onLightboxId = scheduler.attachEvent("onLightbox", function (id) {
        setTimeout(() => {
          const event = scheduler.getEvent(id);
          const lightbox = scheduler.getLightbox();

          if (!lightbox) return;

          if (event && event.id) {
            const decodedTitle = HTMLEntities.decode(
              sanitizeString(event.text ?? "")
            );
            const decodedDescription = HTMLEntities.decode(
              sanitizeString(event.description ?? "")
            );

            event.text = decodedTitle;
            event.description = decodedDescription;

            const textareas = lightbox.querySelectorAll(
              ".dhx_cal_ltext textarea"
            );

            const titleInput = textareas[0] as HTMLTextAreaElement;
            if (titleInput) titleInput.value = decodedTitle;

            const descInput = textareas[1] as HTMLTextAreaElement;
            if (descInput) descInput.value = decodedDescription;
          }

          const checkbox = document.querySelector(
            "input[name='full_day'][type='checkbox']"
          ) as HTMLInputElement;

          // Disable all textareas
          lightbox.querySelectorAll("textarea").forEach((el) => {
            el.setAttribute("disabled", "true");
          });

          // Disable all inputs
          lightbox.querySelectorAll("input").forEach((el) => {
            el.setAttribute("disabled", "true");
          });

          // Disable all selects (dropdowns)
          lightbox.querySelectorAll("select").forEach((el) => {
            el.setAttribute("disabled", "true");
          });

          // Remove the entire time selector block
          const timeBlocks = lightbox.querySelectorAll(
            ".dhx_lightbox_time_select"
          );
          if (timeBlocks) timeBlocks.forEach((t) => t.remove());

          // Remove Delete button
          const deleteBtn = lightbox.querySelector(".dhx_delete_btn_set");
          if (deleteBtn) deleteBtn.remove();

          // Remove Save buttons
          const saveBtns = lightbox.querySelector(
            ".dhx_save_btn_set"
          ) as HTMLElement;

          if (saveBtns) saveBtns.style.display = "none";

          const task = scheduler.getEvent(id);

          const isFullDay =
            task.full_day === true ||
            task.full_day === "1" ||
            task.full_day === 1;

          if (checkbox) {
            checkbox.checked = isFullDay;

            // Disable the checkbox
            checkbox.disabled = true;
          }
        }, 0);
      });

      const afterViewChangeId = scheduler.attachEvent(
        "onViewChange",
        function () {
          setTimeout(() => {
            applyTooltipConfig();
          }, 100);
        }
      );

      const onDataRenderId = scheduler.attachEvent("onDataRender", function () {
        setTimeout(() => {
          applyTooltipConfig();
        }, 100);
      });

      return [
        beforeViewChangeId ?? "",
        onLightboxId ?? "",
        afterViewChangeId ?? "",
        onDataRenderId ?? "",
      ];
    },
    data: mapsData,
    view: schedulerView,
    timeoutCallback: () => {
      const tooltipTimeout = setTimeout(() => {
        applyTooltipConfig();
      }, 100);

      return tooltipTimeout;
    },
  });

  return (
    <>
      <div className="relative">
        {isLoading ? (
          <div className="bg-white min-h-[215px]">
            <SchedulerSkeleton rowCount={20} />
          </div>
        ) : (
          <div
            className={`project-schedule-tab-scheduler !h-[calc(100vh-120px)] 2xl:!h-[calc(100vh-390px)] ${
              isLoading ? "invisible" : "visible"
            }`}
          >
            <div
              ref={schedulerContainer}
              className="dhx_cal_container"
              id="dhx_schedule_cal_container"
            >
              <div className="dhx_cal_navline">
                <div className="dhx_cal_tab" data-tab="map_tab"></div>
              </div>
              <div className="dhx_cal_data"></div>
            </div>
            <div className="add_event_button" data-tooltip="Create new event">
              <span></span>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default memo(SchedulecalendarView);
