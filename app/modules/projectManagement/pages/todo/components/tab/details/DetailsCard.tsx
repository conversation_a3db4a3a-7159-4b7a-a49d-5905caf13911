// React + ag-grid
import { useEffect, useMemo, useRef, useState } from "react";
import { useParams } from "@remix-run/react";
import delay from "lodash/delay";
import dayjs from "dayjs";
// Hooks
import { useTranslation } from "~/hook";
// antd
import type { CheckboxProps, InputRef } from "antd";
// Atoms
import { Spin } from "~/shared/components/atoms/spin";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { ContactDetails } from "~/shared/components/molecules/contactDetails";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { TimePickerField } from "~/shared/components/molecules/timePickerField";
import { AvatarIconPopover } from "~/shared/components/organisms/avatarIconPopover";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { CheckboxGroupList } from "~/shared/components/molecules/checkboxGroupList";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";

// Other
import TdSendEmail from "../../TdSendEmail";
// Constants + Helper
import {
  commonTabOptions,
  fieldStatus,
  TDDetailsField,
  TODO_OTHER_OPTIONS,
} from "../../../utils/constants";
import { getAssigneeValue } from "../../../utils/common";
import { escapeHtmlEntities, sanitizeString } from "~/helpers/helper";
import { defaultConfig } from "~/data";
// Redux + Store
import {
  assigneeNotificationApi,
  updateToDoDetailApi,
} from "../../../redux/action/tdDetailsAction";
import { setActiveField } from "~/redux/slices/selectCustomerSlice";
import {
  updateAssigneDetail,
  updateTodoDetail,
} from "../../../redux/slices/tdDetailsSlice";
import { useAppTDDispatch, useAppTDSelector } from "../../../redux/store";
import {
  filterOptionBySubstring,
  getStatusActionForField,
  getStatusForField,
  multiSelect,
} from "~/shared/utils/helper/common";
import {
  backendDateFormat,
  backendTimeFormat,
  frontEndTimeFormat,
} from "~/shared/utils/helper/defaultDateFormat";
import { resetDash } from "../../../redux/slices/dashboardSlice";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";

const DetailsCard = ({ isReadOnly }: IToDoReadOnlyComponent) => {
  const { _t } = useTranslation();
  const { id }: RouteParams = useParams();

  const dispatch = useAppTDDispatch();
  const { details, isDetailLoading, assigneeDetail }: IToDoInitialState =
    useAppTDSelector((state) => state.tdDetails);
  const { tagCategoryList }: ITagCatInitialState = useAppTDSelector(
    (state) => state.tagCategories
  );

  const dtDivRef = useRef<HTMLLIElement>(null);
  const dateTimeSelectRef = useRef<InputRef>(null);
  const decInpRef = useRef<HTMLInputElement>(null);
  const loadingStatusRef = useRef(fieldStatus);

  // STATES
  const [inputValues, setInputValues] = useState<IToDoDetails>(TDDetailsField);
  const [isOpenSelectCustomer, setIsOpenSelectCustomer] =
    useState<boolean>(false);
  const [isAssignVal, setIsAssignVal] = useState<
    Partial<IToDoAssigneeDetail[]>
  >([]);
  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(fieldStatus);
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const [checkStatusLoading, setCheckStatusLoading] = useState<boolean>(true);
  const [isOpenContact, setIsOpenContact] = useState<boolean>(false);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);
  const [isAssNotificationSending, setAssNotificationSending] =
    useState<boolean>(false);
  const [appAccess, setAppAccess] = useState<boolean>(false);
  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);
  const [selectedData, setSelectedData] = useState<
    Partial<IToDoAssigneeDetail>
  >({});
  const [isDateTimeVisible, setDateTimeVisible] = useState<boolean>(false);
  const [isTimerDropdown, setTimerDropdown] = useState<boolean>(false);
  const [sendEmailTabs, setSendEmailTabs] = useState<CustomerTabs[]>([]);
  const [assigneeEmailTabs, setAssigneeEmailTabs] = useState<CustomerTabs[]>(
    []
  );
  const createdDate = dayjs(details.todo_date, CFConfig.day_js_date_format);

  // use effects
  useEffect(() => {
    if (details.project_id !== "0" && details?.project_id) {
      const assigneeOptions = commonTabOptions.filter(
        (ele) => ele !== defaultConfig.lead_key
      );
      setSendEmailTabs(assigneeOptions);
      setAssigneeEmailTabs(commonTabOptions);
    } else {
      const assigneeOptions = commonTabOptions.filter(
        (ele) => ele !== defaultConfig.lead_key && ele !== "my_project"
      );
      const assigneeDetailOptions = commonTabOptions.filter(
        (ele) => ele !== "my_project"
      );
      setSendEmailTabs(assigneeOptions);
      setAssigneeEmailTabs(assigneeDetailOptions);
    }
  }, [details.project_id]);

  useEffect(() => {
    setInputValues(details);
  }, [details]);

  useEffect(() => {
    if (
      loadingStatus.length > 0 &&
      loadingStatus.some((item) => item.status === "loading")
    ) {
      setCheckStatusLoading(false);
    } else {
      setCheckStatusLoading(true);
    }
  }, [loadingStatus]);

  // use memo
  const tagCateList: ITagList[] = useMemo(
    () =>
      tagCategoryList?.map((item: ITag) => ({
        label: HTMLEntities.decode(sanitizeString(item.name)),
        value: item.original_tag_id.toString(),
      })),
    [tagCategoryList]
  );

  const dueDateTimeFormat = useMemo(() => {
    return `${CFConfig.day_js_date_format} hh:mm A`;
  }, [CFConfig.day_js_date_format]);

  const closeConfirmationModal = () => {
    setIsConfirmDialogOpen(false);
  };

  const handleAssignTo = (data: IToDoAssigneeDetail[]) => {
    const userIdsString =
      data.length > 0 ? data.map((item) => item?.user_id).join(",") : "";
    handleUpdateField(
      {
        assigned_to: userIdsString || "",
      },
      data
    );
  };

  const handleUpdateField = async (
    data: ITodoDetailFields,
    otherData: IToDoAssigneeDetail[] = []
  ) => {
    const field = Object.keys(data)[0] as keyof IToDoDetails;
    const values = Object.values(data)[0] as string;

    const userIdsString =
      otherData.length > 0
        ? otherData.map((item) => item?.user_id).join(",")
        : "";

    const newCheckedOptions = inputValues?.other_check_array.filter(
      (option) => option !== "is_private_to_user"
    );

    if (field === "due_date" || field === "due_time") {
      handleChangeFieldStatus({
        field: "due_date_time",
        status: "loading",
        action: "API",
      });
    }

    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });

    const updateRes = (await updateToDoDetailApi({
      todo_id: id || "",
      ...data,
    })) as IToDoDetailsApiRes;

    if (updateRes?.success) {
      if (field === "due_date" || field === "due_time") {
        handleChangeFieldStatus({
          field: "due_date_time",
          status: "success",
          action: "API",
        });
      }

      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });

      if (field === "task_name" || field === "description") {
        dispatch(
          updateTodoDetail({
            [field]: HTMLEntities.decode(sanitizeString(values)),
          })
        );
      } else if (field === "assigned_to") {
        setIsAssignVal([]);
        dispatch(updateAssigneDetail(otherData));
        dispatch(updateTodoDetail({ assigned_to: userIdsString }));
      } else if (field === "is_shared" && values == "1") {
        dispatch(
          updateTodoDetail({
            ...data,
            is_private_to_user: 0,
            other_check_array: newCheckedOptions,
          })
        );
        setInputValues({
          ...inputValues,
          ...data,
          is_private_to_user: 0,
          other_check_array: newCheckedOptions,
        });
      } else if (field === "due_date") {
        dispatch(
          updateTodoDetail({
            ...data,
            due_date: !!values
              ? dayjs(values).format(CFConfig.day_js_date_format)
              : "",
          })
        );
      } else if (field === "due_time") {
        dispatch(
          updateTodoDetail({
            ...data,
            due_time: !!values ? frontEndTimeFormat(values) : "",
          })
        );
      } else {
        dispatch(updateTodoDetail(data));
      }
      dispatch(resetDash());
    } else {
      if (field === "due_date" || field === "due_time") {
        handleChangeFieldStatus({
          field: "due_date_time",
          status: "error",
          action: "API",
        });
      }

      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      setInputValues({ ...inputValues, [field]: details[field] });
      notification.error({
        description: updateRes?.message,
      });
    }
    setAppAccess(false);
    delay(() => {
      if (field === "due_date" || field === "due_time") {
        handleChangeFieldStatus({
          field: "due_date_time",
          status: "button",
          action: "API",
        });
      }

      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );
      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 3000);
    return updateRes;
  };

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );

    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };

  const handleInpOnChange = ({
    target: { value, name },
  }: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setInputValues({ ...inputValues, [name]: value });
  };

  const handleMultiSelect = (
    values: string | string[],
    selectedOption: string
  ) => {
    if (selectedOption === "categories") {
      setInputValues({
        ...inputValues,
        categories: multiSelect({ key: "categories", values, inputValues }),
      });
      handleUpdateField({
        categories: multiSelect({ key: "categories", values, inputValues }),
      });
    }
  };

  const handleChangeDate = (dateString: string, options: string) => {
    if (
      dateString &&
      dayjs(dateString, CFConfig.day_js_date_format).isBefore(createdDate)
    ) {
      return notification.error({
        description: "Due Date should not be less than the current date.",
      });
    }

    const formatDate = (date: string, type: string) => {
      if (type === "due_date")
        return backendDateFormat(date, CFConfig.day_js_date_format);
      if (type === "due_time") return backendTimeFormat(date);
      return date;
    };

    const formattedDate = dateString ? formatDate(dateString, options) : null;

    const updatedValues = { ...inputValues, [options]: dateString || null };
    setInputValues(updatedValues);

    handleUpdateField({ [options]: formattedDate });
  };

  const handleSharedChange: CheckboxProps["onChange"] = (e) => {
    setAppAccess(true);
    const isChecked = e.target.checked;
    const updates: ITodoDetailFields = { is_shared: isChecked ? 1 : 0 };
    if (
      isChecked &&
      inputValues?.other_check_array.includes("is_private_to_user")
    ) {
      updates.is_private_to_user = 0;
      setInputValues({ ...inputValues, ...updates });
      handleUpdateField(updates);
    } else {
      setInputValues({ ...inputValues, ...updates });
      handleUpdateField(updates);
    }
  };

  const handleNotification = async () => {
    if (!isAssNotificationSending) {
      setAssNotificationSending(true);
      const assignRes = (await assigneeNotificationApi({
        todo_id: Number(id),
      })) as IAssigneeNotificationRes;
      if (assignRes.success) {
        closeConfirmationModal();
      } else {
        notification.error({
          description: assignRes?.message || "Something went wrong",
        });
      }
      setAssNotificationSending(false);
    }
  };

  const convertToOtherCheckSettings = (
    otherField: string[]
  ): ITodoOtherField => {
    return {
      is_private_to_user: otherField?.includes("is_private_to_user") ? 1 : 0,
      is_template: otherField?.includes("is_template") ? 1 : 0,
    };
  };

  const onChangeOtherCheck = async (value: string[], previous: string[]) => {
    handleChangeFieldStatus({
      field: "other_check_status",
      status: "loading",
      action: "API",
    });
    let updates = convertToOtherCheckSettings(value);

    if (updates.is_private_to_user == 1 && inputValues.is_shared == "1") {
      updates.is_shared = 0;
    }

    setInputValues({
      ...inputValues,
      other_check_array: previous,
    });

    const updateRes = await handleUpdateField({ ...updates });
    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: "other_check_status",
        status: "success",
        action: "API",
      });
      dispatch(
        updateTodoDetail({
          ...updates,
        })
      );
      setInputValues({
        ...inputValues,
        ...updates,
        other_check_array: value,
      });
      dispatch(resetDash());
    } else {
      handleChangeFieldStatus({
        field: "other_check_status",
        status: "error",
        action: "API",
      });
      notification.error({
        description: updateRes?.message || "Something went wrong!",
      });

      setInputValues({
        ...inputValues,
        other_check_array: previous,
      });
    }

    delay(() => {
      handleChangeFieldStatus({
        field: "other_check_status",
        status: "button",
        action: "API",
      });
    }, 3000);
  };

  const handleNameOutsideClick = (event: MouseEvent) => {
    const clickedElement = event.target as HTMLElement;
    const targetElement = document.querySelector(".ant-picker-dropdown");

    if (targetElement && targetElement.contains(clickedElement)) {
      return;
    }

    if (
      dtDivRef.current &&
      dtDivRef.current.contains(clickedElement) &&
      clickedElement.tagName.toLowerCase() === "svg" &&
      !isTimerDropdown
    ) {
      setDateTimeVisible(false);
      return;
    }

    if (dtDivRef.current && !dtDivRef.current.contains(event.target as Node)) {
      setDateTimeVisible(false);
    }
  };
  useEffect(() => {
    document.addEventListener("mousedown", handleNameOutsideClick);
    return () => {
      document.removeEventListener("mousedown", handleNameOutsideClick);
    };
  }, []);

  return (
    <CrudCommonCard
      headerTitle={_t("Details")}
      iconProps={{
        icon: "fa-solid fa-file-lines",
        containerClassName:
          "bg-[linear-gradient(180deg,#FF868A1a_0%,#FD33331a_100%)]",
        id: "contractor_details_icon",
        colors: ["#FF868A", "#FD3333"],
      }}
      headerRightButton={
        (inputValues?.show_client_access === "1" ||
          details?.show_client_access === "1") && (
          <CustomCheckBox
            name="is_shared"
            className="gap-1.5"
            checked={
              typeof inputValues?.is_shared === "boolean"
                ? inputValues?.is_shared
                : inputValues?.is_shared?.toString() === "1"
            }
            onChange={handleSharedChange}
            disabled={isReadOnly ? isReadOnly : appAccess}
            loadingProps={{
              isLoading: appAccess,
              className: "bg-[#ffffff]",
            }}
          >
            {_t("Share with Client")}
          </CustomCheckBox>
        )
      }
      children={
        <>
          {isDetailLoading ? (
            <Spin className="w-full h-[260px] flex items-center justify-center" />
          ) : (
            <div className="pt-2">
              <ul className="w-full grid sm:gap-1 gap-2">
                <li className="overflow-hidden">
                  <InputField
                    label={_t("Task Name")}
                    placeholder={_t("Task Name")}
                    name="task_name"
                    value={inputValues?.task_name}
                    labelPlacement="left"
                    editInline={true}
                    iconView={true}
                    fixStatus={getStatusForField(loadingStatus, "task_name")}
                    onChange={handleInpOnChange}
                    readOnly={isReadOnly}
                    onMouseEnter={() => {
                      handleChangeFieldStatus({
                        field: "task_name",
                        status: "edit",
                        action: "ME",
                      });
                    }}
                    onMouseLeaveDiv={() => {
                      handleChangeFieldStatus({
                        field: "task_name",
                        status: "button",
                        action: "ML",
                      });
                    }}
                    onFocus={() =>
                      handleChangeFieldStatus({
                        field: "task_name",
                        status: "save",
                        action: "FOCUS",
                      })
                    }
                    onBlur={(e) => {
                      const value = e?.target?.value.trim();
                      if (value === "") {
                        notification.error({
                          description: _t("Task Name field is required."),
                        });
                        setInputValues({
                          ...inputValues,
                          task_name: details?.task_name,
                        });
                        return false;
                      }
                      if (value === "0") {
                        notification.error({
                          description: _t("Please enter Task Name"),
                        });
                        setInputValues({
                          ...inputValues,
                          task_name: details?.task_name,
                        });
                        return false;
                      }
                      if (value !== details?.task_name) {
                        handleUpdateField({
                          task_name: escapeHtmlEntities(value),
                        });
                      } else {
                        handleChangeFieldStatus({
                          field: "task_name",
                          status: "button",
                          action: "BLUR",
                        });
                        setInputValues({
                          ...inputValues,
                          task_name: details.task_name,
                        });
                      }
                    }}
                  />
                </li>
                <li
                  className={`overflow-hidden flex ${
                    !isDateTimeVisible ? "hidden" : ""
                  }`}
                  ref={dtDivRef}
                >
                  <InlineField
                    label={_t("Due Date/Time")}
                    labelPlacement="left"
                    field={
                      <div className="grid sm:grid-cols-2 gap-1.5 items-center w-full">
                        <DatePickerField
                          minDate={createdDate}
                          label=""
                          labelPlacement="left"
                          placeholder={_t("Select Date")}
                          required={false}
                          editInline={true}
                          iconView={true}
                          inputReadOnly={true}
                          allowClear={true}
                          value={
                            inputValues.due_date
                              ? dayjs(
                                  inputValues?.due_date,
                                  CFConfig.day_js_date_format
                                )
                              : undefined
                          }
                          format={CFConfig.day_js_date_format}
                          fixStatus={getStatusForField(
                            loadingStatus,
                            "due_date"
                          )}
                          onChange={(_, dateString) => {
                            if (
                              inputValues?.due_date !== dateString &&
                              !!dateString
                            ) {
                              handleChangeDate(
                                dateString as string,
                                "due_date"
                              );
                            } else if (!dateString) {
                              handleChangeDate("", "due_date");
                            }
                          }}
                        />
                        <TimePickerField
                          label=""
                          id="timePickerField"
                          name="due_time"
                          placeholder={_t("Select Time")}
                          labelPlacement="left"
                          editInline={true}
                          iconView={true}
                          allowClear={true}
                          inputReadOnly={true}
                          showNow={true}
                          format="hh:mm A"
                          use12Hours
                          value={
                            inputValues?.due_time?.trim()
                              ? dayjs(inputValues.due_time.trim(), "hh:mm A")
                              : null
                          }
                          fixStatus={getStatusForField(
                            loadingStatus,
                            "due_time"
                          )}
                          onOk={() => {
                            setTimerDropdown(false);
                            setDateTimeVisible(false);
                          }}
                          onFocus={() => {
                            setTimerDropdown(true);
                          }}
                          onChange={(_, val) => {
                            setTimerDropdown(false);
                            if (inputValues?.due_time !== val && !!val) {
                              handleChangeDate(val as string, "due_time");
                            } else if (val == "") {
                              handleChangeDate("", "due_time");
                            }
                          }}
                        />
                      </div>
                    }
                  />
                </li>
                <li
                  className={`overflow-hidden ${
                    isDateTimeVisible ? "hidden" : ""
                  }`}
                >
                  <InlineField
                    label={_t("Due Date/Time")}
                    labelPlacement="left"
                    field={
                      <div className="relative w-full group/edit">
                        <InputField
                          ref={dateTimeSelectRef}
                          labelPlacement="left"
                          placeholder={_t("Select Date/Time")}
                          required={false}
                          editInline={true}
                          iconView={true}
                          readOnly={isReadOnly}
                          readOnlyClassName="!h-[34px]"
                          value={
                            !!inputValues?.due_date && !!inputValues?.due_time
                              ? dayjs(
                                  `${inputValues.due_date || ""} ${
                                    inputValues.due_time || ""
                                  }`,
                                  `${CFConfig.day_js_date_format} hh:mm A`
                                ).format(dueDateTimeFormat)
                              : !!inputValues?.due_date
                              ? inputValues.due_date
                              : !!inputValues?.due_time
                              ? dayjs(inputValues.due_time, "hh:mm A").format(
                                  "hh:mm A"
                                )
                              : ""
                          }
                          fixStatus={getStatusForField(
                            loadingStatus,
                            "due_date_time"
                          )}
                          onChange={() => {}}
                          onFocus={() => {
                            setDateTimeVisible(true);
                          }}
                          onMouseLeaveDiv={() => {
                            handleChangeFieldStatus({
                              field: "due_date_time",
                              status: "button",
                              action: "ML",
                            });
                          }}
                        />
                        {(!!inputValues.due_date || !!inputValues.due_time) &&
                        !isReadOnly &&
                        !["loading", "success", "error"].includes(
                          getStatusForField(loadingStatus, "due_date_time")
                        ) ? (
                          <div className="absolute z-10 top-1/2 -translate-y-1/2 right-2.5 opacity-0 group-hover/edit:opacity-100">
                            <FontAwesomeIcon
                              icon="fa-solid fa-circle-xmark"
                              className="text-[#00000040] hover:text-[#00000073] cursor-pointer w-3 h-3"
                              onClick={() => {
                                handleChangeDate("", "due_date");
                                handleChangeDate("", "due_time");
                                setInputValues((prevValues) => ({
                                  ...prevValues,
                                  due_date: "",
                                  due_time: "",
                                }));
                              }}
                            />
                          </div>
                        ) : null}
                      </div>
                    }
                  />
                </li>
                <li className="overflow-hidden">
                  <ButtonField
                    label={_t("Assigned To")}
                    placeholder={_t("Select Assigned To")}
                    labelPlacement="left"
                    readOnlyClassName="sm:block flex"
                    editInline={true}
                    iconView={true}
                    required={false}
                    avatarProps={
                      assigneeDetail?.length == 1
                        ? {
                            user: {
                              name: HTMLEntities.decode(
                                sanitizeString(assigneeDetail[0]?.display_name)
                              ),
                              image: assigneeDetail[0]?.image,
                            },
                          }
                        : undefined
                    }
                    onClick={() => {
                      setIsAssignVal(
                        assigneeDetail?.length
                          ? assigneeDetail.map((assignee) => {
                              return {
                                display_name: assignee?.display_name,
                                type_name:
                                  assignee?.dir_type?.toString() === "1"
                                    ? "Employee"
                                    : assignee?.type_name,
                                image: assignee?.image,
                                user_id: assignee?.user_id,
                              };
                            })
                          : []
                      );
                      setIsOpenSelectCustomer(true);
                    }}
                    value={getAssigneeValue(assigneeDetail)}
                    statusProps={{
                      status: getStatusForField(loadingStatus, "assigned_to"),
                    }}
                    disabled={
                      getStatusForField(loadingStatus, "assigned_to") ===
                      "loading"
                    }
                    readOnly={isReadOnly}
                    rightIcon={
                      <div className="flex items-center gap-1">
                        {assigneeDetail.length === 1 && (
                          <ContactDetailsButton
                            onClick={(event) => {
                              event.stopPropagation();
                              setSelectedUserId(assigneeDetail[0].user_id);
                              setIsOpenContact(true);
                            }}
                          />
                        )}
                        {assigneeDetail.length > 1 && (
                          <AvatarIconPopover
                            placement="bottom"
                            assignedTo={assigneeDetail as IAssignedToUsers[]}
                            setSelectedUserId={(data) => {
                              setSelectedUserId(data.id);
                            }}
                            setIsOpenContactDetails={setIsOpenContact}
                            redirectionIcon
                          />
                        )}
                        {assigneeDetail.length === 1 && (
                          <DirectoryFieldRedirectionIcon
                            directoryId={
                              assigneeDetail[0].user_id?.toString() || ""
                            }
                            directoryTypeKey={
                              (assigneeDetail[0]?.type_name?.toLowerCase() !==
                              "contact"
                                ? assigneeDetail[0]?.type_name?.toLowerCase()
                                : assigneeDetail[0]?.parent_type_key) || ""
                            }
                          />
                        )}
                        {assigneeDetail.length > 0 && !isReadOnly && (
                          <ButtonWithTooltip
                            tooltipTitle={_t("Send Notification")}
                            tooltipPlacement="top"
                            icon="fa-regular fa-paper-plane"
                            onClick={() => setIsConfirmDialogOpen(true)}
                          />
                        )}
                      </div>
                    }
                  />
                </li>
                <li className="overflow-hidden">
                  <SelectField
                    label={_t("Tags")}
                    placeholder={_t("Select Tags")}
                    labelPlacement="left"
                    editInline={true}
                    iconView={true}
                    mode={"multiple"}
                    maxTagValue={"responsive"}
                    value={
                      typeof inputValues?.categories === "string" &&
                      inputValues?.categories
                        ? tagCateList.filter((item) =>
                            (inputValues?.categories ?? "")
                              .split(",")
                              .includes(item?.value || "")
                          )
                        : []
                    }
                    allowClear
                    options={tagCateList}
                    fixStatus={getStatusForField(loadingStatus, "categories")}
                    disabled={
                      getStatusForField(loadingStatus, "categories") ===
                      "loading"
                    }
                    readOnly={isReadOnly}
                    filterOption={(input, option) =>
                      filterOptionBySubstring(input, option?.label as string)
                    }
                    onChange={(value: string | string[]) => {
                      if (typeof value === "object") {
                        setInputValues({
                          ...inputValues,
                          categories: value.join(","),
                        });
                      }
                    }}
                    onBlur={() => {
                      if (inputValues?.categories !== details.categories) {
                        handleUpdateField({
                          categories: inputValues?.categories || "",
                        });
                      } else {
                        handleChangeFieldStatus({
                          field: "categories",
                          status: "button",
                          action: "BLUR",
                        });
                        setInputValues({
                          ...inputValues,
                          categories: details.categories || "",
                        });
                      }
                    }}
                    onCloseTag={(val) => handleMultiSelect(val, "categories")}
                  />
                </li>
                <li>
                  <InlineField
                    label={_t("Other")}
                    labelPlacement="left"
                    field={
                      <CheckboxGroupList
                        name="other_check_status"
                        view="col"
                        onChange={(value) => {
                          onChangeOtherCheck(
                            value,
                            inputValues?.other_check_array
                          );
                        }}
                        className="gap-y-1 checkbox-group-col"
                        value={inputValues.other_check_array}
                        options={TODO_OTHER_OPTIONS}
                        fixStatus={getStatusForField(
                          loadingStatus,
                          "other_check_status"
                        )}
                        disabled={
                          isReadOnly ||
                          getStatusForField(
                            loadingStatus,
                            "other_check_status"
                          ) === "loading"
                        }
                      />
                    }
                  />
                </li>
                <li className="overflow-hidden">
                  <TextAreaField
                    label={_t("Description")}
                    placeholder={_t("Description")}
                    name="description"
                    labelPlacement="left"
                    value={inputValues?.description}
                    editInline={true}
                    iconView={true}
                    fixStatus={getStatusForField(loadingStatus, "description")}
                    readOnly={isReadOnly}
                    onChange={handleInpOnChange}
                    ref={decInpRef}
                    onMouseEnter={() => {
                      handleChangeFieldStatus({
                        field: "description",
                        status: "edit",
                        action: "ME",
                      });
                    }}
                    onMouseLeaveDiv={() => {
                      handleChangeFieldStatus({
                        field: "description",
                        status: "button",
                        action: "ML",
                      });
                    }}
                    onFocus={() =>
                      handleChangeFieldStatus({
                        field: "description",
                        status: "save",
                        action: "FOCUS",
                      })
                    }
                    onBlur={(e) => {
                      const value = e?.target?.value.trim();
                      if (value !== details?.description) {
                        handleUpdateField({
                          description: escapeHtmlEntities(value),
                        });
                      } else {
                        handleChangeFieldStatus({
                          field: "description",
                          status: "button",
                          action: "BLUR",
                        });
                        setInputValues({
                          ...inputValues,
                          description: details.description,
                        });
                      }
                    }}
                    onClickStsIcon={() => {
                      if (
                        getStatusForField(loadingStatus, "description") ===
                        "edit"
                      ) {
                        decInpRef.current?.focus();
                      }
                    }}
                  />
                </li>
              </ul>
              {isConfirmDialogOpen && (
                <ConfirmModal
                  isOpen={isConfirmDialogOpen}
                  modalIcon="fa-regular fa-paper-plane"
                  isLoading={isAssNotificationSending}
                  size="510px"
                  modaltitle={_t("Send Notification")}
                  description={_t(
                    `Do you want to send a notification to the assigned contacts? 
                    Notifications will be sent based on the preferences within the user's account.`
                  )}
                  onCloseModal={closeConfirmationModal}
                  onAccept={handleNotification}
                  onDecline={closeConfirmationModal}
                />
              )}
              {isOpenContact && (
                <ContactDetails
                  isOpenContact={isOpenContact}
                  onCloseModal={() => {
                    setIsOpenContact(false);
                  }}
                  contactId={selectedUserId || ""}
                  onEmailClick={(data) => {
                    setSelectedData(data);
                    setIsSendEmailSidebarOpen(true);
                  }}
                  readOnly={isReadOnly}
                  additional_contact_id={0} // as per PHP additional contact will not selected in assinged to field
                />
              )}
              {isOpenSelectCustomer && (
                <SelectCustomerDrawer
                  projectId={Number(details?.project_id)}
                  openSelectCustomerSidebar={isOpenSelectCustomer}
                  closeDrawer={() => {
                    dispatch(setActiveField(defaultConfig.employee_key));
                    setIsOpenSelectCustomer(false);
                  }}
                  singleSelecte={false}
                  options={sendEmailTabs}
                  setCustomer={(data) => {
                    handleAssignTo(
                      data.length ? (data as IToDoAssigneeDetail[]) : []
                    );
                  }}
                  selectedCustomer={isAssignVal as TselectedContactSendMail[]}
                  groupCheckBox={true}
                  additionalContactDetails={0}
                />
              )}

              {isSendEmailSidebarOpen && (
                <TdSendEmail
                  isOpen={isSendEmailSidebarOpen}
                  options={assigneeEmailTabs}
                  id={Number(details?.project_id)}
                  onSendResponse={() => {
                    setSelectedData({});
                  }}
                  onClose={() => {
                    setIsSendEmailSidebarOpen(false);
                    setSelectedData({});
                  }}
                  selectedCustomer={
                    selectedData?.user_id
                      ? ([selectedData] as TselectedContactSendMail[])
                      : []
                  }
                  groupCheckBox={true}
                  app_access={false}
                  emailData={{ subject: details?.task_name, body: "" }}
                />
              )}
            </div>
          )}
        </>
      }
    />
  );
};

export default DetailsCard;
