import { use<PERSON>ara<PERSON> } from "@remix-run/react";
import { useC<PERSON>back, useEffect, useMemo, useRef, useState } from "react";

// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { TimePickerField } from "~/shared/components/molecules/timePickerField";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
// organisms
import { AvatarIconPopover } from "~/shared/components/organisms/avatarIconPopover";
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";
// Shared
import {
  getStatusActionForField,
  getStatusForField,
} from "~/shared/utils/helper/common";
import {
  backendTimeFormat,
  backendDateFormat,
} from "~/shared/utils/helper/defaultDateFormat";

// dayjs
import dayjs from "dayjs";

// Zustand
import { useGlobalModule } from "~/zustand/global/modules/slice";

// Lodash
import debounce from "lodash/debounce";
import delay from "lodash/delay";

// Hook
import { useTranslation } from "~/hook";
// Other
import { useAppSTDispatch, useAppSTSelector } from "../../../redux/store";
import { fieldStatus, ST_DURATION_TIME } from "../../../utils/constants";
import { updateSTDetailApi } from "../../../redux/action/serviceTicketDetailsAction";
import { updateSTDetail } from "../../../redux/slices/serviceTicketDetailsSlice";
import { setActiveField } from "~/redux/slices/selectCustomerSlice";
import { defaultConfig } from "~/data";
import {
  getDirectaryIdByName,
  getDirectaryKeyById,
} from "~/components/sidebars/multi-select/customer/zustand/action";
import { sanitizeString } from "~/helpers/helper";
import { useExistingCustomers } from "~/components/sidebars/multi-select/customer/zustand";

// Formik
import { useFormik } from "formik";
import * as Yup from "yup";
import { setServiceNotificationApi } from "../../../redux/action/dashboardAction";

const DetailsCard = () => {
  const { checkGlobalModulePermissionByKey } = useGlobalModule();
  const { _t } = useTranslation();
  let { getExistingUsersWithApi } = useExistingCustomers();
  const dateTimeVisibleSelectRef = useRef<HTMLLIElement>(null);
  const { details }: IServiceInitialState = useAppSTSelector(
    (state) => state.serviceDetails
  );

  const [isDateTimeVisible, setDateTimeVisible] = useState<boolean>(false);
  const [isTimerDropdown, setTimerDropdown] = useState<boolean>(false);
  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(fieldStatus);
  const [notifyTechnicians, setNotifyTechnicians] = useState<{
    id: string | number;
    flag: boolean;
  }>({ id: "", flag: false });
  const [sendNotificationLoader, setSendNotificationLoader] =
    useState<boolean>(false);
  const loadingStatusRef = useRef(fieldStatus);
  const dtDivRef = useRef<HTMLLIElement>(null);
  const descriptionFieldRef = useRef<HTMLInputElement>(null);
  const noteFieldRef = useRef<HTMLInputElement>(null);

  const isReadOnly = useMemo(
    () =>
      checkGlobalModulePermissionByKey(CFConfig.service_ticket_module) ===
      "read_only",
    []
  );

  const formik = useFormik({
    initialValues: {
      service_date_only: "",
      service_time: "",
      service_end_time: "",
      customer: {} as TselectedContactSendMail,
      invoice_to: {} as TselectedContactSendMail,
      service_tech: [] as TselectedContactSendMail[],
      customer_contract: "",
      service_duration: "",
      description: "",
      notes: "",
    },
    validationSchema: Yup.object({
      customer: Yup.object().shape({
        user_id: Yup.number()
          .required("Customer field is required.")
          .notOneOf([0], "Customer field is required."),
      }),
      invoice_to: Yup.object().shape({
        user_id: Yup.number().notOneOf([0], "Invoice To field is required."),
      }),
      service_tech: Yup.array().of(
        Yup.object().shape({
          user_id: Yup.number().notOneOf(
            [0],
            "Service Tech field is required."
          ),
        })
      ),
    }),
    onSubmit: () => {},
  });

  useEffect(() => {
    if (details) {
      let values = {
        ...formik.values,
        service_date_only: details.service_date_only || "",
        service_time: details.service_time || "",
        service_end_time: details.service_end_time || "",
        customer_contract: details.customer_contract || "",
        service_duration: (Number(details.service_duration) || "").toString(),
        description: details.description || "",
        notes: details.notes || "",
        customer: {
          user_id: details.customer_id,
          display_name: details.customer_name,
          type_name: getDirectaryIdByName(Number(details?.dir_type)),
          contact_id: details.contact_id,
          image: details.contact_id
            ? ""
            : details.customer_data?.length && details.customer_data[0].image,
          type_key:
            details.customer_data?.length && details.customer_data?.length > 0
              ? details.customer_data[0].type_key
              : "",
          parent_type_key:
            details.customer_data?.length && details.customer_data?.length > 0
              ? details.customer_data[0].parent_type_key
                ? details.customer_data[0].parent_type_key
                : ""
              : "",
        },
        invoice_to:
          !!details?.invoiced_to_data?.[0] && !!details?.billed_to
            ? {
                user_id: details.billed_to,
                display_name: details.billed_to_display_name,
                billed_to_dir_type: details.billed_to_dir_type,
                contact_id: details.billed_to_contact,
                type_name:
                  getDirectaryIdByName(
                    Number(details?.billed_to_dir_type),
                    undefined
                  ) ||
                  details.invoiced_to_data?.[0]?.type_name ||
                  "",
                image: details.billed_to_contact
                  ? ""
                  : details.invoiced_to_data?.[0]?.image || "",
                type_key:
                  getDirectaryKeyById(
                    Number(details?.billed_to_dir_type),
                    undefined
                  ) ||
                  details.invoiced_to_data?.[0]?.type_key ||
                  "",
                parent_type_key:
                  details.invoiced_to_data?.[0]?.parent_type_key || "",
              }
            : !!details?.contact_id &&
              !details?.invoiced_to_data?.[0]?.length &&
              details.billed_to_dir_type
            ? {
                user_id:
                  details.customer_data?.[0]?.billed_to ||
                  details.billed_to ||
                  "",
                display_name:
                  details.billed_to_display_name ||
                  details.customer_data?.[0]?.billed_to_display_name ||
                  "",
                billed_to_dir_type: details.billed_to_dir_type,
                contact_id: details.customer_data?.[0]?.billed_to_contact || "",
                type_name:
                  getDirectaryIdByName(
                    Number(details?.billed_to_dir_type),
                    undefined
                  ) ||
                  details.customer_data?.[0]?.billed_to_type_name ||
                  details.customer_data?.[0]?.type_name ||
                  "",
                image: details.customer_data?.[0]?.billed_to_image || "",
                type_key:
                  getDirectaryKeyById(
                    Number(details?.billed_to_dir_type),
                    undefined
                  ) ||
                  details.customer_data?.[0]?.billed_to_type_key ||
                  "",
                parent_type_key:
                  details.customer_data?.[0]?.parent_type_key || "",
              }
            : {},
      };

      formik.setValues(values);
      if (details.service_technicians) {
        formik.setFieldValue(
          "service_tech",
          details.service_technicians?.map((item) => ({
            ...item,
            type_name: item.dir_type_name,
          })) || []
        );
      }
    } else {
      formik.resetForm();
    }

    return () => {
      formik.resetForm();
    };
  }, [JSON.stringify(details)]);
  const params: RouteParams = useParams();
  const dispatch = useAppSTDispatch();
  const [isOpenSelectCustomer, setIsOpenSelectCustomer] =
    useState<boolean>(false);
  const [customerFieldType, setCustomerFieldType] = useState<string>("");
  const [customerOptions, setCustomerOptions] = useState<CustomerEmailTab[]>(
    []
  );
  const [contactDetailDialogOpen, setContactDetailDialogOpen] =
    useState<boolean>(false);
  const [selectedContactId, setSelectedContactId] = useState<number | null>(
    null
  );
  const [selectedAdiContactId, setSelectedAdiContactId] = useState<
    number | null
  >(null);

  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );

    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      if (!isDateTimeVisible) {
        setLoadingStatus((prevState: IFieldStatus[]) => {
          const newState = prevState.map((item) =>
            item.field === field ? { ...item, status: status, action } : item
          );
          loadingStatusRef.current = newState;
          return newState;
        });
      }
    }
  };

  const handleUpdateField = async (
    data: ISTDetailFieldsBoolean,
    field: IFieldStatus["field"]
  ) => {
    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });
    const updateRes = (await updateSTDetailApi({
      service_ticket_id: params?.id,
      ...data,
    })) as IServiceTicketDetailApiRes;
    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
    } else {
      if (updateRes.statusCode === 403) {
        notification.error({
          description: updateRes.message,
        });
      }
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
    }
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );
      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 3000);
    return updateRes;
  };

  const selectedCustomerList = (): TselectedContactSendMail[] => {
    let selectedCustomer: TselectedContactSendMail[] = [];
    if (customerFieldType == "billedTo") {
      selectedCustomer =
        formik.values.invoice_to.user_id &&
        formik.values.invoice_to.display_name
          ? ([formik.values.invoice_to] as TselectedContactSendMail[])
          : [];
    }
    if (customerFieldType == "serviceTechnician") {
      selectedCustomer = formik.values
        .service_tech as TselectedContactSendMail[];
    }
    if (customerFieldType == "customer") {
      selectedCustomer = formik.values.customer.user_id
        ? ([formik.values.customer] as TselectedContactSendMail[])
        : [];
    }
    return selectedCustomer;
  };

  const handleNameOutsideClick = (event: MouseEvent) => {
    const clickedElement = event.target as HTMLElement;

    const datePickerDropdown = document.querySelector(".ant-picker-dropdown");
    const timePickerDropdowns = document.querySelectorAll(
      ".ant-picker-time-panel"
    );

    if (
      (datePickerDropdown && datePickerDropdown.contains(clickedElement)) ||
      Array.from(timePickerDropdowns).some((timePickerDropdown) =>
        timePickerDropdown.contains(clickedElement)
      )
    ) {
      return;
    }

    if (
      dtDivRef.current &&
      dtDivRef.current.contains(clickedElement) &&
      clickedElement.tagName.toLowerCase() === "svg" &&
      !isTimerDropdown
    ) {
      setDateTimeVisible(false);
      return;
    }

    if (dtDivRef.current && !dtDivRef.current.contains(clickedElement)) {
      setDateTimeVisible(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleNameOutsideClick);
    return () => {
      document.removeEventListener("mousedown", handleNameOutsideClick);
    };
  }, []);
  const handleNotificationData = async () => {
    setSendNotificationLoader(true);
    try {
      console.log(`\n File: #DetailsCard.tsx -> Line: #359 ->  `, details);
      if (
        details?.service_technicians &&
        details?.service_technicians?.length === 0
      ) {
        notification.error({
          description: "Please select service technician.",
        });
        setNotifyTechnicians({ id: "", flag: false });
        setSendNotificationLoader(false);
      } else if (!details?.assigned_to?.some((item) => item.email)) {
        notification.error({
          description: "No email found for service technician.",
        });
        setNotifyTechnicians({ id: "", flag: false });
        setSendNotificationLoader(false);
      } else {
        const resData: Partial<ISTNotificationApiRes> =
          await setServiceNotificationApi(Number(notifyTechnicians.id));
        setNotifyTechnicians({ id: "", flag: false });
        setSendNotificationLoader(false);
        if (!resData.success) {
          notification.error({
            description: resData.message || "Something went wrong.",
          });
        }
      }
    } catch (err) {}
  };
  const dateTimeInputValue = useMemo(() => {
    let value = "";
    if (!!formik.values.service_date_only) {
      value = formik.values.service_date_only;
    }
    if (!!formik.values.service_time) {
      value += (value ? " " : "") + formik.values.service_time;
      if (!!formik.values.service_end_time) {
        value += " - " + formik.values.service_end_time;
      }
    }
    return value;
  }, [
    formik.values.service_date_only,
    formik.values.service_time,
    formik.values.service_end_time,
  ]);

  const handleDateChange = useCallback(
    debounce(
      async (
        dateString: string,
        form: typeof formik.values,
        stDetails: typeof details
      ) => {
        const jobStatus =
          typeof stDetails.job_status === "string"
            ? Number(stDetails.job_status)
            : stDetails.job_status;
        if (!!dateString && dateString !== form.service_date_only) {
          let extraFields = {};
          // "Unscheduled" value is 261
          if (jobStatus?.toString() === "261") {
            extraFields = {
              job_status: "149", // "Scheduled" value is 149
            };
          }
          formik.setFieldValue("service_date_only", dateString);
          const service_date = dateString
            ? backendDateFormat(dateString, CFConfig.day_js_date_format) || ""
            : "";
          const response = await handleUpdateField(
            {
              service_date: service_date || null,
              ...extraFields,
            },
            "service_date"
          );
          if (response.success) {
            dispatch(
              updateSTDetail({
                service_date,
                service_date_only: dateString,
                ...extraFields,
              })
            );
          } else {
            formik.setFieldValue(
              "service_date_only",
              stDetails.service_date_only
            );
          }
        } else {
          if (!dateString && jobStatus?.toString() !== "261") {
            setIsConfirmDialogOpen(true);
          } else {
            const response = await handleUpdateField(
              {
                service_date: "",
              },
              "service_date"
            );
            if (response.success) {
              dispatch(
                updateSTDetail({
                  service_date: "",
                  service_date_only: dateString,
                })
              );
            } else {
              formik.setFieldValue(
                "service_date_only",
                stDetails.service_date_only
              );
            }
          }
        }
      },
      300
    ),
    []
  );

  const handleStartTimeChange = useCallback(
    debounce(
      async (
        time: string,
        form: typeof formik.values,
        stDetails: typeof details
      ) => {
        const { service_end_time, service_time } = form;
        const trimmedEndTime = service_end_time?.trim();
        const formattedTime = backendTimeFormat(time);

        const updateFields = async (
          fields: ISTDetailFieldsBoolean,
          detailUpdate: ISTDetailFieldsBoolean
        ) => {
          const response = await handleUpdateField(fields, "service_time");
          if (response.success) {
            dispatch(updateSTDetail(detailUpdate));
          } else {
            formik.setFieldValue("service_time", stDetails.service_time);
          }
        };

        const clearFields = async () => {
          formik.setFieldValue("service_time", "");
          formik.setFieldValue("service_end_time", "");
          await updateFields(
            {
              service_time: null,
              service_end_time: null,
            },
            { service_time: "", service_end_time: "" }
          );
        };

        if (trimmedEndTime) {
          if (isEndTimeGreaterThanStartTimeAMPM(time, trimmedEndTime)) {
            if (service_time !== time) {
              formik.setFieldValue("service_time", time);
              await updateFields(
                {
                  service_time: time ? formattedTime : null,
                },
                { service_time: time }
              );
            } else {
              await clearFields();
            }
          } else {
            if (time && trimmedEndTime) {
              notification.error({
                description: _t("End Time should be greater than Start Time."),
              });
            } else {
              await clearFields();
            }
          }
        } else {
          formik.setFieldValue("service_time", time);
          await updateFields(
            { service_time: time ? formattedTime : null },
            { service_time: time }
          );
        }
      },
      300
    ),
    []
  );
  const handleEndTimeChange = useCallback(
    debounce(
      async (
        time: string,
        form: typeof formik.values,
        stDetails: typeof details
      ) => {
        const { service_end_time, service_time } = form;
        const trimmedStartTime = service_time?.trim();
        const formattedTime = backendTimeFormat(time);

        const updateFields = async (
          fields: ISTDetailFieldsBoolean,
          detailUpdate: ISTDetailFieldsBoolean
        ) => {
          const response = await handleUpdateField(fields, "service_end_time");
          if (response.success) {
            dispatch(updateSTDetail(detailUpdate));
          } else {
            formik.setFieldValue(
              "service_end_time",
              stDetails.service_end_time
            );
          }
        };

        const clearFields = async () => {
          formik.setFieldValue("service_end_time", "");
          await updateFields(
            {
              service_end_time: null,
            },
            { service_end_time: "" }
          );
        };

        if (trimmedStartTime) {
          if (isEndTimeGreaterThanStartTimeAMPM(trimmedStartTime, time)) {
            if (service_end_time !== time) {
              await updateFields(
                {
                  service_end_time: time ? formattedTime : null,
                },
                { service_end_time: time }
              );
            } else {
              await clearFields();
            }
          } else {
            if (time) {
              notification.error({
                description: _t("End Time should be greater than Start Time."),
              });
              formik.setFieldValue(
                "service_end_time",
                stDetails.service_end_time
              );
            } else {
              await clearFields();
            }
          }
        } else {
          notification.error({ description: _t("Please select start time.") });
          formik.setFieldValue("service_end_time", stDetails.service_end_time);
        }
      },
      300
    ),
    []
  );

  const handleEndTimeChangeClear = useCallback(
    debounce(
      async (
        time: string,
        form: typeof formik.values,
        stDetails: typeof details
      ) => {
        const { service_end_time, service_time } = form;
        const trimmedStartTime = service_time?.trim();
        const formattedTime = backendTimeFormat(time);

        const updateFields = async (
          fields: ISTDetailFieldsBoolean,
          detailUpdate: ISTDetailFieldsBoolean
        ) => {
          const response = await handleUpdateField(fields, "service_end_time");
          if (response.success) {
            dispatch(updateSTDetail(detailUpdate));
          } else {
            formik.setFieldValue(
              "service_end_time",
              stDetails.service_end_time
            );
          }
        };

        const clearFields = async () => {
          formik.setFieldValue("service_end_time", "");
          await updateFields(
            {
              service_end_time: null,
            },
            { service_end_time: "" }
          );
        };

        if (trimmedStartTime) {
          if (isEndTimeGreaterThanStartTimeAMPM(trimmedStartTime, time)) {
            if (service_end_time !== time) {
              await updateFields(
                {
                  service_end_time: time ? formattedTime : null,
                },
                { service_end_time: time }
              );
            } else {
              await clearFields();
            }
          }
        }
      },
      300
    ),
    []
  );
  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Details")}
        iconProps={{
          icon: "fa-solid fa-file-lines",
          containerClassName:
            "bg-[linear-gradient(180deg,#FF868A1a_0%,#FD33331a_100%)]",
          id: "details_icon",
          colors: ["#FF868A", "#FD3333"],
        }}
        children={
          <div className="pt-2">
            <ul className="w-full flex flex-col gap-1 mt-[3px]">
              <li
                className={`overflow-hidden flex ${
                  !isDateTimeVisible ? "hidden" : ""
                }`}
                // ref={dtDivRef}
              >
                <InlineField
                  label={_t("Service Date/Time")}
                  labelPlacement="left"
                  field={
                    <div className="grid grid-cols-3 gap-1 items-center w-full">
                      <DatePickerField
                        label=""
                        name="service_date_only"
                        labelPlacement="left"
                        placeholder={_t("Select Date")}
                        editInline={true}
                        inputReadOnly={true}
                        iconView={true}
                        readOnly={isReadOnly}
                        allowClear={true}
                        value={
                          formik.values.service_date_only
                            ? dayjs(
                                formik.values.service_date_only,
                                CFConfig.day_js_date_format
                              )
                            : undefined
                        }
                        format={CFConfig.day_js_date_format}
                        fixStatus={getStatusForField(
                          loadingStatus,
                          "service_date"
                        )}
                        onChange={(date, dateString) => {
                          if (typeof dateString === "string") {
                            handleDateChange(
                              dateString,
                              formik.values,
                              details
                            );
                          }
                        }}
                        disabled={
                          getStatusForField(
                            loadingStatus,
                            "service_date_only"
                          ) === "loading"
                        }
                      />
                      <TimePickerField
                        label=""
                        editInline={true}
                        inputReadOnly={true}
                        iconView={true}
                        readOnly={isReadOnly}
                        placeholder="Start Time"
                        labelPlacement="left"
                        format="hh:mm A"
                        showNow={true}
                        onFocus={() => {
                          setTimerDropdown(true);
                        }}
                        value={
                          formik.values.service_time?.trim()
                            ? dayjs(
                                formik.values.service_time.trim(),
                                "hh:mm A"
                              )
                            : null
                        }
                        onChange={async (_, time) => {
                          if (typeof time === "string") {
                            handleStartTimeChange(time, formik.values, details);
                            setDateTimeVisible(false);
                          }
                        }}
                      />
                      <TimePickerField
                        label=""
                        editInline={true}
                        inputReadOnly={true}
                        iconView={true}
                        placeholder="End Time"
                        readOnly={isReadOnly}
                        labelPlacement="left"
                        format="hh:mm A"
                        showNow={true}
                        onFocus={() => {
                          setTimerDropdown(true);
                        }}
                        value={
                          formik.values.service_end_time?.trim()
                            ? dayjs(
                                formik.values.service_end_time.trim(),
                                "hh:mm A"
                              )
                            : null
                        }
                        onChange={async (_, time) => {
                          if (typeof time === "string") {
                            handleEndTimeChange(time, formik.values, details);
                            setDateTimeVisible(false);
                          }
                        }}
                      />
                    </div>
                  }
                />
              </li>
              <li
                className={`overflow-hidden ${
                  isDateTimeVisible ? "hidden" : ""
                }`}
                ref={dateTimeVisibleSelectRef}
              >
                <InlineField
                  label={_t("Service Date/Time")}
                  labelPlacement="left"
                  field={
                    <div className="relative w-full group/edit">
                      <InputField
                        labelPlacement="left"
                        placeholder={_t("Select Date")}
                        editInline={true}
                        iconView={true}
                        required={false}
                        readOnly={isReadOnly}
                        readOnlyClassName="!h-[34px]"
                        value={dateTimeInputValue}
                        fixStatus={getStatusForField(
                          loadingStatus,
                          "service_date_time"
                        )}
                        onFocus={() => {
                          setDateTimeVisible(true);
                        }}
                        onMouseEnter={() => {
                          handleChangeFieldStatus({
                            field: "service_date_time",
                            status: "edit",
                            action: "ME",
                          });
                        }}
                        onMouseLeaveDiv={() => {
                          handleChangeFieldStatus({
                            field: "service_date_time",
                            status: "button",
                            action: "ML",
                          });
                        }}
                        onChange={() => {}}
                      />
                      {dateTimeInputValue &&
                      !isReadOnly &&
                      !["loading", "success", "error"].includes(
                        getStatusForField(loadingStatus, "service_date_time")
                      ) ? (
                        <div className="absolute z-10 top-1/2 -translate-y-1/2 right-2.5 opacity-0 group-hover/edit:opacity-100">
                          <FontAwesomeIcon
                            icon="fa-solid fa-circle-xmark"
                            className="text-[#00000040] hover:text-[#00000073] cursor-pointer w-3 h-3"
                            onClick={() => {
                              handleEndTimeChangeClear(
                                "",
                                formik.values,
                                details
                              );
                              handleStartTimeChange("", formik.values, details);
                              handleDateChange("", formik.values, details);
                            }}
                          />
                        </div>
                      ) : null}
                    </div>
                  }
                />
              </li>
              <li className="overflow-hidden">
                <ButtonField
                  label={_t("Customer")}
                  placeholder={_t("Select Customer")}
                  name="customer_id"
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  value={formik.values.customer.display_name ?? ""}
                  statusProps={{
                    status: getStatusForField(loadingStatus, "customer_id"),
                  }}
                  onClick={() => {
                    setCustomerFieldType("customer");
                    setCustomerOptions([
                      defaultConfig.customer_key,
                      defaultConfig.lead_key,
                      "my_project",
                    ]);
                    setIsOpenSelectCustomer(true);
                  }}
                  disabled={
                    getStatusForField(loadingStatus, "customer_id") ===
                    "loading"
                  }
                  avatarProps={
                    formik.values.customer.display_name
                      ? {
                          user: {
                            name: HTMLEntities.decode(
                              sanitizeString(
                                formik.values.customer.display_name
                              )
                            ),
                            image: formik.values.customer.image,
                          },
                        }
                      : undefined
                  }
                  rightIcon={
                    <>
                      {formik.values.customer.display_name &&
                        formik.values.customer.user_id && (
                          <div className="flex gap-1 items-center">
                            <ContactDetailsButton
                              onClick={async () => {
                                setSelectedContactId(
                                  formik.values.customer.user_id || null
                                );
                                setSelectedAdiContactId(
                                  Number(
                                    formik.values.customer.contact_id || ""
                                  )
                                );
                                setContactDetailDialogOpen(true);
                              }}
                            />
                            <DirectoryFieldRedirectionIcon
                              directoryId={formik.values.customer.user_id?.toString()}
                              directoryTypeKey={
                                (formik.values.customer.type_key !== "contact"
                                  ? formik.values.customer.type_key
                                  : formik.values.customer.parent_type_key) ||
                                ""
                              }
                            />
                          </div>
                        )}
                    </>
                  }
                />
              </li>
              <li className="overflow-hidden">
                <ButtonField
                  label={_t("Invoiced To")}
                  placeholder={_t("If Not Customer")}
                  labelPlacement="left"
                  editInline={true}
                  readOnly={isReadOnly}
                  iconView={
                    formik.values.invoice_to.display_name ? true : false
                  }
                  value={formik.values.invoice_to.display_name ?? ""}
                  statusProps={{
                    status: getStatusForField(loadingStatus, "billed_to"),
                  }}
                  onClick={() => {
                    setCustomerFieldType("billedTo");
                    setCustomerOptions([
                      defaultConfig.employee_key,
                      "my_crew",
                      defaultConfig.customer_key,
                      defaultConfig.contractor_key,
                      defaultConfig.vendor_key,
                      defaultConfig.misc_contact_key,
                      "by_service",
                      "my_project",
                    ]);
                    setIsOpenSelectCustomer(true);
                  }}
                  disabled={
                    getStatusForField(loadingStatus, "billed_to") === "loading"
                  }
                  avatarProps={
                    formik.values.invoice_to.display_name
                      ? {
                          user: {
                            name: HTMLEntities.decode(
                              sanitizeString(
                                formik.values.invoice_to.display_name
                              )
                            ),
                            image: formik.values.invoice_to.image,
                          },
                        }
                      : undefined
                  }
                  rightIcon={
                    <>
                      {formik.values.invoice_to.display_name ? (
                        formik.values.invoice_to.user_id ? (
                          <div className="flex gap-1 items-center">
                            <ContactDetailsButton
                              onClick={async () => {
                                setSelectedContactId(
                                  formik.values.invoice_to.user_id || null
                                );
                                setSelectedAdiContactId(
                                  Number(
                                    formik.values.invoice_to.contact_id || ""
                                  )
                                );
                                setContactDetailDialogOpen(true);
                              }}
                            />
                            <DirectoryFieldRedirectionIcon
                              directoryId={
                                formik.values.invoice_to.user_id.toString() ||
                                ""
                              }
                              directoryTypeKey={
                                (formik.values.invoice_to.type_key !== "contact"
                                  ? formik.values.invoice_to.type_key
                                  : formik.values.invoice_to.parent_type_key) ||
                                ""
                              }
                            />
                          </div>
                        ) : null
                      ) : null}
                    </>
                  }
                />
              </li>
              <li className="overflow-hidden">
                <ButtonField
                  label={_t("Service Technician")}
                  placeholder={_t("Select Service Technician")}
                  labelPlacement="left"
                  editInline={true}
                  readOnly={isReadOnly}
                  iconView={true}
                  value={
                    formik.values.service_tech.length
                      ? formik.values.service_tech.length > 1
                        ? formik.values.service_tech.length + " Selected"
                        : HTMLEntities.decode(
                            sanitizeString(
                              formik.values.service_tech
                                .map((item) => item?.display_name)
                                .join(", ")
                            )
                          )
                      : ""
                  }
                  avatarProps={
                    formik.values.service_tech?.length == 1
                      ? {
                          user: {
                            name: HTMLEntities.decode(
                              sanitizeString(
                                formik.values.service_tech[0]?.display_name
                              )
                            ),
                            image: formik.values.service_tech[0]?.image,
                          },
                        }
                      : undefined
                  }
                  statusProps={
                    formik.values.service_tech &&
                    formik.values.service_tech.length > 0
                      ? {
                          status: getStatusForField(
                            loadingStatus,
                            "service_technicians"
                          ),
                        }
                      : {}
                  }
                  onClick={() => {
                    setCustomerFieldType("serviceTechnician");
                    setCustomerOptions([
                      defaultConfig.employee_key,
                      "my_crew",
                      defaultConfig.contractor_key,
                      "by_service",
                      "my_project",
                    ]);
                    setIsOpenSelectCustomer(true);
                  }}
                  rightIcon={
                    <div className="flex items-center gap-1">
                      {formik.values.service_tech &&
                      formik.values.service_tech.length ? (
                        formik.values.service_tech.length === 1 ? (
                          <div className="flex gap-1 items-center">
                            <ContactDetailsButton
                              onClick={() => {
                                setContactDetailDialogOpen(true);
                                setSelectedContactId(
                                  formik.values.service_tech[0]
                                    .user_id as number
                                );
                                setSelectedAdiContactId(
                                  Number(
                                    formik.values.service_tech[0].contact_id ||
                                      ""
                                  )
                                );
                              }}
                            />
                            <DirectoryFieldRedirectionIcon
                              directoryId={
                                formik.values.service_tech[0].user_id?.toString() ||
                                ""
                              }
                              directoryTypeKey={
                                (formik.values.service_tech[0]?.dir_type_name?.toLowerCase() !==
                                "contact"
                                  ? formik.values.service_tech[0]?.dir_type_name?.toLowerCase()
                                  : formik.values.service_tech[0]
                                      .parent_type_key) || ""
                              }
                            />
                          </div>
                        ) : (
                          <AvatarIconPopover
                            placement="bottom"
                            redirectionIcon={true}
                            assignedTo={
                              formik.values.service_tech as IAssignedToUsers[]
                            }
                            setSelectedUserId={(data) => {
                              setSelectedContactId(data?.id);
                              setSelectedAdiContactId(data?.contactId || 0);
                            }}
                            setIsOpenContactDetails={setContactDetailDialogOpen}
                          />
                        )
                      ) : (
                        <></>
                      )}
                      {formik.values.service_tech &&
                        formik.values.service_tech.length > 0 &&
                        !isReadOnly && (
                          <ButtonWithTooltip
                            tooltipTitle={_t("Send Notification")}
                            tooltipPlacement="top"
                            icon="fa-regular fa-paper-plane"
                            onClick={() =>
                              setNotifyTechnicians({
                                id: Number(details?.service_ticket_id),
                                flag: true,
                              })
                            }
                          />
                        )}
                    </div>
                  }
                />
              </li>
              <li>
                <InputField
                  label={_t("Customer Contract") + " #"}
                  placeholder={_t("Customer Contract") + " #"}
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  value={formik.values.customer_contract}
                  maxLength={22}
                  name="customer_contract"
                  fixStatus={getStatusForField(
                    loadingStatus,
                    "customer_contract"
                  )}
                  onChange={(e) => {
                    formik.setFieldValue("customer_contract", e.target.value);
                  }}
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "customer_contract",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "customer_contract",
                      status: "button",
                      action: "ML",
                    });
                  }}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "customer_contract",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onBlur={async (e) => {
                    const value = e.target.value.trim();
                    if ((value || "") !== (details?.customer_contract || "")) {
                      const response = await handleUpdateField(
                        { customer_contract: value },
                        "customer_contract"
                      );
                      if (response.success) {
                        dispatch(updateSTDetail({ customer_contract: value }));
                      } else {
                        formik.setFieldValue(
                          "customer_contract",
                          details?.customer_contract
                        );
                      }
                    } else {
                      handleChangeFieldStatus({
                        field: "customer_contract",
                        status: "button",
                        action: "BLUR",
                      });
                      formik.setFieldValue(
                        "customer_contract",
                        details?.customer_contract
                      );
                    }
                  }}
                />
              </li>
              <li>
                <SelectField
                  label={_t("Duration")}
                  placeholder={_t("Select Duration")}
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  allowClear={true}
                  options={ST_DURATION_TIME}
                  showSearch={false}
                  fixStatus={getStatusForField(
                    loadingStatus,
                    "service_duration"
                  )}
                  value={formik.values.service_duration || undefined}
                  onChange={async (value: string | string[]) => {
                    let newValue = "";
                    if (!Array.isArray(value)) {
                      newValue = value;
                    }
                    formik.setFieldValue("service_duration", newValue);

                    if (
                      (newValue || "") !== (details?.service_duration || "")
                    ) {
                      const response = await handleUpdateField(
                        {
                          service_duration:
                            newValue === undefined ? "" : newValue,
                        },
                        "service_duration"
                      );
                      if (response.success) {
                        dispatch(
                          updateSTDetail({
                            service_duration:
                              newValue === undefined ? "" : newValue,
                          })
                        );
                      } else {
                        formik.setFieldValue(
                          "service_duration",
                          details?.service_duration
                        );
                      }
                    } else {
                      formik.setFieldValue(
                        "service_duration",
                        details?.service_duration || ""
                      );
                    }
                  }}
                />
              </li>
              <li>
                <TextAreaField
                  label={_t("Description")}
                  ref={descriptionFieldRef}
                  rows={3}
                  placeholder={_t("Service Description")}
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  value={formik.values.description}
                  name="description"
                  fixStatus={getStatusForField(loadingStatus, "description")}
                  onChange={(e) => {
                    formik.setFieldValue("description", e.target.value);
                  }}
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "description",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "description",
                      status: "button",
                      action: "ML",
                    });
                  }}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "description",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onBlur={async (e) => {
                    const value = e.target.value.trim();
                    if ((value || "") !== (details?.description || "")) {
                      formik.setFieldValue("description", value);
                      const response = await handleUpdateField(
                        { description: value },
                        "description"
                      );
                      if (response.success) {
                        dispatch(updateSTDetail({ description: value }));
                      } else {
                        formik.setFieldValue(
                          "description",
                          details?.description
                        );
                      }
                    } else {
                      handleChangeFieldStatus({
                        field: "description",
                        status: "button",
                        action: "BLUR",
                      });
                      formik.setFieldValue("description", details?.description);
                    }
                  }}
                  onClickStsIcon={() => {
                    if (
                      getStatusForField(loadingStatus, "description") === "edit"
                    ) {
                      descriptionFieldRef?.current?.focus();
                    }
                  }}
                />
              </li>
              <li>
                <TextAreaField
                  ref={noteFieldRef}
                  label={_t("Service Notes")}
                  rows={3}
                  placeholder={_t("Service Notes")}
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  value={formik.values.notes}
                  name="notes"
                  fixStatus={getStatusForField(loadingStatus, "notes")}
                  onChange={(e) => {
                    formik.setFieldValue("notes", e.target.value);
                  }}
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "notes",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "notes",
                      status: "button",
                      action: "ML",
                    });
                  }}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "notes",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onBlur={async (e) => {
                    const value = e.target.value.trim();
                    if ((value || "") !== (details?.notes || "")) {
                      formik.setFieldValue("notes", value);
                      const response = await handleUpdateField(
                        { notes: value },
                        "notes"
                      );
                      if (response.success) {
                        dispatch(updateSTDetail({ notes: value }));
                      } else {
                        formik.setFieldValue("notes", details?.notes);
                      }
                    } else {
                      handleChangeFieldStatus({
                        field: "notes",
                        status: "button",
                        action: "BLUR",
                      });
                      formik.setFieldValue("notes", details?.notes);
                    }
                  }}
                  onClickStsIcon={() => {
                    if (getStatusForField(loadingStatus, "notes") === "edit") {
                      noteFieldRef?.current?.focus();
                    }
                  }}
                />
              </li>
            </ul>
          </div>
        }
      />
      {isOpenSelectCustomer && (
        <SelectCustomerDrawer
          projectId={details?.project_id}
          openSelectCustomerSidebar={isOpenSelectCustomer}
          closeDrawer={() => {
            dispatch(setActiveField(customerOptions[0]));
            setIsOpenSelectCustomer(false);
            setCustomerFieldType("");
            setCustomerOptions([]);
          }}
          singleSelecte={
            customerFieldType === "serviceTechnician" ? false : true
          }
          options={customerOptions?.length > 0 ? customerOptions : []}
          setCustomer={async (cust) => {
            const data: any = cust;

            const formikInvoiceTo =
              customerFieldType === "serviceTechnician"
                ? {}
                : {
                    user_id: details.billed_to,
                    display_name: details.billed_to_display_name,
                    billed_to_dir_type: details.billed_to_dir_type,
                    contact_id: details.billed_to_contact,
                    type_name:
                      details.invoiced_to_data?.length &&
                      details.invoiced_to_data[0].type_name,
                    image: details.billed_to_contact
                      ? ""
                      : details.invoiced_to_data?.length &&
                        details.invoiced_to_data[0].image,
                    type_key:
                      details.invoiced_to_data?.length &&
                      details.invoiced_to_data[0].type_key,
                  };

            if (customerFieldType === "serviceTechnician") {
              const userIds = data.map((item: any) => item?.user_id);
              formik.setFieldValue("service_tech", data || []);
              const response = await handleUpdateField(
                {
                  service_tech: userIds?.length ? userIds.join(",") : "",
                },
                "service_technicians"
              );
              if (response.success) {
                dispatch(
                  updateSTDetail({
                    service_technicians: data.map((record: any) => ({
                      user_id: record.user_id,
                      directory_id: record.user_id,
                      display_name: record.display_name,
                      image: record.image,
                      dir_type: record.type,
                      dir_type_name: record.type_name,
                      email: record.email,
                    })),
                    assigned_to: data.map((record: any) => ({
                      user_id: record.user_id,
                      contact_id: record.contact_id,
                      user_contact_id: "80648|0",
                      company_name: null,
                      user_type: record.type,
                      assignee_name: record.display_name,
                      assigned_to_name_only: record.display_name,
                      assignee_company: null,
                      email: record.email,
                      image: record.image,
                    })),
                  })
                );
              } else {
                if (details.service_technicians) {
                  const id =
                    details.service_technicians.map(
                      (technician) => technician.user_id
                    ) || [];
                  getExistingUsersWithApi({
                    usersIds: id.join(","),
                    apiDataReturn: (
                      customers: Partial<CustomerSelectedData>[]
                    ) => {
                      formik.setFieldValue("service_tech", customers || []);
                    },
                  });
                } else {
                  formik.setFieldValue("service_tech", []);
                }
              }
            } else if (customerFieldType === "billedTo") {
              const invoiceTo = data?.length ? data[0] || {} : {};
              const display_name =
                invoiceTo?.company_name &&
                invoiceTo?.display_name &&
                !invoiceTo.display_name.includes(invoiceTo.company_name)
                  ? `${invoiceTo.display_name} (${data[0]?.company_name})`
                  : invoiceTo?.display_name?.toString();
              formik.setFieldValue("invoice_to", {
                ...invoiceTo,
                display_name: display_name,
              });
              const errorMessage = await formik.validateField("invoice_to");

              if (errorMessage) {
                notification.error({
                  description: _t(errorMessage),
                });
                formik.setFieldValue("invoice_to", formikInvoiceTo);
              } else {
                const response = await handleUpdateField(
                  {
                    billed_to: invoiceTo.user_id?.toString() || "",
                    billed_to_display_name:
                      invoiceTo.display_name?.toString() || "",
                    billed_to_dir_type: invoiceTo.orig_type?.toString() || "",
                    billed_to_contact: invoiceTo.contact_id || 0,
                  },
                  "billed_to"
                );
                if (response.success) {
                  dispatch(
                    updateSTDetail({
                      billed_to: invoiceTo.user_id?.toString() || "",
                      billed_to_display_name: display_name || "",
                      billed_to_dir_type: invoiceTo.orig_type?.toString() || "",
                      billed_to_contact: invoiceTo.contact_id || 0,
                      invoiced_to_data: data.map((record: any) => ({
                        user_id: record.user_id,
                        first_name: record.first_name,
                        last_name: record.last_name,
                        image: record.image,
                        address1: record.address1,
                        address2: record.address2,
                        phone: record.phone,
                        cell: record.cell,
                        email: record.email,
                        city: record.city,
                        state: record.state,
                        zip: record.zip,
                        type: record.type,
                        company_name: record.company_name,
                        type_name: record.type_name,
                        type_key: record.type_key,
                        parent_type_key: record.parent_type_key,
                      })),
                    })
                  );
                } else {
                  formik.setFieldValue("invoice_to", formikInvoiceTo);
                }
              }
            } else {
              if (data.length > 0) {
                const customer = data?.length ? data[0] || {} : {};

                const display_name =
                  customer?.company_name &&
                  customer?.display_name &&
                  !customer.display_name.includes(customer.company_name)
                    ? `${customer.display_name} (${data[0]?.company_name})`
                    : customer?.display_name?.toString();
                formik.setFieldValue("customer", {
                  ...customer,
                  display_name: display_name || "",
                });
                const formData = {
                  customer_id: customer.user_id?.toString() || "",
                  project_id: null,
                  project_name: null,
                  customer_street: customer?.address1 || "",
                  customer_street2: customer?.address2 || "",
                  customer_city: customer?.city || "",
                  customer_state: customer?.state || "",
                  customer_zip: customer?.zip || "",
                  customer_email: customer?.email || "",
                  contact_id: customer?.contact_id,
                  customer_company: customer.company_name?.toString() || "",
                  cust_access_code: customer?.access_code || null,

                  // need to reset data of invoice To field
                  billed_to: "",
                  billed_to_display_name: "",
                  billed_to_dir_type: "",
                  billed_to_contact: 0,
                };

                let updatedData = {
                  customer_first_name: customer.first_name,
                  customer_id: customer.user_id,
                  contact_id: customer.contact_id,
                  customer_last_name: customer.last_name,
                  customer_city: customer?.city || "",
                  customer_state: customer?.state || "",
                  cust_company: customer.company_name?.toString() || "",
                  cust_email: customer?.email || "",
                  cust_street: customer?.address1 || "",
                  cust_street2: customer?.address2 || "",
                  cust_zip: customer?.zip || "",
                  cust_access_code: customer?.access_code || null,
                  customer_name: display_name || customer?.display_name || "",
                  project_id: null,
                  project_name: null,
                  dir_type: customer.type,
                  customer_data: data.map((record: any) => ({
                    user_id: record.user_id,
                    first_name: record.first_name,
                    last_name: record.last_name,
                    image: record.image,
                    address1: record.address1,
                    address2: record.address2,
                    phone: record.phone,
                    cell: record.cell,
                    email: record.email,
                    city: record.city,
                    state: record.state,
                    zip: record.zip,
                    type: record.type,
                    company_name: record.company_name,
                    type_name: record.type_name,
                    type_key: record.type_key,
                    parent_type_key: record.parent_type_key,
                  })),
                };

                if (customer.billed_to === "0") {
                  updatedData = {
                    ...updatedData,
                    billed_to: details.billed_to,
                    billed_to_display_name: details.billed_to_display_name,
                    billed_to_dir_type: details.billed_to_dir_type,
                    billed_to_contact: details.billed_to_contact,
                    invoiced_to_data: details.invoiced_to_data,
                  };
                } else {
                  updatedData = {
                    ...updatedData,
                    billed_to: "",
                    billed_to_display_name: "",
                    billed_to_dir_type: "",
                    billed_to_contact: 0,
                    invoiced_to_data: [],
                  };
                }
                const formikCustomer = {
                  user_id: details.customer_id,
                  display_name: details.customer_name,
                  type_name: getDirectaryIdByName(Number(details?.dir_type)),
                  contact_id: details.contact_id || 0,
                  image: details.contact_id
                    ? ""
                    : details.customer_data?.length &&
                      details.customer_data[0].image,
                  type_key:
                    details.customer_data?.length &&
                    details.customer_data?.length > 0
                      ? details.customer_data[0].type_key
                      : "",
                };

                const errorMessage = await formik.validateField("customer");

                if (errorMessage) {
                  notification.error({
                    description: _t(errorMessage),
                  });
                  formik.setFieldValue("customer", formikCustomer);
                } else if (Number(customer.billed_to)) {
                  handleChangeFieldStatus({
                    field: "customer_id",
                    status: "loading",
                    action: "API",
                  });
                  getExistingUsersWithApi({
                    usersIds: customer.billed_to?.toString() || "",
                    apiDataReturn: async (
                      customers: Partial<CustomerSelectedData>[]
                    ) => {
                      if (customers?.length) {
                        const invoiceTo = customers[0] || {};
                        formik.setFieldValue("invoice_to", invoiceTo);
                        const response = await handleUpdateField(
                          {
                            ...formData,
                            billed_to: invoiceTo.user_id?.toString() || "",
                            billed_to_display_name:
                              invoiceTo.display_name?.toString() || "",
                            billed_to_dir_type:
                              invoiceTo.orig_type?.toString() || "",
                            billed_to_contact: invoiceTo.contact_id || 0,
                          },
                          "billed_to"
                        );
                        if (response.success) {
                          handleChangeFieldStatus({
                            field: "customer_id",
                            status: "success",
                            action: "API",
                          });

                          dispatch(
                            updateSTDetail({
                              ...updatedData,
                              billed_to: invoiceTo.user_id?.toString() || "",
                              billed_to_display_name:
                                invoiceTo.display_name?.toString() || "",
                              billed_to_dir_type:
                                invoiceTo.orig_type?.toString() || "",
                              billed_to_contact: invoiceTo.contact_id || 0,
                              billed_to_image: invoiceTo.image,
                              invoiced_to_data: data.map((record: any) => ({
                                user_id: record.user_id,
                                first_name: record.first_name,
                                last_name: record.last_name,
                                image: invoiceTo.image,
                                address1: record.address1,
                                address2: record.address2,
                                phone: record.phone,
                                cell: record.cell,
                                email: record.email,
                                city: record.city,
                                state: record.state,
                                zip: record.zip,
                                type: record.type,
                                company_name: record.company_name,
                                type_name: record.type_name,
                                type_key: record.type_key,
                              })),
                            })
                          );
                        } else {
                          formik.setFieldValue("invoice_to", formikInvoiceTo);
                          handleChangeFieldStatus({
                            field: "customer_id",
                            status: "error",
                            action: "API",
                          });
                        }
                        delay(() => {
                          const fieldAction = getStatusActionForField(
                            loadingStatusRef.current,
                            "customer_id"
                          );
                          handleChangeFieldStatus({
                            field: "customer_id",
                            status: "FOCUS" === fieldAction ? "save" : "button",
                            action: fieldAction || "API",
                          });
                        }, 3000);
                      } else {
                        const response = await handleUpdateField(
                          formData,
                          "customer_id"
                        );
                        if (response.success) {
                          dispatch(updateSTDetail(updatedData));
                        } else {
                          formik.setFieldValue("customer", formikCustomer);
                        }
                      }
                    },
                  });
                } else {
                  const response = await handleUpdateField(
                    formData,
                    "customer_id"
                  );

                  if (response.success) {
                    const invoiceDetails = response?.data;

                    if (updatedData.customer_data?.length > 0) {
                      updatedData.customer_data[0].billed_to_image =
                        invoiceDetails.billed_to_image || "";
                    }
                    dispatch(
                      updateSTDetail({
                        ...updatedData,
                        billed_to: invoiceDetails.billed_to?.toString() || "",
                        billed_to_display_name:
                          invoiceDetails.billed_to_display_name?.toString() ||
                          "",
                        billed_to_dir_type:
                          invoiceDetails.billed_to_dir_type?.toString() || "",
                        billed_to_contact:
                          invoiceDetails.billed_to_contact || 0,
                        billed_to_image: invoiceDetails.billed_to_image || "",
                      })
                    );
                  } else {
                    formik.setFieldValue("customer", formikCustomer);
                  }
                }
              } else {
                notification.error({
                  description: _t("Customer field is required."),
                });
              }
            }
          }}
          selectedCustomer={selectedCustomerList()}
          groupCheckBox={true}
          additionalContactDetails={
            customerFieldType === "serviceTechnician" ? 0 : 1
          }
        />
      )}
      {contactDetailDialogOpen && selectedContactId && (
        <ContactDetailsModal
          isOpenContact={contactDetailDialogOpen}
          onCloseModal={() => setContactDetailDialogOpen(false)}
          contactId={Number(selectedContactId)}
          readOnly={isReadOnly}
          // projectId={details?.project_id}
          additional_contact_id={Number(selectedAdiContactId || "")} // as per PHP additional contact will not select from here
        />
      )}
      {notifyTechnicians && (
        <ConfirmModal
          isOpen={notifyTechnicians.flag}
          modalIcon="fa-regular fa-bell"
          descriptionclassName="text-primary-900"
          modaltitle={_t("Notify Technicians")}
          isLoading={sendNotificationLoader}
          description={_t(
            `Do you want to send a notification to the assigned contacts? Notifications will be sent based on the preferences within the user's account.`
          )}
          onCloseModal={() => setNotifyTechnicians({ id: "", flag: false })}
          onAccept={() => {
            handleNotificationData();
          }}
          onDecline={() => setNotifyTechnicians({ id: "", flag: false })}
        />
      )}
      {isConfirmDialogOpen && (
        <ConfirmModal
          isOpen={isConfirmDialogOpen}
          modalIcon="fa-regular fa-file-check"
          modaltitle={"Confirmation"}
          description={_t(
            `A service date is required to mark this ticket as Scheduled. Unless service date is added. No other status other than “Unscheduled” will be assigned. Are you sure you want to add service ticket without service date?`
          )}
          onCloseModal={() => {
            setIsConfirmDialogOpen(false);
            formik.setFieldValue(
              "service_date_only",
              details.service_date_only
            );
          }}
          onAccept={async () => {
            let formData: ISTDetailFieldsBoolean = {
              service_date: "",
            };
            if (details?.job_status?.toString() !== "152") {
              formData = {
                ...formData,
                job_status: 261,
              };
            }
            const response = await handleUpdateField(formData, "service_date");
            if (response.success) {
              dispatch(
                updateSTDetail({
                  service_date: "",
                  service_date_only: "",
                  job_status: formData.job_status || details?.job_status,
                })
              );
            } else {
              formik.setFieldValue(
                "service_date_only",
                details.service_date_only
              );
            }

            setIsConfirmDialogOpen(false);
          }}
          onDecline={() => {
            setIsConfirmDialogOpen(false);
            formik.setFieldValue(
              "service_date_only",
              details.service_date_only
            );
          }}
        />
      )}
    </>
  );
};

export default DetailsCard;

const isEndTimeGreaterThanStartTimeAMPM = (
  startTime: string,
  endTime: string
) => {
  // Parse time and period (AM/PM)
  const parseTime = (time: string) => {
    const [timePart, period] = time.split(" ");
    let [hours, minutes] = timePart.split(":").map(Number);

    // Convert to 24-hour format
    if (period === "PM" && hours !== 12) {
      hours += 12;
    }
    if (period === "AM" && hours === 12) {
      hours = 0;
    }

    return { hours, minutes };
  };

  const start = parseTime(startTime);
  const end = parseTime(endTime);

  // Create Date objects for a fixed date with respective times
  const startDate = new Date(2000, 0, 1, start.hours, start.minutes);
  const endDate = new Date(2000, 0, 1, end.hours, end.minutes);

  // Compare the two dates
  return endDate > startDate;
};
