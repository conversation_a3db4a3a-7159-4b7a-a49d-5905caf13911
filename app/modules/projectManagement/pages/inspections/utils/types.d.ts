interface IDetailsTopBarProps {
  sidebarCollapse: any; // Notes: developer side change this type
  onReloadDirDetails: () => void;
}

interface IInspectionByAgencyData {
  inspection_agency_name: string;
  agency_count: string;
}

interface IInspectionByAgency {
  data: IInspectionByAgencyData[];
  last_refres_time: string;
}

interface IInspectionsByProjectData {
  project_name: string;
  inspection_count_by_project: string;
  progress_percent: number;
}

interface IInspectionsByProject {
  data: IInspectionsByProjectData[];
  last_refres_time: string;
}

interface IInspectionWithTasksToCompeleteData {
  inspection_id: number;
  custom_insp_id: string;
  project_name: string;
  inspection_type: string;
}

interface IInspectionWithTasksToCompelete {
  data: IInspectionWithTasksToCompeleteData[];
  last_refres_time: string;
}

interface IInspectionsFailsInspectionsData {
  insp_date: string;
  insp_id: string;
  inspection_id: number;
  inspection_type: string;
  project_name: string;
}

interface IInspectionsFailsInspections {
  data: IInspectionsFailsInspectionsData[];
  last_refres_time: string;
}

interface IUpcomingInspectionsData {
  project_name: string;
  inspection_type: string;
  insp_date_time: string;
  inspection_date: string;
  inspection_time: string;
}

interface IUpcomingInspections {
  data: IUpcomingInspectionsData[];
  last_refres_time: string;
}

interface IInspectionLastYearChartData {
  insp_count: string;
  inspection_status_name: string;
  inspection_status_color: string;
}

interface IInspectionLastYearChart {
  data: IInspectionLastYearChartData[];
  last_refres_time: string;
}

interface IInspectionThisYearChart {
  data: IInspectionLastYearChartData[];
  last_refres_time: string;
}

interface IInspectionDashBoradData {
  get_inspections_by_agency: IInspectionByAgency;
  get_inspections_by_project: IInspectionsByProject;
  get_inspections_with_tasks_to_complete: IInspectionWithTasksToCompelete;
  get_recently_fails_inspections: IInspectionsFailsInspections;
  get_upcoming_inspections: IUpcomingInspections;
  last_year_inspection_chart: IInspectionLastYearChart;
  this_year_inspection_chart: IInspectionThisYearChart;
}

interface IInspectionsDashBoradApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IInspectionDashBoradData;
  refresh_type?: string;
}

interface IInspectionsDashParams {
  refresh_type?: string;
}

interface IInspectionsDashboardInitialState
  extends Partial<IInspectionDashBoradData> {
  isDashboardLoading: boolean;
  isInitialLoad: boolean;
  isKanbanEnabled: boolean;
  isDefaultViewKanban: boolean;
  shouldWidgetsRefresh: boolean;
  isDataFetched?: boolean;
  searchValue?: string;
}

interface IInspectionTempFil {
  project?: string;
  inspection_status?: string;
  inspection_status_kanban?: string;
  start_date?: string;
  end_date?: string;
}

interface IInspectionListParams {
  limit?: number;
  start?: number;
  search?: string;
  limited_fields?: number;
  is_kanban?: boolean;
  filter_primary_id?: number;
  order_by_name?: string;
  order_by_dir?: string;
  is_global_project?: number;
  ignore_filter?: number;
  filter?: IInspectionTempFil;
  global_project?: number;
  page?: number;
}

interface IInspectionListData {
  inspection_id: number;
  inspection_status: number;
  inspection_type: string;
  project_name: string;
  project_id: number;
  inspection_status_name: string;
  company_inspection_id: string;
  inspection_date: string;
  inspection_agency_name: string;
  inspection_status_key: string;
  is_deleted_permit: string;
  inspection_time: string;
  email_subject: string;
  project_manager_name: string;
  project_manager_profile_image: string;
  inspected_by: string;
}

interface IInspectionListApiRes extends Omit<IDefaultAPIRes, "data"> {
  count: number;
  data: IInspectionListData[];
}

interface IInspectionFilterProps {
  beforeFilterUpdateCallback?: () => void;
  onClearSearch: () => void;
  kanbanView?: boolean;
}

interface IInspectionsTableDropdownItemsProps {
  data:
    | Partial<IInspectionListData>
    | Partial<IInspectionKanBanData>
    | Partial<IInspectionDetails>
    | null;
  refreshTable: Function;
  className?: string;
  iconClassName?: string;
  contentClassName?: string;
  icon?: IFontAwesomeIconProps["icon"];
  isDetailView?: boolean;
  tooltipcontent?: string;
}

interface IDownloadInspectionDocumentWithAction extends IDownloadDocumentBase {
  action: string;
  op: string;
  inspection_id?: number | string;
}

interface IInspectionKanBanListParmas {
  start?: number;
  limit?: number;
  search?: string;
  page?: number;
  is_kanban?: boolean;
  filter?: IInspectionTempFil;
  any_status?: string;
  ignore_filter?: number;
  is_global_project?: number;
}

interface IInspectionKanBanColumnList {
  sort_order: string;
  status_color: string;
  item_id: number;
  key: string;
  is_custom_item: string;
  name: string;
  item_type: string;
  qb_full_name: string;
  qb_account_type: string;
  qb_account_sub_type: string;
  quickbook_category_id: string;
  status_name: string;
  is_deleted: string;
  does_sync_qb: number;
  orig_name: string;
  is_status: number;
  sorting_id: number;
  type: string;
  show_in_progress_bar: number;
  is_collapse_card: number;
  total_count: number;
  kanban_data: IInspectionKanBanData[];
}

interface IInspectionKanBanData {
  inspection_id: number;
  inspection_status: number;
  inspection_type: string;
  project_name: string;
  project_id: number;
  inspection_status_name: string;
  company_inspection_id: string;
  inspection_date: string;
  inspection_agency_name: string;
  inspection_status_key: string;
  is_deleted_permit: string;
  inspection_time: string;
  default_color: string;
  email_subject: string;
}

interface IInspectionKanBanSettings {
  kanban_id: number;
  company_id: number;
  user_id: number;
  module_id: number;
  module_field_id: string;
  default_view: number;
  co_type: string;
  date_modified: string;
  default_view_back: number;
}

interface IInspectionKanBanListApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IInspectionKanBanColumnList[];
  kanban_setting: IInspectionKanBanSettings;
  kanban_estimate_type_selected: number[];
}

interface IUpdateInspectionDetailApiParams
  extends Partial<IInspectionListData | IInspectionDetails> {
  id: strgin | number;
}

interface IInspectionUpdateDetailsApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: {
    is_access: string;
  };
}

interface IInspectionSendEmailForm extends ISendEmailFormDataWithApiDefaultNew {
  t_id?: string;
  inspection_id?: number;
  action: string;
  custom_approve_message?: string;
  req_from?: string;
}

interface IInspectionDetailsParamsData {
  id?: string;
  shouldLoading?: boolean;
  add_event?: boolean;
}

interface IInspectionDetailsCheckList {
  item_id: number;
  todo_id: number;
  task: string;
  status: number;
  is_deleted: number;
  date_added: string;
  date_modified: string;
  company_id: number;
  sort_order: number;
  section_id: number;
}

interface IInspectionDetails {
  inspection_id: number;
  project_id: number;
  inspection_type: string;
  permit_expire_date: string | null;
  inspection_agency: number;
  inspection_status: number;
  notes: string;
  image: string;
  inspected_by: string;
  date_added: string;
  date_modified: string;
  company_id: number;
  user_id: number;
  permit_id: number | null;
  inspection_date: string | null;
  inspection_time: string | null;
  parent_inspection_id: string | null;
  demo_data: number;
  todo_id: string;
  assigned_to: number | null;
  event_id: number;
  contact_id: number | null;
  company_inspection_id: string;
  custom_inspection_id: string;
  is_notes_convert: number;
  is_shared: number;
  project_name: string;
  inspection_status_name: string | null;
  inspection_agency_name: string | null;
  generated_by: string;
  email_subject: string;
  permit_number: string;
  permit_type: string;
  permit_type_name: string;
  assigned_to_name: string | null;
  dir_type: number | null;
  assigned_to_image?: string | undefined;
  assigned_to_name_only: string;
  assigned_to_company_name: string;
  time_added: string;
  is_access: string;
  show_client_access: string;
  is_deleted_permit: string;
  checklist: IInspectionDetailsCheckList[];
}

interface IInspectionDetailsApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IInspectionDetails;
}

interface IInspectionDetailsInitState {
  details: IInspectionDetails;
  isDetailLoading: boolean;
  isInspectionItemsLoading: boolean;
  noDetailsAvailable: boolean;
  loadingStatus: IFieldStatus[];
  permitLogList: IInspectionPermitLogListData[];
  isScrollDownToDetail: boolean;
}

interface IInspectionNotesTabProps {
  projectid?: numger | string;
}

interface IAddInspectionFilePhotoFrm {
  primary_id: number;
  module_id: number;
  module_key: string;
  attach_image?: string;
  files?: IFile[];
  project_id?: string | number;
}

interface IAddInspectionFilePhotoRes extends Omit<IDefaultAPIRes, "data"> {
  data: { aws_files: IFile[] };
}

interface IInspectionPermitLogListParmas {
  ignore_filter: number;
  ignore_filter: number;
  filter: {
    status: string;
    project: string;
  };
  show_deleted_permit: number;
}

interface IInspectionPermitLogListData {
  permit_id: number;
  permission: string;
  permit_type: string;
  expire_date: string;
  is_deleted: number;
  permit_type_name: string;
}

interface IInspectionPermitLogListApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IInspectionPermitLogListData[];
}

interface IInspectionCheckboxStatusTaskList {
  isLoadingCheckBox: Record<number, boolean>;
  setIsLoadingCheckBox: React.Dispatch<
    React.SetStateAction<Record<number, boolean>>
  >;
}

interface IInspectionCheckListProps {
  task: IInspectionDetailsCheckList;
  taskIndex: number;
  isLoadingTaskDragAndDropInSection: Record<number, boolean>;
  checklistContainerRef: React.RefObject<HTMLDivElement>;
  loadingProps: IInspectionCheckboxStatusTaskList; // For Checkbox loading
  inputRefs: React.MutableRefObject<Record<number, TTextAreaRef | null>>;
  addRef: (taskIndex: number, ref: TTextAreaRef | null) => void;
}

interface IAddUpdateInspectionCheckList {
  item_id: number;
  status: number;
  task: string;
  sort_item_order_no?: number;
}

interface IAddUpdateInspectionCheckListApiParams {
  id?: number;
  checklist: IAddUpdateInspectionCheckList[];
}

interface IAddUpdateInspectionCheckListApiResData {
  item_id: number;
  task: string;
  status: number;
  section_id: number;
  sort_order: number;
  is_deleted: number;
}

interface IAddUpdateInspectionCheckListApiRes
  extends Omit<IDefaultAPIRes, "data"> {
  data: IAddUpdateInspectionCheckListApiResData[];
}

interface IInspectionSendNotificationToAssignee {
  id: number;
}
