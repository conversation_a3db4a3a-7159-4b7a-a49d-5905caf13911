import { useEffect, useMemo, useRef, useState } from "react";
import delay from "lodash/delay";

// Hooks + helper
import { useTranslation } from "~/hook";
import { sanitizeString } from "~/helpers/helper";

// Atoms
import { CheckBox } from "~/shared/components/atoms/checkBox";

// molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { TimePickerField } from "~/shared/components/molecules/timePickerField";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";

// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";

// Other
import {
  fieldStatus,
  IDLMaterialsDeliveryField,
} from "~/modules/projectManagement/pages/dailyLogs/utils/constasnts";
import {
  getStatusForField,
  onKeyDownCurrency,
  getStatusActionForField,
} from "~/shared/utils/helper/common";
import {
  displayTimeFormat,
  frontEndTimeFormat,
} from "~/shared/utils/helper/defaultDateFormat";

// Redux
import { updateDLMaterialDeliveryApi } from "~/modules/projectManagement/pages/dailyLogs/redux/action";
import { resetDash } from "~/modules/projectManagement/pages/dailyLogs/redux/slices/dashboardSlice";
import { useAppDLDispatch } from "~/modules/projectManagement/pages/dailyLogs/redux/store";
import { AddMyList } from "../sidebar/addMyList";

const ItemList = ({
  materialItem,
  isReadOnly,
  onAddUpMaterialDeli,
  onDeleteMaterialDeli,
}: IDLMaterialItemListProps) => {
  const { _t } = useTranslation();
  const dispatch = useAppDLDispatch();
  const { inputFormatter, unformatted } = useCurrencyFormatter();
  const loadingStatusRef = useRef(fieldStatus);
  const decInpRef = useRef<HTMLInputElement>(null);
  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(fieldStatus);
  const [inputValues, setInputValues] = useState<IDeliveredMaterial>(
    IDLMaterialsDeliveryField
  );
  const [addItemsMaterialId, setAddItemsMaterialId] = useState<number>(0);

  useEffect(() => {
    setInputValues(materialItem);
  }, []);

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );

    if (
      !(
        checkStatus &&
        (checkStatus.status === "loading" ||
          checkStatus.status === "success" ||
          checkStatus.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };

  const handleInpOnChange = ({
    target: { value, name },
  }: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setInputValues({ ...inputValues, [name]: value });
  };

  const handleChangeDate = (val: string) => {
    setInputValues({
      ...inputValues,
      delivery_time: val,
    });
    handleUpdateField({
      deliveryTime: val,
    });
  };

  const handleUpdateField = async (data: IDLDetailFields) => {
    const field = Object.keys(data)[0] as keyof IDeliveredMaterial;
    if (field === "itemName") {
      setInputValues({ ...inputValues, item_name: data.itemName.toString() });
    }
    if (field === "deliveredBy") {
      setInputValues({
        ...inputValues,
        delivered_by: data.deliveredBy.toString(),
      });
    }
    if (field === "notes") {
      setInputValues({ ...inputValues, notes: data.notes.toString() });
    }

    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });
    const updateRes = (await updateDLMaterialDeliveryApi({
      logId: materialItem.daily_log_id.toString(),
      materialDelivery: [
        {
          itemId: Number(inputValues.item_id),
          itemName: inputValues.item_name,
          deliveredBy: inputValues.delivered_by || "",
          quantity: inputValues.quantity || "",
          quantityBackorderd: inputValues.quantity_backorderd || "",
          qtyUsed: inputValues.qty_used || "",
          deliveryTime: inputValues.delivery_time,
          unitCost: inputValues.unit_cost || "",
          unit: inputValues.unit || "",
          referencePoItemId: inputValues.reference_po_item_id || 0,
          referenceItemId: inputValues.reference_item_id || 0,
          notes: inputValues.notes || "",
          newItem: inputValues.reference_item_id !== 0 ? 1 : 0,
          ...data,
        },
      ],
    })) as IMaterialDeliAddUpRes;

    if (updateRes?.success) {
      dispatch(resetDash());
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      let newData = data;
      if (field === "delivery_time") {
        newData.deliveryTime = frontEndTimeFormat(data.deliveryTime as string);
      }
      onAddUpMaterialDeli(updateRes.data.material_item_delivered[0]);
    } else {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      let newField = field;

      if (field === "itemName") {
        newField = "item_name";
      } else if (field === "deliveryTime") {
        newField = "delivery_time";
      } else if (field === "deliveredBy") {
        newField = "delivered_by";
      } else if (field === "qtyUsed") {
        newField = "qty_used";
      } else if (field === "quantityBackorderd") {
        newField = "quantity_backorderd";
      } else if (field === "referenceItemId") {
        newField = "reference_item_id";
      }
      setInputValues({ ...inputValues, [newField]: materialItem[newField] });
      notification.error({
        description: updateRes?.message,
      });
    }

    // Remove success icon after 3 some second that's why use delay function.
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );
      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 3000);
  };

  const timeField = useMemo(() => {
    return (
      <TimePickerField
        label={_t("Time")}
        labelClass="sm:w-[150px] sm:max-w-[150px]"
        name="delivery_time"
        placeholder="00:00"
        labelPlacement="left"
        editInline={true}
        iconView={true}
        readOnly={isReadOnly}
        allowClear={false}
        suffixIcon={false}
        format="hh:mm A"
        key={inputValues?.delivery_time}
        defaultValue={displayTimeFormat(inputValues?.delivery_time?.trim())}
        fixStatus={getStatusForField(loadingStatus, "deliveryTime")}
        onChange={(_, val) => {
          if (inputValues?.delivery_time !== val) {
            handleChangeDate(val as string);
          }
        }}
      />
    );
  }, [inputValues?.delivery_time, loadingStatus]);
  return (
    <>
      <div className="grid gap-2">
        <CheckBox
          disabled={Number(inputValues?.reference_item_id) > 0 || isReadOnly}
          checked={
            Number(inputValues?.reference_item_id) > 0 ||
            (addItemsMaterialId != 0 &&
              addItemsMaterialId.toString() == materialItem?.item_id)
          }
          className="w-fit gap-1.5"
          onChange={(e) => {
            setAddItemsMaterialId(Number(materialItem?.item_id || 0));
          }}
        >
          {_t("Add Items to Material Items list for later use?")}
        </CheckBox>
        <div className="common-card p-[3px] dark:shadow-[0_4px_24px_0] dark:shadow-dark-900">
          <div className="flex md:items-center items-start dark:bg-dark-900 justify-between rounded-t-md p-[3px] pr-1.5 bg-[#F8F8F8]">
            <div className="w-[calc(100%-30px)] flex md:flex-row flex-col justify-between">
              <div className="lg:w-1/2 md:w-[400px]">
                <InputField
                  name="item_name"
                  placeholder={_t("Delivered Item Name") + "*"}
                  className="text-[15px] font-medium"
                  labelPlacement="left"
                  value={HTMLEntities.decode(
                    sanitizeString(inputValues?.item_name)
                  )}
                  isRequired={true}
                  editInline={true}
                  readOnly={isReadOnly}
                  iconView={true}
                  fixStatus={getStatusForField(loadingStatus, "itemName")}
                  onChange={handleInpOnChange}
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "itemName",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "itemName",
                      status: "button",
                      action: "ML",
                    });
                  }}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "itemName",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onBlur={(e) => {
                    const value = e?.target?.value.trim();
                    if (value === "") {
                      notification.error({
                        description: _t("Item Name is required."),
                      });
                      setInputValues({
                        ...inputValues,
                        item_name: materialItem?.item_name,
                      });
                      return false;
                    }
                    if (value !== materialItem?.item_name) {
                      handleUpdateField({ itemName: value });
                    } else {
                      handleChangeFieldStatus({
                        field: "itemName",
                        status: "button",
                        action: "BLUR",
                      });
                      setInputValues({
                        ...inputValues,
                        item_name: materialItem.item_name,
                      });
                    }
                  }}
                />
              </div>
            </div>
            <ButtonWithTooltip
              tooltipTitle={_t("Delete")}
              disabled={isReadOnly}
              tooltipPlacement="top"
              icon="fa-regular fa-trash-can"
              iconClassName="group-hover/buttonHover:!text-[#FF0000] group-focus-within/buttonHover:!text-[#FF0000]"
              className="focus-within:!bg-[#FF00001a] hover:!bg-[#FF00001a]"
              onClick={onDeleteMaterialDeli}
            />
          </div>
          <ul className="grid md:grid-cols-2 gap-y-1 gap-x-4 p-2">
            <li>
              <InputField
                label={_t("Delivered By")}
                labelClass="sm:w-[150px] sm:max-w-[150px]"
                placeholder={_t("Delivered By")}
                labelPlacement="left"
                name="delivered_by"
                value={inputValues?.delivered_by}
                editInline={true}
                iconView={true}
                readOnly={isReadOnly}
                fixStatus={getStatusForField(loadingStatus, "deliveredBy")}
                onChange={handleInpOnChange}
                onMouseEnter={() => {
                  handleChangeFieldStatus({
                    field: "deliveredBy",
                    status: "edit",
                    action: "ME",
                  });
                }}
                onMouseLeaveDiv={() => {
                  handleChangeFieldStatus({
                    field: "deliveredBy",
                    status: "button",
                    action: "ML",
                  });
                }}
                onFocus={() =>
                  handleChangeFieldStatus({
                    field: "deliveredBy",
                    status: "save",
                    action: "FOCUS",
                  })
                }
                onBlur={(e) => {
                  const value = e?.target?.value.trim();
                  if (value !== materialItem?.delivered_by) {
                    handleUpdateField({ deliveredBy: value });
                  } else {
                    handleChangeFieldStatus({
                      field: "deliveredBy",
                      status: "button",
                      action: "BLUR",
                    });
                    setInputValues({
                      ...inputValues,
                      delivered_by: materialItem.delivered_by,
                    });
                  }
                }}
              />
            </li>
            <li>{timeField}</li>
            <li>
              <InputNumberField
                label={_t("QTY Delivered")}
                labelClass="sm:w-[150px] sm:max-w-[150px]"
                placeholder={_t("QTY Delivered")}
                labelPlacement="left"
                name="quantity"
                value={
                  Number(inputValues?.quantity) !== 0
                    ? inputValues?.quantity
                    : undefined
                }
                editInline={true}
                iconView={true}
                readOnly={isReadOnly}
                fixStatus={getStatusForField(loadingStatus, "quantity")}
                onChange={(value) => {
                  setInputValues({
                    ...inputValues,
                    quantity: value?.toString() ?? "",
                  });
                }}
                formatter={(value) => {
                  const valueToFormat = String(value);
                  return inputFormatter(valueToFormat).value;
                }}
                parser={(value) => {
                  const inputValue = value ? unformatted(value.toString()) : "";
                  return inputValue;
                }}
                onMouseEnter={() => {
                  handleChangeFieldStatus({
                    field: "quantity",
                    status: "edit",
                    action: "ME",
                  });
                }}
                onMouseLeaveDiv={() => {
                  handleChangeFieldStatus({
                    field: "quantity",
                    status: "button",
                    action: "ML",
                  });
                }}
                onFocus={() =>
                  handleChangeFieldStatus({
                    field: "quantity",
                    status: "save",
                    action: "FOCUS",
                  })
                }
                onKeyDown={(event) =>
                  onKeyDownCurrency(event, {
                    integerDigits: 6,
                    decimalDigits: 2,
                    unformatted,
                    allowNegative: false,
                    decimalSeparator: inputFormatter().decimal_separator,
                  })
                }
                onBlur={(e) => {
                  const value = unformatted(e?.target?.value.trim());
                  const originalQtyDelivered = materialItem?.quantity;
                  const QtyUsed = materialItem?.qty_used;

                  const resetToOriginal = () => {
                    setInputValues((prev) => ({
                      ...prev,
                      quantity: originalQtyDelivered,
                    }));
                    handleChangeFieldStatus({
                      field: "quantity",
                      status: "button",
                      action: "BLUR",
                    });
                  };

                  if (!value) {
                    if (QtyUsed) {
                      notification.error({
                        description:
                          "Cannot remove QTY Delivered when QTY Used is already entered.",
                      });
                      resetToOriginal();
                      return;
                    }
                    if (!originalQtyDelivered) {
                      setInputValues((prev) => ({ ...prev, quantity: "" }));
                    } else {
                      handleUpdateField({ quantity: "" });
                      setInputValues((prev) => ({ ...prev, quantity: "" }));
                    }

                    handleChangeFieldStatus({
                      field: "quantity",
                      status: "button",
                      action: "BLUR",
                    });
                    return;
                  }

                  if (Number(value) < Number(QtyUsed)) {
                    notification.error({
                      description:
                        "QTY Delivered must be greater than or equal of QTY Used.",
                    });
                    resetToOriginal();
                    return;
                  }

                  if (value !== originalQtyDelivered) {
                    handleUpdateField({ quantity: value });
                  } else {
                    resetToOriginal();
                  }
                }}
              />
            </li>
            <li>
              <InputNumberField
                label={_t("QTY Backordered")}
                labelClass="sm:w-[150px] sm:max-w-[150px]"
                placeholder={_t("QTY Backordered")}
                labelPlacement="left"
                name="quantity_backorderd"
                value={
                  Number(inputValues?.quantity_backorderd) !== 0
                    ? inputValues?.quantity_backorderd
                    : undefined
                }
                editInline={true}
                iconView={true}
                readOnly={isReadOnly}
                fixStatus={getStatusForField(
                  loadingStatus,
                  "quantityBackorderd"
                )}
                onChange={(value) => {
                  setInputValues({
                    ...inputValues,
                    quantity_backorderd: value?.toString() ?? "",
                  });
                }}
                formatter={(value, info) => {
                  const inputValue = info?.input?.toString().trim();

                  const valueToFormat =
                    inputValue !== "0" && inputValue.length > 0
                      ? unformatted(inputValue)
                      : String(value);

                  return inputFormatter(valueToFormat).value;
                }}
                parser={(value) => {
                  if (!value) return "";
                  const inputValue = unformatted(value.toString());
                  return inputValue;
                }}
                onMouseEnter={() => {
                  handleChangeFieldStatus({
                    field: "quantityBackorderd",
                    status: "edit",
                    action: "ME",
                  });
                }}
                onMouseLeaveDiv={() => {
                  handleChangeFieldStatus({
                    field: "quantityBackorderd",
                    status: "button",
                    action: "ML",
                  });
                }}
                onFocus={() =>
                  handleChangeFieldStatus({
                    field: "quantityBackorderd",
                    status: "save",
                    action: "FOCUS",
                  })
                }
                onKeyDown={(event) =>
                  onKeyDownCurrency(event, {
                    integerDigits: 6,
                    decimalDigits: 2,
                    unformatted,
                    allowNegative: true,
                    decimalSeparator: inputFormatter().decimal_separator,
                  })
                }
                onBlur={(e) => {
                  const value = unformatted(e?.target?.value.trim());
                  if (value !== materialItem?.quantity_backorderd) {
                    handleUpdateField({ quantityBackorderd: value });
                  } else {
                    handleChangeFieldStatus({
                      field: "quantityBackorderd",
                      status: "button",
                      action: "BLUR",
                    });
                    setInputValues({
                      ...inputValues,
                      quantity_backorderd: materialItem.quantity_backorderd,
                    });
                  }
                }}
              />
            </li>
            <li>
              <InputNumberField
                label={_t("QTY Used")}
                labelClass="sm:w-[150px] sm:max-w-[150px]"
                placeholder={_t("QTY Used")}
                labelPlacement="left"
                name="qty_used"
                value={
                  Number(inputValues?.qty_used) !== 0
                    ? inputValues?.qty_used
                    : undefined
                }
                editInline={true}
                iconView={true}
                readOnly={isReadOnly}
                fixStatus={getStatusForField(loadingStatus, "qtyUsed")}
                onChange={(value) => {
                  setInputValues({
                    ...inputValues,
                    qty_used: value?.toString() ?? "",
                  });
                }}
                formatter={(value) => {
                  const valueToFormat = String(value);
                  return inputFormatter(valueToFormat).value;
                }}
                parser={(value) => {
                  const inputValue = value ? unformatted(value.toString()) : "";

                  return inputValue;
                }}
                onMouseEnter={() => {
                  handleChangeFieldStatus({
                    field: "qtyUsed",
                    status: "edit",
                    action: "ME",
                  });
                }}
                onMouseLeaveDiv={() => {
                  handleChangeFieldStatus({
                    field: "qtyUsed",
                    status: "button",
                    action: "ML",
                  });
                }}
                onFocus={() =>
                  handleChangeFieldStatus({
                    field: "qtyUsed",
                    status: "save",
                    action: "FOCUS",
                  })
                }
                onKeyDown={(event) =>
                  onKeyDownCurrency(event, {
                    integerDigits: 6,
                    decimalDigits: 2,
                    unformatted,
                    allowNegative: false,
                    decimalSeparator: inputFormatter().decimal_separator,
                  })
                }
                onBlur={(e) => {
                  const value = unformatted(e?.target?.value.trim());
                  const originalQtyUsed = materialItem?.qty_used;
                  const qtyDelivered = materialItem?.quantity;

                  const resetToOriginal = () => {
                    setInputValues((prev) => ({
                      ...prev,
                      qty_used: originalQtyUsed,
                    }));
                    handleChangeFieldStatus({
                      field: "qtyUsed",
                      status: "button",
                      action: "BLUR",
                    });
                  };

                  if (!value) {
                    if (!originalQtyUsed) {
                      setInputValues((prev) => ({ ...prev, qty_used: "" }));
                    } else {
                      handleUpdateField({ qtyUsed: "" });
                      setInputValues((prev) => ({ ...prev, qty_used: "" }));
                    }

                    handleChangeFieldStatus({
                      field: "qtyUsed",
                      status: "button",
                      action: "BLUR",
                    });
                    return;
                  }

                  if (!qtyDelivered) {
                    notification.error({
                      description:
                        "Please enter QTY Delivered before entering QTY Used.",
                    });
                    resetToOriginal();
                    return;
                  }

                  if (Number(value) > Number(qtyDelivered)) {
                    notification.error({
                      description:
                        "QTY Used must be less than or equal of QTY Delivered.",
                    });
                    resetToOriginal();
                    return;
                  }

                  if (value !== originalQtyUsed) {
                    handleUpdateField({ qtyUsed: value });
                  } else {
                    resetToOriginal();
                  }
                }}
              />
            </li>
            <li>
              <TextAreaField
                label={_t("Notes")}
                labelPlacement="left"
                labelClass="sm:w-[150px] sm:max-w-[150px]"
                editInline={true}
                readOnly={isReadOnly}
                placeholder={_t("Notes")}
                name="notes"
                ref={decInpRef}
                value={inputValues?.notes || ""}
                iconView={true}
                fixStatus={getStatusForField(loadingStatus, "notes")}
                onChange={handleInpOnChange}
                onMouseEnter={() => {
                  handleChangeFieldStatus({
                    field: "notes",
                    status: "edit",
                    action: "ME",
                  });
                }}
                onMouseLeaveDiv={() => {
                  handleChangeFieldStatus({
                    field: "notes",
                    status: "button",
                    action: "ML",
                  });
                }}
                onFocus={() =>
                  handleChangeFieldStatus({
                    field: "notes",
                    status: "save",
                    action: "FOCUS",
                  })
                }
                onBlur={(e) => {
                  const value = e.target.value.trim();
                  if (value !== materialItem?.notes) {
                    handleUpdateField({ notes: value });
                  } else {
                    handleChangeFieldStatus({
                      field: "notes",
                      status: "button",
                      action: "BLUR",
                    });
                    setInputValues({
                      ...inputValues,
                      notes: materialItem.notes,
                    });
                  }
                }}
                onClickStsIcon={() => {
                  if (getStatusForField(loadingStatus, "notes") === "edit") {
                    decInpRef.current?.focus();
                  }
                }}
              />
            </li>
          </ul>
        </div>
      </div>

      {addItemsMaterialId > 0 && (
        <AddMyList
          addMyList={addItemsMaterialId > 0}
          setAddMyList={() => {
            setAddItemsMaterialId(0);
          }}
          type={"material"}
          selecedData={materialItem}
          onSaved={(refId) => {
            setInputValues({
              ...inputValues,
              reference_item_id: refId,
            });
            handleUpdateField({ referenceItemId: refId });
          }}
        />
      )}
    </>
  );
};

export default ItemList;
