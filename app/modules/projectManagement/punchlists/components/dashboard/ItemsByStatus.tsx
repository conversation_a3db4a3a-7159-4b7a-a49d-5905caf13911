import { useEffect, useMemo, useRef, useState } from "react";
// ag + antd
import { type RadioChangeEvent } from "antd";
import { type GridApi, type GridReadyEvent } from "ag-grid-community";

// Atoms
import { Apex<PERSON><PERSON> } from "~/shared/components/atoms/chart";
// Molecules
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
import { DashboardCardHeader } from "~/shared/components/molecules/dashboardCardHeader";
import { StaticTableRowLoading } from "~/shared/components/molecules/staticTableRowLoading";
import PieChartSkeleton from "~/shared/components/molecules/charts/skeleton/PieChart.skeleton";
// Hook
import { useTranslation } from "~/hook";
import { useApexCharts } from "~/shared/hooks/useApexCharts";
// Redux
import { useAppPLDispatch, useAppPLSelector } from "../../redux/store";
import { fetchDashData } from "~/modules/projectManagement/punchlists/redux/action/dashboardAction";
// Other
import { PUNCHLIST_TAB } from "../../utils/constants";
import { getDefaultStatuscolor, sanitizeString } from "~/helpers/helper";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Tag } from "~/shared/components/atoms/tag";
import ToolTipCell from "~/shared/components/molecules/table/ToolTipCell";

const ItemsByStatus = () => {
  const { _t } = useTranslation();
  const gridApiRef = useRef<GridApi | null>(null);
  const dispatch = useAppPLDispatch();

  const [value, setValue] = useState<string>("chart");
  const [chartVal, setChartVal] = useState<string>("");
  const [isCashLoading, setIsCashLoading] = useState<boolean>(false);
  const [rowData, setRowData] = useState<IItemsByStatusData[]>([]);
  const [hoverLabel, setHoverLabel] = useState<string>("Total");
  const [hoverValue, setHoverValue] = useState<number | null>(null);

  const optionsLine = useApexCharts({ type: "donut" });

  const {
    isDashLoading,
    itemsByStatus,
    itemsByStatusLastRefreshTime,
  }: IPunchlistIntlState = useAppPLSelector((state) => state.dashboard);

  const handleRefreshClick = async () => {
    setIsCashLoading(true);
    setRowData([]);
    setHoverValue(null);
    await dispatch(
      fetchDashData({
        refresh_type: "iteam_by_status",
      })
    );
    setIsCashLoading(false);
  };

  const chartSeriesData = useMemo(() => {
    if (!itemsByStatus) return [0, 0];
    return [
      Number(itemsByStatus.open_percentage ?? 0),
      Number(itemsByStatus.close_percentage ?? 0),
    ];
  }, [itemsByStatus]);

  const formattedLabels = useMemo(() => {
    if (!itemsByStatus) return ["Open: 0%", "Closed: 0%"];
    return [
      `Open: ${parseFloat(itemsByStatus.open_percentage || "0").toFixed(2)}%`,
      `Closed: ${parseFloat(itemsByStatus.close_percentage || "0").toFixed(
        2
      )}%`,
    ];
  }, [itemsByStatus]);

  const totalSum = useMemo(() => {
    if (!itemsByStatus?.open && !itemsByStatus?.close) return 0;
    const open = parseInt(itemsByStatus?.open ?? "0", 10);
    const closed = parseInt(itemsByStatus?.close ?? "0", 10);
    return open + closed;
  }, [itemsByStatus]);

  useEffect(() => {
    if (!isCashLoading) {
      if (!!itemsByStatus.open || !!itemsByStatus.close) {
        setRowData([
          { punchlist_status: "Open", completed_items: itemsByStatus.open },
          { punchlist_status: "Closed", completed_items: itemsByStatus.close },
        ]);
      } else {
        setRowData([]);
      }
    }
  }, [itemsByStatus, isCashLoading]);

  const onGridReady = (params: GridReadyEvent) => {
    gridApiRef.current = params?.api as GridApi;
  };

  const chartOptions = useMemo(() => {
    return {
      ...optionsLine,
      chart: {
        offsetY: 15,
        events: {
          dataPointMouseLeave: () => {
            setChartVal(`Total: ${totalSum}`);
            setHoverLabel("Total");
            setHoverValue(totalSum);
          },
        },
      },
      labels: formattedLabels,
      legend: {
        show: true,
        position: "right",
        horizontalAlign: "center",
        floating: !1,
        offsetX: 60,
        offsetY: 30,
        onItemClick: { toggleDataSeries: false },
        onItemHover: { highlightDataSeries: true },
      },
      colors: ["#5C9395", "#45506D"],
      dataLabels: { enabled: false },
      tooltip: {
        enabled: true,
        custom: ({ seriesIndex }: { seriesIndex: number }) => {
          const totalValues = [
            itemsByStatus?.open ?? 0,
            itemsByStatus?.close ?? 0,
          ];
          const labels = ["Open", "Closed"];
          const backgroundColors = ["#5C9395", "#45506D"];

          setHoverLabel(labels[seriesIndex]);
          setHoverValue(Number(totalValues[seriesIndex]));

          return `<div class="custom-tooltip" style="background-color: ${backgroundColors[seriesIndex]}; color: white; padding: 4px; border-radius: 4px;">
                    <span>${labels[seriesIndex]}: ${totalValues[seriesIndex]}</span>
                  </div>`;
        },
      },
      title: {
        text: undefined,
      },
      plotOptions: {
        pie: {
          expandOnClick: false,
          donut: {
            labels: {
              show: true,
              name: {
                show: true,
                fontSize: "12px",
                color: "#000",
                offsetY: -7,
              },
              value: {
                show: true,
                fontSize: "12px",
                color: "#000",
                opacity: 1,
                offsetY: -2,
              },
              total: {
                show: true,
                label: hoverLabel,
                color: "#000",
                fontSize: "12px",
                fontWeight: "600",
                showAlways: true,
                formatter: () => hoverValue ?? `${totalSum}`,
              },
            },
          },
        },
      },
      states: {
        hover: {
          filter: {
            type: "none",
          },
        },
        active: {
          allowMultipleDataPointsSelection: false,
          filter: {
            type: "none",
          },
        },
      },
      responsive: [
        {
          breakpoint: 500, // Adjust for tablets or small screens
          options: {
            legend: {
              offsetX: -20,
            },
          },
        },
      ],
    };
  }, [hoverLabel, hoverValue, totalSum, optionsLine, formattedLabels]);

  const columnDefs = [
    {
      headerName: _t("Status"),
      field: "punchlist_status",
      minWidth: 100,
      maxWidth: 100,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      cellRenderer: ({ data }: IPLTableCellRenderer) => {
        const { color, textColor } = getDefaultStatuscolor(
          data?.punchlist_status === "Open" ? "#008000" : "#d2322d"
        );
        const status = HTMLEntities.decode(
          sanitizeString(data?.punchlist_status || "")
        );
        return !!status ? (
          <Tooltip title={status}>
            <div className="overflow-hidden">
              <Tag
                color={color}
                style={{
                  color: `${textColor || ""}`,
                }}
                className={`${
                  textColor === "" && "!text-primary-900"
                } mx-auto text-13 type-badge common-tag max-w-20`}
              >
                {status}
              </Tag>
            </div>
          </Tooltip>
        ) : (
          <div className="table-tooltip-text">-</div>
        );
      },
    },
    {
      headerName: "# " + _t("of Punchlists"),
      field: "completed_items",
      minWidth: 110,
      flex: 1,
      headerClass: "ag-header-right",
      cellClass: "ag-cell-right",
      suppressMenu: true,
      valueGetter: ({ data }: IPLTableCellRenderer) =>
        HTMLEntities.decode(sanitizeString(data?.completed_items)),
      cellRenderer: ToolTipCell,
    },
  ];

  const noRowsOverlay = () => (
    <StaticTableRowLoading columnDefs={columnDefs} limit={6} />
  );

  const noData = () => (
    <NoRecords
      image={`${window.ENV.CDN_URL}assets/images/no-records-td-summary.svg`}
    />
  );

  return (
    <>
      <>
        <DashboardCardHeader
          title={_t("Items by Status")}
          showRefreshIcon={true}
          isRefreshing={isCashLoading}
          refreshIconTooltip={itemsByStatusLastRefreshTime}
          onClickRefresh={handleRefreshClick}
          rightContent={
            <div className="flex items-center ">
              <ListTabButton
                value={value}
                options={PUNCHLIST_TAB}
                onChange={(e: RadioChangeEvent) => {
                  setValue(e.target.value);
                }}
                className="first:border-r-0"
                activeclassName="!bg-[#EAE8E8] "
              />
            </div>
          }
        />
        <div className="py-2 px-2.5">
          {(isDashLoading || isCashLoading) && value === "chart" ? (
            <div className="pt-6">
              <PieChartSkeleton legendLength={2} />
            </div>
          ) : value === "chart" ? (
            <ApexChart
              className="donut-chart donut-chart-big-round"
              series={chartSeriesData}
              options={chartOptions}
              type={"donut"}
              height={170}
            />
          ) : value === "table" ? (
            <div className="ag-theme-alpine h-[209px]">
              <StaticTable
                key={isDashLoading ? "loading" : "loaded"}
                className="static-table"
                columnDefs={columnDefs}
                onGridReady={onGridReady}
                rowData={rowData}
                noRowsOverlayComponent={
                  isDashLoading || isCashLoading ? noRowsOverlay : noData
                }
              />
            </div>
          ) : (
            <></>
          )}
        </div>
      </>
    </>
  );
};
export default ItemsByStatus;
