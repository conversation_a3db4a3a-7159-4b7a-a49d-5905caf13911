interface IPLRefreshParamsReq {
  refresh_type?: string;
}

interface IPLDashApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: any; // Replace 'any' with the actual type of your data
  refresh_type?: string;
}

interface IPunchlistState {
  searchValue: string;
  openItemsByProject: IOpenItemsByProject[];
  openItemsByAssignedPerson: IOpenItemsByAssignedPerson[];
  itemsByStatus: IPLItemsByStatus;
  openItemsByProjectLastRefreshTime?: string;
  openItemsByAssignedPersonLastRefreshTime?: string;
  itemsByStatusLastRefreshTime?: string;
}

interface IOpenItemsByProject {
  no_of_punchlists: string;
  no_of_items: string;
  project_name: string;
}

interface IOpenItemsByAssignedPerson {
  no_of_items: string;
  assignee_name: string;
}

interface IPLItemsByStatus {
  open: string;
  close: string;
  open_percentage: string;
  close_percentage: string;
}

interface IItemsByStatusData {
  punchlist_status: string;
  completed_items: string;
}

interface IPunchlistIntlState extends IPunchlistState {
  isDashLoading?: boolean;
  isDataFetched?: boolean;
  isOwnData?: number;
}

interface IAddPLRes extends Omit<IDefaultAPIRes, "data"> {
  data: { punchlist_id: string };
}

interface IPLListProps {
  setAddPunchlist: (value: boolean) => void;
  search: string;
}

interface IPLApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: { punchlist: IPLData[] };
}

interface IPLTableCellRenderer {
  data: Partial<IPLData>;
}

interface IPunchListItemTableCellRenderer {
  data: Partial<IPunchListRecItems>;
}

interface IPLData {
  punchlist_id: number;
  punchlist_name: string;
  project_name: string;
  company_punchlist_id: number;
  incomplete_items: string;
  completed_items: string;
  date_added: string;
  project_type_name: string;
  project_type: string;
  customer_name: string;
  customer_name_only: string;
  is_deleted: number;
  email_subject: string;
  punchlist_status: string;
  assignes_to: IAssigneesTo[];
}

interface IAssigneesTo {
  user_id: number;
  assignee_name: string;
  assigned_to_name_only: string;
  image: string;
}

interface IPLListParmas {
  length?: number;
  page?: number;
  company_id?: number;
  user_id?: string | number;
  order_by_dir?: string;
  order_by_name?: string;
  search?: string;
  start: number;
  limit: number;
  is_own_data?: number;
  ignore_filter?: number;
  is_global_project?: number | string;
  filter?: any;
}
interface IArchivePLFrm {
  formData: {
    status: number;
    moduleKey: string;
    moduleId: number;
  };
  paramsData: {
    punchListId: string;
  };
}
interface IDeletePLFrm {
  formData: {
    module_key: string;
  };
  paramsData: {
    punchListId: string;
  };
}

interface IDeletePLRes extends Omit<IDefaultAPIRes, "data"> {
  data: ObjType;
}

interface IPLDetailsInitialState {
  details: Partial<IPLDetails>;
  authorizedList: IAdditionalContact[];
  authorizedContactId: number;
  isDetailLoading: boolean;
  isPunchListItemsLoading: boolean;
  punchList_items: IPunchListRecItems[];
}

interface IPLDetails {
  punchlist_id: number;
  company_id: number;
  project_id: number;
  company_punchlist_id: number;
  punchlist_name: string;
  is_deleted: number;
  date_added: string;
  date_modified: string;
  user_id: number;
  parent_punchlist_id: string;
  demo_data: number;
  employee: string;
  project_name: string;
  prj_address1: string;
  prj_address2: string;
  prj_city: string;
  prj_state: string;
  prj_zip: string;
  prj_id: string;
  email_subject: string;
  incomplete_items: string;
  completed_items: string;
  time_added: string;
  customer_name: string;
  customer_phone: string;
  punchlist_status: string;
}

interface IPLDetailRequest {
  id: string | number;
  add_event?: true;
}

interface IPLDetailApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IPLDetails;
}

interface IFieldStatus {
  field?: string;
  status: IStatus;
  action?: string;
}

interface IPLDetailsUpdateApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: ObjType;
}
interface IPunchListCheckList {
  item_id: number;
  punchlist_id: number;
  punchlist_item_id: number;
  task: string;
  status: number;
  is_deleted: number;
  date_added: string;
  date_modified: string;
  company_id: number;
  sort_order: number;
}

interface IPunchListItemAttachment {
  image_id: number;
  project_id: number;
  type: string;
  type_id: string;
  file_ext: string;
  file_path: string;
  title: string;
  notes: string;
  is_deleted: number;
  date_added: string;
  date_modified: string;
  module_id: number;
  primary_id: number;
  file_tags: string;
  child_item_id: number;
  user_id: number;
  company_id: number;
  file_type: number;
  is_image: number;
  parent_image_id: string;
  demo_data: number;
  height: number;
  width: number;
  size: string;
  mode: string;
  child_item_backup: string;
  child_item_backup12: string;
  upload_from: string;
  image_res: string;
  camera_res: string;
  file_name: string;
  thumb_flag: number;
  large_flag: number;
  static_folder: number;
  folder_id: number;
  refrence_img_id: number;
  quickbook_attachable_id: string;
  is_common_notes: number;
  is_bidding_file: number;
  child_item_name: string;
  file_save_when_send_email: number;
  is_file_shared: number;
  annotation_data?: string;
  original_file_path?: string;
  is_google_drive_file: number;
  companycam_photo_id: string;
  companycam_creator_name: string;
  original_image_id: number;
  is_project_template: number;
  project_template_id: string;
  xero_attachable_id: string;
  thumb_file_path: string;
}

interface IPunchListItemData {
  item_id: number;
  area: string;
  notes: string;
  assigned_to: string;
  completion_date: string;
  date_added: string;
  date_modified: string;
  is_deleted: number;
  punchlist_id: number;
  assigned_item_id: number;
  company_id: number;
  due_date: string;
  parent_item_id: string;
  demo_data: number;
  status: number;
  assigned_contact_id: number;
  status_name: string;
  time_added: string;
  checklist: IPunchListCheckList[];
  item_attachments: IPunchListItemAttachment[];
}

interface IPunchListAssigneeDetail {
  user_id: number;
  contact_id: string;
  user_contact_id: string;
  company_name: string;
  user_type: string;
  assignee_name: string;
  assigned_to_name_only: string;
  assignee_company: string;
  email: string;
  image: string;
  type_name: string;
}

interface IPunchListRecItems {
  item_id: number;
  assigned_to: string;
  assigned_contact_id: number;
  company_name: string;
  total_items: string;
  email_subject: string;
  email: string;
  completed_items: string;
  incomplete_items: string;
  assignee_company: string;
  assignee_name: string;
  assigned_to_name_only: string;
  assigneeDs: IPunchListAssigneeDetail[];
  aws_files: string;
  items: IPunchListItemData[];
}

interface IPunchListItems {
  items: IPunchListRecItems[];
  incomplete_items: string;
}

interface IPunchListItemsApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: IPunchListItems;
}

interface IPunchListSetAssignee {
  display_name?: string;
  user_id?: number;
  contact_id?: number;
  email?: string;
}

type IPunchListItemActionParams = {
  id: string;
  item_id: number;
};

interface IPunchListAttachImageItemReference {
  image_id: string;
  reference_item_number: string;
}

interface IAddPunchListAws {
  file_url: string;
  is_image: string;
  image_res: string;
  notes: string;
  camera_res: string;
  is_file_shared: number;
  attach_item_id: number;
}

interface IAddPunchListCheckListItem {
  item_id: number;
  status: number;
  task: string;
}

interface IAddPLItems {
  item_id?: string;
  status: string;
  area: string;
  due_date: string;
  completion_date: string;
  checklist?: IAddPunchListCheckListItem[];
  aws_files_url?: IAddPunchListAws[];
  attach_image_item_reference?: IPunchListAttachImageItemReference[];
  attach_image?: number[];
}

interface IAddPunchListItem {
  assigned_to: string;
  allow_multiple_assignee: number;
  items: IAddPLItems[];
}

interface IDeletePunchlistSubItemParams {
  punchlistId: number | string;
  itemId: number | string;
  subItemId: number | string;
}

interface IPunchListItemsDeleteApiRes extends Omit<IDefaultAPIRes, "data"> {
  data: ObjType;
}
interface IAddPunchListItemRes extends Omit<IDefaultAPIRes, "data"> {
  data: ObjType;
}
interface ISendPLNotificationRes extends Omit<IDefaultAPIRes, "data"> {
  data: object;
}
