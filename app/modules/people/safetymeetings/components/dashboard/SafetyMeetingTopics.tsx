import { use<PERSON>allback, useEffect, useMemo, useRef, useState } from "react";
import type { RadioChangeEvent } from "antd";
import parse from "html-react-parser";
// hooks
import { getGSettings } from "~/zustand";
import { useTranslation } from "~/hook";
import {
  SAFE_TOPIC_BUTTON_TAB,
  SAFE_TOPIC_FILTER,
} from "~/modules/people/safetymeetings/utils/constants";
import { escapeHtmlEntities, sanitizeString } from "~/helpers/helper";
import { formatNote } from "~/shared/utils/helper/common";

// Redux
import {
  deleteActiveSafetyTopicApi,
  getSafetyTopicsApi,
} from "~/modules/people/safetymeetings/redux/action";
import { useAppSMDispatch } from "~/modules/people/safetymeetings/redux/store";
import { fetchSafetyTopicData } from "~/modules/people/safetymeetings/redux/action/commonSMAction";

// Atoms + Molecules
import { Toolt<PERSON> } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { SelectField } from "~/shared/components/molecules/selectField";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { InfiniteScroll } from "~/shared/components/molecules/InfiniteScroll/InfiniteScroll";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { AddSafetyTopic } from "~/modules/people/safetymeetings/components/sidebar/addSafetyTopic";
import LoadingSafetyMeetingTopics from "./LoadingSafetyMeetingTopics";

const SafetyMeetingTopics = ({
  search,
  addSafetyTopic,
  isReadOnly,
  setAddSafetyTopic,
  onStartMeeting,
  isAvailableMeetOpt,
}: ISafetyMeetingTopicsProps) => {
  const { _t } = useTranslation();
  const dispatch = useAppSMDispatch();

  const { safety_topic_language }: GSettings = getGSettings();
  const listTopRef = useRef<HTMLDivElement | null>(null);
  const limit = 20;
  const [selectedTopicFilter, setSelectedTopicFilter] = useState("");
  const [tab, setTab] = useState<string>("active");
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isLoadingFirst, setIsLoadingFirst] = useState<boolean>(true);
  const [topicList, setTopicList] = useState<IGetSTopicItems[]>([]);
  const [pageNo, setPageNo] = useState<number>(0);
  const [isHasMore, setIsHasMore] = useState<boolean>(true);
  const [delArchConfirm, setDelArchConfirm] = useState<string>("");
  const [selectedData, setSelectedData] = useState<Partial<IGetSTopicItems>>(
    {}
  );
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [isReloadList, setIsReloadList] = useState<boolean>(false);

  const fetchSaftyTopicList = async ({
    page,
    language,
    tabVal,
    searchTopic,
  }: IFetchSaftyTopicList) => {
    try {
      setIsLoading(true);

      const params = {
        start: limit * page,
        limit,
        search: searchTopic ? escapeHtmlEntities(searchTopic) : undefined,
        status:
          tabVal == "active" ? "0" : tabVal == "archived" ? "1" : undefined,
        language:
          language == "both" ? "0,1" : language == "english" ? "0" : "1",
        only_topics: 0,
      };
      if (page === 0) {
        if (listTopRef?.current) {
          listTopRef.current.scrollTop = 0;
        }
        setIsLoadingFirst(true);
        setIsHasMore(true);
        setTopicList([]);
      }
      const resData = (await getSafetyTopicsApi(params)) as IGetSTopicApiRes;
      if (resData?.success) {
        if (page == 0) {
          setTopicList(resData?.data || []);
        } else {
          setTopicList((prevLogList) => [
            ...prevLogList,
            ...(resData?.data || []),
          ]);
        }
        setIsHasMore(resData?.data?.length >= limit);
        setIsLoading(false);
      } else {
        setIsHasMore(false);
      }
      setIsLoading(false);
      setIsLoadingFirst(false);
    } catch (err) {
      setIsHasMore(false);
      setIsLoading(false);
      setIsLoadingFirst(false);
    }
  };

  useEffect(() => {
    if (!!selectedTopicFilter) {
      setPageNo(0);
      fetchSaftyTopicList({
        page: 0,
        language: selectedTopicFilter,
        tabVal: tab,
        searchTopic: search,
      });
    }
  }, [search, tab, selectedTopicFilter]);

  useEffect(() => {
    if (isReloadList) {
      setPageNo(0);
      fetchSaftyTopicList({
        page: 0,
        language: selectedTopicFilter,
        tabVal: tab,
        searchTopic: search,
      });
      setIsReloadList(false);
    }
  }, [isReloadList]);

  const loadMore = useCallback(() => {
    setPageNo((prev) => prev + 1);
    fetchSaftyTopicList({
      page: pageNo + 1,
      language: selectedTopicFilter,
      tabVal: tab,
      searchTopic: search,
    });
  }, [pageNo, search, tab, selectedTopicFilter]);

  const handleSafetyTDelActive = async () => {
    if (isDeleting || !selectedData.topic_id) return;
    setIsDeleting(true);

    try {
      const { success, message } = (await deleteActiveSafetyTopicApi({
        topic_id: Number(selectedData.topic_id),
        parent_topic_id: Number(selectedData?.parent_topic_id || ""),
        status: delArchConfirm,
      })) as IDelAvtiveSMTopicRes;

      if (success) {
        setSelectedData({});
        setDelArchConfirm("");

        setTopicList((prev) =>
          prev.reduce((acc, item) => {
            const isTarget = item.topic_id === selectedData.topic_id;
            const isArchivedNow = delArchConfirm !== "active";
            const shouldRemove =
              isTarget &&
              ((tab === "archived" && !isArchivedNow) ||
                (tab === "active" && isArchivedNow));
            if (shouldRemove) return acc;
            acc.push(
              isTarget ? { ...item, is_deleted: isArchivedNow ? 1 : 0 } : item
            );

            return acc;
          }, [] as typeof prev)
        );
      } else {
        notification.error({
          description: message || "Something went wrong",
        });
      }
    } catch (error) {
      notification.error({
        description: "An unexpected error occurred",
      });
    } finally {
      dispatch(
        fetchSafetyTopicData({
          start: 0,
          limit: 0,
          only_topics: 1,
          status: "0",
          language: "0,1",
        })
      );

      setIsDeleting(false);
    }
  };

  const onCloseDelModal = () => {
    setSelectedData({});
    setDelArchConfirm("");
  };

  useEffect(() => {
    if (safety_topic_language == "0") {
      setSelectedTopicFilter("english");
    } else if (safety_topic_language == "1") {
      setSelectedTopicFilter("spanish");
    } else {
      setSelectedTopicFilter("both");
    }
  }, [safety_topic_language]);

  return (
    <>
      <div
        className={`overflow-y-auto overflow-hidden sm:mb-4 sm:pb-0 pb-14 ${
          !window.ENV.PAGE_IS_IFRAME
            ? "md:h-[calc(100dvh-200px)] h-[calc(100dvh-112px)]"
            : "h-screen"
        }`}
        ref={listTopRef}
      >
        <div className="flex sm:flex-row flex-col items-end gap-2.5 p-4 justify-end sticky top-0 z-[99] bg-[#F8F8F9]">
          <SelectField
            labelPlacement="left"
            fieldClassName="before:hidden w-fit"
            options={SAFE_TOPIC_FILTER}
            showSearch={false}
            className={`!rounded h-[28px] px-2 !font-medium !text-primary-900 bg-[#ECF1F9]`}
            popupClassName="min-w-[150px]"
            placement="bottomRight"
            formInputClassName="w-auto"
            value={selectedTopicFilter}
            onChange={(value) => setSelectedTopicFilter(value as string)}
          />
          <ListTabButton
            value={tab}
            options={SAFE_TOPIC_BUTTON_TAB}
            className="min-w-[100px]"
            onChange={(e: RadioChangeEvent) => {
              setTab(e.target.value);
            }}
            activeclassName="!bg-[#F1F4F9]"
          />
        </div>
        <div className="px-4 mb-4">
          <div
            className={`grid gap-2 ${
              isLoadingFirst ? "overflow-hidden" : "overflow-y-auto"
            }`}
          >
            <InfiniteScroll
              loadMore={loadMore}
              hasMore={isHasMore}
              isLoading={isLoading}
              hideLoadingComponent={!isLoading && topicList.length <= 0}
              continueCall={topicList?.length > 0}
              loadingComponent={
                <LoadingSafetyMeetingTopics
                  skeleton={!topicList.length ? 20 : 2}
                />
              }
            >
              {topicList?.map((item) => {
                return (
                  <CollapseSingleTable
                    title={HTMLEntities.decode(sanitizeString(item?.title))}
                    key={`topic_${item?.topic_id}`}
                    totalRecordIcon={true}
                    rightsideContant={
                      <div className="flex gap-4 items-center">
                        <Typography className="block text-[13px] px-2 py-1 bg-[#e9edf5] rounded">
                          {item?.language == 0 ? _t("English") : _t("Spanish")}
                        </Typography>
                        {item?.is_deleted == 1 ? (
                          <div className="px-2 text-center w-[110px]">-</div>
                        ) : (
                          <div
                            className={`${
                              isAvailableMeetOpt
                                ? "cursor-pointer hover:bg-primary-900"
                                : "cursor-not-allowed hover:bg-primary-900/70"
                            } px-2 py-1 bg-[#e9edf5] group flex items-center gap-2 rounded btn-collapse`}
                            onClick={() => {
                              if (!isAvailableMeetOpt) return;
                              onStartMeeting(item);
                            }}
                          >
                            <Typography className="block h-1.5 w-1.5 bg-[#687182] "></Typography>
                            <Typography className="block text-[13px] group-hover:text-white ">
                              {_t("Start Meeting")}
                            </Typography>
                          </div>
                        )}

                        {!isReadOnly &&
                          (item?.is_deleted === 1 ? (
                            <Tooltip
                              title={
                                <>
                                  {_t(`Status: Archived`)} <br />
                                  {_t(`Click to Activate the safety topic`)}
                                </>
                              }
                            >
                              <FontAwesomeIcon
                                className="cursor-pointer text-base text-primary-900"
                                icon="fa-regular fa-regular-active"
                                onClick={() => {
                                  setSelectedData(item);
                                  setDelArchConfirm("active");
                                }}
                              />
                            </Tooltip>
                          ) : (
                            <Tooltip
                              title={
                                item?.is_use === 1 ? (
                                  <>
                                    {_t(`Status: Active`)} <br />
                                    {_t(
                                      "This topic is in use and cannot be archived."
                                    )}
                                  </>
                                ) : (
                                  <>
                                    {_t(`Status: Active`)} <br />
                                    {_t("Click to Archive the safety topic")}
                                  </>
                                )
                              }
                            >
                              <FontAwesomeIcon
                                className={`${
                                  item?.is_use === 0 ? "cursor-pointer" : ""
                                } text-base text-primary-900`}
                                icon={
                                  item?.is_use === 1
                                    ? "fa-regular fa-ban"
                                    : "fa-regular fa-box-archive"
                                }
                                onClick={() => {
                                  if (item?.is_use === 1) return;
                                  setSelectedData(item);
                                  setDelArchConfirm("archive");
                                }}
                              />
                            </Tooltip>
                          ))}
                      </div>
                    }
                    children={
                      <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
                        <div className="ag-theme-alpine editor-list">
                          {item?.note?.trim() ? (
                            parse(formatNote(item.note))
                          ) : (
                            <NoRecords
                              className="w-full"
                              image={`${window.ENV.CDN_URL}assets/images/no-records-sm-topic.svg`}
                            />
                          )}
                        </div>
                      </div>
                    }
                  />
                );
              })}

              {!isLoading && topicList.length <= 0 && (
                <NoRecords
                  className="h-[calc(100vh-280px)] min-h-72"
                  rootClassName="w-full max-w-64"
                  image={`${window.ENV.CDN_URL}assets/images/create-record-list-view.svg`}
                  text={
                    <Typography className="sm:text-base text-xs text-black font-semibold">
                      {_t("No Record Found")}
                    </Typography>
                  }
                />
              )}
            </InfiniteScroll>
          </div>
        </div>
      </div>
      {addSafetyTopic && (
        <AddSafetyTopic
          drowerOpen={addSafetyTopic}
          setDrowerOpen={setAddSafetyTopic}
          setIsReloadList={setIsReloadList}
          safetyTopicLanguage={safety_topic_language}
        />
      )}

      {delArchConfirm !== "" && (
        <ConfirmModal
          isOpen={delArchConfirm !== ""}
          modaltitle={_t(delArchConfirm === "archive" ? "Archive" : "Active")}
          description={_t(
            delArchConfirm === "archive"
              ? "Are you sure you want to Archive this item? To view it or Activate it later, set the filter to show Archived items."
              : "Are you sure you want to Activate this data?"
          )}
          withConfirmText={delArchConfirm === "delete"}
          modalIcon={
            delArchConfirm === "archive"
              ? "fa-regular fa-box-archive"
              : "fa-regular fa-regular-active"
          }
          isLoading={isDeleting}
          onAccept={(data) => {
            handleSafetyTDelActive();
          }}
          onDecline={onCloseDelModal}
          onCloseModal={onCloseDelModal}
        />
      )}
    </>
  );
};
export default SafetyMeetingTopics;
