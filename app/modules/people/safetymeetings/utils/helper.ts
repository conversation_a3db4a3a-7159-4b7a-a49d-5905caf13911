export const convertImageUrlToBase64 = (
  url: string
): Promise<{ base64: string; width: number; height: number }> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = "anonymous";

    img.onload = () => {
      const canvas = document.createElement("canvas");
      canvas.width = img.width;
      canvas.height = img.height;

      const ctx = canvas.getContext("2d");
      if (ctx) {
        // Fill background with white
        ctx.fillStyle = "#ffffff";
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Draw image on top
        ctx.drawImage(img, 0, 0);
      }

      const base64 = canvas.toDataURL("image/jpeg");

      resolve({
        base64,
        width: img.width,
        height: img.height,
      });
    };

    img.onerror = (err) => {
      reject(new Error("Failed to load image: " + err));
    };

    img.src = url;
  });
};
