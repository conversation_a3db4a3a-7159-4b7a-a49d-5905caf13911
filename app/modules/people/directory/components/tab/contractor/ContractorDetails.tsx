import { useEffect, useMemo, useRef, useState } from "react";
import delay from "lodash/delay";
import isEmpty from "lodash/isEmpty";
import { defaultConfig } from "~/data";
// TODO replace this with redux.
import { getGConfig, getGSettings } from "~/zustand";
import type { InputRef } from "antd";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { Popover } from "~/shared/components/atoms/popover";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { FieldStatus } from "~/shared/components/molecules/fieldStatus";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { InputField } from "~/shared/components/molecules/inputField";
import { InputPhoneNumber } from "~/shared/components/molecules/inputPhoneNumber";
import { SelectField } from "~/shared/components/molecules/selectField";

import DirSendEmail from "../../DirSendEmail";
// constants
import {
  addItemObject,
  dirDetailsField,
  fieldStatus,
} from "~/modules/people/directory/utils/constasnts";
import { getPhoneFormat } from "~/shared/utils/helper/defaultPhoneFormat";
import {
  getStatusForField,
  multiSelect,
  onKeyDownDigit,
  filterOptionBySubstring,
  validatePhoneNumber,
  sanitizeAndTruncate,
  validateEmail,
  getStatusActionForField,
} from "~/shared/utils/helper/common";
import {
  escapeHtmlEntities,
  replaceDOMParams,
  sanitizeString,
} from "~/helpers/helper";
// redux store, slice and action
import { useTranslation } from "~/hook";
import { useAppDispatch, useAppSelector } from "../../../redux/store";
import {
  dirReActiveBounceEmail,
  updateDirDetailApi,
} from "../../../redux/action/dirDetailsAction";
import { updateDirDetail } from "../../../redux/slices/dirDetailsSlice";
import { addScopeServices } from "~/redux/action/getScopeServicesAction";
import { addScopeServicesAct } from "~/redux/slices/getScopeServicesSlice";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";

const ContractorDetails = ({ isReadOnly = false }: { isReadOnly: boolean }) => {
  const gConfig: GConfig = getGConfig();
  const gSettings: GSettings = getGSettings();
  const { _t } = useTranslation();
  const loadingStatusRef = useRef(fieldStatus);
  const inputEmailRef = useRef<InputRef>(null);
  const inputPhoneRef = useRef<InputRef>(null);
  const inputPhone2Ref = useRef<InputRef>(null);
  const inputCellRef = useRef<InputRef>(null);
  const inputFaxRef = useRef<InputRef>(null);
  const [isEmailEditing, setIsEmailEditing] = useState<boolean>(false);

  const { details }: IDirInitialState = useAppSelector(
    (state) => state.dirDetails
  );
  const { scopSerList }: IScopSerInitialState = useAppSelector(
    (state) => state.scopService
  );

  const dispatch = useAppDispatch();
  const [inputValues, setInputValues] = useState<IDirDetails>(dirDetailsField);

  const [scopSerDataAdd, setScopSerDataAdd] = useState<ICommonCustomDataFrm>(
    {}
  );
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);
  const [isLoadingReActEmail, setIsLoadingReActEmail] =
    useState<boolean>(false);

  const [copied, setCopied] = useState<boolean>(false);

  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(fieldStatus);
  const [searchSOSText, setSearchSOSText] = useState("");

  useEffect(() => {
    setInputValues(details);
  }, [details.user_id, details.email, details.cell]);

  const scopServiceList: IScopSerList[] = useMemo(
    () =>
      scopSerList?.map((item: IScopSer) => ({
        label: replaceDOMParams(sanitizeString(item.service_name)),
        value: item.service_id.toString(),
      })),
    [scopSerList]
  );

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );

    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };

  const handleUpdateField = async (data: IDirDetailFields) => {
    const field = Object.keys(data)[0] as keyof IDirDetails;
    const values = Object.values(data)[0];

    if (
      (field === "phone" ||
        field === "phone2" ||
        field === "cell" ||
        field === "fax") &&
      isEmpty(getPhoneFormat(gSettings?.phone_format))
    ) {
      data[field] = sanitizeAndTruncate(values as string, 15);
    }

    setInputValues({ ...inputValues, ...data });
    if (
      (field === "phone" ||
        field === "phone2" ||
        field === "cell" ||
        field === "fax") &&
      values !== "" &&
      !validatePhoneNumber(values as string, gSettings?.phone_format)
    ) {
      setInputValues({ ...inputValues, [field]: details[field] });
      notification.error({
        description: `Enter a valid ${field} number`,
      });
      return false;
    }

    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });

    const updateRes = (await updateDirDetailApi({
      directory_id: details?.user_id || "",
      type: details?.type || "",
      module_id: gConfig?.module_id,
      ...data,
    })) as IDirDetailsUpdateApiRes;

    if (updateRes?.success) {
      if (field === "email") {
        const emailBounce = updateRes?.data?.emailBounceData;
        dispatch(
          updateDirDetail({
            email_bounce_id: emailBounce?.emailBounceId || null,
            bounce_message: emailBounce?.bounceMessage || null,
            email_bounce_status: emailBounce?.emailBounceStatus || null,
          })
        );
      }
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      dispatch(updateDirDetail(data));
    } else {
      handleChangeFieldStatus({
        field: field,
        status: "error",
        action: "API",
      });
      setInputValues({ ...inputValues, [field]: details[field] });
      notification.error({
        description: updateRes?.message,
      });
    }

    // Remove success icon after 3 some second that's why use delay function.
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );

      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 3000);
  };

  const handleInpOnChange = ({
    target: { value, name },
  }: React.ChangeEvent<HTMLInputElement>) => {
    setInputValues({ ...inputValues, [name]: value });
  };

  const handleMultiSelect = (
    values: string | string[],
    selectedOption: string
  ) => {
    if (selectedOption === "tags") {
      setInputValues({
        ...inputValues,
        tags: multiSelect({ key: "tags", values, inputValues }),
      });
      handleUpdateField({
        tags: multiSelect({ key: "tags", values, inputValues }),
      });
    } else {
      setInputValues({
        ...inputValues,
        services: multiSelect({ key: "services", values, inputValues }),
      });
      handleUpdateField({
        services: multiSelect({ key: "services", values, inputValues }),
      });
    }
  };

  const handlekeyDown = (
    event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    if (event.key === "Enter") {
      const value = event?.currentTarget?.value?.trim();
      const newType = onEnterSelectSearchValue(event, scopServiceList || []);
      if (newType) {
        setScopSerDataAdd({
          service_name: escapeHtmlEntities(newType),
        });
        setIsConfirmDialogOpen(true);
      } else if (value) {
        notification.error({
          description: "Records already exist, no new records were added.",
        });
      }
    }
  };

  const closeConfirmationModal = () => {
    setIsConfirmDialogOpen(false);
  };

  const handleAddCustomData = async () => {
    if (!isAddingCustomData && scopSerDataAdd?.service_name) {
      setIsAddingCustomData(true);

      const scopSerRes = (await addScopeServices({
        serviceName: scopSerDataAdd?.service_name,
      })) as IScopSerAddUpRes;

      const newTermId: string = inputValues.services
        ? `${inputValues.services},${scopSerRes?.data?.service_id.toString()}`
        : scopSerRes?.data?.service_id.toString();

      if (scopSerRes?.success) {
        dispatch(addScopeServicesAct(scopSerRes?.data));

        setInputValues({
          ...inputValues,
          services: newTermId,
        });
        handleUpdateField({
          services: newTermId,
        });
        setIsConfirmDialogOpen(false);
      } else {
        notification.error({ description: scopSerRes.message });
      }
      setIsAddingCustomData(false);
    }
  };

  const copyToClipboard = (text: string) => {
    const el = document.createElement("textarea");
    el.value = text;
    document.body.appendChild(el);
    el.select();
    document.execCommand("copy");
    document.body.removeChild(el);
  };

  const handleActiveBounceEmail = async (id: number) => {
    if (!isLoadingReActEmail && id) {
      setIsLoadingReActEmail(true);
      const reActEmailRes = (await dirReActiveBounceEmail({
        postmarkBounceId: id.toString(),
      })) as IDirReActiveBounceEmailRes;
      if (reActEmailRes?.success) {
        dispatch(
          updateDirDetail({
            email_bounce_id: null,
            bounce_message: null,
            email_bounce_status: null,
          })
        );
      } else {
        notification.error({ description: reActEmailRes.message });
      }
      setIsLoadingReActEmail(false);
    }
  };
  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Details")}
        iconProps={{
          icon: "fa-solid fa-file-lines",
          containerClassName:
            "bg-[linear-gradient(180deg,#FF868A1a_0%,#FD33331a_100%)]",
          id: "contractor_details_icon",
          colors: ["#FF868A", "#FD3333"],
        }}
        children={
          <div className="pt-2">
            <ul className="w-full grid sm:gap-1 gap-2">
              <li className="overflow-hidden">
                <InputField
                  label={_t("Title")}
                  value={inputValues?.title}
                  name="title"
                  placeholder={_t("Title")}
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  fixStatus={getStatusForField(loadingStatus, "title")}
                  onChange={handleInpOnChange}
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "title",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "title",
                      status: "button",
                      action: "ML",
                    });
                  }}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "title",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onBlur={(e) => {
                    const value = e?.target?.value.trim();
                    if (value !== details?.title) {
                      handleUpdateField({ title: value });
                    } else {
                      handleChangeFieldStatus({
                        field: "title",
                        status: "button",
                        action: "BLUR",
                      });
                      setInputValues({
                        ...inputValues,
                        title: details.title,
                      });
                    }
                  }}
                />
              </li>
              <li className="overflow-hidden">
                <InlineField
                  label={_t("Email")}
                  labelPlacement="left"
                  field={
                    <div
                      className={`flex items-center gap-1 overflow-hidden ${
                        isReadOnly ? "" : "focus-within:w-full hover:w-full"
                      } ${
                        !inputValues?.email?.trim()
                          ? "w-full"
                          : "sm:w-fit w-full"
                      }`}
                    >
                      {isEmailEditing ? (
                        <InputField
                          ref={inputEmailRef}
                          value={inputValues?.email}
                          name="email"
                          placeholder={_t("Email")}
                          labelPlacement="left"
                          editInline={true}
                          readOnly={isReadOnly}
                          iconView={true}
                          onChange={handleInpOnChange}
                          fixStatus={getStatusForField(loadingStatus, "email")}
                          onFocus={() =>
                            handleChangeFieldStatus({
                              field: "email",
                              status: "save",
                              action: "FOCUS",
                            })
                          }
                          onBlur={(e) => {
                            setIsEmailEditing(false);
                            const value = e?.target?.value.trim();
                            if (value !== details?.email) {
                              if (value && !validateEmail(value)) {
                                notification.error({
                                  description: _t("Invalid email address."),
                                });

                                setInputValues({
                                  ...inputValues,
                                  email: details?.email,
                                });
                                return;
                              }
                              handleUpdateField({ email: value });
                            } else {
                              setInputValues({
                                ...inputValues,
                                email: details?.email,
                              });
                              handleChangeFieldStatus({
                                field: "email",
                                status: "button",
                                action: "BLUR",
                              });
                            }
                          }}
                        />
                      ) : (
                        <div
                          className={`${
                            isReadOnly
                              ? "cursor-default"
                              : "focus-within:w-full hover:w-full cursor-text"
                          } ${
                            !inputValues?.email?.trim()
                              ? "w-full"
                              : "max-w-[calc(100%-27px)] sm:w-fit w-full"
                          }`}
                        >
                          <div
                            className={`text-sm text-primary-900 px-1.5 flex justify-between items-center h-[34px] ${
                              !isReadOnly &&
                              "hover:bg-[#f4f5f6] min-[768px]:bg-transparent bg-[#f4f5f6] focus-within:bg-[#f4f5f6]"
                            } `}
                            tabIndex={0}
                            onFocus={() => {
                              if (isReadOnly) {
                                return false;
                              }
                              setIsEmailEditing(true);
                              setTimeout(() => {
                                inputEmailRef?.current?.focus();
                              }, 0);
                            }}
                            onMouseEnter={() => {
                              if (isReadOnly) {
                                return false;
                              }
                              handleChangeFieldStatus({
                                field: "email",
                                status: "edit",
                                action: "ME",
                              });
                            }}
                            onMouseLeave={() => {
                              if (isReadOnly) {
                                return false;
                              }
                              handleChangeFieldStatus({
                                field: "email",
                                status: "button",
                                action: "ML",
                              });
                            }}
                          >
                            <Tooltip title={inputValues.email} placement="top">
                              <Typography
                                className={
                                  inputValues?.email?.trim()
                                    ? "truncate text-primary-900 block"
                                    : "text-[#bdbdbd] tracking-[0.3px] font-light"
                                }
                              >
                                {inputValues?.email?.trim() || _t("Email")}
                              </Typography>
                            </Tooltip>
                            <FieldStatus
                              className="ml-2"
                              status={getStatusForField(loadingStatus, "email")}
                            />
                          </div>
                        </div>
                      )}
                      {!!details?.email && (
                        <Popover
                          placement="right"
                          trigger="hover"
                          content={
                            <div className="dark:bg-dark-900">
                              <div className="p-1">
                                <ul className="max-h-[calc(100vh-300px)] ">
                                  <li
                                    className="py-1 px-2.5 cursor-pointer group/email hover:!bg-blue-50 rounded-sm"
                                    onClick={() =>
                                      setIsSendEmailSidebarOpen(true)
                                    }
                                  >
                                    <Typography className="text-xs text-primary-900 dark:text-white">
                                      {_t("Send Email Through System")}
                                    </Typography>
                                  </li>
                                  <li
                                    className="py-1 px-2.5 cursor-pointer group/email hover:!bg-blue-50 rounded-sm"
                                    onClick={() => {
                                      copyToClipboard(details?.email || "");
                                      setCopied(true);
                                      setTimeout(() => setCopied(false), 3000);
                                    }}
                                  >
                                    <Typography className="text-xs text-primary-900 dark:text-white">
                                      {copied
                                        ? _t("Email Copied!")
                                        : _t("Copy Email Address")}
                                    </Typography>
                                  </li>
                                  <li className="py-1 px-2.5 cursor-pointer group/email hover:!bg-blue-50 rounded-sm">
                                    <a href={`mailto:${details?.email}`}>
                                      <Typography className="text-xs text-primary-900 dark:text-white">
                                        {_t("Open Email Client")}
                                      </Typography>
                                    </a>
                                  </li>
                                </ul>
                              </div>
                            </div>
                          }
                        >
                          <div className="!w-6 !h-6 cursor-pointer flex items-center rounded justify-center group/buttonHover hover:!bg-[#f0f0f0]">
                            <FontAwesomeIcon
                              icon="fa-regular fa-envelope"
                              className="w-3.5 h-3.5 text-primary-900/80 group-hover/buttonHover:text-primary-900"
                            />
                          </div>
                        </Popover>
                      )}
                    </div>
                  }
                />
              </li>
              <li
                className={
                  details?.email_bounce_status == 1 ? "block" : "hidden"
                }
              >
                <Typography
                  onClick={() => {
                    if (isLoadingReActEmail || isReadOnly) {
                      return;
                    }
                    handleActiveBounceEmail(details?.email_bounce_id || 0);
                  }}
                  className={`text-[#D2322D] text-xs block sm:pl-[177px] ${
                    isLoadingReActEmail ? "opacity-70" : ""
                  } ${
                    isLoadingReActEmail || isReadOnly
                      ? "pointer-event-none"
                      : "cursor-pointer"
                  }`}
                >
                  {details?.bounce_message}
                </Typography>
              </li>
              <li>
                <InlineField
                  label={_t("Phone")}
                  labelPlacement="left"
                  field={
                    <div className="flex gap-1 items-center">
                      <div className="w-full max-w-[160px]">
                        <Tooltip
                          title={_t(
                            `For SMS features, input your number in the "Cell" field.`
                          )}
                          placement="top"
                        >
                          <div className="w-full">
                            <InputPhoneNumber
                              label=""
                              ref={inputPhoneRef}
                              value={inputValues?.phone}
                              name="phone"
                              mask={getPhoneFormat(gSettings?.phone_format)}
                              placeholder={
                                gSettings?.phone_format || _t("Phone")
                              }
                              labelPlacement="left"
                              editInline={true}
                              iconView={true}
                              readOnly={isReadOnly}
                              readOnlyClassName="sm:w-[140px] w-[140px]"
                              fixStatus={getStatusForField(
                                loadingStatus,
                                "phone"
                              )}
                              onChange={handleInpOnChange}
                              onMouseEnter={() => {
                                handleChangeFieldStatus({
                                  field: "phone",
                                  status: "edit",
                                  action: "ME",
                                });
                              }}
                              onMouseLeaveDiv={() => {
                                handleChangeFieldStatus({
                                  field: "phone",
                                  status: "button",
                                  action: "ML",
                                });
                              }}
                              onFocus={() =>
                                handleChangeFieldStatus({
                                  field: "phone",
                                  status: "save",
                                  action: "FOCUS",
                                })
                              }
                              onBlur={({
                                target: { value },
                              }: React.FocusEvent<HTMLInputElement>) => {
                                if (value !== details?.phone) {
                                  handleUpdateField({ phone: value });
                                } else {
                                  handleChangeFieldStatus({
                                    field: "phone",
                                    status: "button",
                                    action: "BLUR",
                                  });
                                  setInputValues({
                                    ...inputValues,
                                    phone: details.phone,
                                  });
                                }
                              }}
                              onKeyDown={(
                                event: React.KeyboardEvent<HTMLInputElement>
                              ) => {
                                if (event.metaKey || event.ctrlKey) {
                                  return;
                                }
                                if (gSettings?.phone_format !== "") {
                                  return false;
                                }
                                if (event.key === "Enter") {
                                  setInputValues({
                                    ...inputValues,
                                    phone: event?.currentTarget?.value,
                                  });
                                }
                                return onKeyDownDigit(event, {
                                  integerDigits: 14,
                                });
                              }}
                              onClickStsIcon={() => {
                                inputPhoneRef?.current?.focus();
                              }}
                            />
                          </div>
                        </Tooltip>
                      </div>
                      <div className="w-full max-w-[100px]">
                        <InputField
                          label=""
                          value={inputValues?.phone_ext}
                          name="phone_ext"
                          placeholder={_t("Ext.")}
                          labelPlacement="left"
                          editInline={true}
                          iconView={true}
                          readOnly={isReadOnly}
                          readOnlyClassName="sm:w-[55px] w-[55px]"
                          fixStatus={getStatusForField(
                            loadingStatus,
                            "phone_ext"
                          )}
                          onChange={handleInpOnChange}
                          onKeyDown={(
                            event: React.KeyboardEvent<HTMLInputElement>
                          ) => {
                            if (event.key === "Enter") {
                              setInputValues({
                                ...inputValues,
                                phone_ext: event?.currentTarget?.value,
                              });
                            } else {
                              return onKeyDownDigit(event, {
                                integerDigits: 4,
                              });
                            }
                          }}
                          onMouseEnter={() => {
                            handleChangeFieldStatus({
                              field: "phone_ext",
                              status: "edit",
                              action: "ME",
                            });
                          }}
                          onMouseLeaveDiv={() => {
                            handleChangeFieldStatus({
                              field: "phone_ext",
                              status: "button",
                              action: "ML",
                            });
                          }}
                          onFocus={() =>
                            handleChangeFieldStatus({
                              field: "phone_ext",
                              status: "save",
                              action: "FOCUS",
                            })
                          }
                          onBlur={(e) => {
                            const value = e?.target?.value.trim();
                            if (value !== details?.phone_ext) {
                              handleUpdateField({
                                phone_ext: sanitizeAndTruncate(value, 5),
                              });
                            } else {
                              handleChangeFieldStatus({
                                field: "phone_ext",
                                status: "button",
                                action: "BLUR",
                              });
                              setInputValues({
                                ...inputValues,
                                phone_ext: details.phone_ext,
                              });
                            }
                          }}
                        />
                      </div>
                    </div>
                  }
                />
              </li>
              <li>
                <InlineField
                  label={_t("Phone 2")}
                  labelPlacement="left"
                  field={
                    <div className="flex gap-1 items-center">
                      <div className="w-full max-w-[160px]">
                        <Tooltip
                          title={_t(
                            `For SMS features, input your number in the "Cell" field.`
                          )}
                          placement="top"
                        >
                          <div className="w-full">
                            <InputPhoneNumber
                              label=""
                              ref={inputPhone2Ref}
                              value={inputValues?.phone2}
                              name="phone2"
                              mask={getPhoneFormat(gSettings?.phone_format)}
                              placeholder={
                                gSettings?.phone_format || _t("Phone 2")
                              }
                              labelPlacement="left"
                              editInline={true}
                              iconView={true}
                              readOnly={isReadOnly}
                              readOnlyClassName="sm:w-[140px] w-[140px]"
                              fixStatus={getStatusForField(
                                loadingStatus,
                                "phone2"
                              )}
                              onChange={handleInpOnChange}
                              onMouseEnter={() => {
                                handleChangeFieldStatus({
                                  field: "phone2",
                                  status: "edit",
                                  action: "ME",
                                });
                              }}
                              onMouseLeaveDiv={() => {
                                handleChangeFieldStatus({
                                  field: "phone2",
                                  status: "button",
                                  action: "ML",
                                });
                              }}
                              onFocus={() =>
                                handleChangeFieldStatus({
                                  field: "phone2",
                                  status: "save",
                                  action: "FOCUS",
                                })
                              }
                              onBlur={({
                                target: { value },
                              }: React.FocusEvent<HTMLInputElement>) => {
                                if (value !== details?.phone2) {
                                  handleUpdateField({ phone2: value });
                                } else {
                                  handleChangeFieldStatus({
                                    field: "phone2",
                                    status: "button",
                                    action: "BLUR",
                                  });
                                  setInputValues({
                                    ...inputValues,
                                    phone2: details.phone2,
                                  });
                                }
                              }}
                              onKeyDown={(
                                event: React.KeyboardEvent<HTMLInputElement>
                              ) => {
                                if (event.metaKey || event.ctrlKey) {
                                  return;
                                }
                                if (gSettings?.phone_format !== "") {
                                  return false;
                                }
                                if (event.key === "Enter") {
                                  setInputValues({
                                    ...inputValues,
                                    phone2: event?.currentTarget?.value,
                                  });
                                }
                                return onKeyDownDigit(event, {
                                  integerDigits: 14,
                                });
                              }}
                              onClickStsIcon={() => {
                                inputPhone2Ref?.current?.focus();
                              }}
                            />
                          </div>
                        </Tooltip>
                      </div>
                      <div className="w-full max-w-[100px]">
                        <InputField
                          label=""
                          value={inputValues?.phone_ext2}
                          name="phone_ext2"
                          placeholder={_t("Ext.")}
                          labelPlacement="left"
                          editInline={true}
                          iconView={true}
                          readOnly={isReadOnly}
                          readOnlyClassName="sm:w-[55px] w-[55px]"
                          fixStatus={getStatusForField(
                            loadingStatus,
                            "phone_ext2"
                          )}
                          onChange={handleInpOnChange}
                          onKeyDown={(
                            event: React.KeyboardEvent<HTMLInputElement>
                          ) => {
                            if (event.key === "Enter") {
                              setInputValues({
                                ...inputValues,
                                phone_ext2: event?.currentTarget?.value,
                              });
                            } else {
                              return onKeyDownDigit(event, {
                                integerDigits: 4,
                              });
                            }
                          }}
                          onMouseEnter={() => {
                            handleChangeFieldStatus({
                              field: "phone_ext2",
                              status: "edit",
                              action: "ME",
                            });
                          }}
                          onMouseLeaveDiv={() => {
                            handleChangeFieldStatus({
                              field: "phone_ext2",
                              status: "button",
                              action: "ML",
                            });
                          }}
                          onFocus={() =>
                            handleChangeFieldStatus({
                              field: "phone_ext2",
                              status: "save",
                              action: "FOCUS",
                            })
                          }
                          onBlur={(e) => {
                            const value = e?.target?.value.trim();
                            if (value !== details?.phone_ext2) {
                              handleUpdateField({
                                phone_ext2: sanitizeAndTruncate(value, 5),
                              });
                            } else {
                              handleChangeFieldStatus({
                                field: "phone_ext2",
                                status: "button",
                                action: "BLUR",
                              });
                              setInputValues({
                                ...inputValues,
                                phone_ext2: details.phone_ext2,
                              });
                            }
                          }}
                        />
                      </div>
                    </div>
                  }
                />
              </li>
              <li className="overflow-hidden">
                <InlineField
                  label={_t("Cell")}
                  labelPlacement="left"
                  field={
                    <div className="w-full">
                      <InputPhoneNumber
                        label=""
                        ref={inputCellRef}
                        value={inputValues?.cell}
                        name="cell"
                        mask={getPhoneFormat(gSettings?.phone_format)}
                        placeholder={gSettings?.phone_format || _t("Cell")}
                        labelPlacement="left"
                        editInline={true}
                        iconView={true}
                        readOnly={isReadOnly}
                        fixStatus={getStatusForField(loadingStatus, "cell")}
                        onChange={handleInpOnChange}
                        onMouseEnter={() => {
                          handleChangeFieldStatus({
                            field: "cell",
                            status: "edit",
                            action: "ME",
                          });
                        }}
                        onMouseLeaveDiv={() => {
                          handleChangeFieldStatus({
                            field: "cell",
                            status: "button",
                            action: "ML",
                          });
                        }}
                        onFocus={() =>
                          handleChangeFieldStatus({
                            field: "cell",
                            status: "save",
                            action: "FOCUS",
                          })
                        }
                        onBlur={({
                          target: { value },
                        }: React.FocusEvent<HTMLInputElement>) => {
                          if (value !== details?.cell) {
                            handleUpdateField({ cell: value });
                          } else {
                            handleChangeFieldStatus({
                              field: "cell",
                              status: "button",
                              action: "BLUR",
                            });
                            setInputValues({
                              ...inputValues,
                              cell: details.cell,
                            });
                          }
                        }}
                        onKeyDown={(
                          event: React.KeyboardEvent<HTMLInputElement>
                        ) => {
                          if (event.metaKey || event.ctrlKey) {
                            return;
                          }
                          if (gSettings?.phone_format !== "") {
                            return false;
                          }
                          if (event.key === "Enter") {
                            setInputValues({
                              ...inputValues,
                              cell: event?.currentTarget?.value,
                            });
                          }
                          return onKeyDownDigit(event, {
                            integerDigits: 14,
                          });
                        }}
                        onClickStsIcon={() => {
                          inputCellRef?.current?.focus();
                        }}
                      />
                    </div>
                  }
                />
              </li>
              <li className="overflow-hidden">
                <InputPhoneNumber
                  label={_t("Fax")}
                  ref={inputFaxRef}
                  value={inputValues?.fax}
                  name="fax"
                  mask={getPhoneFormat(gSettings?.phone_format)}
                  placeholder={gSettings?.phone_format || _t("Fax")}
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  fixStatus={getStatusForField(loadingStatus, "fax")}
                  onChange={handleInpOnChange}
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "fax",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "fax",
                      status: "button",
                      action: "ML",
                    });
                  }}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "fax",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onBlur={({
                    target: { value },
                  }: React.FocusEvent<HTMLInputElement>) => {
                    if (value !== details?.fax) {
                      handleUpdateField({ fax: value });
                    } else {
                      handleChangeFieldStatus({
                        field: "fax",
                        status: "button",
                        action: "BLUR",
                      });
                      setInputValues({
                        ...inputValues,
                        fax: details.fax,
                      });
                    }
                  }}
                  onKeyDown={(event: React.KeyboardEvent<HTMLInputElement>) => {
                    if (event.metaKey || event.ctrlKey) {
                      return;
                    }
                    if (gSettings?.phone_format !== "") {
                      return false;
                    }
                    if (event.key === "Enter") {
                      setInputValues({
                        ...inputValues,
                        fax: event?.currentTarget?.value,
                      });
                    }
                    return onKeyDownDigit(event, {
                      integerDigits: 14,
                    });
                  }}
                  onClickStsIcon={() => {
                    inputFaxRef?.current?.focus();
                  }}
                />
              </li>
              <li className="overflow-hidden">
                <SelectField
                  label={_t("Scope of Service")}
                  placeholder={_t("Select Services")}
                  maxTagValue={"responsive"}
                  onSearch={(input) => {
                    if (input.length <= 150) {
                      setSearchSOSText(input);
                    }
                  }}
                  searchValue={searchSOSText}
                  value={
                    typeof inputValues?.services === "string" &&
                    inputValues?.services
                      ? scopServiceList.filter((item) =>
                          (inputValues?.services ?? "")
                            .split(",")
                            .includes(item?.value)
                        )
                      : []
                  }
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  options={scopServiceList}
                  allowClear
                  fixStatus={getStatusForField(loadingStatus, "services")}
                  disabled={
                    getStatusForField(loadingStatus, "services") === "loading"
                  }
                  mode="multiple"
                  filterOption={(input, option) =>
                    filterOptionBySubstring(input, option?.label as string)
                  }
                  addItem={addItemObject}
                  onInputKeyDown={(e) => handlekeyDown(e)}
                  onChange={(value: string | string[]) => {
                    if (typeof value === "object") {
                      setInputValues({
                        ...inputValues,
                        services: value.join(","),
                      });
                    }
                    setSearchSOSText("");
                  }}
                  onBlur={() => {
                    if (inputValues?.services !== details.services) {
                      handleUpdateField({
                        services: inputValues?.services || "",
                      });
                    } else {
                      handleChangeFieldStatus({
                        field: "services",
                        status: "button",
                        action: "BLUR",
                      });
                    }
                    setSearchSOSText("");
                  }}
                  onCloseTag={(val) => {
                    handleMultiSelect(val, "services");
                    setSearchSOSText("");
                  }}
                />
              </li>
              <li className="overflow-hidden">
                <InputField
                  label={_t("Account") + " #"}
                  placeholder={_t("Account Number")}
                  value={inputValues?.account}
                  name="account"
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  fixStatus={getStatusForField(loadingStatus, "account")}
                  onChange={handleInpOnChange}
                  maxLength={12}
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "account",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "account",
                      status: "button",
                      action: "ML",
                    });
                  }}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "account",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onBlur={(e) => {
                    const value = e?.target?.value.trim();
                    if (value !== details?.account) {
                      handleUpdateField({ account: value });
                    } else {
                      handleChangeFieldStatus({
                        field: "account",
                        status: "button",
                        action: "BLUR",
                      });
                      setInputValues({
                        ...inputValues,
                        account: details.account,
                      });
                    }
                  }}
                />
              </li>
            </ul>
          </div>
        }
      />
      {isConfirmDialogOpen && (
        <ConfirmModal
          isOpen={isConfirmDialogOpen}
          modalIcon="fa-regular fa-clipboard-list-check"
          zIndex={9999}
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${replaceDOMParams(
              sanitizeString(scopSerDataAdd?.service_name || "")
            )}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={closeConfirmationModal}
          onAccept={() => {
            handleAddCustomData();
          }}
          onDecline={closeConfirmationModal}
        />
      )}
      <DirSendEmail
        isOpen={isSendEmailSidebarOpen}
        onSendResponse={() => {}}
        options={[
          defaultConfig.employee_key,
          "my_crew",
          defaultConfig.customer_key,
          defaultConfig.lead_key,
          defaultConfig.contractor_key,
          defaultConfig.vendor_key,
          defaultConfig.misc_contact_key,
          "by_service",
        ]}
        groupCheckBox={true}
        selectedCustomer={
          [
            {
              user_id: Number(details?.user_id) || 0,
              display_name: details?.module_display_name || "",
              type_key: details?.type_key,
              type_name: details?.type_name,
              email: details?.email || "",
            },
          ] as TselectedContactSendMail[]
        }
        app_access={false}
        onClose={() => {
          setIsSendEmailSidebarOpen(false);
        }}
      />
    </>
  );
};

export default ContractorDetails;
