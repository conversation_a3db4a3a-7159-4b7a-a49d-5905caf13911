// Hook
import { useIframe, useTranslation } from "~/hook";
// atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { Radio } from "~/shared/components/atoms/radioButton";
import { RadioGroup } from "~/shared/components/atoms/radioGroup";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { TimePickerField } from "~/shared/components/molecules/timePickerField";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
// Organisms
import { SelectProject } from "~/shared/components/organisms/selectProject";
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";
// Other
import {
  getGConfig,
  getGProject,
  getGSettings,
  useExistingProjects,
} from "~/zustand";
import { useEffect, useMemo, useRef, useState } from "react";
import { useFormik, FormikValues } from "formik";
import { filterOptionBySubstring } from "~/shared/utils/helper/common";
import { getCustomFieldAccess } from "~/shared/utils/helper/getCustomFieldAccess";
import {
  backendDateFormat,
  backendTimeFormat,
  displayTimeFormat,
} from "~/shared/utils/helper/defaultDateFormat";
import { useDirectoryKeyValue } from "~/shared/hooks/useCustomField/useDirectoryKeyValue";
import { useSideBarCustomField } from "~/shared/hooks/useCustomField/useSidebarCustomField";
import { formAddIncidentSchema } from "./utils";
import * as yup from "yup";
import { sanitizeString } from "~/helpers/helper";
import dayjs from "dayjs";
import { formatCustomFieldForRequest } from "~/shared/utils/helper/customFieldSidebarFormat";
import { defaultConfig } from "~/data";
import { useAppINDispatch, useAppINSelector } from "../../redux/store";
import {
  addIncCustomData,
  createIncident,
  fetchIncidentTypeData,
} from "../../redux/action/addIncidentAction";
import {
  NavigateFunction,
  useNavigate,
  useSearchParams,
} from "@remix-run/react";
import CustomFieldsComp from "./CustomFieldsComp";
import { incidentTypes } from "../../utils/constants";
import {
  addIncCustomDataAct,
  setCallINListApi,
} from "../../redux/slices/addIncidentSlice";
import delay from "lodash/delay";
import { addItemObject } from "~/modules/people/directory/utils/constasnts";
import { sendMessageKeys } from "~/components/page/$url/data";
import { setModuleAutoIncrementId } from "~/zustand/module-auto-increment-primary-id/actions";
import { getModuleAutoIncrementPrimaryId } from "~/zustand/module-auto-increment-primary-id/store";
import {
  EVENT_LOGGER_ACTION,
  EVENT_LOGGER_NAME,
} from "~/shared/constants/event-logger";
import { useExistingCustomers } from "~/components/sidebars/multi-select/customer/zustand";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
import OnlyRequiredCustomFields from "~/shared/components/organisms/OnlyRequiredCustomFields/OnlyRequiredCustomFields";

const AddIncidents = ({
  addIncidents,
  setAddIncidents,
  openedUsingBtn,
  setOpenedUsingBtn,
  defaultProjectId,
  action,
  employeeId,
}: IAddIncidentsProps) => {
  const { _t } = useTranslation();
  const navigate: NavigateFunction = useNavigate();
  const [incidentType, setIncidentType] = useState<string>("employee_writeup");
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const [customIncidentId, setCustomIncidentId] = useState<number>();
  const [customDataAdd, setCustomDataAdd] = useState<ICommonCustomDataFrm>({});
  const { module_singular_name, module_id, module_key }: GConfig = getGConfig();
  const { isNoAccessCustomField }: ICustomFieldAccess = getCustomFieldAccess();
  const { date_format, is_custom_incident_id }: GSettings = getGSettings();
  const [selectedEmployee, setSelectedEmployee] = useState<
    TselectedContactSendMail[]
  >([]);
  const [employeeLoading, setEmployeeLoading] = useState<boolean>(false);
  const [isOpenEmployeeContactDetails, setIsOpenEmployeeContactDetails] =
    useState<boolean>(false);
  const [isSubmit, setIsSubmit] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSelectEmployeeOpen, setIsSelectEmployeeOpen] =
    useState<boolean>(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const dispatch = useAppINDispatch();
  const {
    incidentTypeData,
    isIncidentTypeLoading,
  }: { incidentTypeData: ICustomData[]; isIncidentTypeLoading: boolean } =
    useAppINSelector((state) => state.addIncident);
  const { parentPostMessage } = useIframe();

  const { directoryKeyValue, directory }: IDirectoryFormCustomField =
    useDirectoryKeyValue();
  const {
    need_to_increment,
    last_primary_id,
  }: Partial<IResponseGetModuleAutoNumber> =
    getModuleAutoIncrementPrimaryId() || {};

  const [selectedProject, setSelectedProject] = useState<IProject[]>([]);
  const [selectedDefaultProject, setSelectedDefaultProject] = useState<
    IProject[]
  >([]);
  // this is will NF once testing done it will be merge on dev
  // const [drawerConfirmDialogOpen, setDrawerConfirmDialogOpen] =
  //   useState<boolean>(false);
  const previousIncidentVal = useRef("");

  const { componentList, loadingCustomField } = useSideBarCustomField(
    { directory, directoryKeyValue } as IDirectoryFormCustomField,
    {
      moduleId: module_id,
    } as IRequestCustomFieldForSidebar
  );
  const [isSelectProjectOpen, setIsSelectProjectOpen] =
    useState<boolean>(false);
  const { project_id }: GProject = getGProject();

  const closeConfirmationModal = () => {
    previousIncidentVal.current = "";
    setIsConfirmDialogOpen(false);
  };
  const defaultDate = dayjs().format(date_format);
  const defaultTime = dayjs().format("hh:mm A");

  const {
    initValues,
    validateArray,
  }: {
    initValues: IIncidentFormState;
    validateArray: string[];
  } = formAddIncidentSchema(
    isNoAccessCustomField,
    incidentType,
    defaultDate,
    defaultTime
  );

  const handleSubmit = async (
    values: IIncidentFormState,
    { resetForm }: FormikValues
  ) => {
    setIsLoading(true);
    let isCustomFieldValid = true;

    if (componentList.length && !isNoAccessCustomField) {
      for (let index = 0; index < componentList.length; index++) {
        const value =
          "custom_fields" in formik.values
            ? formik.values.custom_fields?.[componentList[index].name]
            : [];
        const multiple = componentList[index].multiple;
        const typeComponent = componentList[index].type;
        if (multiple || typeComponent === "checkbox-group") {
          if (!value?.length) {
            isCustomFieldValid = false;
            break;
          }
        } else if (!value) {
          isCustomFieldValid = false;
          break;
        }
      }
    }

    if (!isCustomFieldValid) {
      setIsLoading(false);
      return;
    }
    if (formik.values.incident_date) {
      formik.values.incident_date = backendDateFormat(
        formik.values.incident_date.toString(),
        date_format
      );
    }
    if (formik.values.incident_time) {
      formik.values.incident_time = backendTimeFormat(
        formik.values.incident_time
      );
    }
    if (formik.values.txn_type === "incident" && employeeId) {
      formik.values.emp_involved = [Number(employeeId)];
    }

    const formData = {
      ...formik.values,
      custom_incident_id: HTMLEntities.encode(formik.values.custom_incident_id),
      custom_fields:
        "custom_fields" in formik.values &&
        formik.values.custom_fields &&
        !isNoAccessCustomField
          ? formatCustomFieldForRequest(
              formik.values.custom_fields,
              componentList,
              date_format
            ).custom_fields
          : undefined,
    };
    if (incidentType === "incident") {
      delete formData.employee_id;
    } else {
      delete formData.incident_type;
    }

    if (is_custom_incident_id === 0) {
      delete formData.custom_incident_id;
    }

    if (incidentType === "employee_writeup") {
      formData.custom_incident_id = customIncidentId?.toString();
    }

    if (!formData.incident_time) {
      delete formData.incident_time;
    }

    if (selectedProject && selectedProject.length) {
      formData.project_name = selectedProject[0].project_name;
    }

    const res: Partial<IResponse<IIncidentAddResponse>> = await createIncident(
      formData
    );
    setSelectedEmployee([]);
    setIsLoading(false);
    if (res.success && res.statusCode === 200) {
      EventLogger.log(
        EVENT_LOGGER_NAME.incidents + EVENT_LOGGER_ACTION.added,
        1
      );
      if (window && window.ENV && window.ENV.PAGE_IS_IFRAME) {
        parentPostMessage(sendMessageKeys?.modal_change, {
          open: false,
        });
        setAddIncidents(false);
      } else if (res.data && res.data.id) {
        navigate(`${res.data.id}`);
      }
      setAddIncidents(false);
    } else {
      formik.setFieldValue("incident_date", defaultDate);
      formik.setFieldValue("incident_time", defaultTime);
      // setAddIncidents(false);
      notification.error({
        description: res?.message || "Something went wrong",
      });
    }
    dispatch(setCallINListApi());
  };

  const staticValidationSchema = validateArray.reduce((acc, fieldName) => {
    if (fieldName === "custom_incident_id") {
      acc[fieldName] =
        is_custom_incident_id === 0
          ? yup.string()
          : yup.string().required("This field is required.");
    } else {
      acc[fieldName] = yup.string().required("This field is required.");
    }
    return acc;
  }, {} as Record<string, yup.StringSchema>);

  const dynamicValidationSchema = componentList.reduce((acc, fieldName) => {
    if (fieldName.multiple || fieldName.type === "checkbox-group") {
      acc[fieldName.name] = yup
        .array()
        .of(yup.string().required("This field is required."))
        .min(1, "This field is required.")
        .required("This field is required.");
    } else {
      acc[fieldName.name] = yup.string().required("This field is required.");
    }
    return acc;
  }, {} as Record<string, yup.StringSchema | yup.AnySchema>);

  const validationSchema =
    componentList.length && !isNoAccessCustomField
      ? yup.object().shape({
          ...staticValidationSchema,
          custom_fields: yup.object().shape(dynamicValidationSchema),
        })
      : yup.object().shape({
          ...staticValidationSchema,
        });

  const initialFormValues =
    componentList.length && !isNoAccessCustomField
      ? {
          ...initValues,
          custom_fields: componentList.reduce((acc, item) => {
            acc[item.name] = item?.value ?? "";
            return acc;
          }, {} as ICustomFieldInitValue),
        }
      : initValues;

  const formik = useFormik({
    initialValues: initialFormValues,
    onSubmit: handleSubmit,
    validationSchema,
  });

  const [initialValuesState, setInitialValuesState] = useState<any>(
    formik.values
  );

  const { errors } = formik;

  // incident_type dropdown Options
  const incidentTypeOptions = useMemo(
    () =>
      incidentTypeData.length
        ? incidentTypeData
            .filter((item) => item?.name?.trim())
            .map((item) => {
              return {
                label: `${HTMLEntities.decode(sanitizeString(item.name))}`,
                value: item.item_id,
                disabled:
                  item.item_id === "self_reported_from_timecard" ? true : false,
              };
            })
        : [],
    [incidentTypeData]
  );

  const handlekeyDown = (
    event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>,
    itemType: number
  ) => {
    if (
      event.key === "Enter" &&
      itemType == incidentTypes?.classificationTypeId
    ) {
      const value = event?.currentTarget?.value?.trim();
      const newType = onEnterSelectSearchValue(
        event,
        incidentTypeOptions || []
      );
      if (newType) {
        previousIncidentVal.current = newType;
        setCustomDataAdd({
          itemType: incidentTypes?.classificationTypeId,
          name: HTMLEntities.encode(newType),
        });
        setIsConfirmDialogOpen(true);
      } else if (value) {
        notification.error({
          description: "Records already exist, no new records were added.",
        });
      }
    }
  };
  const handleAddCustomData = async () => {
    if (!isAddingCustomData && customDataAdd?.name) {
      setIsAddingCustomData(true);

      const cDataRes = (await addIncCustomData({
        itemType: customDataAdd?.itemType,
        name: customDataAdd?.name,
      })) as ICustomDataAddUpRes;

      if (cDataRes?.success) {
        dispatch(addIncCustomDataAct(cDataRes?.data));
        delay(() => {
          if (customDataAdd?.itemType == incidentTypes?.classificationTypeId) {
            formik.setFieldValue("incident_type", cDataRes?.data?.item_id);
          }
        }, 500);
        setIsConfirmDialogOpen(false);
        delay(() => {
          previousIncidentVal.current = "";
        }, 500);
      } else {
        notification.error({ description: cDataRes.message });
        previousIncidentVal.current = "";
      }
      setIsAddingCustomData(false);
    }
  };

  let { getExistingProjectsWithApi }: UseExistingProjectsResponse =
    useExistingProjects("project");
  let { getExistingUsersWithApi } = useExistingCustomers();

  const fetchDefaultProject = async (id: string) => {
    const projects = await getExistingProjectsWithApi(id);
    const projectName = projects && projects[0] && projects[0].project_name;
    const projectId = Number(id) || projects[0]?.key;

    if (projectId && projectName) {
      setSelectedProject([
        {
          id: projectId,
          project_name: projectName,
        },
      ]);
      setSelectedDefaultProject([
        {
          id: projectId,
          project_name: projectName,
        },
      ]);
    } else {
      setSelectedProject([]);
      setSelectedDefaultProject([]);
    }
  };

  useEffect(() => {
    //Call the auto number api only when drawer open and
    if (Number(is_custom_incident_id) == 2 && addIncidents) {
      setModuleAutoIncrementId(module_id, module_key);
    }
  }, [is_custom_incident_id, addIncidents]);

  useEffect(() => {
    if (
      Number(is_custom_incident_id) === 2 &&
      need_to_increment &&
      last_primary_id
    ) {
      setCustomIncidentId(Number(need_to_increment) + Number(last_primary_id));
      formik.setValues({
        ...formik.values,
        custom_incident_id: (
          Number(need_to_increment) + Number(last_primary_id)
        ).toString(),
      });
      setInitialValuesState((prevState: any) => ({
        ...prevState,
        custom_incident_id: (
          Number(need_to_increment) + Number(last_primary_id)
        ).toString(),
      }));
    } else if (Number(is_custom_incident_id) === 0) {
      formik.setValues({
        ...formik.values,
        custom_incident_id: "", // Brijesh Kevadiya told to send "" in case if the is_custom_work_orders_id is 0 -> "Set at number"
        // custom_work_order_id: (gSetting?.custom_work_orders_id).toString(),
      });
      setInitialValuesState((prevState: any) => ({
        ...prevState,
        custom_incident_id: "",
      }));
    } else {
      formik.setValues({
        ...formik.values,
        custom_incident_id: "",
      });
      setInitialValuesState((prevState: any) => ({
        ...prevState,
        custom_incident_id: "",
      }));
    }
  }, [is_custom_incident_id, need_to_increment, last_primary_id, addIncidents]);

  useEffect(() => {
    dispatch(
      fetchIncidentTypeData({
        types: [312],
        moduleId: 37,
      })
    );
  }, []);

  useEffect(() => {
    if (!openedUsingBtn) {
      if (defaultProjectId) {
        fetchDefaultProject(defaultProjectId.toString());
      } else if (!selectedProject.length && project_id) {
        fetchDefaultProject(project_id.toString());
      }
      if (employeeId) {
        setEmployeeLoading(true);
        getExistingUsersWithApi({
          usersIds: employeeId?.toString(),
          apiDataReturn: (customers: Partial<CustomerSelectedData>[]) => {
            setEmployeeLoading(false);
            if (customers && Array.isArray(customers) && customers.length) {
              setSelectedEmployee(customers);
            }
          },
        });
        // setDefaultEmployee(Number(employeeId));
      }
    } else if (!selectedProject.length && project_id) {
      fetchDefaultProject(project_id.toString());
    }
  }, [defaultProjectId, employeeId, project_id, action]);

  useEffect(() => {
    if (selectedEmployee && selectedEmployee.length) {
      const empId = selectedEmployee[0].user_id;
      formik.setFieldValue("employee_id", String(empId));
    } else {
      formik.setFieldValue("employee_id", "");
    }
  }, [selectedEmployee]);

  useEffect(() => {
    if (selectedProject && selectedProject.length) {
      formik.setFieldValue(
        "project_id",
        selectedProject[0].id || selectedProject[0]?.key
      );
      setInitialValuesState({
        ...initialValuesState,
        project_id: selectedProject[0].id || selectedProject[0]?.key,
      });
    } else {
      formik.setFieldValue("project_id", "");
      setInitialValuesState({
        ...initialValuesState,
        project_id: "",
      });
    }
  }, [selectedProject]);

  // this is will NF once testing done it will be merge on dev
  // const isFormModified = useMemo(() => {
  //   return JSON.stringify(formik.values) !== JSON.stringify(initialValuesState);
  // }, [formik.values, initialValuesState]);

  // const drawerCloseConfirmationModal = () => {
  //   addIncidents;
  //   setDrawerConfirmDialogOpen(false);
  // };

  // const handleAlertBox = async () => {
  //   setDrawerConfirmDialogOpen(false);
  //   if ([...searchParams.entries()].length) {
  //     setSearchParams({});
  //   }
  //   setAddIncidents(false);
  //   setIsSubmit(false);
  // };

  // const handleCloseDrawer = () => {
  //   if (!isFormModified) {
  //     if ([...searchParams.entries()].length) {
  //       setSearchParams({});
  //     }
  //     setAddIncidents(false);
  //     setIsSubmit(false);
  //   } else {
  //     setDrawerConfirmDialogOpen(true);
  //   }
  // };

  return (
    <>
      <Drawer
        open={addIncidents}
        rootClassName="drawer-open"
        width={718}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-triangle-exclamation"
              />
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {incidentType === "incident"
                ? _t(`Add ${module_singular_name ?? "Incidents"}`)
                : _t("Add Employee Write-Up")}
            </Header>
          </div>
        }
        // this is will NF once testing done it will be merge on dev
        closeIcon={
          !window.ENV.PAGE_IS_IFRAME ? (
            <CloseButton
              // onClick={() => handleCloseDrawer()}
              onClick={() => {
                if ([...searchParams.entries()].length) {
                  setSearchParams({});
                }
                setAddIncidents(false);
                setIsSubmit(false);
              }}
            />
          ) : null
        }
      >
        <form className="py-4" onSubmit={formik.handleSubmit}>
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <div className="grid gap-4">
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <InlineField
                    label={_t("Type")}
                    labelPlacement="top"
                    isRequired={true}
                    field={
                      <div className="w-full pt-3">
                        <RadioGroup
                          name="txn_type"
                          defaultValue="employee_writeup"
                          onChange={(e) => {
                            formik.setValues({
                              ...formik.values,
                              ...initValues,
                              project_id:
                                selectedProject && selectedProject.length
                                  ? selectedProject[0].id
                                  : "",
                              employee_id:
                                selectedEmployee && selectedEmployee.length
                                  ? selectedEmployee[0].user_id
                                  : "",
                            });
                            !employeeId && setSelectedEmployee([]);

                            // setting the default project on change
                            project_id && project_id !== "0"
                              ? String(project_id) !==
                                  String(selectedProject[0].id) &&
                                setSelectedProject(selectedDefaultProject)
                              : defaultProjectId
                              ? String(defaultProjectId) !==
                                  String(selectedProject[0].id) &&
                                setSelectedProject(selectedDefaultProject)
                              : setSelectedProject([]);

                            if (
                              defaultProjectId &&
                              selectedDefaultProject.length
                            ) {
                              formik.setFieldValue(
                                "project_id",
                                selectedDefaultProject[0].id ||
                                  selectedProject[0]?.key
                              );
                            }

                            formik.setFieldValue("txn_type", e.target.value);
                            setIncidentType(e.target.value);
                            formik.setErrors({});
                            formik.setTouched({});
                            setIsSubmit(false);
                          }}
                        >
                          <Radio
                            id="add_employee_group"
                            name="add_incidents"
                            value="employee_writeup"
                          >
                            {_t("Employee Write-Up")}
                          </Radio>
                          <Radio
                            id="add_incidents_group"
                            name="add_incidents"
                            value="incident"
                          >
                            {_t("Incident")}
                          </Radio>
                        </RadioGroup>
                      </div>
                    }
                  />
                </div>
                <div className="w-full">
                  <ButtonField
                    label={_t("Location")}
                    name="project_id"
                    labelPlacement="top"
                    onClick={() => setIsSelectProjectOpen(true)}
                    value={
                      selectedProject.length &&
                      HTMLEntities.decode(
                        sanitizeString(selectedProject[0].project_name)
                      )
                    }
                    addonBefore={
                      selectedProject.length &&
                      selectedProject[0].id &&
                      typeof selectedProject[0].id === "number" ? (
                        <ProjectFieldRedirectionIcon
                          projectId={`${selectedProject[0].id}`}
                        />
                      ) : null
                    }
                  />
                </div>
                {/* Notes: THis comment remove by developer */}
                {/* <div className="w-full">
                  <ButtonField
                    label={_t("Project")}
                    name="projectId"
                    labelPlacement="top"
                    required={true}
                    addonBefore={
                      <ProjectFieldRedirectionIcon
                        onClick={(e) => {
                          e.stopPropagation();
                        }}
                        projectId={""}
                      />
                    }
                  />
                </div> */}
                <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                  <div className="w-full">
                    <DatePickerField
                      label={_t("Date")}
                      labelPlacement="top"
                      placeholder=""
                      name="incident_date"
                      format={date_format}
                      isRequired={true}
                      allowClear={false}
                      value={
                        formik.values.incident_date
                          ? dayjs(formik.values.incident_date, date_format)
                          : undefined
                      }
                      onChange={(_, dateString) =>
                        formik.setFieldValue(
                          "incident_date",
                          dateString.toString()
                        )
                      }
                      errorMessage={
                        isSubmit && !formik.values.incident_date
                          ? errors.incident_date
                          : formik.touched.incident_date && errors.incident_date
                          ? errors.incident_date
                          : ""
                      }
                      disabledDate={(currentDate) =>
                        currentDate && currentDate > dayjs().endOf("day")
                      }
                    />
                  </div>
                  <div className="w-full">
                    <TimePickerField
                      label={_t("Time")}
                      labelPlacement="top"
                      placeholder=""
                      format="hh:mm A"
                      name="incident_time"
                      allowClear={true}
                      value={displayTimeFormat(
                        formik.values.incident_time?.trim()
                      )}
                      onChange={(_, val) => {
                        if (!val) {
                          formik.setFieldValue("incident_time", "12:00 AM");
                        } else {
                          formik.setFieldValue("incident_time", val);
                        }
                      }}
                      errorMessage={
                        isSubmit && !formik.values.incident_time
                          ? errors.incident_time
                          : formik.touched.incident_time && errors.incident_time
                          ? errors.incident_time
                          : ""
                      }
                      disabledTime={(current) => {
                        const now = dayjs();

                        const inputDate = dayjs(
                          formik.values.incident_date,
                          date_format
                        );

                        // If the input date is today, disable future times
                        if (inputDate.isSame(now, "day")) {
                          return {
                            disabledHours: () => {
                              return Array.from(
                                { length: 24 },
                                (_, i) => i
                              ).filter((hour) => hour > now.hour());
                            },
                            disabledMinutes: () => {
                              if (current.hour() === now.hour()) {
                                return Array.from(
                                  { length: 60 },
                                  (_, i) => i
                                ).filter((minute) => minute > now.minute());
                              }
                              return [];
                            },
                            disabledSeconds: () => {
                              if (
                                current.hour() === now.hour() &&
                                current.minute() === now.minute()
                              ) {
                                return Array.from(
                                  { length: 60 },
                                  (_, i) => i
                                ).filter((second) => second > now.second());
                              }
                              return [];
                            },
                          };
                        } else {
                          // If the input date is not today, allow all times
                          return {};
                        }
                      }}
                    />
                  </div>
                </div>
                {incidentType === "incident" ? (
                  <>
                    <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                      <div className="w-full">
                        <InputField
                          label={_t("Incident #")}
                          name="custom_incident_id"
                          labelPlacement="top"
                          disabled={is_custom_incident_id == 0}
                          isRequired={
                            is_custom_incident_id === 0 ? false : true
                          }
                          id="custom_incident_id"
                          value={
                            is_custom_incident_id == 0
                              ? "Save To View"
                              : formik.values?.custom_incident_id
                          }
                          maxLength={21}
                          autoComplete="off"
                          onChange={(e) => {
                            formik.setFieldValue(
                              "custom_incident_id",
                              e.target.value.trimStart()
                            );
                            if (e.target.value?.trimStart()) {
                              formik.setFieldError("custom_incident_id", "");
                            } else {
                              formik.setFieldError(
                                "custom_incident_id",
                                _t("This field is required.")
                              );
                            }
                          }}
                          onBlur={() => {
                            formik.validateField("custom_incident_id");
                          }}
                          errorMessage={
                            is_custom_incident_id === 0
                              ? ""
                              : isSubmit && !formik.values.custom_incident_id
                              ? _t("This field is required.")
                              : formik.touched.custom_incident_id &&
                                errors.custom_incident_id
                              ? errors.custom_incident_id
                              : ""
                          }
                        />
                      </div>
                      <div className="w-full">
                        <SelectField
                          label={_t("Classification")}
                          labelPlacement="top"
                          isRequired={true}
                          showSearch={true}
                          name="incident_type"
                          options={incidentTypeOptions}
                          disabled={isIncidentTypeLoading}
                          value={
                            previousIncidentVal && previousIncidentVal.current
                              ? {
                                  label: `${previousIncidentVal.current.trim()}`,
                                  value: "",
                                }
                              : incidentTypeOptions.filter((item) => {
                                  return (
                                    String(formik.values.incident_type) ===
                                    String(item?.value)
                                  );
                                })
                          }
                          onChange={(val, options) => {
                            options &&
                              formik.setFieldValue(
                                "incident_type_name",
                                options.label.toString()
                              );
                            formik.setFieldValue("incident_type", val);
                          }}
                          errorMessage={
                            isSubmit && !formik.values.incident_type
                              ? _t("This field is required.")
                              : formik.touched.incident_type &&
                                errors.incident_type
                              ? errors.incident_type
                              : ""
                          }
                          addItem={addItemObject}
                          onInputKeyDown={(e) =>
                            handlekeyDown(e, incidentTypes.classificationTypeId)
                          }
                          optionFilterProp="children"
                          filterOption={(input, option) =>
                            filterOptionBySubstring(
                              input,
                              option?.label as string
                            )
                          }
                        />
                      </div>
                    </div>

                    <div className="w-full">
                      <TextAreaField
                        required={true}
                        name={"description"}
                        label={_t("Description")}
                        labelPlacement="top"
                        value={HTMLEntities.decode(
                          sanitizeString(formik.values.description)
                        )}
                        onChange={(e) => {
                          const trimmedValue =
                            e && e.target && e.target.value
                              ? e.target.value.trim() === ""
                                ? e.target.value.trim()
                                : e.target.value
                              : "";
                          formik.setFieldValue("description", trimmedValue);
                        }}
                        errorMessage={
                          isSubmit && !formik.values.description
                            ? errors.description
                            : formik.touched.description && errors.description
                            ? errors.description
                            : ""
                        }
                      />
                    </div>
                  </>
                ) : incidentType === "employee_writeup" ? (
                  <>
                    <div className="w-full">
                      <ButtonField
                        label={_t("Employee")}
                        labelPlacement="top"
                        required={true}
                        isDisabled={isIncidentTypeLoading}
                        onClick={() => {
                          setIsSelectEmployeeOpen(true);
                        }}
                        value={
                          selectedEmployee &&
                          HTMLEntities.decode(
                            sanitizeString(selectedEmployee[0]?.display_name)
                          )
                        }
                        errorMessage={
                          isSubmit && !formik.values.employee_id
                            ? _t("This field is required.")
                            : formik.touched.employee_id && errors.employee_id
                            ? errors.employee_id
                            : ""
                        }
                        avatarProps={{
                          user: {
                            name: HTMLEntities.decode(
                              sanitizeString(selectedEmployee[0]?.display_name)
                            ),
                            image: selectedEmployee[0]?.image,
                          },
                          loading: employeeLoading,
                        }}
                        addonBefore={
                          selectedEmployee &&
                          selectedEmployee.length &&
                          selectedEmployee[0].user_id ? (
                            <div className="flex gap-1 items-center">
                              <ContactDetailsButton
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setIsOpenEmployeeContactDetails(true);
                                }}
                              />
                              <DirectoryFieldRedirectionIcon
                                className="!w-5 !h-5"
                                directoryId={selectedEmployee[0].user_id.toString()}
                                directoryTypeKey={
                                  selectedEmployee[0].type_key || ""
                                }
                              />
                            </div>
                          ) : (
                            <></>
                          )
                        }
                      />
                    </div>
                    <div className="w-full">
                      <TextAreaField
                        required={true}
                        name={"description"}
                        label={_t("Description")}
                        labelPlacement="top"
                        value={HTMLEntities.decode(
                          sanitizeString(formik.values.description)
                        )}
                        onChange={(e) => {
                          const trimValue =
                            e?.target?.value.trim() === ""
                              ? e?.target?.value.trim()
                              : e?.target?.value;
                          formik.setFieldValue("description", trimValue);
                        }}
                        errorMessage={
                          isSubmit && !formik.values.description
                            ? errors.description
                            : formik.touched.description && errors.description
                            ? errors.description
                            : ""
                        }
                      />
                    </div>
                  </>
                ) : (
                  <></>
                )}
              </SidebarCardBorder>
              <OnlyRequiredCustomFields
                componentList={componentList}
                formik={formik}
                isSubmit={isSubmit}
                loadingCustomField={loadingCustomField}
              />
            </div>
          </div>
          <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
            <PrimaryButton
              htmlType="submit"
              onClick={() => setIsSubmit(true)}
              buttonText={
                incidentType === "incident"
                  ? _t(`Create ${module_singular_name}`)
                  : _t("Create Employee Write-Up")
              }
              disabled={isLoading || loadingCustomField}
              isLoading={isLoading}
            />
          </div>
        </form>
      </Drawer>
      {isSelectProjectOpen && (
        <SelectProject
          isSingleSelect={true}
          open={isSelectProjectOpen}
          setOpen={setIsSelectProjectOpen}
          selectedProjects={selectedProject}
          onProjectSelected={(data) => {
            setSelectedProject(data);
          }}
          isRequired={false}
          genericProjects="project"
          category="project_location"
          module_key={module_key}
        />
      )}
      {isSelectEmployeeOpen && (
        <SelectCustomerDrawer
          closeDrawer={() => {
            setIsSelectEmployeeOpen(false);
          }}
          singleSelecte={true}
          openSelectCustomerSidebar={isSelectEmployeeOpen}
          options={[defaultConfig.employee_key, "my_crew", "my_project"]}
          setCustomer={(data) => setSelectedEmployee(data)}
          selectedCustomer={selectedEmployee}
          groupCheckBox={false}
          additionalContactDetails={0}
          projectId={
            selectedProject[0]?.id ? Number(selectedProject[0]?.id) : undefined
          }
        />
      )}
      {isConfirmDialogOpen && (
        <ConfirmModal
          isOpen={isConfirmDialogOpen}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${HTMLEntities.decode(
              sanitizeString(customDataAdd?.name?.trim() || "")
            )}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={closeConfirmationModal}
          onAccept={() => {
            handleAddCustomData();
          }}
          onDecline={() => {
            previousIncidentVal.current = "";
            closeConfirmationModal();
          }}
        />
      )}
      {isOpenEmployeeContactDetails && (
        <ContactDetailsModal
          isOpenContact={isOpenEmployeeContactDetails}
          contactId={Number(selectedEmployee[0]?.user_id) || undefined}
          onCloseModal={() => {
            setIsOpenEmployeeContactDetails(false);
          }}
          additional_contact_id={0}
        />
      )}
      {/* this is will NF once testing done it will be merge on dev */}
      {/* {drawerConfirmDialogOpen && (
        <ConfirmModal
          isOpen={drawerConfirmDialogOpen}
          modalIcon="fa-regular fa-file-check"
          modaltitle={_t("Confirmation")}
          description={_t(
            `Do you really want to leave this page and lose your unsaved changes?`
          )}
          onCloseModal={drawerCloseConfirmationModal}
          onAccept={() => {
            handleAlertBox();
          }}
          onDecline={drawerCloseConfirmationModal}
        />
      )} */}
    </>
  );
};

export default AddIncidents;
