// Hooks
import { useTranslation } from "~/hook";
// Shared components
import { useNavigate, useRevalidator } from "@remix-run/react";
import dayjs from "dayjs";
import { parseParamsFromURL } from "~/components/page/$url/helper";
import { sanitizeString } from "~/helpers/helper";
import {
  filterOptionBySubstring,
  getStatusForField,
} from "~/shared/utils/helper/common";
import { displayTimeFormat } from "~/shared/utils/helper/defaultDateFormat";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { IconButton } from "~/shared/components/molecules/iconButton";
import { InputField } from "~/shared/components/molecules/inputField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { FieldStatus } from "~/shared/components/molecules/fieldStatus";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { TopBarSkeleton } from "~/shared/components/molecules/topBarSkeleton";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { TimePickerField } from "~/shared/components/molecules/timePickerField";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";
// Other
import {
  getGConfig,
  getGSettings,
  setCommonSidebarCollapse,
  useGModules,
} from "~/zustand";
import { useAppINDispatch, useAppINSelector } from "../../redux/store";
import IncidentTableActions from "../dashboard/IncidentTableAction";
import IncidentTableDropdownItems from "../dashboard/IncidentTableDropdownItems";
import { useEffect, useMemo, useRef, useState } from "react";
import escape from "lodash/escape";
import { setIncidentDetailLoading } from "../../redux/slices/incidentDetailSlice";
import { addItemObject } from "~/modules/people/directory/utils/constasnts";
import { addIncCustomData } from "../../redux/action/addIncidentAction";
import { addIncCustomDataAct } from "../../redux/slices/addIncidentSlice";
import { incidentTypes } from "../../utils/constants";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";

const DetailsTopBar = ({
  sidebarCollapse,
  setIsSelectProjectOpen,
  selectedProject,
  loadingStatus,
  setInputValues,
  inputValues,
  handleChangeFieldStatus,
  handleUpdateField,
  onChangeDate,
}: IIncidentsDetailsTopBarProps) => {
  const { _t } = useTranslation();
  const navigate = useNavigate();
  const { date_format, is_custom_incident_id }: GSettings = getGSettings();
  const { module_access, module_singular_name, page_is_iframe }: GConfig =
    getGConfig();
  const dispatch = useAppINDispatch();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { allow_delete_module_items = "0" } = user || {};
  const [isFocused, setIsFocused] = useState(false);

  const {
    incidentTypeData,
    isIncidentTypeLoading,
  }: {
    incidentTypeData: IIncidentCustomData[];
    isIncidentTypeLoading: boolean;
  } = useAppINSelector((state) => state.addIncident);

  const confirmDialogOpen: boolean = useAppINSelector(
    (state) => state.incidentDropdown.confirmDialogOpen
  );
  const shareLinkModalOpen: boolean = useAppINSelector(
    (state) => state.incidentDropdown.shareLinkModelOpen
  );
  const incidentListPdfViewOpen: boolean = useAppINSelector(
    (state) => state.incidentDropdown.incidentListPdfViewOpen
  );
  const { checkModuleAccessByKey } = useGModules();
  const { module_key, module_name }: GConfig = getGConfig();
  const revalidator = useRevalidator();
  const [getSelectStatus, setGetSelectStatus] = useState<
    IIncidentCustomData | undefined
  >();
  const [customDataAdd, setCustomDataAdd] = useState<ICommonCustomDataFrm>({});
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);
  const closeConfirmationModal = () => {
    previousIncidentVal.current = "";
    setIsConfirmDialogOpen(false);
  };

  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);

  const isNavigate: boolean = true;
  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(module_key) === "read_only",
    [module_key]
  );
  const { incidentDetail, isIncidentDetailLoading }: IIncidentDetailState =
    useAppINSelector((state) => state.incidentDetail);

  const previousIncidentVal = useRef("");

  const handlekeyDown = (
    event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>,
    itemType: number
  ) => {
    if (!event.currentTarget.value?.trim()) {
      return;
    }
    if (event.key === "Enter") {
      const value = event?.currentTarget?.value?.trim();
      const newType = onEnterSelectSearchValue(
        event,
        incidentTypeOptions || []
      );
      if (newType) {
        previousIncidentVal.current = event.currentTarget.value;

        setCustomDataAdd({
          itemType,
          name: HTMLEntities.encode(newType),
        });
        setIsConfirmDialogOpen(true);
      } else if (value) {
        notification.error({
          description: "Records already exist, no new records were added.",
        });
      }
    }
  };

  const handleAddCustomData = async () => {
    if (!isAddingCustomData && customDataAdd?.name) {
      setIsAddingCustomData(true);

      const cDataRes = (await addIncCustomData({
        itemType: customDataAdd?.itemType,
        name: customDataAdd?.name,
      })) as ICustomDataAddUpRes;

      if (cDataRes?.success) {
        dispatch(addIncCustomDataAct(cDataRes?.data));
        let newData = {};
        let newDataForUpdation = {};
        newDataForUpdation = { status: cDataRes?.data?.item_id };
        newData = { incident_type: cDataRes?.data?.item_id };

        setInputValues({
          ...inputValues,
          ...newData,
        });
        handleUpdateField(newDataForUpdation);
        setIsConfirmDialogOpen(false);
        previousIncidentVal.current = "";
      } else {
        notification.error({ description: cDataRes.message });
        previousIncidentVal.current = "";
      }
      setIsAddingCustomData(false);
    }
  };

  // incident_type dropdown Options
  const incidentTypeOptions = useMemo(
    () =>
      incidentTypeData.length
        ? incidentTypeData.map((item) => {
            return {
              label: HTMLEntities.decode(sanitizeString(item.name.trim())),
              value: item.item_id || item.key,
              disabled:
                inputValues &&
                item.item_id === "self_reported_from_timecard" &&
                inputValues.incident_type === "self_reported_from_timecard"
                  ? false
                  : item.item_id === "self_reported_from_timecard"
                  ? true
                  : false,
            };
          })
        : [],
    [incidentTypeData, inputValues?.incident_type]
  );

  useEffect(() => {
    if (
      incidentTypeData &&
      inputValues?.incident_type &&
      !isIncidentTypeLoading
    ) {
      let data = incidentTypeData?.find((item: IIncidentCustomData) => {
        if (inputValues && inputValues.incident_type) {
          return (
            (item.item_id?.toString() || item?.key.toString()) ===
            inputValues.incident_type.toString()
          );
        }
      });
      setGetSelectStatus(
        data && Object.keys(data).length
          ? data
          : inputValues.incident_type && inputValues.incident_type_name
          ? ({
              type_id: inputValues.incident_type,
              name: inputValues.incident_type_name + " (Archived)",
              default_color: "#223558",
            } as IIncidentCustomData)
          : undefined
      );
    } else {
      setGetSelectStatus(undefined);
    }
  }, [incidentTypeData, inputValues?.incident_type]);

  useEffect(() => {
    if (revalidator.state === "loading") {
      dispatch(setIncidentDetailLoading(true));
    } else {
      dispatch(setIncidentDetailLoading(false));
    }
  }, [revalidator.state]);

  const now = dayjs();
  const TimePickerFieldJSX = useMemo(() => {
    return (
      <TimePickerField
        label=""
        name="incident_time"
        labelPlacement="left"
        placeholder={_t("hh:mm")}
        editInline={true}
        iconView={true}
        readOnlyClassName="text-xs px-0 !h-auto "
        readOnly={module_access === "read_only"}
        fixStatus={getStatusForField(loadingStatus, "incident_time")}
        value={
          inputValues && inputValues.incident_time
            ? displayTimeFormat(inputValues?.incident_time)
            : undefined
        }
        allowClear={true}
        onChange={(_, val) => {
          const incidentDate = dayjs(inputValues?.incident_date, date_format);
          const incidentTime = dayjs(val.toString(), "hh:mm A");
          const notifiedDate = dayjs(inputValues?.notify_date, date_format);
          const notifiedTime = dayjs(inputValues?.notify_time, "hh:mm A");

          if (incidentDate.isSame(notifiedDate, "day")) {
            if (notifiedTime.diff(incidentTime, "hour") < 0) {
              return notification.error({
                description:
                  "Notified date/time should be greater than or equal to Incident date/time",
              });
            }

            if (notifiedTime.isSame(incidentTime, "hour")) {
              if (notifiedTime.diff(incidentTime, "minute")) {
                return notification.error({
                  description:
                    "Notified date/time should be greater than or equal to Incident date/time",
                });
              }
            }

            if (
              notifiedTime.isSame(incidentTime, "hour") &&
              notifiedTime.isSame(incidentTime, "minute")
            ) {
              if (notifiedTime.diff(incidentTime, "second")) {
                return notification.error({
                  description:
                    "Notified date/time should be greater than or equal to Incident date/time",
                });
              }
            }
          }
          if (inputValues?.incident_time !== val) {
            onChangeDate(val as string, "incident_time");
          }
        }}
        fieldClassName="header-date-picker w-[72px]"
        className="p-0 !bg-transparent hover:!bg-transparent"
        format="hh:mm A"
        statusIconClassName="!top-1/2 !-translate-y-1/2 !right-0"
        inputStatusClassName="!w-3.5 !h-3.5"
        disabledTime={(current) => {
          const now = dayjs();

          const inputDate = dayjs(inputValues?.incident_date, date_format);

          // If the input date is today, disable future times
          if (inputDate.isSame(now, "day")) {
            return {
              disabledHours: () => {
                return Array.from({ length: 24 }, (_, i) => i).filter(
                  (hour) => hour > now.hour()
                );
              },
              disabledMinutes: () => {
                if (current.hour() === now.hour()) {
                  return Array.from({ length: 60 }, (_, i) => i).filter(
                    (minute) => minute > now.minute()
                  );
                }
                return [];
              },
              disabledSeconds: () => {
                if (
                  current.hour() === now.hour() &&
                  current.minute() === now.minute()
                ) {
                  return Array.from({ length: 60 }, (_, i) => i).filter(
                    (second) => second > now.second()
                  );
                }
                return [];
              },
            };
          } else {
            // If the input date is not today, allow all times
            return {};
          }
        }}
      />
    );
  }, [inputValues, getStatusForField(loadingStatus, "incident_time")]);

  return (
    <>
      <div className="sticky top-0 z-[99] bg-[#F8F8F9] p-[15px] pb-0 mb-[15px]">
        <div className="flex items-center bg-white dark:bg-dark-800 py-[5px] px-3.5 shadow-[0_4px_24px_0] shadow-[#22292f1a] rounded-md">
          <div className="w-full flex md:flex-row flex-col items-center justify-between ">
            {isIncidentDetailLoading ? (
              <TopBarSkeleton num={3} />
            ) : (
              <>
                <div className="flex items-center md:mt-0 mt-2.5 md:order-1 order-2 sm:mr-0 !mr-auto md:w-[calc(100%-150px)] w-full">
                  <div className="w-11 h-11 flex items-center justify-center bg-[#E25A32] rounded-full relative before:absolute before:w-[39px] before:h-[39px] before:top-1/2 before:left-1/2 before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full before:border-2 before:border-white">
                    <FontAwesomeIcon
                      className="w-[18px] h-[18px] text-white"
                      icon="fa-light fa-triangle-exclamation"
                    />
                  </div>
                  <div
                    className={`flex flex-col gap-0.5 w-[calc(100%-44px)] ${
                      isReadOnly ? "pl-2" : "pl-2.5"
                    }`}
                  >
                    <ButtonField
                      label=""
                      labelProps={{
                        labelClass: "!hidden",
                      }}
                      mainReadOnlyClassName="sm:w-fit max-w-full"
                      className="h-6 py-0 w-full gap-0"
                      readOnlyClassName="text-base h-6 font-medium whitespace-nowrap truncate sm:block flex"
                      inputClassName="w-fit"
                      fieldClassName="w-auto"
                      spanWidthClass="w-fit"
                      buttonClassName="!text-base font-medium"
                      placeholder={_t("Select Project")}
                      labelPlacement="left"
                      name="project_id"
                      editInline={true}
                      iconView={true}
                      onClick={() => {
                        setIsSelectProjectOpen(true);
                      }}
                      value={
                        (selectedProject &&
                          HTMLEntities.decode(
                            sanitizeString(selectedProject.project_name)
                          )) ||
                        ""
                      }
                      headerTooltip={`Project: ${HTMLEntities.decode(
                        sanitizeString(selectedProject?.project_name)
                      )}`}
                      statusProps={{
                        status: getStatusForField(loadingStatus, "project_id"),
                        className: "right-6 flex",
                        iconProps: {
                          className: "!w-[15px] !h-[15px]",
                        },
                      }}
                      isDisabled={
                        getStatusForField(loadingStatus, "project_id") ===
                        "loading"
                      }
                      readOnly={module_access === "read_only"}
                      required={true}
                      rightIcon={
                        selectedProject &&
                        selectedProject.id &&
                        selectedProject?.project_name &&
                        !isNaN(Number(selectedProject.id)) ? (
                          <ProjectFieldRedirectionIcon
                            projectId={`${selectedProject?.id}`}
                          />
                        ) : null
                      }
                    />
                    <div className="flex items-center gap-2 flex-wrap">
                      {" "}
                      <Tooltip
                        title={
                          isFocused
                            ? HTMLEntities.decode(
                                sanitizeString(
                                  inputValues?.custom_incident_id
                                    ? inputValues.custom_incident_id
                                    : inputValues?.company_incident_id
                                    ? inputValues?.company_incident_id
                                    : ""
                                )
                              )
                            : inputValues?.txn_type === "incident"
                            ? `Inc. #${HTMLEntities.decode(
                                sanitizeString(
                                  inputValues?.custom_incident_id
                                    ? inputValues.custom_incident_id
                                    : inputValues?.company_incident_id
                                    ? inputValues?.company_incident_id
                                    : ""
                                )
                              )}`
                            : `Employee Write-Up #${HTMLEntities.decode(
                                sanitizeString(
                                  inputValues?.custom_incident_id
                                    ? inputValues.custom_incident_id
                                    : inputValues?.company_incident_id
                                    ? inputValues?.company_incident_id
                                    : ""
                                )
                              )}`
                        }
                        placement="topLeft"
                      >
                        {" "}
                        <InputField
                          placeholder={
                            `${
                              inputValues?.txn_type === "incident"
                                ? _t("Inc.")
                                : "Employee Write-Up"
                            }` + " #"
                          }
                          labelPlacement="left"
                          editInline={true}
                          iconView={true}
                          name={`${
                            incidentDetail?.custom_incident_id
                              ? "custom_incident_id"
                              : incidentDetail?.company_incident_id
                              ? "company_incident_id"
                              : "custom_incident_id"
                          }`}
                          readOnly={module_access === "read_only"}
                          className="h-[22px] text-sm font-medium py-0 w-full 2xl:w-[400px] xl:w-[300px]"
                          formInputClassName="ellipsis-input-field max-w-[400px]"
                          inputStatusClassName="!w-[15px] !h-[15px]"
                          readOnlyClassName="text-sm h-[22px] font-medium whitespace-nowrap truncate block"
                          iconClassName="!w-3 !h-3"
                          disabled={is_custom_incident_id === 0}
                          value={
                            isFocused
                              ? HTMLEntities.decode(
                                  sanitizeString(
                                    incidentDetail?.custom_incident_id
                                      ? inputValues?.custom_incident_id
                                      : incidentDetail?.company_incident_id
                                      ? inputValues?.company_incident_id
                                      : ""
                                  )
                                )
                              : inputValues?.txn_type === "incident"
                              ? `Inc. #${HTMLEntities.decode(
                                  sanitizeString(
                                    inputValues?.custom_incident_id
                                      ? inputValues.custom_incident_id
                                      : inputValues?.company_incident_id
                                      ? inputValues?.company_incident_id
                                      : ""
                                  )
                                )}`
                              : `Employee Write-Up #${HTMLEntities.decode(
                                  sanitizeString(
                                    inputValues?.custom_incident_id
                                      ? inputValues.custom_incident_id
                                      : inputValues?.company_incident_id
                                      ? inputValues?.company_incident_id
                                      : ""
                                  )
                                )}`
                          }
                          fixStatus={getStatusForField(
                            loadingStatus,
                            "custom_incident_id"
                          )}
                          onChange={({
                            target: { value, name },
                          }: React.ChangeEvent<
                            HTMLInputElement | HTMLTextAreaElement
                          >) => {
                            setInputValues({
                              ...inputValues,
                              [name]: value,
                            });
                          }}
                          onMouseEnter={() => {
                            handleChangeFieldStatus({
                              field: "custom_incident_id",
                              status: "edit",
                              action: "ME",
                            });
                          }}
                          onMouseLeaveDiv={() => {
                            handleChangeFieldStatus({
                              field: "custom_incident_id",
                              status: "button",
                              action: "ML",
                            });
                          }}
                          onFocus={() => {
                            setIsFocused(true);
                            handleChangeFieldStatus({
                              field: "custom_incident_id",
                              status: "save",
                              action: "FOCUS",
                            });
                          }}
                          onBlur={(e) => {
                            setIsFocused(false);
                            const value = e.target.value.trim();
                            if (value) {
                              if (
                                String(value) !==
                                  HTMLEntities.decode(
                                    sanitizeString(
                                      String(incidentDetail?.custom_incident_id)
                                    )
                                  ) &&
                                String(value) !==
                                  HTMLEntities.decode(
                                    sanitizeString(
                                      String(
                                        incidentDetail?.company_incident_id
                                      )
                                    )
                                  )
                              ) {
                                handleUpdateField({
                                  custom_incident_id: escape(value),
                                });
                              } else {
                                handleChangeFieldStatus({
                                  field: "custom_incident_id",
                                  status: "button",
                                  action: "BLUR",
                                });
                                setInputValues({
                                  ...inputValues,
                                  [e.target.name]:
                                    incidentDetail &&
                                    incidentDetail.custom_incident_id
                                      ? incidentDetail.custom_incident_id
                                      : incidentDetail &&
                                        incidentDetail.company_incident_id
                                      ? incidentDetail.company_incident_id
                                      : "",
                                });
                              }
                            } else {
                              notification.error({
                                description: `${
                                  inputValues?.txn_type === "incident"
                                    ? "Inc. #"
                                    : "Employee Write-Up #"
                                } field is required.`,
                              });
                            }
                          }}
                        />
                      </Tooltip>
                    </div>
                    <div
                      className={`flex lg:items-center items-start gap-2 flex-col lg:flex-row ${
                        isReadOnly ? "pl-1.5" : ""
                      }`}
                    >
                      <div className="flex items-center gap-2">
                        <Tooltip
                          title={
                            incidentTypeOptions?.find(
                              (item) =>
                                String(item.value) ===
                                String(
                                  inputValues ? inputValues.incident_type : ""
                                )
                            )?.label
                          }
                        >
                          <SelectField
                            name="status"
                            placeholder={_t("Select Classification")}
                            labelPlacement="left"
                            value={
                              previousIncidentVal && previousIncidentVal.current
                                ? {
                                    label: `${previousIncidentVal.current.trim()}`,
                                    value: "",
                                  }
                                : !isIncidentTypeLoading && inputValues
                                ? inputValues.incident_type
                                  ? incidentTypeOptions.filter((item) => {
                                      return (
                                        String(inputValues.incident_type) ===
                                        String(item?.value)
                                      );
                                    }).length
                                    ? incidentTypeOptions.filter((item) => {
                                        return (
                                          String(inputValues.incident_type) ===
                                          String(item?.value)
                                        );
                                      })
                                    : incidentDetail?.txn_type === "incident" &&
                                      inputValues.incident_type_name &&
                                      inputValues?.incident_type
                                    ? {
                                        label: `${
                                          inputValues.incident_type_name
                                            ? HTMLEntities.decode(
                                                sanitizeString(
                                                  inputValues.incident_type_name.trim()
                                                )
                                              ) + " (Archived)"
                                            : ""
                                        }`,
                                        value: inputValues?.incident_type,
                                      }
                                    : incidentDetail?.txn_type ===
                                        "employee_writeup" &&
                                      inputValues.discipline_type_name &&
                                      inputValues?.incident_type
                                    ? {
                                        label: `${
                                          inputValues.discipline_type_name
                                            ? HTMLEntities.decode(
                                                sanitizeString(
                                                  inputValues.discipline_type_name.trim()
                                                )
                                              ) + " (Archived)"
                                            : ""
                                        }`,
                                        value: inputValues?.incident_type,
                                      }
                                    : []
                                  : []
                                : []
                            }
                            formInputClassName={`w-fit overflow-visible ${
                              module_access === "read_only"
                                ? "bg-primary-900/10 rounded"
                                : ""
                            }`}
                            containerClassName="overflow-visible"
                            fieldClassName="before:hidden w-fit"
                            className={`h-[21px] directory-stage header-select-status-dropdown !rounded !text-primary-900 bg-[#ECF1F9] max-w-[250px] ${
                              !inputValues?.incident_type
                                ? "header-select-placeholder"
                                : ""
                            }`}
                            popupClassName="min-w-[260px]"
                            readOnly={module_access === "read_only"}
                            showSearch={true}
                            title=""
                            options={incidentTypeOptions}
                            allowClear={
                              incidentDetail?.txn_type === "incident"
                                ? false
                                : true
                            }
                            onClear={() => {
                              handleUpdateField({
                                status: "",
                                incident_type_name: "",
                              });
                            }}
                            addItem={addItemObject}
                            disabled={
                              isIncidentTypeLoading ||
                              getStatusForField(loadingStatus, "status") ===
                                "loading"
                            }
                            filterOption={(input, option) =>
                              filterOptionBySubstring(
                                input,
                                option?.label as string
                              )
                            }
                            onChange={(value: string | string[]) => {
                              // if (typeof value === "number") {
                              setInputValues({
                                ...inputValues,
                                incident_type: value,
                              });
                              // }
                            }}
                            optionFilterProp="children"
                            onInputKeyDown={(e) =>
                              handlekeyDown(
                                e,
                                incidentDetail?.txn_type === "incident"
                                  ? incidentTypes.classificationTypeId
                                  : incidentTypes.disciplineTypeId
                              )
                            }
                            onSelect={(e, options) => {
                              const value = e.toString();

                              if (
                                incidentDetail &&
                                String(value) !==
                                  String(incidentDetail.incident_type)
                              ) {
                                handleUpdateField({
                                  status: value ? value : "",
                                  ...(options &&
                                    options.label && {
                                      incident_type_name: options.label,
                                    }),
                                });
                              } else {
                                handleChangeFieldStatus({
                                  field: "status",
                                  status: "button",
                                  action: "BLUR",
                                });
                              }
                            }}
                          />
                        </Tooltip>
                        {["loading", "success", "error"].includes(
                          getStatusForField(loadingStatus, "status")
                        ) && (
                          <FieldStatus
                            className="flex items-center"
                            iconProps={{
                              className: "!w-[15px] !h-[15px]",
                            }}
                            status={getStatusForField(loadingStatus, "status")}
                          />
                        )}
                      </div>
                      <div className="flex items-center gap-2 pt-0.5">
                        <div className="flex whitespace-nowrap items-center relative">
                          <div className="bg-[#22355812] dark:bg-[#2f3842] rounded py-1 pl-1.5 pr-6 flex items-center ">
                            <FontAwesomeIcon
                              className="text-base w-3.5 h-3.5 text-primary-900 dark:text-white/80"
                              icon="fa-regular fa-calendar"
                            />
                          </div>
                          <div className="py-px px-1.5 text-primary-900 dark:text-white/90 bg-white dark:bg-dark-800 relative -ml-[18px] font-medium rounded-full shadow-[0_0_10px_-1px] dark:shadow-[#393f47] shadow-[#ddd] text-xs leading-4 truncate md:max-w-fit max-w-[200px]">
                            <DatePickerField
                              label=""
                              name="incident_date"
                              labelPlacement="left"
                              placeholder={_t("Select Date")}
                              editInline={true}
                              iconView={true}
                              fieldClassName="header-date-picker w-[92px]"
                              readOnlyClassName="text-xs px-0 !h-auto !bg-transparent"
                              className="p-0 !bg-transparent"
                              format={date_format}
                              isRequired={true}
                              allowClear={false}
                              readOnly={module_access === "read_only"}
                              value={
                                inputValues && inputValues.incident_date
                                  ? dayjs(
                                      inputValues.incident_date,
                                      date_format
                                    )
                                  : undefined
                              }
                              disabled={
                                getStatusForField(
                                  loadingStatus,
                                  "incident_date"
                                ) === "loading"
                              }
                              fixStatus={getStatusForField(
                                loadingStatus,
                                "incident_date"
                              )}
                              onChange={(_, dateString) => {
                                const inputDate = dayjs(
                                  dateString as string,
                                  date_format
                                );
                                const notifiedDate = dayjs(
                                  inputValues?.notify_date,
                                  date_format
                                );
                                if (notifiedDate.isBefore(inputDate)) {
                                  return notification.error({
                                    description:
                                      "Notified date/time should be greater than or equal to Incident date/time",
                                  });
                                }
                                let reset_time = false;

                                if (onChangeDate) {
                                  if (inputDate.isSame(now, "day")) {
                                    if (
                                      displayTimeFormat(
                                        inputValues?.incident_time
                                      )?.isAfter(now, "hour")
                                    ) {
                                      reset_time = true;
                                    }

                                    if (
                                      displayTimeFormat(
                                        inputValues?.incident_time
                                      )?.isSame(now, "hour") &&
                                      displayTimeFormat(
                                        inputValues?.incident_time
                                      )?.isAfter(now, "minute")
                                    ) {
                                      reset_time = true;
                                    }
                                  }

                                  onChangeDate(
                                    dateString as string,
                                    "incident_date",
                                    reset_time
                                  );
                                }
                              }}
                              statusIconClassName="!top-1/2 !-translate-y-1/2 !right-0"
                              inputStatusClassName="!w-3.5 !h-3.5"
                              disabledDate={(currentDate) =>
                                currentDate &&
                                currentDate > dayjs().endOf("day")
                              }
                            />
                          </div>
                        </div>
                        <div className="flex whitespace-nowrap items-center relative">
                          <div className="bg-[#22355812] dark:bg-[#2f3842] rounded py-1 pl-1.5 pr-6 flex items-center ">
                            <FontAwesomeIcon
                              className="text-base w-3.5 h-3.5 text-primary-900 dark:text-white/80"
                              icon="fa-regular fa-clock"
                            />
                          </div>
                          <div className="py-px px-1.5 text-primary-900 dark:text-white/90 bg-white dark:bg-dark-800 relative -ml-[18px] font-medium rounded-full shadow-[0_0_10px_-1px] dark:shadow-[#393f47] shadow-[#ddd] text-xs leading-4 truncate md:max-w-fit max-w-[200px]">
                            {TimePickerFieldJSX}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex md:justify-end gap-2.5 md:order-2 order-1 md:w-fit w-full justify-between">
                  <div className="flex gap-2.5">
                    {!window.ENV.PAGE_IS_IFRAME && (
                      <div
                        className="flex items-center cursor-pointer md:!hidden"
                        onClick={() => {
                          const params: Partial<IframeRouteParams> =
                            parseParamsFromURL(window?.location?.pathname);
                          if (params?.page && params?.id) {
                            navigate("/" + params?.page);
                          }
                        }}
                      >
                        <IconButton
                          htmlType="button"
                          variant="default"
                          className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                          iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                          icon="fa-regular fa-chevron-left"
                        />
                      </div>
                    )}
                    <div>
                      <IconButton
                        htmlType="button"
                        variant="default"
                        className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                        iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                        icon="fa-regular fa-bars"
                        onClick={() =>
                          setCommonSidebarCollapse(!sidebarCollapse)
                        }
                      />
                    </div>
                  </div>

                  <ul className="flex items-center justify-end gap-2.5">
                    <li>
                      <ButtonWithTooltip
                        tooltipTitle={_t("Refresh")}
                        tooltipPlacement="top"
                        icon="fa-regular fa-arrow-rotate-right"
                        iconClassName={`!text-primary-900 ${
                          isIncidentDetailLoading
                            ? "group-hover/buttonHover:!text-primary-900 fa-spin"
                            : "group-hover/buttonHover:!text-deep-orange-500"
                        }`}
                        className={`!w-[34px] !h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 ${
                          isIncidentDetailLoading
                            ? "hover:bg-transparent"
                            : "hover:!bg-deep-orange-500/5"
                        }`}
                        disabled={isIncidentDetailLoading}
                        onClick={() => {
                          revalidator.revalidate();
                        }}
                      />
                    </li>
                    {module_access !== "read_only" && (
                      <li>
                        <IncidentTableActions
                          data={inputValues}
                          dispatch={dispatch}
                          iconClassName="text-primary-900 group-hover/buttonHover:text-deep-orange-500"
                          tooltipcontent={_t("More")}
                          isDeleteAccess={
                            allow_delete_module_items === "0" || page_is_iframe
                          }
                          buttonClass="!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !rounded !shadow-primary-200 hover:!bg-deep-orange-500/5"
                        />
                        <IncidentTableDropdownItems
                          confirmDialogOpen={confirmDialogOpen}
                          data={inputValues}
                          dispatch={dispatch}
                          shareLinkModalOpen={shareLinkModalOpen}
                          incidentListPdfViewOpen={incidentListPdfViewOpen}
                          isNavigate={isNavigate}
                        />
                      </li>
                    )}
                  </ul>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
      {isConfirmDialogOpen && (
        <ConfirmModal
          isOpen={isConfirmDialogOpen}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${HTMLEntities.decode(
              sanitizeString(customDataAdd?.name?.trim() || "")
            )}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={closeConfirmationModal}
          onAccept={() => {
            handleAddCustomData();
          }}
          onDecline={() => {
            previousIncidentVal.current = "";
            closeConfirmationModal();
          }}
        />
      )}
    </>
  );
};

export default DetailsTopBar;
