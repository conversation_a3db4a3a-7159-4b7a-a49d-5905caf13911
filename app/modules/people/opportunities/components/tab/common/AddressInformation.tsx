import isEmpty from "lodash/isEmpty";
import { useEffect, useMemo, useRef, useState } from "react";
// Atoms
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { GoogleMap } from "~/shared/components/molecules/googleMap";
import { FieldLabel } from "~/shared/components/molecules/fieldLabel";
import { FieldStatus } from "~/shared/components/molecules/fieldStatus";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
// Other
import { getGConfig, useGModules } from "~/zustand";
import {
  dirAddrLatLong,
  fieldStatus,
} from "~/modules/people/directory/utils/constasnts";

import { useParams } from "@remix-run/react";
import delay from "lodash/delay";
import { useTranslation } from "~/hook";
import { getStatusForField } from "~/shared/utils/helper/common";
import { getAddressComponent } from "~/shared/utils/helper/locationAddress";
import { updateOppDetailApi } from "../../../redux/action/opportunityDetailsActions";
import { updateOppAddr } from "../../../redux/slices/opportunityDetailsSlice";
import { useAppOPPDispatch, useAppOPPSelector } from "../../../redux/store";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { Button } from "~/shared/components/atoms/button";
import { Popover } from "~/shared/components/atoms/popover";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";

const AddressInformation = () => {
  const { _t } = useTranslation();
  const { id: opportunity_id } = useParams();
  const dispatch = useAppOPPDispatch();
  const { module_key } = getGConfig();
  const currentModule = getCurrentMenuModule();

  const { checkModuleAccessByKey } = useGModules();
  //   const dispatch = useAppDispatch();
  const [viewUnit, setViewUnit] = useState<boolean>(false);
  const addressInfoRef = useRef<HTMLDivElement | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [contactAddress, setContactAddress] = useState<boolean>(false);
  const toggleAddressInfo = () => {
    if (!loading) setViewUnit((current) => !current);
  };

  const { addressInfo, oppDetail } = useAppOPPSelector(
    (state) => state.opportunityDetails
  );

  useEffect(() => {
    if (addressInfoRef?.current) {
      const mouseEventHandler = (e: MouseEvent) => {
        if (!addressInfoRef?.current?.contains(e.target as Node)) {
          setViewUnit(false);
        }
      };
      window.addEventListener("mousedown", mouseEventHandler);
      return () => {
        window.removeEventListener("mousedown", mouseEventHandler);
      };
    }
  }, [addressInfoRef]);

  const {
    address1,
    address2,
    city,
    state,
    zip,
    latitude,
    longitude,
    user_id,
    temperature_scale,
  } = addressInfo;
  const initialValues: IOppDetailFields = useMemo(
    () => ({
      address1: address1 || "",
      address2: address2 || "",
      city: city || "",
      state: state || "",
      latitude: Number(latitude) || 0,
      longitude: Number(longitude) || 0,
      zip: zip || "",
      user_id: user_id || "",
    }),
    [addressInfo]
  );

  const [inputValues, setInputValues] =
    useState<IOppDetailFields>(initialValues);

  useEffect(() => {
    setInputValues((prev: IDirAddrInfo) => ({
      ...prev,
      address1: address1 || "",
      address2: address2 || "",
      city: city || "",
      state: state || "",
      latitude: Number(latitude) || 0,
      longitude: Number(longitude) || 0,
      zip: zip || "",
      user_id: user_id || "",
    }));
  }, [addressInfo, viewUnit]);

  const hasOppAddress1 = Boolean(inputValues?.address1);
  const hasOppAddress2 = Boolean(inputValues?.address2);
  const hasOppCity = Boolean(inputValues?.city);
  const hasOppState = Boolean(inputValues?.state);
  const hasOppZip = Boolean(inputValues?.zip);

  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(fieldStatus);
  const [locationLatLong, setLocationLatLong] =
    useState<IDirAddrLatLong>(dirAddrLatLong);

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );
    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) =>
        prevState.map((item) =>
          item.field === field ? { ...item, status: status } : item
        )
      );
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setInputValues({ ...inputValues, [name]: value });
  };

  const isCustAddressChanged = useMemo(() => {
    if (!oppDetail || !addressInfo) return false;

    const { cust_address1, cust_address2 } = oppDetail;

    const isAddress1Empty = !address1;
    const isAddress2Empty = !address2;

    const isAddressChanged =
      cust_address1 &&
      cust_address2 &&
      (cust_address1 !== address1 || cust_address2 !== address2);

    return isAddress1Empty && isAddress2Empty && isAddressChanged;
  }, [oppDetail, addressInfo]);
  const handleCopyCustAddress = async () => {
    const {
      cust_address1 = "",
      cust_address2 = "",
      cust_city = "",
      cust_state = "",
      cust_zip = "",
    } = oppDetail || {};

    // Create address update data
    const updateAddData = {
      address1: cust_address1,
      address2: cust_address2,
      city: cust_city,
      state: cust_state,
      zip: cust_zip,
    };

    // Update the address information
    await updateAddressInfo(updateAddData);
    setContactAddress(false);
  };
  const handleSelectedLocation = async (place: IdirAddrPlaceDetails) => {
    setLocationLatLong({
      ...locationLatLong,
      latitude: place?.geometry?.location?.lat(),
      longitude: place?.geometry?.location?.lng(),
    });
    const streetNumber = getAddressComponent(place, "street_number")
      ? `${getAddressComponent(place, "street_number")} `
      : "";
    const updateValues: IOppDetailFields = {
      address1: `${streetNumber}${getAddressComponent(place, "route")}`,
      address2: "",
      city: getAddressComponent(place, "locality"),
      state: getAddressComponent(place, "administrative_area_level_1"),
      zip: getAddressComponent(place, "postal_code"),
      latitude: place?.geometry?.location?.lat(),
      longitude: place?.geometry?.location?.lng(),
    };
    setInputValues(updateValues);
    await updateAddressInfo(updateValues);
  };
  const contactAddressChange = (newOpen: boolean) => {
    setContactAddress(newOpen);
  };
  const handleInputBlur = async (data?: boolean) => {
    if (viewUnit && data != true) {
      return false;
    }
    const updateValues: IOppDetailFields = {
      ...inputValues,
    };
    delete updateValues.user_id;
    await updateAddressInfo(updateValues);
  };

  async function updateAddressInfo(updateInfo: IOppDetailFields) {
    if (
      updateInfo &&
      String(updateInfo.address1)?.trim() === String(address1)?.trim() &&
      String(updateInfo.address2)?.trim() === String(address2)?.trim() &&
      String(updateInfo.state)?.trim() === String(state)?.trim() &&
      String(updateInfo.city)?.trim() === String(city)?.trim() &&
      String(updateInfo.zip)?.trim() === String(zip)?.trim()
    ) {
      return;
    }
    setLoading(true);
    setViewUnit(false);
    handleChangeFieldStatus({
      field: "address_info",
      status: "loading",
      action: "API",
    });
    let id = opportunity_id ? opportunity_id : "";

    const updateRes = (await updateOppDetailApi(
      {
        // directory_id: addressInfo?.user_id?.toString() || "",
        // module_id: gConfig?.module_id,
        ...updateInfo,
      },
      id
    )) as IOppUpdateApiRes;
    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: "address_info",
        status: "success",
        action: "API",
      });
      dispatch(updateOppAddr(updateInfo));
    } else {
      handleChangeFieldStatus({
        field: "address_info",
        status: "error",
        action: "API",
      });
      setInputValues((prev: IOppDetailFields) => ({
        ...prev,
        address1: addressInfo?.address1 ?? "",
        address2: addressInfo?.address2 ?? "",
        city: addressInfo?.city ?? "",
        state: addressInfo?.state ?? "",
        zip: addressInfo?.zip ?? "",
        latitude: addressInfo?.latitude || 0,
        longitude: addressInfo?.longitude || 0,
      }));
      notification.error({
        description: updateRes?.message,
      });
    }
    setLoading(false);
    delay(() => {
      handleChangeFieldStatus({
        field: "address_info",
        status: "button",
        action: "API",
      });
    }, 3000);
  }

  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(module_key) === "read_only",
    [module_key]
  );

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Address Information")}
        iconProps={{
          icon: "fa-solid fa-address-card",
          containerClassName:
            "bg-[linear-gradient(180deg,#B08CAB1a_0%,#734B6D1a_100%)]",
          id: "address_information_icon",
          colors: ["#B08CAB", "#734B6D"],
        }}
        headerRightButton={
          isCustAddressChanged ? (
            <Popover
              content={
                <div className="min-w-[272px]">
                  <Typography className="block text-sm py-2 px-3.5 bg-gray-50 relative before:w-full before:h-px before:bg-[linear-gradient(177deg,#a5a5a53d_24%,#fafafa_100%)] before:bottom-0 before:left-1/2 before:-translate-x-1/2 before:absolute">
                    {_t(
                      `Copy Contact Address to ${
                        currentModule?.singular_name || "Opportunity"
                      } Address?`
                    )}
                  </Typography>
                  <div className="py-2 px-3.5">
                    <Typography className="block text-13">
                      <Typography className="font-medium text-13">
                        {_t("Street")}
                        {": "}
                      </Typography>
                      {oppDetail?.cust_address1}
                    </Typography>
                    <Typography className="block text-13">
                      <Typography className="font-medium text-13">
                        {_t("Street")}
                        {" 2: "}
                      </Typography>
                      {oppDetail?.cust_address2}
                    </Typography>
                    <Typography className="block text-13">
                      <Typography className="font-medium text-13">
                        {_t("City")}/{_t("ST")}/{_t("Zip")}
                        {": "}
                      </Typography>
                      {oppDetail?.cust_city}
                      {oppDetail?.cust_city && oppDetail?.cust_state
                        ? `, ${oppDetail?.cust_state}`
                        : oppDetail?.cust_state}
                      {oppDetail?.cust_zip ? ` ${oppDetail?.cust_zip}` : ""}
                    </Typography>
                    <div className="flex gap-2 justify-center mt-3">
                      <Button
                        type="primary"
                        className="w-fit primary-btn"
                        onClick={() => handleCopyCustAddress()}
                      >
                        {_t("Yes")}
                      </Button>
                      <Button
                        className="hover:!border-primary-900 hover:!text-primary-900 active:!border-primary-900 active:!text-primary-900"
                        onClick={() => setContactAddress(false)}
                      >
                        {_t("No")}
                      </Button>
                    </div>
                  </div>
                </div>
              }
              placement="left"
              trigger="click"
              open={contactAddress}
              onOpenChange={contactAddressChange}
            >
              <Button
                type="primary"
                className="px-2 h-[30px] justify-center font-medium !bg-[#EBF1F9] !text-primary-900 !border-0 dark:text-white/90 dark:!bg-dark-400"
              >
                {_t("Copy from Contact Address?")}
              </Button>
            </Popover>
          ) : (
            <></>
          )
        }
        children={
          <>
            <ul className="pt-2">
              <li>
                <InlineField
                  label={_t("Address")}
                  labelPlacement="left"
                  labelClass="sm:w-[100px] sm:max-w-[100px]"
                  field={
                    <ul
                      className={`grid items-start w-full ${
                        viewUnit ? "" : "xl:grid-cols-2 gap-2"
                      }`}
                    >
                      <li
                        className={`w-full gap-1 flex justify-between p-1.5 pt-2 ${
                          isReadOnly
                            ? "cursor-default sm:bg-transparent bg-[#f4f5f6]"
                            : "hover:bg-[#f4f5f6] min-[768px]:bg-transparent bg-[#f4f5f6] cursor-pointer"
                        } ${viewUnit ? "hidden" : ""}`}
                        onClick={() => {
                          if (isReadOnly) {
                            return false;
                          }
                          toggleAddressInfo();
                        }}
                        onMouseEnter={() => {
                          handleChangeFieldStatus({
                            field: "address_info",
                            status: "edit",
                            action: "ME",
                          });
                        }}
                        onMouseLeave={() => {
                          handleChangeFieldStatus({
                            field: "address_info",
                            status: "button",
                            action: "ML",
                          });
                        }}
                      >
                        <ul className="w-full">
                          {isEmpty(address1) &&
                          isEmpty(address2) &&
                          isEmpty(city) &&
                          isEmpty(state) &&
                          isEmpty(zip) ? (
                            "-"
                          ) : (
                            <>
                              <li className="text-primary-900 text-sm">
                                {inputValues?.address1}
                                {(hasOppAddress1 && hasOppAddress2) ||
                                (hasOppAddress1 &&
                                  (hasOppCity || hasOppState || hasOppZip))
                                  ? " "
                                  : ""}
                              </li>
                              <li className="text-primary-900 text-sm">
                                {inputValues?.address2}
                                {hasOppAddress2 &&
                                (hasOppCity || hasOppState || hasOppZip)
                                  ? " "
                                  : ""}
                              </li>
                              <li>
                                <Typography className="text-primary-900 text-sm">
                                  {inputValues?.city}
                                </Typography>
                                <Typography className="text-primary-900 text-sm">
                                  {hasOppCity && hasOppState
                                    ? `, ${inputValues?.state}`
                                    : inputValues?.state}
                                  {""}
                                </Typography>
                                <Typography className="text-primary-900 text-sm">
                                  {hasOppZip && (hasOppState || hasOppCity)
                                    ? `, ${inputValues?.zip}`
                                    : inputValues?.zip}
                                </Typography>
                              </li>
                            </>
                          )}
                        </ul>
                        {!isReadOnly && (
                          <FieldStatus
                            status={getStatusForField(
                              loadingStatus,
                              "address_info"
                            )}
                          />
                        )}
                      </li>
                      <li className="w-full grid 2xl:grid-cols-2 gap-2">
                        <GoogleMap
                          key={
                            [
                              addressInfo.address1,
                              addressInfo.city,
                              addressInfo.state,
                              addressInfo.zip,
                            ].join("-") || "map-empty"
                          }
                          ref={addressInfoRef}
                          cssStyle={{ height: "150px" }}
                          addressInfo={inputValues}
                          mapAddress={{
                            address1: addressInfo.address1 || "",
                            address2: addressInfo.address2 || "",
                            city: addressInfo.city || "",
                            state: addressInfo.state || "",
                            zip: addressInfo.zip || "",
                          }}
                          temperature_scale={temperature_scale}
                          handleInputChange={handleInputChange}
                          handleSelectedLocation={handleSelectedLocation}
                          isEditable={viewUnit}
                          handleInputBlur={handleInputBlur}
                          title={[address1, address2, city, state, zip]
                            .filter((value) => !!value)
                            .join(", ")}
                        />
                      </li>
                    </ul>
                  }
                />
              </li>
            </ul>
          </>
        }
      />
    </>
  );
};

export default AddressInformation;
