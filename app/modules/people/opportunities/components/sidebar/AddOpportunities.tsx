import { Form, useNavigate } from "@remix-run/react";
import { useEffect, useMemo, useState } from "react";

// Atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Popover } from "~/shared/components/atoms/popover";
import { Typography } from "~/shared/components/atoms/typography";
import { Button } from "~/shared/components/atoms/button";

// Molecules
import { FieldLabel } from "~/shared/components/molecules/fieldLabel";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { InputField } from "~/shared/components/molecules/inputField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";

// Organisms
import OnlyRequiredCustomFields from "~/shared/components/organisms/OnlyRequiredCustomFields/OnlyRequiredCustomFields";

// Shared
import { useSideBarCustomField } from "~/shared/hooks/useCustomField/useSidebarCustomField";
import { useDirectoryKeyValue } from "~/shared/hooks/useCustomField/useDirectoryKeyValue";

// Zustand
import {
  getOpportunityCustomDataOpportunityStageTypes,
  getOpportunityCustomDataProjectTypes,
} from "../../zustand/customData/slice";

// Formik
import { useFormik } from "formik";
import * as Yup from "yup";

// Other
import { useTranslation } from "~/hook";
import {
  formatAmount,
  getApiDefaultParams,
  sanitizeString,
} from "~/helpers/helper";
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";
import { setModuleAutoIncrementId } from "~/zustand/module-auto-increment-primary-id/actions";
import { getModuleAutoIncrementPrimaryId } from "~/zustand/module-auto-increment-primary-id/store";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";
import { getCustomFieldAccess } from "~/shared/utils/helper/getCustomFieldAccess";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import { formatCustomFieldForRequest } from "~/shared/utils/helper/customFieldSidebarFormat";
import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";
import { apiRoutes } from "~/route-services/routes";
import { filterOptionBySubstring } from "~/shared/utils/helper/common";
import { addItemObject } from "~/modules/people/directory/utils/constasnts";
import { defaultPreviousOppVal, oppDetailType } from "../../utils/constants";
import { useAppOPPDispatch, useAppOPPSelector } from "../../redux/store";
import {
  addOppCustomData,
  fetchProjectTypeData,
} from "../../redux/action/opportunityDetailsActions";
import { addOppCustomDataAct } from "../../redux/slices/opportunityDetailsSlice";
import delay from "lodash/delay";
import { useExistingCustomers } from "~/components/sidebars/multi-select/customer/zustand";
import {
  EVENT_LOGGER_ACTION,
  EVENT_LOGGER_NAME,
} from "~/shared/constants/event-logger";
import { setOppCallListApi } from "../../redux/slices/opportunityAddSlice";
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
import { backendDateFormat } from "~/shared/utils/helper/defaultDateFormat";
import { getGSettings } from "~/zustand";
import escape from "lodash/escape";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";

const AddOpportunities = ({
  addOpportunities,
  setAddOpportunities,
  module,
  openedUsingBtn,
  leadId,
  customerId,
  setSearchParams,
}: IAddOpportunitiesProps) => {
  const navigate = useNavigate();

  const { _t } = useTranslation();

  const [directoryOpen, setDirectoryOpen] = useState<boolean>(false);
  const [contactOpen, setContactOpen] = useState<boolean>(false);
  const [isSubmit, setIsSubmit] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [contactAddress, setContactAddress] = useState<boolean>(false);

  const projectTypes = getOpportunityCustomDataProjectTypes();
  const stageTypes = getOpportunityCustomDataOpportunityStageTypes();
  const { formatter } = useCurrencyFormatter();

  const { directoryKeyValue, directory }: IDirectoryFormCustomField =
    useDirectoryKeyValue();

  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);

  const { projectTypeData, isProjectTypeDataLoading, stageData } =
    useAppOPPSelector((state) => {
      return state.opportunityDetails;
    });
  // const { opportunityTypeData, isOpportunityTypeLoading } = useAppOPPSelector(
  //   (state) => state.addOpportunity
  // );

  const [previousOppVal, setPreviousOppVal] = useState(defaultPreviousOppVal);
  const closeConfirmationModal = () => {
    setIsConfirmDialogOpen(false);
  };
  const [customDataAdd, setCustomDataAdd] = useState<ICommonCustomDataFrm>({});
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const [isDefaultStage, setIsDefaultStage] = useState<boolean>(false);
  const [projectType, setProjectType] = useState<string>("");
  const { isNoAccessCustomField }: ICustomFieldAccess = getCustomFieldAccess();
  const dispatch = useAppOPPDispatch();
  let { getExistingUsersWithApi } = useExistingCustomers();

  const appSettings = getGlobalAppSettings();
  const { is_custom_opportunity_id, date_format = "YYYY-MM-DD" } =
    appSettings || {};
  const { date_format: dateFormat } = getGSettings();

  const {
    need_to_increment,
    last_primary_id,
    need_to_modify_response,
  }: Partial<IResponseGetModuleAutoNumber> =
    getModuleAutoIncrementPrimaryId() || {};

  const isCustomOpportunityId = is_custom_opportunity_id === 0 ? false : true;

  const { componentList, loadingCustomField } = useSideBarCustomField(
    { directory, directoryKeyValue } as IDirectoryFormCustomField,
    {
      moduleId: module?.module_id,
      userType: "22",
    } as IRequestCustomFieldForSidebar
  );

  useEffect(() => {
    // dispatch(
    //   fetchOpportunityTypeData({
    //     types: [188],
    //     moduleId: 88,
    //   })
    // );
    dispatch(
      fetchProjectTypeData({
        types: [
          oppDetailType.stageId,
          oppDetailType.projectTypeId,
          oppDetailType.referralSourceId,
        ],
        moduleId: 88,
      })
    );
  }, []);

  const initialFormValues = componentList.length
    ? {
        custom_fields: componentList.reduce((acc, item) => {
          acc[item.name] = item?.value ?? "";
          return acc;
        }, {} as ICustomFieldInitValue),
      }
    : {};

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      customer: {} as TselectedContactSendMail,
      custom_opportunity_id: "",
      stage: "",
      project_type: "",
      project_type_name: "",
      project_id: "",
      project_name: "",
      custom_fields: initialFormValues.custom_fields
        ? initialFormValues.custom_fields
        : {},
    },
    validationSchema: Yup.object({
      project_type: Yup.string().required("This field is required."),
      stage: Yup.string().required("This field is required."),
      customer: Yup.object().shape({
        user_id: Yup.number()
          .required("This field is required.")
          .notOneOf([0], "Select any Contact."),
      }),
      project_id:
        is_custom_opportunity_id === 0
          ? Yup.string()
          : Yup.string().required("This field is required."),
      // .test(
      //   "no-invalid-values",
      //   "Invalid input. Please enter a valid value.",
      //   (value) => {
      //     // Reject all variations of zeros
      //     if (/^0+$/.test(value || "")) return false;

      //     // Additional valid value checks (e.g., length or specific pattern)
      //     return true; // Passes validation
      //   }
      // ),
      project_name: Yup.string().trim().required("This field is required."),
    }),
    onSubmit: async (values) => {
      setIsSubmit(true);
      setIsLoading(true);
      let isCustomFieldValid = true;
      if (componentList.length && !isNoAccessCustomField) {
        for (let index = 0; index < componentList.length; index++) {
          const value = values?.custom_fields?.[componentList[index].name];
          const multiple = componentList[index].multiple;
          const typeComponent = componentList[index].type;
          if (multiple || typeComponent === "checkbox-group") {
            if (!value?.length) {
              isCustomFieldValid = false;
              break;
            }
          } else if (!value) {
            isCustomFieldValid = false;
            break;
          }
        }
      }

      if (!formik.isValid || !isCustomFieldValid) {
        setIsLoading(false);
        return;
      }

      const isCustomFieldAvailable = Boolean(
        formik.values.custom_fields &&
          Object.keys(formik.values.custom_fields).length
      );

      const formData = {
        customer_id: Number(formik.values.customer?.user_id),
        ...(Number(formik.values.customer.contact_id)
          ? { customer_contact_id: formik.values.customer.contact_id }
          : {}),
        stage: formik.values.stage,
        project_type: formik.values.project_type,
        project_type_name: formik.values.project_type_name,
        project_id: HTMLEntities.encode(formik.values.project_id),
        project_name: escape(formik.values.project_name),
        custom_fields:
          isCustomFieldAvailable && !isNoAccessCustomField
            ? formatCustomFieldForRequest(
                formik.values.custom_fields,
                componentList,
                date_format
              ).custom_fields
            : undefined,
        access_to_custom_fields:
          isCustomFieldAvailable && !isNoAccessCustomField ? 1 : 0,
        ...("address1" in formik.values && formik.values.address1
          ? { address1: formik.values.address1 }
          : {}),
        ...("address2" in formik.values && formik.values.address2
          ? { address2: formik.values.address2 }
          : {}),
        ...("city" in formik.values && formik.values.city
          ? { city: formik.values.city }
          : {}),
        ...("state" in formik.values && formik.values.state
          ? { state: formik.values.state }
          : {}),
        ...("zip" in formik.values && formik.values.zip
          ? { zip: formik.values.zip }
          : {}),
        ...("score" in formik.values && formik.values.score
          ? { score: formik.values.score }
          : {}),
        ...("referral_source" in formik.values && formik.values.referral_source
          ? { referral_source: formik.values.referral_source }
          : {}),
        ...("budget_amount" in formik.values && formik.values.budget_amount
          ? {
              budget_amount: Number(formik.values.budget_amount)
                ? Number(formik.values.budget_amount) * 100
                : undefined,
            }
          : {}),
        ...("est_sales_date" in formik.values && formik.values.est_sales_date
          ? { est_sales_date: formik.values.est_sales_date }
          : {}),
      };

      try {
        const data = await getWebWorkerApiParams({
          otherParams: {
            ...getValuableObj(formData),
          },
        });
        const response = (await webWorkerApi({
          url: apiRoutes.OPPORTUNITIES.add,
          method: "post",
          data: data,
        })) as IAddOpportunityApiResponse;
        if (response?.success) {
          EventLogger.log(
            EVENT_LOGGER_NAME.opportunities + EVENT_LOGGER_ACTION.added,
            1
          );
          setIsSubmit(false);
          setIsLoading(false);
          setAddOpportunities(!addOpportunities);
          navigate(`${response?.data?.inserted_id?.toString()}`);
        } else {
          notification.error({
            description: response?.message,
          });
        }
      } catch (error) {
        if (!(error as IAddOpportunityApiResponse)?.success) {
          notification.error({
            description: (error as IAddOpportunityApiResponse)?.message,
          });
        }
        setIsSubmit(false);
        setIsLoading(false);
      } finally {
        setIsSubmit(false);
        setIsLoading(false);
      }
      dispatch(setOppCallListApi());
    },
  });

  const [checkboxState, setCheckboxState] = useState({
    address: true,
    qualityScore: true,
    stage: true,
    referralSource: true,
    estimatedValue: true,
    estimatedSalesDate: true,
  });

  const handleCheckboxChange = (field: keyof typeof checkboxState) => {
    setCheckboxState((prev) => ({ ...prev, [field]: !prev[field] }));
  };

  const copyToClipboard = () => {
    // if ("stage" in formik.values.customer) {
    //   formik.setFieldValue(
    //     "stage",
    //     formik.values.customer.stage
    //       ?.toString()
    //       ?.replace(/lead/g, "opportunity")
    //   );
    // }

    if (checkboxState.stage) {
      if (
        (leadId
          ? !!formik.values.customer.stage
          : isNaN(Number(formik.values.customer.stage))) &&
        formik.values.customer.stage &&
        !(formik.values.customer.stage === "lead_stage_awarded") &&
        formik.values.customer.stage_name
      ) {
        const transformedStage = formik.values.customer.stage
          ?.toString()
          .replace("lead", "opportunity");

        const isStage = stageOptions?.some(
          (item) => item?.value === transformedStage
        );

        if (
          (isStage && transformedStage) ||
          (transformedStage && isDefaultStage)
        ) {
          formik.setFieldValue("stage", transformedStage);
        } else {
          formik.setFieldValue("stage", "");
        }
      } else {
        formik.setFieldValue("stage", "");
      }
    }
    if (checkboxState.address) {
      formik.setFieldValue(
        "address1",
        "address1" in formik.values.customer && formik.values.customer.address1
          ? formik.values.customer.address1
          : ""
      );
      formik.setFieldValue(
        "address2",
        "address2" in formik.values.customer && formik.values.customer.address2
          ? formik.values.customer.address2
          : ""
      );
      formik.setFieldValue(
        "city",
        "city" in formik.values.customer && formik.values.customer.city
          ? formik.values.customer.city
          : ""
      );
      formik.setFieldValue(
        "state",
        "state" in formik.values.customer && formik.values.customer.state
          ? formik.values.customer.state
          : ""
      );
      formik.setFieldValue(
        "zip",
        "zip" in formik.values.customer && formik.values.customer.zip
          ? formik.values.customer.zip
          : ""
      );
    }
    if (
      checkboxState.qualityScore &&
      "quality" in formik.values.customer &&
      formik.values.customer.quality
    ) {
      formik.setFieldValue(
        "score",
        formik.values.customer.quality ? formik.values.customer?.quality : "0"
      );
    }
    if (checkboxState.referralSource) {
      if (
        "referral_source" in formik.values.customer &&
        isNaN(Number(formik.values.customer.referral_source)) &&
        formik.values.customer.referral_source
      ) {
        formik.setFieldValue(
          "referral_source",
          formik.values.customer.referral_source
            ? formik.values.customer.referral_source.toString() === "angi_leads"
              ? "opportunity_angi_leads"
              : formik.values.customer.referral_source
                  .toString()
                  .replace("lead", "opportunity")
            : ""
        );
      } else {
        formik.setFieldValue("referral_source", "");
      }
    }
    if (checkboxState.estimatedValue) {
      formik.setFieldValue(
        "budget_amount",
        "lead_value" in formik.values.customer &&
          formik.values.customer.lead_value
          ? (Number(formik.values.customer?.lead_value) / 100).toString()
          : 0
      );
    }
    if (checkboxState.estimatedSalesDate) {
      if (
        "estimate_sales_date" in formik.values.customer &&
        formik.values.customer.estimate_sales_date
      ) {
        formik.setFieldValue(
          "est_sales_date",
          "estimate_sales_date" in formik.values.customer &&
            formik.values.customer.estimate_sales_date
            ? backendDateFormat(
                String(formik.values.customer.estimate_sales_date),
                dateFormat
              )
            : ""
        );
      } else {
        formik.setFieldValue("est_sales_date", "");
      }
    }
    setContactAddress(false);
    setCheckboxState({
      address: true,
      qualityScore: true,
      stage: true,
      referralSource: true,
      estimatedValue: true,
      estimatedSalesDate: true,
    });
  };

  useEffect(() => {
    if (
      Number(is_custom_opportunity_id) === 2 &&
      need_to_increment &&
      typeof last_primary_id !== "undefined"
    ) {
      formik.setFieldValue(
        "project_id",
        (Number(need_to_increment) + Number(last_primary_id)).toString()
      );
    } else {
      formik.setValues({
        ...formik.values,
        project_id: "",
      });
    }
  }, [
    is_custom_opportunity_id,
    need_to_increment,
    last_primary_id,
    addOpportunities,
    loadingCustomField,
  ]);

  useEffect(() => {
    //Call the auto number api only when drawer open and
    if (
      Number(is_custom_opportunity_id) == 2 &&
      addOpportunities &&
      module &&
      module.module_id &&
      module.module_key
    ) {
      setModuleAutoIncrementId(module.module_id, module.module_key);
    }
  }, [is_custom_opportunity_id, addOpportunities]);

  // stage dropdown Options
  // const stageTypeOptions = useMemo(
  //   () =>
  //     stageTypes.length
  //       ? stageTypes.map((item) => {
  //           return {
  //             label: `${HTMLEntities.decode(sanitizeString(item.name))}`,
  //             value: item.item_id,
  //           };
  //         })
  //       : [],
  //   [stageTypes]
  // );
  const stageOptions = useMemo(() => {
    return stageData
      .filter((item) => String(item.item_id) !== "opportunity_stage_awarded")
      .map((item) => ({
        label: item.name || "",
        value: item.item_id?.toString() as string,
      }));
    // .sort(
    //   (a, b) =>
    //     statusNames.indexOf(a.name || "") - statusNames.indexOf(b.name || "")
    // );
  }, [stageData]);

  const projectTypeOptions = useMemo(
    () =>
      projectTypeData.length
        ? projectTypeData.map((item) => {
            return {
              label: HTMLEntities.decode(sanitizeString(item.name.trim())), // At all place sanitizeString is removed : https://app.clickup.com/t/86cz5wejh
              value: item.item_id || item.key,
            };
          })
        : [],
    [projectTypeData]
  );

  const handlekeyDown = (
    event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>,
    itemType: number
  ) => {
    if (event.key === "Enter") {
      const value = event?.currentTarget?.value?.trim();
      if (itemType === oppDetailType.projectTypeId) {
        const newType = onEnterSelectSearchValue(
          event,
          projectTypeOptions || []
        );
        if (newType) {
          setPreviousOppVal({
            projectType: newType,
            stage: "",
          });
          setCustomDataAdd({
            itemType,
            name: HTMLEntities.encode(newType),
          });
          setIsConfirmDialogOpen(true);
        } else if (value) {
          notification.error({
            description: "Records already exist, no new records were added.",
          });
        }
      }
      if (itemType === oppDetailType.stageId) {
        const newType = onEnterSelectSearchValue(event, stageOptions || []);
        if (newType) {
          setPreviousOppVal({
            stage: newType,
            projectType: "",
          });
          setCustomDataAdd({
            itemType,
            name: HTMLEntities.encode(newType),
          });
          setIsConfirmDialogOpen(true);
        } else if (value) {
          notification.error({
            description: "Records already exist, no new records were added.",
          });
        }
      }
    }
  };

  const handleAddCustomData = async () => {
    if (customDataAdd?.name?.includes("\\")) {
      const availableOption = customDataAdd?.name.split("\\")[0];
      let isLabelAvailable = false;
      if (customDataAdd.itemType === oppDetailType.projectTypeId) {
        isLabelAvailable = projectTypeOptions?.some(
          (type: Option) =>
            type?.label.toLowerCase().trim() ===
            availableOption.toLowerCase().trim()
        );
      } else if (customDataAdd.itemType === oppDetailType.stageId) {
        isLabelAvailable = stageOptions?.some(
          (type: Option) =>
            type?.label.toLowerCase().trim() ===
            availableOption.toLowerCase().trim()
        );
      }
      if (isLabelAvailable) {
        notification.error({
          description: "Records already exist - no new records were added.",
        });
        if (customDataAdd.itemType === oppDetailType.projectTypeId) {
          formik.setFieldValue("project_type", formik.values.project_type);
          formik.setFieldValue(
            "project_type_name",
            formik.values.project_type_name
          );
          setProjectType(formik.values.project_type);

          setPreviousOppVal({
            ...previousOppVal,
            projectType: "",
          });
        } else if (customDataAdd.itemType === oppDetailType.stageId) {
          formik.setFieldValue("stage", formik.values.stage);
          setPreviousOppVal({
            ...previousOppVal,
            stage: "",
          });
        }
        setIsConfirmDialogOpen(false);
        return;
      }
    }

    if (!isAddingCustomData && customDataAdd?.name) {
      setIsAddingCustomData(true);

      const cDataRes = (await addOppCustomData({
        itemType: customDataAdd?.itemType,
        name: customDataAdd?.name,
      })) as ICustomDataAddUpRes;

      if (cDataRes?.success) {
        dispatch(addOppCustomDataAct(cDataRes?.data));
        // let newData = {};
        delay(() => {
          if (customDataAdd.itemType === oppDetailType.projectTypeId) {
            formik.setFieldValue("project_type", cDataRes?.data?.item_id);
            setProjectType(cDataRes?.data?.item_id);
            formik.setFieldValue("project_type_name", cDataRes?.data?.name);
          } else if (customDataAdd.itemType === oppDetailType.stageId) {
            formik.setFieldValue("stage", cDataRes?.data?.item_id);
          }
        }, 500);

        //  else if (customDataAdd.itemType === oppDetailType.referralSourceId) {
        //   newData = { referral_source: cDataRes?.data?.item_id };
        // }
        // setInputValues({
        //   ...inputValues,
        //   ...newData,
        // });
        // formik.setFieldValue("project_type", value);
        // handleUpdateField(newData);
        setIsConfirmDialogOpen(false);
        delay(() => {
          setPreviousOppVal(defaultPreviousOppVal);
        }, 500);
      } else {
        notification.error({ description: cDataRes.message });
        setIsConfirmDialogOpen(false);
        setPreviousOppVal(defaultPreviousOppVal);
      }
      setIsAddingCustomData(false);
    }
  };
  const getCustomer = async (usersIds: string) => {
    let customer: Partial<CustomerSelectedData> = {};
    const data = await getApiDefaultParams({
      otherParams: {
        filter: {
          status: "0",
        },
        directory: [2, 3, 4, 22, 23, 204],
        directories: usersIds,
        start: 0,
        sales_address_details: 1,
        limit: 10,
        app_users: false,
        service_details: undefined,
        contacts_on_demand: 1,
      },
    });

    const response = (await webWorkerApi<IgetTagsRes>({
      url: apiRoutes.COMMON.get_global_directory,
      method: "post",
      data: data,
    })) as IGetGlobalDirectoryRes;
    if (response?.success) {
      customer = response?.data[0] || {};
      formik.setFieldValue("customer", customer);
      if (customer?.lead_project_type) {
        formik.setFieldValue("project_type", customer?.lead_project_type);
        setProjectType(customer?.lead_project_type?.toString());
        formik.setFieldValue(
          "project_type_name",
          customer?.lead_project_type_name
        );
      }
      if (!!customer?.stage) {
        setIsDefaultStage(true);
      }
      formik.setFieldValue("stage", customer?.stage);
      formik.setFieldValue("stage_name", customer?.stage_name);
      setContactAddress(true);
    } else {
      formik.setFieldValue("customer", {});
      setContactAddress(false);
    }
  };
  useEffect(() => {
    if (!openedUsingBtn) {
      if ((leadId || customerId) && !loadingCustomField) {
        getCustomer(leadId?.toString() || customerId?.toString() || "");
      }
    }
  }, [leadId, loadingCustomField, customerId]);

  return (
    <>
      <Drawer
        open={addOpportunities}
        rootClassName="drawer-open"
        width={718}
        classNames={{
          body: "!p-0 !overflow-hidden",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-door-open"
              />
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {_t(`Add ${module?.singular_name || "Opportunity"} `)}
            </Header>
          </div>
        }
        closeIcon={
          <CloseButton
            onClick={() => {
              setSearchParams({});
              setAddOpportunities(false);
            }}
          />
        }
      >
        <Form
          method="post"
          noValidate
          className="py-4"
          onSubmit={formik.handleSubmit}
        >
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <div className="grid gap-4">
              <SidebarCardBorder addGap={true}>
                <div>
                  {
                    <Popover
                      content={
                        <div className="min-w-[272px]">
                          <Typography className="block text-sm py-2 px-3.5 rounded-t-lg bg-gray-50 relative before:w-full before:h-px before:bg-[linear-gradient(177deg,#a5a5a53d_24%,#fafafa_100%)] before:bottom-0 before:left-1/2 before:-translate-x-1/2 before:absolute">
                            {_t(
                              `Copy Contact Address to ${
                                module?.singular_name || "Opportunity"
                              } Address?`
                            )}
                          </Typography>
                          <div className="py-2 px-3.5">
                            <Typography className="flex items-start text-13">
                              <CheckBox
                                className="gap-1.5 text-primary-900 font-medium"
                                name="address"
                                checked={checkboxState.address}
                                onChange={() => handleCheckboxChange("address")}
                              >
                                {_t("Address:")}
                              </CheckBox>
                              <div className="flex flex-wrap md:max-w-fit max-w-[calc(100%-86px)]">
                                <Typography>
                                  {"address1" in formik.values.customer &&
                                    formik.values.customer.address1}
                                </Typography>
                                {"address2" in formik.values.customer &&
                                  formik.values.customer.address2 && (
                                    <Typography>
                                      {formik.values.customer.address1
                                        ? ", "
                                        : ""}
                                      {formik.values.customer.address2}
                                    </Typography>
                                  )}
                                {"city" in formik.values.customer &&
                                  formik.values.customer.city && (
                                    <Typography>
                                      {formik.values.customer.address1 ||
                                      formik.values.customer.address2
                                        ? ", "
                                        : ""}
                                      {formik.values.customer.city}
                                    </Typography>
                                  )}
                                {"state" in formik.values.customer &&
                                  formik.values.customer.state && (
                                    <Typography>
                                      {formik.values.customer.address1 ||
                                      formik.values.customer.address2 ||
                                      formik.values.customer.city
                                        ? ", "
                                        : ""}
                                      {formik.values.customer.state}
                                    </Typography>
                                  )}
                                {"zip" in formik.values.customer &&
                                  formik.values.customer.zip && (
                                    <Typography>
                                      {formik.values.customer.address1 ||
                                      formik.values.customer.address2 ||
                                      formik.values.customer.city ||
                                      formik.values.customer.state
                                        ? ", "
                                        : ""}
                                      {formik.values.customer.zip}
                                    </Typography>
                                  )}
                                {!formik.values.customer?.address1 &&
                                  !formik.values.customer?.address2 &&
                                  !formik.values.customer?.city &&
                                  !formik.values.customer?.state &&
                                  !formik.values.customer?.zip && (
                                    <Typography>{"-"}</Typography>
                                  )}
                              </div>
                            </Typography>
                            <Typography className="flex items-start text-13">
                              <CheckBox
                                className="gap-1.5 text-primary-900 font-medium"
                                name="qualityScore"
                                checked={checkboxState.qualityScore}
                                onChange={() =>
                                  handleCheckboxChange("qualityScore")
                                }
                              >
                                {_t("Score:")}
                              </CheckBox>
                              <Typography>
                                {"quality" in formik.values.customer &&
                                Number(formik.values.customer.quality)
                                  ? formik.values.customer.quality
                                  : "-"}
                              </Typography>
                            </Typography>
                            {formik.values.customer.stage &&
                              !(
                                formik.values.customer.stage ===
                                "lead_stage_awarded"
                              ) &&
                              (leadId
                                ? !!formik.values.customer.stage
                                : isNaN(
                                    Number(formik.values.customer.stage)
                                  )) && (
                                <Typography className="flex items-start text-13">
                                  <CheckBox
                                    className="gap-1.5 text-primary-900 font-medium"
                                    name="stage"
                                    checked={checkboxState.stage}
                                    onChange={() =>
                                      handleCheckboxChange("stage")
                                    }
                                  >
                                    {_t("Stage:")}
                                  </CheckBox>
                                  <Typography>
                                    {formik.values.customer.stage_name
                                      ? formik.values.customer.stage_name
                                      : "-"}
                                  </Typography>
                                </Typography>
                              )}
                            {/* <Typography className="block text-13">
                                <Typography className="font-medium text-13">
                                  {_t("Stage") + ": "}
                                </Typography>
                                {"stage_name" in formik.values.customer &&
                                  formik.values.customer.stage_name}
                              </Typography> */}
                            {"referral_source" in formik.values.customer &&
                              isNaN(
                                Number(formik.values.customer.referral_source)
                              ) &&
                              formik.values.customer.referral_source && (
                                <Typography className="flex items-start text-13">
                                  <CheckBox
                                    className="gap-1.5 text-primary-900 font-medium"
                                    name="referralSource"
                                    checked={checkboxState.referralSource}
                                    onChange={() =>
                                      handleCheckboxChange("referralSource")
                                    }
                                  >
                                    {_t("Referral Source:")}
                                  </CheckBox>
                                  <Typography>
                                    {formik.values.customer.referral_source_name
                                      ? formik.values.customer
                                          .referral_source_name
                                      : "-"}
                                  </Typography>
                                </Typography>
                              )}
                            <Typography className="flex items-start text-13">
                              <CheckBox
                                className="gap-1.5 text-primary-900 font-medium"
                                name="estimatedValue"
                                checked={checkboxState.estimatedValue}
                                onChange={() =>
                                  handleCheckboxChange("estimatedValue")
                                }
                              >
                                {_t("Estimated Value:")}
                              </CheckBox>
                              <Typography>
                                {"lead_value" in formik.values.customer &&
                                Number(formik.values.customer.lead_value)
                                  ? formatter(
                                      formatAmount(
                                        Number(
                                          Number(
                                            formik.values.customer.lead_value
                                          ) / 100
                                        )
                                      )
                                    ).value_with_symbol
                                  : "-"}
                              </Typography>
                            </Typography>
                            <Typography className="flex items-start text-13">
                              <CheckBox
                                className="gap-1.5 text-primary-900 font-medium"
                                name="estimatedSalesDate"
                                checked={checkboxState.estimatedSalesDate}
                                onChange={() =>
                                  handleCheckboxChange("estimatedSalesDate")
                                }
                              >
                                {_t("Est. Sales Date:")}
                              </CheckBox>
                              <Typography>
                                {"estimate_sales_date" in
                                  formik.values.customer &&
                                formik.values.customer.estimate_sales_date
                                  ? formik.values.customer.estimate_sales_date
                                  : "-"}
                              </Typography>
                            </Typography>
                            <div className="flex gap-2 justify-center mt-3">
                              <Button
                                type="primary"
                                className="w-fit"
                                onClick={copyToClipboard}
                              >
                                {_t("Yes")}
                              </Button>
                              <Button onClick={() => setContactAddress(false)}>
                                {_t("No")}
                              </Button>
                            </div>
                          </div>
                        </div>
                      }
                      placement="left"
                      trigger="click"
                      open={contactAddress}
                      onOpenChange={(newOpen: boolean) => {
                        setContactAddress(newOpen);
                      }}
                    ></Popover>
                  }
                  <FieldLabel required={true}>{_t("Contact")}</FieldLabel>
                  <ButtonField
                    name="contact"
                    labelPlacement="top"
                    required={true}
                    onClick={() => {
                      setDirectoryOpen(true);
                    }}
                    placeholder={_t("Select/Create Customer")}
                    value={
                      formik.values.customer.user_id &&
                      HTMLEntities.decode(
                        sanitizeString(formik.values.customer.display_name)
                      )
                    }
                    addonBefore={
                      formik.values.customer.user_id ? (
                        <div className="flex items-center gap-1">
                          <ContactDetailsButton
                            onClick={(e) => {
                              e.stopPropagation();
                              setContactOpen(true);
                            }}
                          />
                          <DirectoryFieldRedirectionIcon
                            className="!w-5 !h-5"
                            directoryId={
                              formik.values.customer.user_id.toString() || ""
                            }
                            directoryTypeKey={
                              formik?.values.customer?.type_key !== "contact"
                                ? formik?.values.customer?.type_key || ""
                                : formik?.values.customer?.parent_type_key || ""
                            }
                          />
                        </div>
                      ) : null
                    }
                    errorMessage={
                      isSubmit ? formik.errors.customer?.user_id : ""
                    }
                  />
                </div>
                <div className="w-full">
                  <InputField
                    label={_t("Title")}
                    labelPlacement="top"
                    placeholder={_t("Opportunity Name")}
                    value={formik.values.project_name}
                    onChange={(e) =>
                      formik.setFieldValue("project_name", e.target.value)
                    }
                    isRequired
                    errorMessage={isSubmit ? formik.errors.project_name : ""}
                  />
                </div>
                <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                  <div className="w-full">
                    <InputField
                      name="custom_opportunity_id"
                      id="custom_opportunity_id"
                      label={_t("Opportunity ID")}
                      labelPlacement="top"
                      onChange={(e) => {
                        formik.setFieldValue(
                          "project_id",
                          e.target.value.trimStart()
                        );
                        if (e.target.value?.trimStart()) {
                          formik.setFieldError("project_id", "");
                        } else {
                          formik.setFieldError(
                            "project_id",
                            _t("This field is required.")
                          );
                        }
                      }}
                      disabled={!isCustomOpportunityId}
                      isRequired={isCustomOpportunityId}
                      // value={formik.values.project_id}
                      value={
                        is_custom_opportunity_id == 0
                          ? "Save To View"
                          : formik.values?.project_id
                      }
                      maxLength={21}
                      autoComplete="off"
                      errorMessage={isSubmit ? formik.errors.project_id : ""}
                    />
                  </div>
                  <div className="w-full">
                    <SelectField
                      label={_t("Stage")}
                      showSearch={true}
                      isRequired={true}
                      labelPlacement="top"
                      value={
                        previousOppVal && previousOppVal?.stage
                          ? {
                              label: previousOppVal.stage.trim(),
                              value: "",
                            }
                          : [
                              ...stageOptions,
                              ...(leadId
                                ? [
                                    {
                                      label:
                                        formik.values.customer.stage_name || "",
                                      value: formik.values.customer.stage || "",
                                    },
                                  ]
                                : []),
                            ].find(
                              (item) =>
                                String(formik.values.stage) ===
                                String(item?.value)
                            ) || null
                      }
                      options={stageOptions}
                      onChange={(value) => {
                        formik.setFieldValue("stage", value);
                      }}
                      errorMessage={
                        isSubmit && !formik.values.stage
                          ? formik.errors.stage
                          : ""
                      }
                      addItem={addItemObject}
                      onInputKeyDown={(e) =>
                        handlekeyDown(e, oppDetailType.stageId)
                      }
                      filterOption={(input, option) =>
                        filterOptionBySubstring(input, option?.label as string)
                      }
                    />
                  </div>
                </div>
                <div className="w-full">
                  {/* <SelectField
                    label={_t("Project Type")}
                    showSearch={true}
                    isRequired={true}
                    labelPlacement="top"
                    value={formik.values.project_type}
                    options={projectTypes.map((projectType) => ({
                      label: projectType.name,
                      value: projectType.item_id.toString(),
                    }))}
                    onChange={(value) => {
                      formik.setFieldValue("project_type", value);
                    }}
                    errorMessage={isSubmit ? formik.errors.project_type : ""}
                  /> */}
                  <SelectField
                    name="project_type"
                    label={_t("Project Type")}
                    isRequired={true}
                    labelPlacement="top"
                    value={
                      previousOppVal && previousOppVal.projectType
                        ? {
                            label: `${previousOppVal.projectType.trim()}`,
                            value: "",
                          }
                        : !isProjectTypeDataLoading
                        ? formik.values.project_type
                          ? projectTypeOptions.filter((item) => {
                              return (
                                String(formik.values.project_type) ===
                                String(item?.value)
                              );
                            }).length
                            ? projectTypeOptions.filter((item) => {
                                return (
                                  String(formik.values.project_type) ===
                                  String(item?.value)
                                );
                              })
                            : formik.values.project_type_name &&
                              formik.values?.project_type
                            ? {
                                label: `${
                                  formik.values.project_type_name
                                    ? HTMLEntities.decode(
                                        sanitizeString(
                                          formik.values.project_type_name?.trim()
                                        )
                                      ) + " (Archived)"
                                    : ""
                                }`,
                                value: formik.values?.project_type,
                              }
                            : []
                          : []
                        : []
                    }
                    options={projectTypeOptions}
                    allowClear={false}
                    showSearch={true}
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                      filterOptionBySubstring(input, option?.label as string)
                    }
                    onChange={(
                      value: string | string[],
                      options?: BaseOptionType | DefaultOptionType
                    ) => {
                      formik.setValues({
                        ...formik.values,
                        project_type: value?.toString(),
                        project_type_name: options?.label,
                      });
                      setProjectType(value?.toString());
                    }}
                    errorMessage={
                      (!projectType || projectType === "0") && isSubmit
                        ? "This field is required."
                        : ""
                    }
                    addItem={addItemObject}
                    onInputKeyDown={(e) => {
                      handlekeyDown(e, oppDetailType.projectTypeId);
                    }}
                  />
                </div>
              </SidebarCardBorder>
              <OnlyRequiredCustomFields
                componentList={componentList}
                formik={formik}
                isSubmit={isSubmit}
                loadingCustomField={loadingCustomField}
              />
            </div>
          </div>
          <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
            <PrimaryButton
              htmlType="submit"
              onClick={() => setIsSubmit(true)}
              buttonText={_t(
                `Create ${module?.singular_name || "Opportunity"}`
              )}
              className="w-full justify-center"
              disabled={isLoading || loadingCustomField}
              isLoading={isLoading}
            />
          </div>
        </Form>
      </Drawer>

      {directoryOpen && (
        <SelectCustomerDrawer
          closeDrawer={() => {
            setDirectoryOpen(false);
          }}
          singleSelecte={true}
          openSelectCustomerSidebar={directoryOpen}
          options={[CFConfig.customer_key, CFConfig.lead_key]}
          setCustomer={(data) => {
            let selectedCustomer: Partial<TselectedContactSendMail>;
            if (Array.isArray(data)) {
              selectedCustomer = data[0];
            } else {
              selectedCustomer = data;
            }

            if (
              !selectedCustomer ||
              Object.keys(selectedCustomer).length === 0
            ) {
              formik.setFieldValue("customer", {});
              formik.setFieldValue("project_type", "");
              setProjectType("");
              formik.setFieldValue("project_type_name", "");
              formik.setFieldValue("stage", "");
              return;
            }

            if (selectedCustomer && Object.keys(selectedCustomer).length) {
              if (selectedCustomer?.lead_project_type) {
                // setIsSubmit(true);
                formik.setFieldValue(
                  "project_type",
                  selectedCustomer?.lead_project_type?.toString() || ""
                );
                formik.setFieldValue(
                  "project_type_name",
                  selectedCustomer?.lead_project_type_name
                );
                setProjectType(selectedCustomer?.lead_project_type?.toString());
              } else {
                // setIsSubmit(true);
                setProjectType("");
                formik.setFieldValue("project_type", "");
                formik.setFieldValue("project_type_name", "");
              }

              setContactAddress(true);
            }
            if (Number(selectedCustomer?.contact_id)) {
              const {
                user_address1,
                user_address2,
                user_city,
                user_state,
                user_zip,
              } = selectedCustomer;

              selectedCustomer = {
                ...selectedCustomer,
                address1: user_address1,
                address2: user_address2,
                city: user_city,
                state: user_state,
                zip: user_zip,
              };
            }
            setIsDefaultStage(false);
            formik.setFieldValue("customer", selectedCustomer || {});
          }}
          selectedCustomer={
            formik.values.customer.user_id ? [formik.values.customer] : []
          }
          groupCheckBox={false}
        />
      )}
      {contactOpen && (
        <ContactDetailsModal
          isOpenContact={contactOpen}
          contactId={formik.values.customer.user_id}
          onCloseModal={() => {
            setContactOpen(false);
          }}
          additional_contact_id={0}
        />
      )}
      {isConfirmDialogOpen && (
        <ConfirmModal
          isOpen={isConfirmDialogOpen}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${HTMLEntities.decode(
              sanitizeString(customDataAdd?.name?.trim() || "")
            )}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={closeConfirmationModal}
          onAccept={() => {
            handleAddCustomData();
          }}
          onDecline={() => {
            setPreviousOppVal(defaultPreviousOppVal);
            closeConfirmationModal();
          }}
        />
      )}
    </>
  );
};

export default AddOpportunities;
